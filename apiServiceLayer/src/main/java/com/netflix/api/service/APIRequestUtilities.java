package com.netflix.api.service;

import com.netflix.api.platform.http.throttling.APIRequestThrottle;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;

public class APIRequestUtilities {

  private APIRequestUtilities() {}

  private static final Property<Boolean> ENFORCE_BROWNOUT =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.enforce.brownout", Boolean.class)
          .orElse(true);

  public static boolean isInBrownoutMode() {
    return ENFORCE_BROWNOUT.get() && APIRequestThrottle.BROWNOUT.get();
  }
}
