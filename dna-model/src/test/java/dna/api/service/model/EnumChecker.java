package dna.api.service.model;

import static org.junit.jupiter.api.Assertions.fail;

import java.util.Arrays;
import java.util.Collection;
import java.util.stream.Collectors;

class EnumChecker {

  private EnumChecker() {}

  /**
   * Checks if provided array of enum values (e1Values) matches all enum values of provided class
   * (e2Class)
   *
   * @param e1Values array of enum values (typically this should be the non-DNA values)
   * @param e2Class DNA class that represents the enum values of e1Values
   * @param <E1> non-DNA enum class
   * @param <E2> DNA enum class
   * @param filename the file where the enum is defined
   */
  static <E1 extends Enum<E1>, E2 extends Enum<E2>> void noMissingValues(
      E1[] e1Values, Class<E2> e2Class, String filename) {
    checkNotMissing(e1Values, e2Class, filename);
  }

  /**
   * Checks if provided array of enum values (e1Values) matches all enum values of provided class
   * (e2Class). Skips "UNRECOGNIZED" value in e1Values (all proto enums have UNRECOGNIZED value).
   *
   * @param e1Values array of enum values (typically this should be the non-DNA values)
   * @param e2Class DNA class that represents the enum values of e1Values
   * @param <E1> non-DNA enum class
   * @param <E2> DNA enum class
   * @param filename the file where the enum is defined
   */
  static <E1 extends Enum<E1>, E2 extends Enum<E2>> void noMissingValuesSkippingUnrecognized(
      E1[] e1Values, Class<E2> e2Class, String filename) {
    Arrays.stream(e1Values)
        .filter(v -> !"UNRECOGNIZED".equals(v.name()))
        .toList()
        .toArray(e1Values);
    e1Values = Arrays.copyOfRange(e1Values, 0, e1Values.length - 1);

    checkNotMissing(e1Values, e2Class, filename);
  }

  static <E1 extends Enum<E1>, E2 extends Enum<E2>> Collection<E1> findMissingValues(
      E1[] e1Values, Class<E2> e2Class) {
    return Arrays.stream(e1Values)
        .filter(
            e1Value -> {
              try {
                Enum.valueOf(e2Class, e1Value.name());
                return false;
              } catch (IllegalArgumentException e) {
                return true;
              }
            })
        .collect(Collectors.toSet());
  }

  private static <E1 extends Enum<E1>, E2 extends Enum<E2>> void checkNotMissing(
      E1[] values, Class<E2> type, String filename) {
    var missing = findMissingValues(values, type);
    if (missing.isEmpty()) return;

    var subject = missing.size() > 1 ? "values" : "value";
    var names = missing.stream().map(Enum::name).collect(Collectors.joining(", "));
    var className = type.getSimpleName();
    var message =
        String.format(
            "Missing %s %s for %s (hint: add the missing %s to the enum defined in %s)",
            subject, names, className, subject, filename);

    fail(message);
  }
}
