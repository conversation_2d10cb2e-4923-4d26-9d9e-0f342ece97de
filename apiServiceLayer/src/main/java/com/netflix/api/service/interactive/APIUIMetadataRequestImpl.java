package com.netflix.api.service.interactive;

import com.netflix.api.service.interactive.APIInteractiveExperienceService.APIInteractiveUserStateSnapshot;
import com.netflix.api.service.interactive.APIInteractiveExperienceService.APIUIMetadataRequest;
import java.util.List;

public class APIUIMetadataRequestImpl implements APIUIMetadataRequest {
  boolean isKidsUi;
  boolean useSslForAssets;
  List<String> preferredLocales;
  List<String> supportedLocalesForDevice;
  boolean usePseudoLoc;
  List<String> imageExtensionPreferences;
  String audioLocaleOverride;
  String textLocaleOverride;
  String schemaVersion;
  List<String> momentIds;
  Long playbackPositionMs;
  String intent;
  APIInteractiveUserStateSnapshot userState;
  Double imageScaleFactor;

  public boolean isKidsUi() {
    return isKidsUi;
  }

  public boolean isUseSslForAssets() {
    return useSslForAssets;
  }

  public APIUIMetadataRequest setUseSslForAssets(boolean useSslForAssets) {
    this.useSslForAssets = useSslForAssets;
    return this;
  }

  public List<String> getPreferredLocales() {
    return preferredLocales;
  }

  public APIUIMetadataRequest setPreferredLocales(List<String> preferredLocales) {
    this.preferredLocales = preferredLocales;
    return this;
  }

  public List<String> getSupportedLocalesForDevice() {
    return supportedLocalesForDevice;
  }

  public boolean isUsePseudoLoc() {
    return usePseudoLoc;
  }

  public APIUIMetadataRequest setUsePseudoLoc(boolean usePseudoLoc) {
    this.usePseudoLoc = usePseudoLoc;
    return this;
  }

  public List<String> getImageExtensionPreferences() {
    return imageExtensionPreferences;
  }

  public APIUIMetadataRequest setImageExtensionPreferences(List<String> imageExtensionPreferences) {
    this.imageExtensionPreferences = imageExtensionPreferences;
    return this;
  }

  public String getAudioLocaleOverride() {
    return audioLocaleOverride;
  }

  public APIUIMetadataRequest setAudioLocaleOverride(String audioLocaleOverride) {
    this.audioLocaleOverride = audioLocaleOverride;
    return this;
  }

  public String getTextLocaleOverride() {
    return textLocaleOverride;
  }

  public APIUIMetadataRequest setTextLocaleOverride(String textLocaleOverride) {
    this.textLocaleOverride = textLocaleOverride;
    return this;
  }

  public String getSchemaVersion() {
    return schemaVersion;
  }

  public APIUIMetadataRequest setSchemaVersion(String schemaVersion) {
    this.schemaVersion = schemaVersion;
    return this;
  }

  public List<String> getMomentIds() {
    return momentIds;
  }

  public APIUIMetadataRequest setMomentIds(List<String> momentIds) {
    this.momentIds = momentIds;
    return this;
  }

  public Long getPlaybackPositionMs() {
    return playbackPositionMs;
  }

  public APIUIMetadataRequest setPlaybackPositionMs(Long playbackPositionMs) {
    this.playbackPositionMs = playbackPositionMs;
    return this;
  }

  public String getIntent() {
    return intent;
  }

  public APIUIMetadataRequest setIntent(String intent) {
    this.intent = intent;
    return this;
  }

  public APIInteractiveUserStateSnapshot getUserState() {
    return userState;
  }

  public APIUIMetadataRequest setUserState(APIInteractiveUserStateSnapshot userState) {
    this.userState = userState;
    return this;
  }

  public Double getImageScaleFactor() {
    return imageScaleFactor;
  }

  public APIUIMetadataRequest setImageScaleFactor(Double imageScaleFactor) {
    this.imageScaleFactor = imageScaleFactor;
    return this;
  }
}
