package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NapaPostplayTeaserSection extends NapaPostplaySection {
  private NapaPostplayItemData videoItem;

  public NapaPostplayItemData getVideoItem() {
    return videoItem;
  }

  public NapaPostplayTeaserSection setVideoItem(NapaPostplayItemData videoItem) {
    this.videoItem = videoItem;
    return this;
  }
}
