package com.netflix.api.platform.stats;

import com.netflix.archaius.api.Property;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.atomic.AtomicReferenceArray;
import java.util.concurrent.locks.ReentrantLock;
import org.jetbrains.annotations.NotNull;

public abstract class RollingNumber {

  private static final Time ACTUAL_TIME = new ActualTime();
  private final Time time;

  final int timeInMilliseconds;
  final int numberOfBuckets;
  final int bucketSizeInMilliseconds;

  final BucketCircularArray buckets;
  private final CumulativeSum cumulativeSum = new CumulativeSum();

  protected RollingNumber(Property<Integer> timeInMilliseconds, Property<Integer> numberOfBuckets) {
    this(ACTUAL_TIME, timeInMilliseconds.get(), numberOfBuckets.get());
  }

  protected RollingNumber(Time time, int timeInMilliseconds, int numberOfBuckets) {
    this.time = time;
    this.timeInMilliseconds = timeInMilliseconds;
    this.numberOfBuckets = numberOfBuckets;

    if (timeInMilliseconds % numberOfBuckets != 0) {
      throw new IllegalArgumentException(
          "The timeInMilliseconds must divide equally into numberOfBuckets. For example 1000/10 is ok, 1000/11 is not.");
    }
    bucketSizeInMilliseconds = timeInMilliseconds / numberOfBuckets;

    buckets = new BucketCircularArray(numberOfBuckets);
  }

  public void increment(Type type) {
    getCurrentBucket().get(type).incrementAndGet();
  }

  /**
   * A number in a rolling bucket that you can set.
   *
   * @param type
   * @param value
   */
  public void set(Type type, int value) {
    getCurrentBucket().get(type).set(value);
  }

  /**
   * A compareAndSet counterpart to <code>set</code> to allow atomically safe updates of an existing
   * value.
   *
   * <p>Will set value to 'value' if 'current value' == 'expected'
   *
   * @param type
   * @param expected
   * @param value
   * @return true if successful, false means the current actual value was not equal to expected
   *     value.
   */
  public boolean compareAndSet(Type type, int expected, int value) {
    return getCurrentBucket().get(type).compareAndSet(expected, value);
  }

  /**
   * Force a reset of all counters (clear all buckets) so that statistics start being gathered from
   * scratch.
   */
  public void reset() {
    // if we are resetting, that means the lastBucket won't have a chance to be captured in
    // CumulativeSum, so let's
    // do it here
    Bucket lastBucket = buckets.peekLast();
    if (lastBucket != null) {
      cumulativeSum.addBucket(lastBucket);
    }

    // clear buckets so we start over again
    buckets.clear();
  }

  /**
   * Get the sum of all buckets in the rolling counter for the given CounterType.
   *
   * @param type
   * @return
   */
  public int getCount(Type type) {
    return getSum(type);
  }

  /**
   * Get the cumulative sum of all buckets ever since the JVM started without rolling.
   *
   * <p>See <code>getCount</code> for the rolling sum.
   *
   * @param type
   * @return
   */
  public long getCumulativeCount(Type type) {
    // this isn't 100% atomic since multiple threads can be affecting latestBucket & cumulativeSum
    // independently
    // but that's okay since the count is always a moving target and we're accepting a "point in
    // time" best attempt
    // we are however putting 'getValueOfLatestBucket' first since it can have side-affects on
    // cumulativeSum whereas
    // the inverse is not true
    return getValueOfLatestBucket(type) + cumulativeSum.get(type).get();
  }

  /**
   * Get the sum of all buckets in the rolling counter for the given CounterType.
   *
   * @param type
   * @return
   */
  public int getSum(Type type) {
    Bucket lastBucket = getCurrentBucket();
    if (lastBucket == null) return 0;

    int sum = 0;
    for (Bucket b : buckets) {
      sum += b.get(type).get();
    }
    return sum;
  }

  /**
   * Get the sum of all buckets in the rolling counter for the given CounterType.
   *
   * @param type
   * @return
   */
  public int getValueOfLatestBucket(Type type) {
    Bucket lastBucket = getCurrentBucket();
    if (lastBucket == null) return 0;
    // we have bucket data so we'll return the lastBucket
    return lastBucket.get(type).get();
  }

  /**
   * Get an array of values for all buckets in the rolling counter for the given CounterType.
   *
   * <p>Index 0 is the oldest bucket.
   *
   * @param type
   * @return
   */
  public int[] getValues(Type type) {
    Bucket lastBucket = getCurrentBucket();
    if (lastBucket == null) return new int[0];

    // get buckets as an array (which is a copy of the current state at this point in time)
    Bucket[] bucketArray = buckets.getArray();

    // we have bucket data so we'll return an array of values for all buckets
    int[] values = new int[bucketArray.length];
    int i = 0;
    for (Bucket bucket : bucketArray) {
      values[i++] = bucket.get(type).get();
    }
    return values;
  }

  /**
   * Get the max value of values in all buckets for the given CounterType.
   *
   * @param type
   * @return
   */
  public int getMaxValue(Type type) {
    int[] values = getValues(type);
    if (values.length == 0) return 0;

    Arrays.sort(values);
    return values[values.length - 1];
  }

  private final ReentrantLock newBucketLock = new ReentrantLock();

  Bucket getCurrentBucket() {
    long currentTime = time.getCurrentTimeInMillis();

    /* a shortcut to try and get the most common result of immediately finding the current bucket */

    /**
     * Retrieve the latest bucket if the given time is BEFORE the end of the bucket window,
     * otherwise it returns NULL.
     *
     * <p>NOTE: This is thread-safe because it's accessing 'buckets' which is a LinkedBlockingDeque
     */
    Bucket currentBucket = buckets.peekLast();
    if (currentBucket != null
        && currentTime < currentBucket.windowStart + bucketSizeInMilliseconds) {
      // if we're within the bucket 'window of time' return the current one
      // NOTE: We do not worry if we are BEFORE the window in a weird case of where thread
      // scheduling causes that
      // to occur,
      // we'll just use the latest as long as we're not AFTER the window
      return currentBucket;
    }

    /* if we didn't find the current bucket above, then we have to create one */

    /**
     * The following needs to be synchronized/locked even with a synchronized/thread-safe data
     * structure such as LinkedBlockingDeque because the logic involves multiple steps to check
     * existence, create an object then insert the object. The 'check' or 'insertion' themselves are
     * thread-safe by themselves but not the aggregate algorithm, thus we put this entire block of
     * logic inside synchronized.
     *
     * <p>I am using a tryLock if/then
     * (http://download.oracle.com/javase/6/docs/api/java/util/concurrent/locks/Lock.html#tryLock())
     * so that a single thread will get the lock and as soon as one thread gets the lock all others
     * will go the 'else' block and just return the currentBucket until the newBucket is created.
     * This should allow the throughput to be far higher and only slow down 1 thread instead of
     * blocking all of them in each cycle of creating a new bucket based on some testing (and it
     * makes sense that it should as well).
     *
     * <p>This means the timing won't be exact to the millisecond as to what data ends up in a
     * bucket, but that's acceptable. It's not critical to have exact precision to the millisecond,
     * as long as it's rolling, if we can instead reduce the impact synchronization.
     *
     * <p>More importantly though it means that the 'if' block within the lock needs to be careful
     * about what it changes that can still be accessed concurrently in the 'else' block since we're
     * not completely synchronizing access.
     *
     * <p>For example, we can't have a multi-step process to add a bucket, remove a bucket, then
     * update the sum since the 'else' block of code can retrieve the sum while this is all
     * happening. The trade-off is that we don't maintain the rolling sum and let readers just
     * iterate bucket to calculate the sum themselves. This is an example of favoring
     * write-performance instead of read-performance and how the tryLock versus a synchronized block
     * needs to be accommodated.
     */
    if (newBucketLock.tryLock()) {
      try {
        if (buckets.peekLast() == null) {
          // the list is empty so create the first bucket
          Bucket newBucket = new Bucket(currentTime);
          buckets.addLast(newBucket);
          return newBucket;
        } else {
          // We go into a loop so that it will create as many buckets as needed to catch up to the
          // current
          // time
          // as we want the buckets complete even if we don't have transactions during a period of
          // time.
          for (int i = 0; i < numberOfBuckets; i++) {
            // we have at least 1 bucket so retrieve it
            Bucket lastBucket = buckets.peekLast();
            if (currentTime < lastBucket.windowStart + bucketSizeInMilliseconds) {
              // if we're within the bucket 'window of time' return the current one
              // NOTE: We do not worry if we are BEFORE the window in a weird case of where thread
              // scheduling causes that to occur,
              // we'll just use the latest as long as we're not AFTER the window
              return lastBucket;
            } else if (currentTime - (lastBucket.windowStart + bucketSizeInMilliseconds)
                > timeInMilliseconds) {
              // the time passed is greater than the entire rolling counter so we want to clear it
              // all and
              // start from scratch
              reset();
              // recursively call getCurrentBucket which will create a new bucket and return it
              return getCurrentBucket();
            } else { // we're past the window so we need to create a new bucket
              // create a new bucket and add it as the new 'last'
              buckets.addLast(new Bucket(lastBucket.windowStart + bucketSizeInMilliseconds));
              // add the lastBucket values to the cumulativeSum
              cumulativeSum.addBucket(lastBucket);
            }
          }
          // we have finished the for-loop and created all of the buckets, so return the lastBucket
          // now
          return buckets.peekLast();
        }
      } finally {
        newBucketLock.unlock();
      }
    } else {
      currentBucket = buckets.peekLast();
      if (currentBucket != null) {
        // we didn't get the lock so just return the latest bucket while another thread creates the
        // next one
        return currentBucket;
      } else {
        // the rare scenario where multiple threads raced to create the very first bucket
        // wait slightly and then use recursion while the other thread finishes creating a bucket
        try {
          Thread.sleep(5);
        } catch (Exception e) {
          // ignore
        }
        return getCurrentBucket();
      }
    }
  }

  public abstract Type[] getAllTypes();

  protected interface Time {
    long getCurrentTimeInMillis();
  }

  private static class ActualTime implements Time {

    @Override
    public long getCurrentTimeInMillis() {
      return System.currentTimeMillis();
    }
  }

  /** Counters for a given 'bucket' of time. */
  class Bucket {
    final long windowStart;
    final AtomicInteger[] atomicIntegerForCounterType;

    Bucket(long startTime) {
      this.windowStart = startTime;

      // initialize the array of AtomicIntegers
      atomicIntegerForCounterType = new AtomicInteger[getAllTypes().length];
      for (Type type : getAllTypes()) {
        atomicIntegerForCounterType[type.ordinal()] = new AtomicInteger();
      }
    }

    AtomicInteger get(Type type) {
      return atomicIntegerForCounterType[type.ordinal()];
    }
  }

  /** Cumulative counters (from start of JVM) from each Type */
  private class CumulativeSum {
    final AtomicLong[] atomicLongForCounterType;

    CumulativeSum() {

      // initialize the array of AtomicLong
      atomicLongForCounterType = new AtomicLong[getAllTypes().length];
      for (Type type : getAllTypes()) {
        atomicLongForCounterType[type.ordinal()] = new AtomicLong();
      }
    }

    public void addBucket(Bucket lastBucket) {
      for (Type type : getAllTypes()) {
        atomicLongForCounterType[type.ordinal()].addAndGet(lastBucket.get(type).get());
      }
    }

    AtomicLong get(Type type) {
      return atomicLongForCounterType[type.ordinal()];
    }
  }

  public interface Type {
    int ordinal();
  }

  /**
   * This is a circular array acting as a FIFO queue.
   *
   * <p>It purposefully does NOT implement Deque or some other Collection interface as it only
   * implements functionality necessary for this RollingNumber use case.
   *
   * <p>Important Thread-Safety Note: This is ONLY thread-safe within the context of RollingNumber
   * and the protection it gives in the <code>getCurrentBucket</code> method. It uses
   * AtomicReference objects to ensure anything done outside of <code>getCurrentBucket</code> is
   * thread-safe, and to ensure visibility of changes across threads (ie. volatility) but the
   * addLast and removeFirst methods are NOT thread-safe for external access they depend upon the
   * lock.tryLock() protection in <code>getCurrentBucket</code> which ensures only a single thread
   * will access them at at time.
   */
  class BucketCircularArray implements Iterable<Bucket> {
    private final AtomicReference<ListState> state;
    private final int dataLength; // we don't resize, we always stay the same, so remember this
    private final int numBuckets;

    /**
     * Immutable object that is atomically set every time the state of the BucketCircularArray
     * changes
     *
     * <p>This handles the compound operations
     */
    private class ListState {
      /*
       * this is an AtomicReferenceArray and not a normal Array because we're copying the reference
       * between ListState objects and multiple threads could maintain references across these
       * compound operations so I want the visibility/concurrency guarantees
       */
      private final AtomicReferenceArray<Bucket> data;
      private final int size;
      private final int tail;
      private final int head;

      private ListState(AtomicReferenceArray<Bucket> data, int head, int tail) {
        this.head = head;
        this.tail = tail;
        if (head == 0 && tail == 0) {
          size = 0;
        } else {
          this.size = (tail + dataLength - head) % dataLength;
        }
        this.data = data;
      }

      public Bucket tail() {
        if (size == 0) {
          return null;
        } else {
          // we want to get the last item, so size()-1
          return data.get(convert(size - 1));
        }
      }

      private Bucket[] getArray() {
        /*
         * this isn't technically thread-safe since it requires multiple reads on something that can change
         * but since we never clear the data directly, only increment/decrement head/tail we would never get a NULL
         * just potentially return stale data which we are okay with doing
         */
        ArrayList<Bucket> array = new ArrayList<>();
        for (int i = 0; i < size; i++) {
          array.add(data.get(convert(i)));
        }
        return array.toArray(new Bucket[0]);
      }

      private ListState incrementTail() {
        /* if incrementing results in growing larger than 'length' which is the max we should be at, then also increment head (equivalent of removeFirst but done atomically) */
        if (size == numBuckets) {
          // increment tail and head
          return new ListState(data, (head + 1) % dataLength, (tail + 1) % dataLength);
        } else {
          // increment only tail
          return new ListState(data, head, (tail + 1) % dataLength);
        }
      }

      public ListState clear() {
        return new ListState(new AtomicReferenceArray<>(dataLength), 0, 0);
      }

      public ListState addBucket(Bucket b) {
        /*
         * We could in theory have 2 threads addBucket concurrently and this compound operation would interleave.
         * <p>
         * This should NOT happen since getCurrentBucket is supposed to be executed by a single thread.
         * <p>
         * If it does happen, it's not a huge deal as incrementTail() will be protected by compareAndSet and one of the two addBucket calls will succeed with one of the Buckets.
         * <p>
         * In either case, a single Bucket will be returned as "last" and data loss should not occur and everything keeps in sync for head/tail.
         * <p>
         * Also, it's fine to set it before incrementTail because nothing else should be referencing that index position until incrementTail occurs.
         */
        data.set(tail, b);
        return incrementTail();
      }

      // The convert() method takes a logical index (as if head was
      // always 0) and calculates the index within elementData
      private int convert(int index) {
        return (index + head) % dataLength;
      }
    }

    BucketCircularArray(int size) {
      AtomicReferenceArray<Bucket> bucket =
          new AtomicReferenceArray<>(size + 1); // + 1 as extra room for
      state = new AtomicReference<>(new ListState(bucket, 0, 0));
      dataLength = bucket.length();
      numBuckets = size;
    }

    public void clear() {
      while (true) {
        /*
         * it should be very hard to not succeed the first pass thru since this is typically is only called from
         * a single thread protected by a tryLock, but there is at least 1 other place (at time of writing this comment)
         * where reset can be called from (CircuitBreaker.markSuccess after circuit was tripped) so it can
         * in an edge-case conflict.
         *
         * Instead of trying to determine if someone already successfully called clear() and we should skip
         * we will have both calls reset the circuit, even if that means losing data added in between the two
         * depending on thread scheduling.
         *
         * The rare scenario in which that would occur, we'll accept the possible data loss while clearing it
         * since the code has stated its desire to clear() anyways.
         */
        ListState current = state.get();
        ListState newState = current.clear();
        if (state.compareAndSet(current, newState)) {
          return;
        }
      }
    }

    /**
     * Returns an iterator on a copy of the internal array so that the iterator won't fail by
     * buckets being added/removed concurrently.
     */
    @NotNull
    public Iterator<Bucket> iterator() {
      return List.of(getArray()).iterator();
    }

    public void addLast(Bucket o) {
      ListState currentState = state.get();
      // create new version of state (what we want it to become)
      ListState newState = currentState.addBucket(o);

      /*
       * use compareAndSet to set in case multiple threads are attempting (which shouldn't be the case because since addLast will ONLY be called by a single thread at a time due to protection provided in <code>getCurrentBucket</code>)
       */
      state.compareAndSet(currentState, newState);
    }

    public Bucket getLast() {
      return peekLast();
    }

    public int size() {
      // the size can also be worked out each time as:
      // (tail + data.length() - head) % data.length()
      return state.get().size;
    }

    public Bucket peekLast() {
      return state.get().tail();
    }

    private Bucket[] getArray() {
      return state.get().getArray();
    }
  }
}
