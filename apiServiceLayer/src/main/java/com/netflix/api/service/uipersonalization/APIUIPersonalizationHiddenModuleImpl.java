package com.netflix.api.service.uipersonalization;

import com.netflix.uipersonalization.protogen.HiddenModule;

public class APIUIPersonalizationHiddenModuleImpl implements APIUIPersonalizationHiddenModule {
  private final HiddenModule hiddenModule;

  APIUIPersonalizationHiddenModuleImpl(HiddenModule hiddenModule) {
    this.hiddenModule = hiddenModule;
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#HiddenModule.module_id"
   */
  @Override
  public String getModuleId() {
    return this.hiddenModule.getModuleId();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#HiddenModule.esn"
   */
  @Override
  public String getEsn() {
    return this.hiddenModule.getEsn();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#HiddenModule.device_type_id"
   */
  @Override
  public String getDeviceTypeId() {
    return this.hiddenModule.getDeviceTypeId();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#HiddenModule.ui_flavor"
   */
  @Override
  public String getUiFlavor() {
    return this.hiddenModule.getUiFlavor();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#HiddenModule.creation_timestamp"
   */
  @Override
  public Long getCreationTimestamp() {
    return this.hiddenModule.getCreationTimestamp();
  }

  public static HiddenModule toHiddenModule(APIUIPersonalizationHiddenModule module) {
    return ((APIUIPersonalizationHiddenModuleImpl) module).hiddenModule;
  }
}
