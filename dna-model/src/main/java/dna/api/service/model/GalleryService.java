package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class GalleryService implements GraphData {
  public Map<String, AnnotatedList> nonMemberGallery;

  public Map<String, AnnotatedList> memberGallery;

  public Map<String, Map<String, AnnotatedLocale>> galleryLanguages;
}
