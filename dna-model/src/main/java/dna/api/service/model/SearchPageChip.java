package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** Deprecated, to be removed */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SearchPageChip {
  private SearchPageEntity entity;

  public SearchPageEntity getEntity() {
    return entity;
  }

  public SearchPageChip setEntity(SearchPageEntity entity) {
    this.entity = entity;
    return this;
  }
}
