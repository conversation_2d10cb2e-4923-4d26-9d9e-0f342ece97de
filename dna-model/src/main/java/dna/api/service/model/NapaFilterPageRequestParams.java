package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaFilterPageRequestParams {
  private NapaFilterRequestKind filterRequestKind;

  private NapaContentKindFilter contentTypeFilter;

  private Boolean onlyOriginals;

  /** turbo collections */
  private List<Long> categories;

  private NapaYearRangeFilter yearRangeFilter;

  private List<NapaFilterLanguageInput> originalLanguagesFilter;

  private List<NapaFilterLanguageInput> dubLanguagesFilter;

  private List<NapaFilterLanguageInput> subtitleLanguagesFilter;

  private List<NapaFilterMaturityViewingLevel> maturityViewingLevels;

  public NapaFilterRequestKind getFilterRequestKind() {
    return filterRequestKind;
  }

  public NapaContentKindFilter getContentTypeFilter() {
    return contentTypeFilter;
  }

  public Boolean getOnlyOriginals() {
    return onlyOriginals;
  }

  public List<Long> getCategories() {
    return categories;
  }

  public NapaYearRangeFilter getYearRangeFilter() {
    return yearRangeFilter;
  }

  public List<NapaFilterLanguageInput> getOriginalLanguagesFilter() {
    return originalLanguagesFilter;
  }

  public List<NapaFilterLanguageInput> getDubLanguagesFilter() {
    return dubLanguagesFilter;
  }

  public List<NapaFilterLanguageInput> getSubtitleLanguagesFilter() {
    return subtitleLanguagesFilter;
  }

  public List<NapaFilterMaturityViewingLevel> getMaturityViewingLevels() {
    return maturityViewingLevels;
  }

  public NapaFilterPageRequestParams setFilterRequestKind(NapaFilterRequestKind filterRequestKind) {
    this.filterRequestKind = filterRequestKind;
    return this;
  }

  public NapaFilterPageRequestParams setContentTypeFilter(NapaContentKindFilter contentTypeFilter) {
    this.contentTypeFilter = contentTypeFilter;
    return this;
  }

  public NapaFilterPageRequestParams setOnlyOriginals(Boolean onlyOriginals) {
    this.onlyOriginals = onlyOriginals;
    return this;
  }

  public NapaFilterPageRequestParams setCategories(List<Long> categories) {
    this.categories = categories;
    return this;
  }

  public NapaFilterPageRequestParams setYearRangeFilter(NapaYearRangeFilter yearRangeFilter) {
    this.yearRangeFilter = yearRangeFilter;
    return this;
  }

  public NapaFilterPageRequestParams setOriginalLanguagesFilter(
      List<NapaFilterLanguageInput> originalLanguagesFilter) {
    this.originalLanguagesFilter = originalLanguagesFilter;
    return this;
  }

  public NapaFilterPageRequestParams setDubLanguagesFilter(
      List<NapaFilterLanguageInput> dubLanguagesFilter) {
    this.dubLanguagesFilter = dubLanguagesFilter;
    return this;
  }

  public NapaFilterPageRequestParams setSubtitleLanguagesFilter(
      List<NapaFilterLanguageInput> subtitleLanguagesFilter) {
    this.subtitleLanguagesFilter = subtitleLanguagesFilter;
    return this;
  }

  public NapaFilterPageRequestParams setMaturityViewingLevels(
      List<NapaFilterMaturityViewingLevel> maturityViewingLevels) {
    this.maturityViewingLevels = maturityViewingLevels;
    return this;
  }
}
