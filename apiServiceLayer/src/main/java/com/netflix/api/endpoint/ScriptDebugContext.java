package com.netflix.api.endpoint;

import com.netflix.api.service.APICountry;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import jakarta.servlet.http.HttpServletRequest;

/*
 * Gathers String representation of relevant context for an API request for debugging purposes
 *
 * At the moment, contains:
 * - URI
 * - Query params
 * - Customer ID
 * - ESN
 * - NetflixId cookie
 * - SecureNetflixId cookies
 * - Country ID
 *
 */
public class ScriptDebugContext {

  private ScriptDebugContext() {}

  public static String getRequestInfo() {
    final APIRequest apiRequest = APIRequest.getCurrentRequest();

    String reqId = "";
    String uri = "";
    String queryParams = "";
    String esn = "";
    String countryString = "";
    String netflixId = "";
    String secureNetflixId = "";
    if (apiRequest != null) {
      final APIRequestContext reqContext = apiRequest.getRequestContext();
      final HttpServletRequest servletRequest = apiRequest.getServletRequest();
      if (servletRequest != null) {
        uri = servletRequest.getRequestURI();
        queryParams = servletRequest.getQueryString();
        reqId = reqContext.getRequestId();
      }
      esn = reqContext.getESN();
      final APICountry country = reqContext.getCountry();
      if (country != null) {
        countryString = country.getId();
      }
    }

    return "url=["
        + uri
        + "]"
        + "query=["
        + queryParams
        + "]"
        + "esn=["
        + esn
        + "]"
        + "nfid=["
        + netflixId
        + "]"
        + "snfid=["
        + secureNetflixId
        + "]"
        + "country=["
        + countryString
        + "]"
        + "reqid=["
        + reqId
        + "]";
  }
}
