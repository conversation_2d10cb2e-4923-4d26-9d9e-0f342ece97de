package com.netflix.api.service.batch;

import com.google.inject.Provider;
import com.netflix.api.dependencies.merchagg.MerchAggPlatformAdapter;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.identity.CurrentIdentityResult;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.map.annotation.MapAnnotationConstants;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.type.IdObject;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public class APILoloUtils {

  private APILoloUtils() {}

  public static final String SUPPORTED_LOLOMO_FEATURES_KEY = "supportedLolomoFeatures";
  public static final String SUPPORTED_BILLBOARDS_KEY = "billboardTypesSupported";
  public static final String CLIENT_APP_CAPABILITY_KEY = "clientAppCapability";
  public static final String OUI_ANNOTATIONS_PLATFORM_KEY = "platform";
  public static final String OUI_ANNOTATIONS_UIVERSION_KEY = "uiVersion";
  public static final String OUI_ANNOTATIONS_ORCVERSION_KEY = "orcVersion";
  public static final String OUI_ANNOTATIONS_IDIOM_KEY = "idiom";

  public static Map<String, Object> getRequestContextEntities(
      Provider<APIRequest> currentRequest, boolean fallbacksEnabled) {
    return getRequestContextEntities(
        currentRequest.get().getUser(), currentRequest.get(), fallbacksEnabled);
  }

  public static Map<String, Object> getRequestContextEntities(
      APIUser user, APIRequest currentRequest, boolean fallbacksEnabled) {
    Map<String, Object> annotations = new HashMap<>();
    // entities available in the request context explicitly set
    annotations.put(
        MapAnnotationConstants.COUNTRY, RequestContextWrapper.get().getCountry().getId());
    if (user != null) {
      Long customerId = APIUserUtil.getCustomerId(user);
      checkAndLogInconsistentCustomerId(customerId);
      annotations.put(MapAnnotationConstants.VISITOR, customerId);
      annotations.put(MapAnnotationConstants.IS_KIDS_DEPRECATED, user.isJustForKidsExperience());
      annotations.put(MapAnnotationConstants.IS_KIDS, user.isJustForKidsExperience());
    }
    if (RequestContextWrapper.get().getLocale() != null) {
      annotations.put(MapAnnotationConstants.LOCALE, RequestContextWrapper.get().getLocale());
    }
    APIRequestContext requestContext = currentRequest.getRequestContext();
    if (requestContext != null) {
      if (requestContext.getESN() != null) {
        annotations.put(MapAnnotationConstants.ESN, requestContext.getESN());
      }

      if (requestContext.getDeviceTypeId() != null) {
        annotations.put(
            MapAnnotationConstants.DEVICE_ID, String.valueOf(requestContext.getDeviceTypeId()));
      }

      // "legacy" is the default
      if (!annotations.containsKey(MapAnnotationConstants.UI_FLAVOR)) {
        annotations.put(MapAnnotationConstants.UI_FLAVOR, requestContext.getUIFlavor().name());
      }

      annotations.put(MapAnnotationConstants.REQUEST_ID, requestContext.getRequestId());
      annotations.put(MapAnnotationConstants.APP_NAME, "api");
    }

    // do NOT get random rows back in case of timeout on getCachedList
    annotations.put(MapAnnotationConstants.FALLBACKS_ENABLED, fallbacksEnabled);
    return annotations;
  }

  private static void checkAndLogInconsistentCustomerId(Long apiUserCustomerId) {
    Long passportCustomerId =
        Optional.ofNullable(CurrentIdentityResult.getPassportIdentity())
            .flatMap(PassportIdentity::getProfileId)
            .orElse(null);

    if (!Objects.equals(apiUserCustomerId, passportCustomerId)) {
      MantisPublishContext.getCurrent()
          .add(
              "api.loloutils.inconsistent.cid",
              String.format("passport=%d,apiUser=%d", passportCustomerId, apiUserCustomerId));
    }
  }

  static Map<String, Object> buildAnnotations(
      Map<String, Object> requestAnnotations,
      Provider<APIRequest> currentRequest,
      boolean fallbacksEnabled) {

    Map<String, Object> annotations =
        Optional.ofNullable(requestAnnotations).map(HashMap::new).orElse(new HashMap<>());
    MerchAggPlatformAdapter.tryAddMultiLanguageCatalogAnnotation(annotations);

    // hack for DNA-1115, remove if MAP provides a new annotation that's a List instead of a Set and
    // all callers start using it
    if (annotations.get("interactivePackageVideos") instanceof List) {
      annotations.put(
          "interactivePackageVideos",
          new HashSet<>((List<?>) annotations.get("interactivePackageVideos")));
    }
    Map<String, Object> requestContextAnnotations =
        getRequestContextEntities(currentRequest, fallbacksEnabled);
    // incoming annotations should override request context ones
    requestContextAnnotations.putAll(annotations);

    return requestContextAnnotations;
  }

  @SuppressWarnings("unchecked")
  public static Object sanitize(Object o) {
    if (o == null) return null;
    if (o instanceof Map) {
      Map<Object, Object> map = (Map) o;
      Map<Object, Object> m = new LinkedHashMap<>();
      for (Map.Entry<Object, Object> e : map.entrySet()) {
        m.put(sanitize(e.getKey()), sanitize(e.getValue()));
      }
      return m;
    } else if (o instanceof Iterable) {
      Iterable i = (Iterable) o;
      List<Object> l = new ArrayList<>();
      for (Object obj : i) {
        l.add(sanitize(obj));
      }
      return l;
    } else if (o instanceof IdObject) {
      return ((IdObject) o).getId();
    } else {
      return o;
    }
  }
}
