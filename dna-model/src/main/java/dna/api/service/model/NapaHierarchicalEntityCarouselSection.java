package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NapaHierarchicalEntityCarouselSection extends SearchPageSection {
  private List<NapaEntityCarouselSection> subsections;

  public List<NapaEntityCarouselSection> getSubsections() {
    return subsections;
  }

  public NapaHierarchicalEntityCarouselSection setSubsections(
      List<NapaEntityCarouselSection> subsections) {
    this.subsections = subsections;
    return this;
  }
}
