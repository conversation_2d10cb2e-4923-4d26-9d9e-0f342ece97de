package com.netflix.api.service.batch;

import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import com.netflix.api.service.UIFlavor;
import com.netflix.request.core.UiFlavors;
import com.netflix.request.protogen.UiFlavor;
import java.util.Map;
import java.util.Optional;
import org.springframework.stereotype.Component;

@Component
public class USTUiFlavorExtractor implements UiFlavorExtractor {

  private static final String MAP_UIFLAVOR_ANNOTATION = "UIFlavor";

  @Override
  public String extractUiFlavor(Map<String, Object> annotations) {
    return Optional.ofNullable((String) annotations.get(MAP_UIFLAVOR_ANNOTATION))
        .orElseGet(
            () ->
                Optional.ofNullable(APIRequest.getCurrentRequest())
                    .map(APIRequest::getRequestContext)
                    .map(APIRequestContext::getUIFlavor)
                    .map(UIFlavor::name)
                    .orElse(null));
  }

  @Override
  public UiFlavor convertUiFlavor(String uiFlavor) {
    if (uiFlavor == null || uiFlavor.isEmpty()) {
      return null;
    } else {
      return UiFlavors.fromNameOrDefault(uiFlavor);
    }
  }
}
