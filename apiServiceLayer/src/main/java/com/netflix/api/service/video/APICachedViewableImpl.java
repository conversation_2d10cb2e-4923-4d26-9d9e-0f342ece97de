package com.netflix.api.service.video;

import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.vms.provider.VideoFormatDescriptorProvider;

public class APICachedViewableImpl extends APICachedVideoImpl implements APIViewable {

  APICachedViewableImpl(
      CompleteVideo video,
      APIVideoFactory videoFactory,
      USTVideoMetadataAdapter videoMetadataAdapter,
      VideoFormatDescriptorProvider videoFormatDescriptorProvider) {
    super(video, videoFactory, videoMetadataAdapter, videoFormatDescriptorProvider);
  }
}
