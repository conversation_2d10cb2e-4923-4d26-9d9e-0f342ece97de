package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class UmsUrlResponse {
  private String longUrl;

  private String shortUrl;

  private Long expirationTs;

  public String getLongUrl() {
    return longUrl;
  }

  public String getShortUrl() {
    return shortUrl;
  }

  public Long getExpirationTs() {
    return expirationTs;
  }

  public UmsUrlResponse setLongUrl(String longUrl) {
    this.longUrl = longUrl;
    return this;
  }

  public UmsUrlResponse setShortUrl(String shortUrl) {
    this.shortUrl = shortUrl;
    return this;
  }

  public UmsUrlResponse setExpirationTs(Long expirationTs) {
    this.expirationTs = expirationTs;
    return this;
  }
}
