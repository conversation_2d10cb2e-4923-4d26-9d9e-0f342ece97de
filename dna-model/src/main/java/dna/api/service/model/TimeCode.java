package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.video.APITimecode */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class TimeCode {
  private String type;

  private String label;

  private Long start;

  private Long end;

  private Map<String, String> annotations;

  public String getType() {
    return type;
  }

  public String getLabel() {
    return label;
  }

  public Long getStart() {
    return start;
  }

  public Long getEnd() {
    return end;
  }

  public Map<String, String> getAnnotations() {
    return annotations;
  }

  public TimeCode setType(String type) {
    this.type = type;
    return this;
  }

  public TimeCode setLabel(String label) {
    this.label = label;
    return this;
  }

  public TimeCode setStart(Long start) {
    this.start = start;
    return this;
  }

  public TimeCode setEnd(Long end) {
    this.end = end;
    return this;
  }

  public TimeCode setAnnotations(Map<String, String> annotations) {
    this.annotations = annotations;
    return this;
  }
}
