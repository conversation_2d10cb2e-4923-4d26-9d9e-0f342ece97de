package com.netflix.api.platform;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AppClientContext {

  public static final String X_NETFLIX_CLIENT_CONTEXT = "X-Netflix.Request.Client.Context";
  private static final Logger logger = LoggerFactory.getLogger(AppClientContext.class);
  private static final ObjectMapper reader = new ObjectMapper();
  private static final AppClientContext DEFAULT = new AppClientContext(State.NONE, Reason.NONE);
  private static final Property<Boolean> UNKNOWN_AS_BACKGROUND =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.clientContext.unknownAsBackground", Boolean.class)
          .orElse(false);

  public enum State {
    NONE,
    UNKNOWN,
    BACKGROUND,
    FOREGROUND,
    IDLE;

    private final String displayName;

    State() {
      this.displayName = name().toLowerCase();
    }

    public String getDisplayName() {
      return displayName;
    }

    private static State parse(String value) {
      if (value == null || value.isBlank()) return NONE;

      return switch (value.toLowerCase()) {
        case "background" -> BACKGROUND;
        case "foreground" -> FOREGROUND;
        case "idle" -> IDLE;
        default -> UNKNOWN;
      };
    }
  }

  public enum Reason {
    NONE,
    UNKNOWN,
    PUSH,
    USER;

    private final String displayName;

    Reason() {
      this.displayName = name().toLowerCase();
    }

    public String getDisplayName() {
      return displayName;
    }

    private static Reason parse(String value) {
      if (value == null || value.isBlank()) return NONE;

      return switch (value.toLowerCase()) {
        case "push" -> PUSH;
        case "user" -> USER;
        default -> UNKNOWN;
      };
    }
  }

  private final State state;
  private final Reason reason;

  private AppClientContext(State state, Reason reason) {
    this.state = state;
    this.reason = reason;
  }

  public State getState() {
    return state;
  }

  public Reason getReason() {
    return reason;
  }

  public boolean isBackground() {
    return state == State.BACKGROUND
        || state == State.IDLE
        || (UNKNOWN_AS_BACKGROUND.get() && state == State.UNKNOWN);
  }

  public static AppClientContext getOrCreate(HttpServletRequest request) {
    AppClientContext appContext;
    // check request attributes first
    appContext = (AppClientContext) request.getAttribute("appClientContext");
    if (appContext == null) {
      appContext = parseContext(request.getHeader(X_NETFLIX_CLIENT_CONTEXT));
      request.setAttribute("appClientContext", appContext);
    }
    return appContext;
  }

  @VisibleForTesting
  static AppClientContext parseContext(String header) {
    AppClientContext appContext = DEFAULT;
    if (header != null && !header.isEmpty()) {
      try {
        Map<String, String> map = reader.readValue(header, new TypeReference<>() {});
        appContext = new AppClientContext(parseState(map), parseReason(map));
      } catch (Exception e) {
        logger.info("Could not parseState client context {}", header, e);
      }
    }
    return appContext;
  }

  private static State parseState(final Map<String, String> map) {
    String appState = map.get("appstate");
    if (appState == null) {
      appState = map.get("appState");
    }

    return State.parse(appState);
  }

  private static Reason parseReason(final Map<String, String> map) {
    return Reason.parse(map.get("reason"));
  }
}
