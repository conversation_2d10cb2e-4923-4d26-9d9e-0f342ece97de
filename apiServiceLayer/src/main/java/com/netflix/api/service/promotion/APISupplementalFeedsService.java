package com.netflix.api.service.promotion;

import java.util.Collection;
import rx.Single;

/** Generic service to host logic associated with promotion features */
public interface APISupplementalFeedsService {

  /**
   * TBD
   *
   * @param videoIds
   * @return
   */
  Single<APIExtrasResponse> getExtrasByVideoIds(Collection<Integer> videoIds);

  /**
   * TBD
   *
   * @param guids
   * @return
   */
  Single<APIExtrasResponse> getExtrasByGuids(Collection<String> guids);
}
