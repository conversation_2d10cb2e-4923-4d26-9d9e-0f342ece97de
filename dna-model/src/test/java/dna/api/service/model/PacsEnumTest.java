package dna.api.service.model;

import static dna.api.service.model.EnumChecker.noMissingValues;

import com.netflix.pacs.protogen.Feature;
import com.netflix.pacs.protogen.ResponseClassification;
import org.junit.jupiter.api.Test;

class PacsEnumTest {

  // These enums are defined and generated from this file.
  private static final String FILENAME = "dna-router/src/main/resources/pacs.graphql";

  @Test
  void pacsDetail() {
    // make sure that we have all the enum values that pacs does, otherwise add to pacs.graphql and
    // run ./gradlew generateFalcor
    noMissingValues(
        com.netflix.pacs.protogen.PacsDetailCode.values(), PacsDetailCode.class, FILENAME);
  }

  @Test
  void pacsExperience() {
    // make sure that we have all the enum values that pacs does, otherwise add to pacs.graphql and
    // run ./gradlew generateFalcor
    noMissingValues(
        com.netflix.pacs.protogen.PacsExperience.values(), PacsExperience.class, FILENAME);
  }

  @Test
  void pacsResponseClassification() {
    // make sure that we have all the enum values that pacs does, otherwise add to pacs.graphql and
    // run ./gradlew generateFalcor
    noMissingValues(ResponseClassification.values(), PacsResponseClassification.class, FILENAME);
  }

  @Test
  void pacsProfileType() {
    // make sure that we have all the enum values that pacs does, otherwise add to pacs.graphql and
    // run ./gradlew generateFalcor
    noMissingValues(
        com.netflix.pacs.protogen.PacsProfileType.values(), PacsProfileType.class, FILENAME);
  }

  @Test
  void pacsFeatureType() {
    // make sure that we have all the enum values that pacs does, otherwise add to pacs.graphql and
    // run ./gradlew generateFalcor
    noMissingValues(Feature.values(), PacsFeature.class, FILENAME);
  }
}
