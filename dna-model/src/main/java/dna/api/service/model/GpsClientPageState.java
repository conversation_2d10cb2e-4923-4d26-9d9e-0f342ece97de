package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class GpsClientPageState {
  /** Current page version from most recent page response. */
  private String pageVersion;

  public String getPageVersion() {
    return pageVersion;
  }

  public GpsClientPageState setPageVersion(String pageVersion) {
    this.pageVersion = pageVersion;
    return this;
  }
}
