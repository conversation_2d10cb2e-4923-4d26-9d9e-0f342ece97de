package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class ImpressionsHistoryRequest {
  /** @deprecated not used anymore */
  @Deprecated private String impressionContext;

  private Integer videoId;

  /** optional map of params, will be passed into IHS service */
  private Map<String, String> params;

  @Deprecated
  public String getImpressionContext() {
    return impressionContext;
  }

  public Integer getVideoId() {
    return videoId;
  }

  public Map<String, String> getParams() {
    return params;
  }

  @Deprecated
  public ImpressionsHistoryRequest setImpressionContext(String impressionContext) {
    this.impressionContext = impressionContext;
    return this;
  }

  public ImpressionsHistoryRequest setVideoId(Integer videoId) {
    this.videoId = videoId;
    return this;
  }

  public ImpressionsHistoryRequest setParams(Map<String, String> params) {
    this.params = params;
    return this;
  }
}
