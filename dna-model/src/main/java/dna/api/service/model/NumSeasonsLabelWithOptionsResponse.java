package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NumSeasonsLabelWithOptionsResponse {
  private String label;

  public String getLabel() {
    return label;
  }

  public NumSeasonsLabelWithOptionsResponse setLabel(String label) {
    this.label = label;
    return this;
  }
}
