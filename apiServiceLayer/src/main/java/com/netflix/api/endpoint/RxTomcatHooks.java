package com.netflix.api.endpoint;

import com.netflix.api.service.APIRequest;
import com.netflix.lang.BindingContexts;
import com.netflix.lang.RequestVariable;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.Observable;
import rx.Observable.OnSubscribe;
import rx.Subscriber;
import rx.observers.SafeSubscriber;
import rx.plugins.RxJavaObservableExecutionHook;
import rx.subscriptions.Subscriptions;

/**
 * A set of RxJava hooks that works together to maintain a per-request Map of Observables in flight
 * {@see ObservablesInFlight}. For each Observable execution, the start hook adds the Observable,
 * and the complete/error/unsubscribe hooks unlatch and remove it. This data structure allows an
 * external service (Tomcat) to wait on the completion of all {@link Observable}s that occur in a
 * given request. Although we do not take advantage of that and just clear the map before the
 * current request processing starts.
 */
public class RxTomcatHooks extends RxJavaObservableExecutionHook {
  private static final Logger logger = LoggerFactory.getLogger(RxTomcatHooks.class);

  private RxTomcatHooks() {}

  private static final RequestVariable<ObservablesInFlight> requestObservables =
      new RequestVariable<ObservablesInFlight>() {
        @Override
        public ObservablesInFlight initialValue() {
          return new ObservablesInFlight();
        }
      };

  public static class ObservablesInFlight {
    private final int MAX_OUTSTANDING = 100000;

    private final ConcurrentHashMap<Subscriber<?>, Exception> inFlightSubscriberHashes =
        new ConcurrentHashMap<>();
    // 100000 is max number of observables outstanding per request
    private final Semaphore observablePermits = new Semaphore(MAX_OUTSTANDING);

    private void add(Subscriber<?> subscriber, Exception excep) {
      if (inFlightSubscriberHashes.putIfAbsent(subscriber, excep) == null) {
        observablePermits.tryAcquire(1);
      }
    }

    private void complete(Subscriber<?> subscriber) {
      if (inFlightSubscriberHashes.remove(subscriber) != null) {
        observablePermits.release(1);
      }
    }

    private int getNumberInFlight() {
      return MAX_OUTSTANDING - observablePermits.availablePermits();
    }

    @Override
    public String toString() {
      return "In-Flight Observables: " + getNumberInFlight();
    }

    public Map<Subscriber<?>, Exception> getHashes() {
      return inFlightSubscriberHashes;
    }
  }

  private static class ContextOnSubscribe<T> implements OnSubscribe<T> {
    private final OnSubscribe<T> f;
    public Exception excep = new Exception();

    public ContextOnSubscribe(OnSubscribe<T> f) {
      this.f = f;
    }

    @Override
    public void call(Subscriber<? super T> t1) {
      f.call(t1);
    }
  }

  @Override
  public <T> OnSubscribe<T> onCreate(OnSubscribe<T> f) {
    return new ContextOnSubscribe<>(f);
  }

  @Override
  public <T> Observable.OnSubscribe<T> onSubscribeStart(
      final Observable<? extends T> observableInsance,
      final Observable.OnSubscribe<T> onSubscribe) {
    String method = "get ";
    if (APIRequest.getCurrentRequest() != null
        && APIRequest.getCurrentRequest().getServletRequest() != null) {
      method =
          APIRequest.getCurrentRequest().getServletRequest().getMethod()
              + " "
              + APIRequest.getCurrentRequest().getServletRequest().getRequestURI();
    }
    final String methodStr = method;

    if (!BindingContexts.isInContext()) {
      logger.error(
          "*** subscribe NOT IN CONTEXT : " + Thread.currentThread().getName() + " : " + methodStr);
      return onSubscribe;
    }

    return subscriber -> {
      final Subscriber<?> underlyingSubscriber =
          ((SafeSubscriber<? super T>) subscriber).getActual();

      final Exception excep;
      if (onSubscribe instanceof ContextOnSubscribe)
        excep = new Exception(((ContextOnSubscribe<?>) onSubscribe).excep);
      else excep = new Exception();

      requestObservables.get().add(underlyingSubscriber, excep);

      Subscriber<T> wrapped =
          new Subscriber<T>(subscriber) {
            @Override
            public void onCompleted() {
              if (!BindingContexts.isInContext()) {
                logger.error(
                    "*** onCompleted NOT IN CONTEXT : "
                        + Thread.currentThread().getName()
                        + " : "
                        + methodStr);
              }
              subscriber.onCompleted();
              requestObservables.get().complete(underlyingSubscriber);
            }

            @Override
            public void onError(Throwable e) {
              if (!BindingContexts.isInContext()) {
                logger.error(
                    "*** onError NOT IN CONTEXT : "
                        + Thread.currentThread().getName()
                        + " : "
                        + methodStr);
              }
              subscriber.onError(e);
              requestObservables.get().complete(underlyingSubscriber);
            }

            @Override
            public void onNext(T t) {
              if (!BindingContexts.isInContext()) {
                logger.error(
                    "*** onNext NOT IN CONTEXT : "
                        + Thread.currentThread().getName()
                        + " : "
                        + methodStr);
              }
              subscriber.onNext(t);
            }
          };

      try {
        wrapped.add(
            Subscriptions.create(
                () -> {
                  if (!BindingContexts.isInContext()) {
                    logger.error(
                        "*** unsubscribe NOT IN CONTEXT : "
                            + Thread.currentThread().getName()
                            + " : "
                            + methodStr);
                  }
                  subscriber.unsubscribe();
                  requestObservables.get().complete(underlyingSubscriber);
                }));
      } catch (Throwable ex) {
        logger.error("Caught error adding unsubscribe action in RxTomcatHooks");
      }

      try {
        onSubscribe.call(wrapped);
      } catch (Throwable ex) {
        logger.error("Found a subscribe method breaking the Rx contract :" + ex);
        wrapped.onError(ex);
      }
    };
  }

  @Override
  public Throwable onSubscribeError(Throwable e) {
    logger.error("*** onSubscribeError : " + e);
    return e;
  }

  public static void clear() {
    requestObservables.set(new ObservablesInFlight());
  }
}
