package com.netflix.api.service.identity;

import com.google.common.base.Preconditions;
import com.netflix.api.service.identity.APIUser.Experience;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import org.apache.commons.lang.StringEscapeUtils;

public class MembershipProfileReader extends BaseProfileReader {

  private final AccountProfileRemote accountProfile;

  MembershipProfileReader(
      APIUserInternal user,
      AccountProfileRemote accountProfile,
      LocalizedProfileLogic ab8960LocalizedDefaultKidsProfileTestLogic) {
    super(user, ab8960LocalizedDefaultKidsProfileTestLogic);
    this.accountProfile =
        Preconditions.checkNotNull(accountProfile, "AccountProfile must be provided");
  }

  @Override
  protected String profileName() {
    return accountProfile.getBoxedProfileName();
  }

  @Override
  protected String primaryLanguage() {
    return accountProfile.getBoxedPrimaryLang();
  }

  @Override
  public boolean isFirstUse() {
    return accountProfile.getOptionalIsProfileFirstUse().orElse(false);
  }

  @Override
  public Experience getExperience() {
    return switch (accountProfile.getExperienceTypeEnum()) {
      case just_for_kids -> Experience.jfk;
      // TVUI has a bug where they cannot handle new experience types, return standard
      default -> Experience.standard;
    };
  }

  @Override
  protected String htmlDecodedProfileName() {
    return accountProfile
        .getOptionalProfileName()
        .map(StringEscapeUtils::unescapeHtml)
        .orElse(null);
  }
}
