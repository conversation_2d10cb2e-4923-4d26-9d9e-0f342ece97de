package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PostVOIPCallInfo {
  private String authToken;

  private String callData;

  public String getAuthToken() {
    return authToken;
  }

  public String getCallData() {
    return callData;
  }

  public PostVOIPCallInfo setAuthToken(String authToken) {
    this.authToken = authToken;
    return this;
  }

  public PostVOIPCallInfo setCallData(String callData) {
    this.callData = callData;
    return this;
  }
}
