package com.netflix.api.service.identity;

import com.google.common.base.Strings;
import com.netflix.api.util.Cryptex2Manager;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class APIUserPersonalDataImpl implements APIUserPersonalData {

  private static final Logger logger = LoggerFactory.getLogger(APIUserPersonalDataImpl.class);

  private final AccountProfileRemote accountProfile;
  private final Cryptex2Manager cryptex2;

  APIUserPersonalDataImpl(AccountProfileRemote accountProfile, Cryptex2Manager cryptex2) {
    this.accountProfile = accountProfile;
    this.cryptex2 = cryptex2;
  }

  @Override
  public CompletionStage<String> getEmailAddress() {
    return decryptEncryptedEmail(accountProfile.getBoxedEncryptedEmail());
  }

  @Override
  public CompletionStage<String> getAccountEmailAddress() {
    return decryptEncryptedEmail(accountProfile.getBoxedEncryptedAccountEmail());
  }

  private CompletionStage<String> decryptEncryptedEmail(String encryptedEmail) {
    if (!Strings.isNullOrEmpty(encryptedEmail)) {
      return cryptex2
          .symmetricDecrypt(Cryptex2Manager.SUBSCRIBER_EMAIL_DATA_KEY, encryptedEmail)
          .exceptionally(
              ex -> {
                logger.error("error decrypting email {}", encryptedEmail);
                return null;
              });
    }
    return CompletableFuture.completedFuture(encryptedEmail);
  }
}
