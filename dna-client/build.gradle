apply plugin: 'netflix.lombok'

apply from: file('../dependency-properties.gradle')
apply from: file('../dependency-overrides.gradle')

dependencies {
    // DO NOT ADD NON-PROJECT DEPENDENCIES HERE
    implementation project(':dna-model')
    implementation project(":api-global-dependencies")
}

compileJava {
    dependsOn ':dna-router:generateFalcorClientFromAst'
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring:spring-boot-netflix-bom:latest.release'
}
