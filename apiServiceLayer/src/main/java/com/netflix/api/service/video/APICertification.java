package com.netflix.api.service.video;

/**
 * A maturity-level rating assigned to a video by a ratings agency.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.related1.png"
 * alt="APIVideo-related objects"> <img
 * src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.related2.png"
 * alt="APIVideo-related objects">
 */
public interface APICertification {

  /**
   * Value of the assigned certification - e.g., 'R', 'PG-13'
   *
   * @return a String that contains the name given by the ratings agency to the rating they have
   *     given
   */
  String getValue();

  /**
   * Get the ID of the certification system
   */
  Integer getBoardId();

  /**
   * Certification board that is rating the title - e.g., 'MPAA' in the US.
   *
   * @return the name of the certification board
   */
  String getBoard();

  /**
   * A maturity level assigned by Netflix based on the certification board rating. This is an
   * integer between {@code 10} and {@code 1000}, inclusive, where lower numbers indicate a higher
   * likelihood of acceptability to an immature audience. You can use this number to roughly compare
   * the maturity levels of titles rated by different rating agencies. There are three special
   * values:
   *
   * <ul>
   *   <li>{@code 75} - the title has not been rated but we believe it is family-safe
   *   <li>{@code 130} - corresponds to the MPAA "not rated" categorization
   *   <li>{@code 1000} - corresponds to the MPAA "unrated" categorization
   * </ul>
   *
   * @return the Netflix-assigned maturity level for this rating, as an integer
   */
  int getMaturityLevel();

  /**
   * A maturity reason/description assigned by Netflix based on the certification board rating.
   *
   * @return the Netflix-generated description of the rating and its justification
   */
  String getMaturityDescription();

  /**
   * @return: video specific rating reason if available. Else return empty string.
   * @see {@link #getMaturityDescription()} for a method with fallback.
   *     <p>Like getVideoSpecificRatingReason(), the getMaturityDescription() method also returns a
   *     video specific rating reason if available. However when video specific rating reason is not
   *     available, getMaturityDescription() returns generic rating id description, whereas this
   *     method does not.
   */
  String getVideoSpecificRatingReason();

  /**
   * This information gives reasons why a video is given a particular rating. For example: this is
   * used by NICAM-AGE to give NICAM-Advisory information as reason IDs.
   *
   * @return
   */
  APIMovieRatingReason getRatingReason();

  /**
   * @return: Confirmation (or classification) number issued by board for this video rating. Null
   *     when no value is available. Only some boards (ex: KMRB) issue such confirmation IDs.
   */
  String getCertSystemConfirmationId();

  Integer getRatingId();
}
