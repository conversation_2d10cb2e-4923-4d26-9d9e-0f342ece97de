package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class CollectionMetadata {
  private Long collectionId;

  private String title;

  private String evidenceText;

  private Integer numNewTitles;

  private StaffPicksEvidence staffPicksEvidence;

  public Long getCollectionId() {
    return collectionId;
  }

  public String getTitle() {
    return title;
  }

  public String getEvidenceText() {
    return evidenceText;
  }

  public Integer getNumNewTitles() {
    return numNewTitles;
  }

  public StaffPicksEvidence getStaffPicksEvidence() {
    return staffPicksEvidence;
  }

  public CollectionMetadata setCollectionId(Long collectionId) {
    this.collectionId = collectionId;
    return this;
  }

  public CollectionMetadata setTitle(String title) {
    this.title = title;
    return this;
  }

  public CollectionMetadata setEvidenceText(String evidenceText) {
    this.evidenceText = evidenceText;
    return this;
  }

  public CollectionMetadata setNumNewTitles(Integer numNewTitles) {
    this.numNewTitles = numNewTitles;
    return this;
  }

  public CollectionMetadata setStaffPicksEvidence(StaffPicksEvidence staffPicksEvidence) {
    this.staffPicksEvidence = staffPicksEvidence;
    return this;
  }
}
