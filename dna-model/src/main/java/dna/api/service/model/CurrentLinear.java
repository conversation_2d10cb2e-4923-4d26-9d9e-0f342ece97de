package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class CurrentLinear {
  private Long currentOffsetSecs;

  private LinearScheduleItem currentVideo;

  private LinearResponseMetadata metadata;

  public Long getCurrentOffsetSecs() {
    return currentOffsetSecs;
  }

  public LinearScheduleItem getCurrentVideo() {
    return currentVideo;
  }

  public LinearResponseMetadata getMetadata() {
    return metadata;
  }

  public CurrentLinear setCurrentOffsetSecs(Long currentOffsetSecs) {
    this.currentOffsetSecs = currentOffsetSecs;
    return this;
  }

  public CurrentLinear setCurrentVideo(LinearScheduleItem currentVideo) {
    this.currentVideo = currentVideo;
    return this;
  }

  public CurrentLinear setMetadata(LinearResponseMetadata metadata) {
    this.metadata = metadata;
    return this;
  }
}
