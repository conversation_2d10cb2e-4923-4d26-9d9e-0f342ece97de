package com.netflix.api.service.obelix;

import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.dloc.protogen.DlocServiceGrpc.DlocServiceStub;
import com.netflix.dloc.protogen.MapData;
import com.netflix.dloc.protogen.ObelixRequest;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
public class APIObelixServiceImpl implements APIObelixService {

  private final DlocServiceStub dlocServiceStub;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public APIObelixServiceImpl(@GrpcSpringClient("dloc") DlocServiceStub dlocServiceStub) {
    this.dlocServiceStub = dlocServiceStub;
  }

  @Override
  public Observable<String> getSimpleStringProperty(
      final String namespace,
      final String bundle,
      final String language,
      final String stringDataKey,
      final String defaultValue) {
    return getAllStringProperties(namespace, bundle, language)
        .map(values -> values.get(stringDataKey))
        .onErrorReturn(ignore -> defaultValue);
  }

  private Observable<Map<String, String>> getAllStringProperties(
      String namespace, String bundle, String language) {
    return RxObservable.defer(
            dlocServiceStub::getAllStringProperties,
            ObelixRequest.newBuilder()
                .setNamespace(namespace)
                .setBundle(bundle)
                .setLanguage(language)
                .setKeysAsValues(false)
                .build())
        .map(MapData::getValuesMap);
  }
}
