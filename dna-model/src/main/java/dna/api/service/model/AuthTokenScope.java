package dna.api.service.model;

import javax.annotation.Generated;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
public enum AuthTokenScope {
  BYPASS_CURRENT_PASSWORD_CHALLENGE,

  CHAN<PERSON>_PASSWORD,

  DEFAULT,

  MSL_LOGIN_ANDROID_SIGNUP_BRIDGE,

  UNKNOWN_SCOPE,

  FREEMIUM_UPGRADE_VIA_INAPP_LINK,

  FREEMIUM_UPGRADE_VIA_EMAIL,

  EMAIL_VERIFICATION,

  ACCESS_OTP_WEBPAGE,

  ENABLE_PROFILE_TRANSFER,

  DISABLE_PROFILE_TRANSFER,

  WEB_PAYMENTS_CALLBACK,

  WEB_PAYMENTS_REPLAY,

  MHU_PERSUASION_PAYER_SESSION_TRANSFER,

  MHU_PERSUASION_BORROWER_SESSION_TRANSFER,

  MHU_ADDON_BENEFICIARY_ACCOUNT_CREATION,

  SIGN_OUT_OF_ALL_DEVICES,

  OCGA_USAGE,

  REJOIN_RECHARGE_PLAN_ON_WEB,

  TVUI_PAYMENT_UPDATE_SESSION_TRANSFER,

  MHU_HOME_MISDETECTION_VERIFY_TV,

  MHU_HOUSEHOLD_UPDATE_SESSION_TRANSFER,

  CUSTOMER_SUPPORT_VIA_INAPP_LINK,

  ACCOUNT_LITE_SESSION_TRANSFER,

  CHANGE_PLAN_VIA_INAPP_LINK,

  CHANGE_PLAN_SESSION_TRANSFER,

  SET_BENEFICIARY_PASSWORD,

  MHU_EBI_SESSION_CONTINUITY,

  MHU_UPDATE_PRIMARY_LOCATION,

  MOBILE_DEVICE_UPGRADE,

  MHU_CONFIRM_ISOLATED_SET_TOP_BOX,

  FORGET_PASSWORD_SESSION_TRANSFER,

  PROFILE_LOCK_RESET_SESSION_TRANSFER,

  ANDROID_SIGNUP_VIA_SMS_OR_IN_APP_LINK,

  ANDROID_SIGNUP_VIA_EMAIL,

  MHU_CONFIRM_ACCOUNT_VIA_INAPP_LINK,

  WINDOWS_PWA_MIGRATION,

  ANDROID_CONCURRENT_STREAMS_UPSELL_VIA_INAPP_LINK
}
