package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * fields that go to MAP Note: API will automatically fill in the values that it has access to -
 * profile, locale etc. for actual annotation values see:
 * http://us-east-1.discoveryprod.netflix.net:7001/discovery/resolver/cluster/api-prod-x1next-x2main:8077/simplifiedMap/refreshLolomoTester
 */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class RefreshLolomoListRequest {
  private String lolomoId;

  private String listId;

  private String listContext;

  private Boolean volatileList;

  private Map<String, Object> annotations;

  public String getLolomoId() {
    return lolomoId;
  }

  public String getListId() {
    return listId;
  }

  public String getListContext() {
    return listContext;
  }

  public Boolean getVolatileList() {
    return volatileList;
  }

  public Map<String, Object> getAnnotations() {
    return annotations;
  }

  public RefreshLolomoListRequest setLolomoId(String lolomoId) {
    this.lolomoId = lolomoId;
    return this;
  }

  public RefreshLolomoListRequest setListId(String listId) {
    this.listId = listId;
    return this;
  }

  public RefreshLolomoListRequest setListContext(String listContext) {
    this.listContext = listContext;
    return this;
  }

  public RefreshLolomoListRequest setVolatileList(Boolean volatileList) {
    this.volatileList = volatileList;
    return this;
  }

  public RefreshLolomoListRequest setAnnotations(Map<String, Object> annotations) {
    this.annotations = annotations;
    return this;
  }
}
