package com.netflix.api.service.identity;

import com.netflix.api.service.APIRequest;
import com.netflix.api.service.obelix.APIObelixService;
import java.util.regex.Pattern;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

public class APIProfileErrors {

  static final String DEFAULT_PROFILE_NAME_CONTAINS_INVALID_CHARACTER_MESSAGE =
      "Sorry, the following characters cannot be used in names: < > \"";
  static final String invalidCharacters = "<>\"";
  static final Pattern invalidCharRegex =
      Pattern.compile("[" + invalidCharacters + "]", Pattern.CASE_INSENSITIVE);
  static final int MAX_ALLOWED_PROFILE_NAME_LENGTH = 50;
  static final String DEFAULT_PROFILE_NAME_TOO_LONG_MESSAGE =
      "Sorry, names must be less than " + MAX_ALLOWED_PROFILE_NAME_LENGTH + " characters.";
  static final String DEFAULT_PROFILE_NAME_TOO_SHORT_MESSAGE = "Please enter a name.";
  static final int MIN_ALLOWED_PROFILE_NAME_LENGTH = 1;
  static final String DEFAULT_UNKNOWN_EDIT_ERROR_MESSAGE =
      "Sorry, something went wrong. Please try again.";
  static final String DEFAULT_UNKNOWN_CREATE_ERROR_MESSAGE =
      "Sorry, something went wrong. Please try again.";
  static final String DEFAULT_DUPLICATE_PROFILE_ICON_MESSAGE =
      "Sorry, that icon is already being used. Please choose a different icon.";
  static final String DEFAULT_DUPLICATE_PROFILE_NAME_MESSAGE =
      "Sorry, each name can only be used once.";
  static final String UNKNOWN_PROFILE_EDIT_FAILURE_MESSAGE_KEY = "profile.edit.unknown.error.msg";
  static final String UNKNOWN_PROFILE_CREATE_FAILURE_MESSAGE_KEY = "profile.add.unknown.error.msg";
  static final String PROFILE_NAME_CONTAINS_INVALID_CHARS_MESSAGE_KEY =
      "profile.addedit.name.contains.invalid.chars.msg";
  static final String PROFILE_NAME_TOO_SHORT_MESSAGE_KEY = "profile.addedit.name.too.short.msg";
  static final String PROFILE_NAME_TOO_LONG_MESSAGE_KEY = "profile.addedit.name.too.long.msg";
  static final String DUPLICATE_PROFILE_ICON_MESSAGE_KEY = "profile.addedit.duplicate.icon.msg";
  static final String DUPLICATE_PROFILE_NAME_MESSAGE_KEY = "profile.addedit.duplicate.name.msg";
  static final String API_RESOURCE_BUNDLE = "APIResourceBundle";
  static final String DELETE_PROFILE_ERROR_MESSAGE_KEY = "profile.delete.failure.msg";
  static final String DEFAULT_DELETE_PROFILE_ERROR_MESSAGE = "Sorry, couldn't delete this profile.";
  static final String NAMESPACE = "APINamespace";

  @Component
  public static class Localizer {

    private final APIObelixService obelixService;

    @Autowired
    public Localizer(APIObelixService apiObelixService) {
      this.obelixService = apiObelixService;
    }

    public String localize(String key, String defaultMsg) {
      try {
        String locale =
            APIRequest.getCurrentRequest().getRequestContext() == null
                    || APIRequest.getCurrentRequest().getRequestContext().getLocale() == null
                ? "en-US"
                : APIRequest.getCurrentRequest().getRequestContext().getLocale().getId();
        return obelixService
            .getSimpleStringProperty(NAMESPACE, API_RESOURCE_BUNDLE, locale, key, defaultMsg)
            .toBlocking()
            .first();
      } catch (Exception e) {
        return defaultMsg;
      }
    }
  }
}
