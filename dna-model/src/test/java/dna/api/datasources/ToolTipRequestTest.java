package dna.api.datasources;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.params.provider.Arguments.arguments;

import com.netflix.uipersonalization.protogen.Feature;
import com.netflix.uipersonalization.protogen.UiCategory;
import dna.api.service.model.ToolTipFeature;
import java.util.List;
import netflix.context.client.category.ClientCategory;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.FieldSource;

class ToolTipRequestTest {
  @ParameterizedTest
  @FieldSource
  void uiPersonalizationFeature(ToolTipFeature input, Feature expected) {
    var request = new ToolTipRequest(null, null, input, null);
    assertEquals(expected, request.uiPersonalizationFeature());
  }

  static final List<Arguments> uiPersonalizationFeature =
      List.of(
          arguments(ToolTipFeature.MOST_LIKED, Feature.MOST_LIKED_EXPLANATION),
          arguments(ToolTipFeature.PAUSE_AD, Feature.PAUSE_AD_EXPLANATION));

  @ParameterizedTest
  @FieldSource
  void uiPersonalizationDevice(ClientCategory input, UiCategory expected) {
    var request = new ToolTipRequest(null, null, null, input);
    assertEquals(expected, request.uiPersonalizationDevice().getUiCategory());
  }

  static final List<Arguments> uiPersonalizationDevice =
      List.of(
          arguments(ClientCategory.TV, UiCategory.TV),
          arguments(ClientCategory.ATV, UiCategory.TV),
          arguments(ClientCategory.IOS, UiCategory.IOS),
          arguments(ClientCategory.WEB, UiCategory.WEB),
          arguments(ClientCategory.ANDROID, UiCategory.ANDROID),
          arguments(ClientCategory.UNSPECIFIED, UiCategory.UICATEGORY_UNSPECIFIED),
          arguments(ClientCategory.WIN, UiCategory.UICATEGORY_UNSPECIFIED),
          arguments(ClientCategory.UNRECOGNIZED, UiCategory.UICATEGORY_UNSPECIFIED));
}
