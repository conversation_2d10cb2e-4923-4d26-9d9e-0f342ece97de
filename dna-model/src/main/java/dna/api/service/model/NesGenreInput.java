package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * A category or family of content, genre entities can be core film genres like action, comedy,
 * drama or they can be alternative genres like Live-action Movies based on Manga.
 */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NesGenreInput {
  private Long id;

  public Long getId() {
    return id;
  }

  public NesGenreInput setId(Long id) {
    this.id = id;
    return this;
  }
}
