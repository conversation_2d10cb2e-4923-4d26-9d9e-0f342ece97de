package com.netflix.api.platform.util;

class TrieNode {
  final TrieNode[] children;
  boolean isLeaf; // Quick way to check if any children exist
  boolean isWord; // Does this node represent the last character of a word
  private char character; // The character this node represents
  private String word; // The word at this node (if present)

  /** Constructor for top level root node. */
  TrieNode() {
    children = new TrieNode[27]; // extra spot for special characters and numbers
    isLeaf = true;
    isWord = false;
  }

  /** Constructor for child node. */
  TrieNode(char character) {
    this();
    this.character = character;
  }

  /**
   * Adds a word to this node. This method is called recursively and adds child nodes for each
   * successive letter in the word, therefore recursive calls will be made with partial words.
   *
   * @param word the word to add
   */
  void addWord(String word, int index) {
    isLeaf = false;
    int charPos = word.charAt(index) - 'a';

    if (charPos < 0 || charPos > 26) {
      charPos = 26;
    }

    if (children[charPos] == null) {
      children[charPos] = new TrieNode(word.charAt(index));
    }

    if ((word.length() - index) > 1) {
      children[charPos].addWord(word, index + 1);
    } else {
      children[charPos].isWord = true;
      children[charPos].word = word;
    }
  }

  /**
   * Returns the child TrieNode representing the given char, or null if no node exists.
   *
   * @param c
   * @return
   */
  TrieNode getNode(char c) {
    int idx = c - 'a';
    if (idx < 0 || idx > 26) {
      // all slots are fixed, except 26 which can hold any number of values
      idx = 26;
      if (children[idx] != null && children[idx].getCharacter() != c) {
        return null;
      }
    }

    return children[idx];
  }

  /** Returns the word at this node */
  String getWord() {
    return this.word;
  }

  char getCharacter() {
    return this.character;
  }
}
