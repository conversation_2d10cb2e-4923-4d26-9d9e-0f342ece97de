package com.netflix.api.filters;

import static com.netflix.api.endpoint.EndpointContextInitializerImpl.BOT_LEGACY_COUNTRY;
import static com.netflix.api.endpoint.EndpointContextInitializerImpl.BOT_LEGACY_LANGUAGE;
import static com.netflix.api.endpoint.EndpointContextInitializerImpl.BOT_LOCALE_HEADER;
import static com.netflix.api.endpoint.EndpointContextInitializerImpl.LANGUAGE_COUNTRY_SEPARATOR;
import static com.netflix.api.identity.IdentityFilter.getAllowedIdentitySourcesForPassport;
import static com.netflix.api.platform.MicrocontextFlags.MICROCONTEXT_DNA_AUGMENTED;
import static com.netflix.api.platform.MicrocontextFlags.MICROCONTEXT_SHADOW_ENABLED;

import com.netflix.api.filters.BodyPublishingFilter.RequestWrapper;
import com.netflix.api.platform.NetflixESN;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.identity.IdentityPassportResult;
import com.netflix.api.platform.util.ParamConstants;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.util.ServiceRequestUtils;
import com.netflix.api.util.ServiceUtils;
import com.netflix.i18n.NFLocale;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.base.ServerUtils;
import com.netflix.microcontext.access.server.resolvers.RequestHeaderResolver;
import com.netflix.microcontext.access.server.resolvers.RequestQueryResolver;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.resolvers.ClientResolvers;
import com.netflix.microcontext.init.resolvers.VisitResolvers;
import com.netflix.passport.extract.PassportExtractionMechanism;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.introspect.PassportIdentityFactory;
import com.netflix.passport.introspect.PassportIntrospector;
import com.netflix.passport.introspect.SingletonPassportIntrospectorFactory;
import com.netflix.passport.protobuf.Source;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Registry;
import com.netflix.type.ISOCountry;
import com.netflix.type.NFCountry;
import com.netflix.type.proto.Countries;
import com.netflix.type.proto.Locales;
import com.netflix.type.protogen.BasicTypes.Country;
import com.netflix.type.protogen.BasicTypes.DeviceType;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import netflix.context.Context;
import netflix.context.client.ClientContext;
import netflix.context.client.flavor.ClientFlavor;
import netflix.context.device.DeviceContext;
import netflix.context.geo.GeoContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(Ordered.LOWEST_PRECEDENCE - 5) // #9
public class MicrocontextFilter implements Filter {

  private static final Logger logger = LoggerFactory.getLogger(MicrocontextFilter.class);

  private final SingletonPassportIntrospectorFactory introspectorFactory;
  private final PassportIdentityFactory identityFactory;
  private final Registry registry;

  @Autowired
  public MicrocontextFilter(
      SingletonPassportIntrospectorFactory introspectorFactory,
      PassportIdentityFactory identityFactory,
      Registry registry) {
    this.introspectorFactory = introspectorFactory;
    this.identityFactory = identityFactory;
    this.registry = registry;
  }

  @Override
  public void doFilter(ServletRequest req, ServletResponse response, FilterChain chain)
      throws IOException, ServletException {
    if (ServiceRequestUtils.shouldApplyFilters(req) && MICROCONTEXT_SHADOW_ENABLED.get()) {
      HttpServletRequest request = (HttpServletRequest) req;
      Optional<Context> fromHeader = ServerUtils.fromHeader(request);
      try {
        // setup data sources
        final Set<Source> allowedSources = getAllowedIdentitySourcesForPassport();

        Optional<PassportIdentity> identity = getPassportIdentity(request);

        RequestContext requestContext = CurrentRequestContext.get();
        Country resolvedCountry =
            getCountry(
                request, fromHeader.map(Context::getGeo).orElse(null), requestContext.getCountry());
        final HeaderResolver headerResolver = RequestHeaderResolver.of(request);
        final ParamResolver paramResolver = RequestQueryResolver.of(request.getParameterMap());

        // initialize builder
        final Context.Builder builder =
            CurrentMicrocontext.requestContext()
                .map(Context::toBuilder)
                .orElse(Context.newBuilder());

        builder.setRequestId(requestContext.getRequestId()).setCountry(resolvedCountry);

        // client
        if (!builder.hasClient()) {
          Optional<ClientContext> clientContext =
              ClientResolvers.resolve(headerResolver, paramResolver, true, builder.getDevice());
          if (clientContext.isPresent()
              && clientContext.get().getClientFlavor() != ClientFlavor.UNSPECIFIED) {
            registry.counter("microcontext.uiflavor.resolve", "result", "header").increment();
          }
          registry.counter("microcontext.client.resolve", "true", "false").increment();
          clientContext.ifPresent(builder::setClient);
        } else {
          registry.counter("microcontext.client.resolve", "result", "false").increment();
        }

        // clear geo
        //noinspection deprecation
        builder.clearGeo();

        // prepopulate values from request context and override them from passport
        DeviceContext.Builder deviceBuilder = DeviceContext.newBuilder();
        String requestContextEsn = requestContext.getDeviceId();
        if (requestContextEsn != null) {
          deviceBuilder.setEsn(requestContextEsn);
        }
        if (requestContext.getDeviceType() != null) {
          Integer deviceTypeId = requestContext.getDeviceType().getId();
          deviceBuilder.setType(DeviceType.newBuilder().setId(deviceTypeId).build());
        }

        if (identity.isPresent()) {
          registry.counter("microcontextfilter.call", "source", "passport").increment();
          PassportIdentity pass = identity.get();

          // resolve esn
          NetflixESN netflixESN =
              NetflixESN.create(request, new IdentityPassportResult(pass, allowedSources), pass);
          String resolvedEsn = netflixESN != null ? netflixESN.getESN() : null;
          Source device = pass.getDeviceClaims().getDeviceInformationSource();

          boolean canAccess = allowedSources == null || allowedSources.contains(device);
          String passportEsn = canAccess ? pass.getEsn().orElse(null) : null;
          if (Objects.equals(resolvedEsn, passportEsn)) {
            registry
                .counter(
                    "microcontext.esn.resolve",
                    "result",
                    "true",
                    "source",
                    netflixESN != null ? netflixESN.getSource().name() : "null")
                .increment();
          } else {
            logger.debug(
                "Resolved esn different than passport {} resolved {}", passportEsn, netflixESN);
            registry
                .counter(
                    "microcontext.esn.resolve",
                    "result",
                    "false",
                    "source",
                    netflixESN != null ? netflixESN.getSource().name() : "null")
                .increment();
          }
          if (netflixESN != null) {
            deviceBuilder.setEsn(resolvedEsn);
          }

          Integer passDeviceTypeId = canAccess ? pass.getDeviceTypeId().orElse(null) : null;
          if (passDeviceTypeId != null) {
            deviceBuilder.setType(DeviceType.newBuilder().setId(passDeviceTypeId));
          }

          // TODO remove once microcontext is updated to not have user
          builder.clearUser();
        } else {
          // TODO scrape headers etc for esn
          registry.counter("microcontextfilter.call", "source", "fallback").increment();
        }
        // add locales
        builder
            .addAllLocales(
                getLocales(request, resolvedCountry).stream().map(Locales::toProtobuf).toList())
            .setDevice(deviceBuilder);

        // add visit if not present in builder
        if (!builder.hasVisit()) {
          builder.setVisit(VisitResolvers.resolve(headerResolver));
        }

        // add values from request metadata, need to wrap the request in order to extract the body
        if (MICROCONTEXT_DNA_AUGMENTED.get()) {
          try {
            RequestWrapper wrappedRequest = new RequestWrapper(request);
            builder.setClient(builder.getClientBuilder());

            // merge resolved values into provided context, in the future we may want to short
            // circuit resolution of values in favor of provided values
            fromHeader.ifPresent(builder::mergeFrom);
            CurrentMicrocontext.set(builder.build());
            chain.doFilter(wrappedRequest, response);
            return;
          } catch (Exception e) {
            logger.debug("Could not augment context", e);
          }
        } else {
          // merge resolved values into provided context, in the future we may want to short circuit
          // resolution of values in favor of provided values
          fromHeader.ifPresent(builder::mergeFrom);
          CurrentMicrocontext.set(builder.build());
        }
      } catch (Exception e) {
        logger.error("Could not initialize context", e);
      }
    }
    chain.doFilter(req, response);
  }

  private Optional<PassportIdentity> getPassportIdentity(HttpServletRequest request) {
    try {
      PassportIntrospector introspector =
          introspectorFactory.createPassportIntrospector(
              request::getHeader, PassportExtractionMechanism.ANY_LOOK_INTO_REQUEST_HEADERS_FIRST);
      final Optional<PassportIntrospector> o = Optional.ofNullable(introspector);
      registry
          .counter(
              "microcontextfilter.passport.call", "result", o.isPresent() ? "success" : "missing")
          .increment();
      if (o.isEmpty()) return Optional.empty();
      return Optional.ofNullable(identityFactory.createPassportIdentity(o.get().getPassport()));
    } catch (Exception e) {
      registry.counter("microcontextfilter.passport.call", "result", "error").increment();
      logger.info("exception getting passport introspector", e);
      return Optional.empty();
    }
  }

  /**
   * Algorithm for choosing locale of an incoming HTTP request
   *
   * <pre>
   * 1. Use the HTTP query param for key "languages"
   * 2. Find the user's preferred locales and use those
   * 3. Use NFLocale.getMatchingLocales on the previously looked-up country
   * 4. Use NFLocale.getSUpportedLocales on the previously looked-up country
   * 5. Use the locales already in the RequestContext
   * </pre>
   *
   * @param request incoming HTTP request
   * @param resolvedCountry country of HTTP request {@see getCountry()}
   * @return locale of the HTTP request, according to above algorithm
   */
  private static List<String> getLocales(HttpServletRequest request, Country resolvedCountry) {
    // for bot requests, set locale to the locale passed by the bot
    if (ServiceUtils.isBot(request)) {
      NFLocale seoLocale = getSeoLocale(request);
      if (seoLocale != null) return Collections.singletonList(seoLocale.getId());
    }
    // now try the request parameter
    String localeFromHttp = getParam(request, ParamConstants.LANGUAGES);
    if (localeFromHttp != null) {
      return RequestContextWrapper.convertLocales(localeFromHttp).stream()
          .map(NFLocale::getId)
          .toList();
    } else {
      List<String> matchingLocales = getMatchingLocales(resolvedCountry);
      if (!matchingLocales.isEmpty()) {
        return matchingLocales;
      } else {
        List<String> supportedLocales = getSupportedLocales(resolvedCountry);
        if (supportedLocales != null && !supportedLocales.isEmpty()) {
          return supportedLocales;
        }
      }
      return RequestContextWrapper.convertLocales(CurrentRequestContext.get().getLocaleList())
          .stream()
          .map(NFLocale::getId)
          .toList();
    }
  }

  public static List<String> getSupportedLocales(Country country) {
    List<NFLocale> supportedLocales = NFLocale.getSupportedLocales(Countries.toBasicType(country));
    return supportedLocales != null
        ? supportedLocales.stream().map(NFLocale::getId).toList()
        : null;
  }

  public static List<String> getMatchingLocales(Country country) {
    return NFLocale.getMatchingLocaleList(
            new ArrayList<>(), null, country.getId(), NFLocale.MATCH_UI_AND_MESSAGES)
        .stream()
        .map(NFLocale::getId)
        .toList();
  }

  private static String getParam(HttpServletRequest request, String param) {
    String paramValue = request.getParameter(param);
    if (paramValue == null || paramValue.isEmpty()) {
      return null;
    } else {
      return paramValue;
    }
  }

  private static Country getCountry(
      final HttpServletRequest request, final GeoContext geoContext, ISOCountry reqContextCountry) {
    // for bot based requests, set country to the code passed in the header (website is responsible
    // for validating a bot request)
    if (ServiceUtils.isBot(request)) {
      ISOCountry seoCountry = getSeoCountry(request);
      if (seoCountry != null) {
        return Countries.toProtobuf(seoCountry);
      }
    }
    /*Temporary support for LG Korea: DNA-1160*/
    if (ServiceUtils.isDETRequest(request)) {
      ISOCountry detCountry = getCountryFromHTTPParam(request);
      if (detCountry != null) return Countries.toProtobuf(detCountry);
    }

    if (geoContext != null && geoContext.hasCountry()) {
      return geoContext.getCountry();
    }

    // country will only be null if geo lookup fails, and we do not have a default country
    // configured for the stack
    ISOCountry validCountryResolved = getCountryFromHTTPParam(request);

    if (validCountryResolved != null) {
      return Countries.toProtobuf(validCountryResolved);
    }

    if (reqContextCountry != null) {
      return Countries.toProtobuf(reqContextCountry);
    }

    return Countries.toProtobuf("US");
  }

  private static ISOCountry getCountryFromHTTPParam(final HttpServletRequest request) {
    String countryFromHttp = getParam(request, "country");
    if (countryFromHttp != null) {
      try {
        return NFCountry.findInstance(countryFromHttp);
      } catch (IllegalArgumentException iae) {
        if (logger.isWarnEnabled()) logger.warn("Invalid country parameter={}", countryFromHttp);
      }
    }
    return null;
  }

  @SuppressWarnings("DuplicatedCode")
  private static ISOCountry getSeoCountry(HttpServletRequest request) {
    NFCountry result;
    try {
      // try legacy first
      String countryCode = request.getHeader(BOT_LEGACY_COUNTRY);
      if (countryCode != null) {
        result = NFCountry.findInstance(countryCode);
        if (Objects.equals(result, NFCountry.AA))
          throw new APIServiceRuntimeException("AA is not an allowed country in bot headers!");
        return result;
      }

      // facebook bot code -- get seo prefix which is of the format language-country (e.g., en-US)
      String localeString = request.getHeader(BOT_LOCALE_HEADER);
      if (localeString != null) {
        NFLocale locale = NFLocale.findInstance(localeString);
        if (locale != null && locale.getCountry() != null) {
          result = NFCountry.findInstance(locale.getCountry());
          if (Objects.equals(result, NFCountry.AA))
            throw new APIServiceRuntimeException("AA is not an allowed country in bot headers!");
          return result;
        }
      }
    } catch (Exception e) {
      logger.error("Error getting seo country, rejecting request", e);
      throw e;
    }
    return null;
  }

  @SuppressWarnings("DuplicatedCode")
  private static NFLocale getSeoLocale(HttpServletRequest request) {
    try {
      // try legacy first
      String countryCode = request.getHeader(BOT_LEGACY_COUNTRY);
      String language = request.getHeader(BOT_LEGACY_LANGUAGE);
      if (countryCode != null && language != null) {
        return NFLocale.findInstance(language + LANGUAGE_COUNTRY_SEPARATOR + countryCode);
      }

      // facebook bot code -- get seo prefix which is of the format language-country (e.g., en-US)
      String localeString = request.getHeader(BOT_LOCALE_HEADER);
      if (localeString != null) {
        return NFLocale.findInstance(localeString);
      }
    } catch (Exception e) {
      logger.warn("Error getting seo locale, resorting to default country", e);
    }
    return null;
  }
}
