package com.netflix.api.service.video;

import com.netflix.videocolor.protogen.Colors;
import java.util.Objects;

public class APIColorsImpl implements APIColors {

  private final APIColor foreground;
  private final APIColor background;

  public APIColorsImpl(Colors value) {
    this.foreground =
        value != null && value.hasOptionalForeground()
            ? new APIColorImpl(value.getOptionalForeground())
            : null;
    this.background =
        value != null && value.hasOptionalBackground()
            ? new APIColorImpl(value.getOptionalBackground())
            : null;
  }

  @Override
  public APIColor getForeground() {
    return foreground;
  }

  @Override
  public APIColor getBackground() {
    return background;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIColorsImpl)) {
      return false;
    }
    APIColorsImpl apiColors = (APIColorsImpl) o;
    return Objects.equals(getForeground(), apiColors.getForeground())
        && Objects.equals(getBackground(), apiColors.getBackground());
  }

  @Override
  public int hashCode() {
    return Objects.hash(getForeground(), getBackground());
  }

  @Override
  public String toString() {
    return "APIColors{" + "foreground=" + foreground + ", background=" + background + '}';
  }
}
