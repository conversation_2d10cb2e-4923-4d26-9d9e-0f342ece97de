package com.netflix.api.adapters;

import com.netflix.api.dependencies.subscriber.AccountProfileHandler;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.i18n.NFLocale;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.migration.UserMigration;
import com.netflix.microcontext.access.server.utils.ResolverContextUtils;
import com.netflix.spectator.api.Registry;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.type.proto.Locales;
import jakarta.annotation.Nullable;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import netflix.context.Context;
import netflix.context.Context.Builder;
import netflix.context.experimentation.ExperimentationContext;
import netflix.context.user.UserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MicrocontextAdapter {
  private static final Logger logger = LoggerFactory.getLogger(MicrocontextAdapter.class);

  private static final Property<Boolean> ENABLE_MICROCONTEXT_USER_SYNC =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("microcontext.adapter.user-sync.enabled", Boolean.class)
          .orElse(true);

  private final AccountProfileHandler accountProfileHandler;
  private final Registry registry;

  @Autowired
  public MicrocontextAdapter(AccountProfileHandler accountProfileHandler, Registry registry) {
    this.accountProfileHandler = accountProfileHandler;
    this.registry = registry;
  }

  public void syncUser(@Nullable Long customerId) {
    if (ENABLE_MICROCONTEXT_USER_SYNC.get()) {
      syncContext(userSynchronizer(customerId));
    }
  }

  private static void syncContext(ContextSynchronizer synchronizer) {
    Context current = CurrentMicrocontext.get().toProto();
    Context synced = synchronizer.synchronize(current);
    if (current != synced) {
      CurrentMicrocontext.set(synced);
    }
  }

  private ContextSynchronizer userSynchronizer(@Nullable Long customerId) {
    return current -> {
      Optional<UserContext> userContext = UserMigration.getUser(current);
      Long mcCid =
          userContext.isPresent() && userContext.get().hasCurrentUser()
              ? userContext.get().getCurrentUser().getId()
              : null;

      if (Objects.equals(customerId, mcCid)) {
        registry.counter("microcontext.adapter.user-sync", "result", "same").increment();
        return current;
      }

      Builder builder = current.toBuilder();
      AccountProfileRemote accountProfileRemote = getAccountProfile(customerId);
      if (accountProfileRemote != null) {
        // add locales
        accountProfileRemote
            .getOptionalPrimaryLang()
            .ifPresent(
                s ->
                    builder.addAllLocales(
                        Collections.singletonList(
                            Locales.toProtobuf(NFLocale.findInstance(s).getId()))));
        registry.counter("microcontext.adapter.user-sync", "result", "cid").increment();
        return builder.build();
      }

      // No user and no vdid
      builder.clearUser();
      registry.counter("microcontext.adapter.user-sync", "result", "none").increment();
      return ResolverContextUtils.setExperimentation(
              builder, ExperimentationContext.getDefaultInstance())
          .build();
    };
  }

  private AccountProfileRemote getAccountProfile(@Nullable Long customerId) {
    AccountProfileRemote accountProfileRemote = null;
    if (customerId == null || customerId <= 0L) {
      registry.counter("microcontext.adapter.subscriber.call", "result", "no_cid").increment();
    } else {
      try {
        accountProfileRemote = accountProfileHandler.getBlocking(customerId).orElse(null);
        if (accountProfileRemote != null) {
          registry.counter("microcontext.adapter.subscriber.call", "result", "success").increment();
        } else {
          registry.counter("microcontext.adapter.subscriber.call", "result", "empty").increment();
        }
      } catch (Exception e) {
        registry.counter("microcontext.adapter.subscriber.call", "result", "error").increment();
        logger.info("Could not retrieve accountProfile", e);
      }
    }
    return accountProfileRemote;
  }

  @FunctionalInterface
  interface ContextSynchronizer {
    Context synchronize(Context context);
  }
}
