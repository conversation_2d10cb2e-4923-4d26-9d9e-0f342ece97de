package com.netflix.api.service.identity;

import com.netflix.api.dependencies.subscriber.AccountProfileHandler;
import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.subscriberservice.protogen.SubscriberManagementServiceGrpc.SubscriberManagementServiceBlockingStub;

public interface APIUserFactory {

  SubscriberManagementServiceBlockingStub getSubscriberManagementService();

  APIUserInternal createUser(long customerId);

  APIUserInternal createUser(long customerId, Long accountOwnerId);

  APIUserInternal createUser(IdentityResult<?> identityResult, boolean invalidateCache);

  APIUserInternal createUser(long customerId, boolean invalidateCache);

  APIUserInternal createUser(long customerId, Long accountOwnerId, boolean invalidateCache);

  APIUserInternal createUser(AccountProfileRemote accountProfile);

  AccountProfileHandler getAccountProfileHandler();
}
