package com.netflix.api.service.uipersonalization;

import com.netflix.uipersonalization.protogen.SecondaryLanguages;
import java.util.List;

public class APIUIPersonalizationSecondaryLanguagesImpl
    implements APIUIPersonalizationSecondaryLanguages {
  private final SecondaryLanguages secondaryLanguages;

  APIUIPersonalizationSecondaryLanguagesImpl(SecondaryLanguages secondaryLanguages) {
    this.secondaryLanguages = secondaryLanguages;
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#SecondaryLanguages.language"
   */
  @Override
  public List<String> getLanguages() {
    return this.secondaryLanguages.getLanguageList();
  }
}
