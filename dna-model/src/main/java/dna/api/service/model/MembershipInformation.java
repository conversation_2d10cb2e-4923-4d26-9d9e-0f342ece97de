package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class MembershipInformation implements GraphData {
  public Long billingPeriodEndDate;

  public String formattedCurrentPrice;

  @Deprecated public Boolean isSDOnly;

  public Boolean isHDEnabled;

  public Boolean isUHDEnabled;

  @Deprecated public Boolean isFreemium;

  public Integer maxStreams;

  public Integer maxDownloadDevices;

  public Atom<List<PlanDeviceType>> allowedDeviceTypes;

  public Long getBillingPeriodEndDate() {
    return billingPeriodEndDate;
  }

  public String getFormattedCurrentPrice() {
    return formattedCurrentPrice;
  }

  @Deprecated
  public Boolean getIsSDOnly() {
    return isSDOnly;
  }

  public Boolean getIsHDEnabled() {
    return isHDEnabled;
  }

  public Boolean getIsUHDEnabled() {
    return isUHDEnabled;
  }

  @Deprecated
  public Boolean getIsFreemium() {
    return isFreemium;
  }

  public Integer getMaxStreams() {
    return maxStreams;
  }

  public Integer getMaxDownloadDevices() {
    return maxDownloadDevices;
  }

  public Atom<List<PlanDeviceType>> getAllowedDeviceTypes() {
    return allowedDeviceTypes;
  }

  public MembershipInformation setBillingPeriodEndDate(Long billingPeriodEndDate) {
    this.billingPeriodEndDate = billingPeriodEndDate;
    return this;
  }

  public MembershipInformation setFormattedCurrentPrice(String formattedCurrentPrice) {
    this.formattedCurrentPrice = formattedCurrentPrice;
    return this;
  }

  @Deprecated
  public MembershipInformation setIsSDOnly(Boolean isSDOnly) {
    this.isSDOnly = isSDOnly;
    return this;
  }

  public MembershipInformation setIsHDEnabled(Boolean isHDEnabled) {
    this.isHDEnabled = isHDEnabled;
    return this;
  }

  public MembershipInformation setIsUHDEnabled(Boolean isUHDEnabled) {
    this.isUHDEnabled = isUHDEnabled;
    return this;
  }

  @Deprecated
  public MembershipInformation setIsFreemium(Boolean isFreemium) {
    this.isFreemium = isFreemium;
    return this;
  }

  public MembershipInformation setMaxStreams(Integer maxStreams) {
    this.maxStreams = maxStreams;
    return this;
  }

  public MembershipInformation setMaxDownloadDevices(Integer maxDownloadDevices) {
    this.maxDownloadDevices = maxDownloadDevices;
    return this;
  }

  public MembershipInformation setAllowedDeviceTypes(
      Atom<List<PlanDeviceType>> allowedDeviceTypes) {
    this.allowedDeviceTypes = allowedDeviceTypes;
    return this;
  }
}
