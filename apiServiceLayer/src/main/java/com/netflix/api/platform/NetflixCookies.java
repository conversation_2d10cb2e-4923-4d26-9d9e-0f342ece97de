package com.netflix.api.platform;

public class NetflixCookies {

  private NetflixCookies() {}

  private static final String COOKIE_NAME;
  private static final String SECURE_COOKIE_NAME;

  static {
    if (System.getProperty("netflix.environment", "test").equals("prod")) {
      COOKIE_NAME = "NetflixId";
      SECURE_COOKIE_NAME = "SecureNetflixId";
    } else {
      COOKIE_NAME = "NetflixIdTest";
      SECURE_COOKIE_NAME = "SecureNetflixIdTest";
    }
  }

  public static String getNetflixIdCookieName() {
    return COOKIE_NAME;
  }

  public static String getSecureNetflixIdCookieName() {
    return SECURE_COOKIE_NAME;
  }
}
