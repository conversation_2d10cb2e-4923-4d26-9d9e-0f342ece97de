package com.netflix.api.platform.http.throttling;

import com.netflix.servo.annotations.DataSourceType;
import com.netflix.servo.annotations.Monitor;
import java.util.concurrent.atomic.LongAdder;

public class ThrottleStats {

  @Monitor(name = "concurrentRequestCount", type = DataSourceType.GAUGE)
  public final LongAdder concurrentRequestCount = new LongAdder();

  @Monitor(name = "concurrentBackgroundRequestCount", type = DataSourceType.GAUGE)
  public final LongAdder concurrentBackgroundRequestCount = new LongAdder();

  @Monitor(name = "requestReceived", type = DataSourceType.COUNTER)
  public final LongAdder requestReceived = new LongAdder();

  @Monitor(name = "responseSent", type = DataSourceType.COUNTER)
  public final LongAdder responseSent = new LongAdder();

  @Monitor(name = "requestThrottled", type = DataSourceType.COUNTER)
  public final LongAdder throttleCount = new LongAdder();

  @Monitor(name = "backgroundRequestThrottled", type = DataSourceType.COUNTER)
  public final LongAdder throttleBackgroundCount = new LongAdder();
}
