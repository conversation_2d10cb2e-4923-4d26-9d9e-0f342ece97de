package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * A layout that contains cover art for a title and aligns with Netflix's box art size and aspect
 * ratio.
 */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NapaBoxshotLayout extends NapaLayout {}
