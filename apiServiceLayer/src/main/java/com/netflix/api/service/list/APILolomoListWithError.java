package com.netflix.api.service.list;

import com.netflix.api.service.APIAnnotation;
import com.netflix.api.service.ListItem;
import java.util.List;
import rx.Observable;
import rx.Single;

/**
 * Obeys the APIList interface while only returning an error.
 *
 * <p>This is useful when there's an error during construction. Since APIList is designed to be
 * returned synchronously, letting the APIList wrap the exception is important to allow consumers to
 * use normal RxJava error-handling. Errors will surface observably through access methods, rather
 * than getting thrown at construct-time.
 *
 * @param <T>
 */
public class APILolomoListWithError<T extends APIEntity> implements APILolomoList<T> {
  private final RuntimeException ex;
  private final Observable<?> asyncEx;

  public APILolomoListWithError(Throwable ex) {
    if (ex instanceof RuntimeException) {
      this.ex = (RuntimeException) ex;
    } else {
      this.ex = new RuntimeException(ex);
    }
    this.asyncEx = Observable.error(this.ex);
  }

  @Override
  public Observable<String> getListName() {
    return (Observable<String>) asyncEx;
  }

  @Override
  public Observable<String> getListId() {
    return (Observable<String>) asyncEx;
  }

  @Override
  public Observable<List<T>> toOrderedList() {
    return (Observable<List<T>>) asyncEx;
  }

  @Override
  public Observable<T> toItems() {
    return (Observable<T>) asyncEx;
  }

  @Override
  public Observable<Integer> getSize() {
    return (Observable<Integer>) asyncEx;
  }

  @Override
  public Observable<ListItem<T>> getListItems() {
    return (Observable<ListItem<T>>) asyncEx;
  }

  @Override
  public Observable<APIAnnotation<String, Object>> getAnnotations() {
    return (Observable<APIAnnotation<String, Object>>) asyncEx;
  }

  @Override
  public String getName() {
    throw ex;
  }

  @Override
  public String getId() {
    throw ex;
  }

  @Override
  public Single<ListContext> getContext() {
    return ((Observable<ListContext>) asyncEx).toSingle();
  }
}
