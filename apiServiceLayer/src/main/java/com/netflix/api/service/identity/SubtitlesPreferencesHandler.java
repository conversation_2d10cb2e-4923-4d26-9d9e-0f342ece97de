package com.netflix.api.service.identity;

import static java.util.Optional.ofNullable;

import com.google.protobuf.ProtocolMessageEnum;
import com.netflix.api.grpc.GrpcCallHelpers.Future;
import com.netflix.api.grpc.GrpcCallHelpers.RxSingle;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APISubtitleSettings.Opacity;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.userpreferences2.protogen.DeleteSubtitleSettingsRequest;
import com.netflix.userpreferences2.protogen.DeleteSubtitleSettingsResponse;
import com.netflix.userpreferences2.protogen.Device.Group;
import com.netflix.userpreferences2.protogen.GetLegacySubtitleSettingsRequest;
import com.netflix.userpreferences2.protogen.GetSubtitleSettingsRequest;
import com.netflix.userpreferences2.protogen.GetSubtitleSettingsResponse;
import com.netflix.userpreferences2.protogen.SubtitleSettings;
import com.netflix.userpreferences2.protogen.SubtitlesServiceGrpc.SubtitlesServiceStub;
import com.netflix.userpreferences2.protogen.WriteSubtitleSettingsRequest;
import com.netflix.userpreferences2.protogen.WriteSubtitleSettingsResponse;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.Function;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Single;

@Component
public class SubtitlesPreferencesHandler {

  private final SubtitlesServiceStub subtitlesServiceStub;

  @Autowired
  public SubtitlesPreferencesHandler(
      @GrpcSpringClient("userpreferences2") SubtitlesServiceStub subtitlesServiceStub) {
    this.subtitlesServiceStub = subtitlesServiceStub;
  }

  public CompletionStage<GetSubtitleSettingsResponse> getSubtitlePreferences(Long customerId) {
    return Future.call(
        subtitlesServiceStub::getSubtitleSettingsForAllGroups,
        GetSubtitleSettingsRequest.newBuilder().setProfileId(String.valueOf(customerId)).build());
  }

  Single<APISubtitleSettings> getLegacySubtitlePreferences(Long customerId) {
    String uiFlavor =
        ofNullable(APIRequest.getCurrentRequest().getRequestContext().getUIFlavor())
            .map(Enum::name)
            .orElse(null);
    GetLegacySubtitleSettingsRequest.Builder subtitlesReqBuilder =
        GetLegacySubtitleSettingsRequest.newBuilder().setProfileId(String.valueOf(customerId));
    if (uiFlavor != null) {
      subtitlesReqBuilder.setUiFlavor(uiFlavor);
    }
    return RxSingle.defer(
            subtitlesServiceStub::getLegacySubtitleSettings, subtitlesReqBuilder.build())
        .map(
            legacySubtitleSettingsResponse -> {
              SubtitleSettings subtitleSettings =
                  legacySubtitleSettingsResponse.getSubtitleSettings();
              return toAPISubtitleSettings(subtitleSettings);
            });
  }

  public CompletionStage<WriteSubtitleSettingsResponse> writeSubtitlePreference(
      Long customerId, List<SubtitleSettings> settings) {
    WriteSubtitleSettingsRequest.Builder builder =
        WriteSubtitleSettingsRequest.newBuilder()
            .setProfileId(String.valueOf(customerId))
            .addAllSubtitleSettings(settings);
    return Future.call(subtitlesServiceStub::writeSubtitleSettings, builder.build());
  }

  public CompletionStage<DeleteSubtitleSettingsResponse> deleteSubtitlePreference(
      Long customerId, String applicationGrp) {
    if (applicationGrp == null) {
      return CompletableFuture.failedFuture(
          new IllegalArgumentException("Must provide a valid application group"));
    }
    DeleteSubtitleSettingsRequest deleteSubtitleSettingsRequest =
        DeleteSubtitleSettingsRequest.newBuilder()
            .setProfileId(String.valueOf(customerId))
            .setDeviceGroup(Group.valueOf(applicationGrp))
            .build();

    return Future.call(subtitlesServiceStub::deleteSubtitleSettings, deleteSubtitleSettingsRequest);
  }

  private static APISubtitleSettings toAPISubtitleSettings(SubtitleSettings subtitleSettings) {

    APISubtitleSettings apiSubtitleSettings = APISubtitleSettingsImpl.empty();
    apiSubtitleSettings.setCharacterOpacity(
        convertOpacity.apply(subtitleSettings.getCcCharOpacity()));
    apiSubtitleSettings.setBackgroundOpacity(
        convertOpacity.apply(subtitleSettings.getCcBgOpacity()));
    apiSubtitleSettings.setWindowOpacity(
        convertOpacity.apply(subtitleSettings.getCcWindowOpacity()));
    apiSubtitleSettings.setCharacterColor(convertColor.apply(subtitleSettings.getCcCharColor()));
    apiSubtitleSettings.setBackgroundColor(convertColor.apply(subtitleSettings.getCcBgColor()));
    apiSubtitleSettings.setWindowColor(convertColor.apply(subtitleSettings.getCcWindowColor()));
    apiSubtitleSettings.setCharacterEdgeColor(
        convertColor.apply(subtitleSettings.getCcCharEdgeColor()));
    apiSubtitleSettings.setCharacterSize(convertCharSize.apply(subtitleSettings.getCcCharSize()));
    apiSubtitleSettings.setCharacterStyle(
        convertCharStyle.apply(subtitleSettings.getCcCharStyle()));
    apiSubtitleSettings.setCharacterEdgeAttributes(
        convertEdgetAttr.apply(subtitleSettings.getCcCharEdgeAttrs()));
    return apiSubtitleSettings;
  }

  private static final Function<ProtocolMessageEnum, Opacity> convertOpacity =
      toEnum(APISubtitleSettings.Opacity.class);

  private static final Function<com.google.protobuf.ProtocolMessageEnum, APISubtitleSettings.Color>
      convertColor = toEnum(APISubtitleSettings.Color.class);

  private static final Function<com.google.protobuf.ProtocolMessageEnum, APISubtitleSettings.Size>
      convertCharSize = toEnum(APISubtitleSettings.Size.class);

  private static final Function<com.google.protobuf.ProtocolMessageEnum, APISubtitleSettings.Style>
      convertCharStyle = toEnum(APISubtitleSettings.Style.class);

  private static final Function<
          com.google.protobuf.ProtocolMessageEnum, APISubtitleSettings.EdgeAttributes>
      convertEdgetAttr = toEnum(APISubtitleSettings.EdgeAttributes.class);

  public static <E1 extends com.google.protobuf.ProtocolMessageEnum, E2 extends Enum<E2>>
      Function<E1, E2> toEnum(Class<E2> e2Class) {
    return e1 -> {
      try {
        return E2.valueOf(e2Class, e1.getValueDescriptor().getName());
      } catch (IllegalArgumentException ignored) {
      }
      return null;
    };
  }
}
