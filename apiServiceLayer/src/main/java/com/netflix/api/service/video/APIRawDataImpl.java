/** */
package com.netflix.api.service.video;

import com.google.common.base.Preconditions;
import java.util.Map;
import rx.Observable;

public class APIRawDataImpl implements APIRawData {

  private final Map<String, ?> data;

  public static APIRawData newInstance(final Map<String, ?> rawData) {
    Preconditions.checkNotNull(rawData, "Raw data must not be null");
    return new APIRawDataImpl(rawData);
  }

  private APIRawDataImpl(final Map<String, ?> rawData) {
    data = Map.copyOf(rawData);
  }

  @Override
  public String getName() {
    return null;
  }

  @Override
  public Object getId() {
    return null;
  }

  @Override
  public Observable<Map<String, ?>> toObservable() {
    return Observable.just(data);
  }
}
