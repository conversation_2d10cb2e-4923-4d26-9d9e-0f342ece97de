package com.netflix.api.service.video;

import static java.util.Map.entry;

import com.netflix.aballocator.protogen.AllocResponse;
import com.netflix.api.adapters.StreamingClientServiceAdapter;
import com.netflix.api.platform.context.ImmutableRequestContext;
import com.netflix.api.platform.context.ProductAccessContext;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.util.CompletionStageAdapter;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.platform.util.RequestScopedSingle;
import com.netflix.api.service.APIClientCapabilitiesInternal;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.RequestContextInternal;
import com.netflix.api.service.abtest.ABAdapterBase;
import com.netflix.api.util.ServiceUtils;
import com.netflix.archaius.api.Property;
import com.netflix.images.protogen.ClientCapabilityList;
import com.netflix.images.protogen.ImageCriteria;
import com.netflix.lang.RequestVariable;
import com.netflix.pacs.protogen.ContentGrants;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.type.proto.Countries;
import com.netflix.type.proto.Locales;
import com.netflix.type.proto.Visitors;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Single;

/** Author: sgudiboina Date: 5/4/16 Time: 2:35 PM */
// tony was here 4/2/24 Time: 10:21:21 AM
@Component
public class ImageResolverUtils {
  private static final Logger logger = LoggerFactory.getLogger(ImageResolverUtils.class);

  private static final Property<Boolean> ENABLE_NQ_DIMENSION =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("stats.enableNQ", Boolean.class)
          .orElse(false);

  // only enable in trafficmonitor shards and non-prod envs, else get metrics explosion via
  // endpointPath dimension
  public static final Property<Boolean> TRACKING_CALLS_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.artwork.tracking.calls.enabled", Boolean.class)
          .orElse(false);

  public static final Property<Boolean> TRACKING_PACS_CALLS_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.artwork.tracking.pacs.calls.enabled", Boolean.class)
          .orElse(false);

  public static final Property<Boolean> PERFORM_NEW_CONTENT_CHECK =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.artwork.perform.new.content.check", Boolean.class)
          .orElse(true);

  private final RequestScopedSingle<ImageCriteria> cachedRequestLevelCriteria =
      new RequestScopedSingle<>();

  public static final RequestVariable<Map<Map<String, Object>, AtomicInteger>>
      AGGREGATE_CALL_METRICS =
          new RequestVariable<>() {
            @Override
            public Map<Map<String, Object>, AtomicInteger> initialValue() {
              return new ConcurrentHashMap<>();
            }
          };

  public static final RequestVariable<Map<String, AtomicInteger>> DETAILED_CALL_METRICS =
      new RequestVariable<>() {
        @Override
        public Map<String, AtomicInteger> initialValue() {
          return new ConcurrentHashMap<>();
        }
      };

  private final ABAdapterBase abAdapterBase;
  private final StreamingClientServiceAdapter streamingClientServiceAdapter;
  private final ProductAccessContext productAccessContext;

  @Autowired
  public ImageResolverUtils(
      ABAdapterBase abAdapterBase,
      StreamingClientServiceAdapter streamingClientServiceAdapter,
      ProductAccessContext productAccessContext) {
    this.abAdapterBase = abAdapterBase;
    this.streamingClientServiceAdapter = streamingClientServiceAdapter;
    this.productAccessContext = productAccessContext;
  }

  private static final String NA = "N/A";

  static String getEndpointPathAsTag() {
    var request = APIRequest.getCurrentRequest();
    if (request == null) return NA;

    var servlet = request.getServletRequest();
    if (servlet == null) return NA;

    var path = servlet.getPathInfo();
    if (path == null) return NA;

    return path;
  }

  public static void trackCall(
      boolean explicitBatching,
      String imageType,
      int batchSize,
      String uiFlavor,
      ContentGrants contentGrants) {
    if (!TRACKING_CALLS_ENABLED.get()) {
      // no-op
      return;
    }

    // bucketing of batch size
    String batchSizeTag = explicitBatching ? ServiceUtils.buildBatchSizeTags(batchSize) : "1";
    Map<String, Object> aggregateKey =
        buildAggregateKey(
            explicitBatching, imageType, getEndpointPathAsTag(), batchSizeTag, uiFlavor);
    AGGREGATE_CALL_METRICS.get().putIfAbsent(aggregateKey, new AtomicInteger(0));
    AGGREGATE_CALL_METRICS.get().get(aggregateKey).incrementAndGet();

    // detailed metrics end up in Mantis but will be used to produce Atlas counters
    String detailedKey = buildDetailedKey(explicitBatching, batchSize, contentGrants);
    DETAILED_CALL_METRICS.get().putIfAbsent(detailedKey, new AtomicInteger(0));
    DETAILED_CALL_METRICS.get().get(detailedKey).addAndGet(explicitBatching ? 1 : batchSize);
  }

  private static String buildDetailedKey(
      boolean explicitBatching, int batchSize, ContentGrants contentGrants) {
    StringBuilder sb = new StringBuilder("api.artwork.request.");
    sb.append(explicitBatching ? "batched" : "nonbatched");
    if (explicitBatching) {
      sb.append(".").append(batchSize <= 1 ? String.valueOf(batchSize) : "moreThan1");
    }
    if (TRACKING_PACS_CALLS_ENABLED.get() && contentGrants != null) {
      trackPacsContentGroups(".pcg:", contentGrants.getPlayableContentGroupsList(), sb);
      trackPacsContentGroups(".mcg:", contentGrants.getMerchableContentGroupsList(), sb);
      trackPacsContentGroups(".dcg:", contentGrants.getDownloadableContentGroupsList(), sb);
    }
    return sb.toString();
  }

  private static void trackPacsContentGroups(
      String key, List<String> contentGroupData, StringBuilder sb) {
    String contentGrpStr =
        (contentGroupData != null && !contentGroupData.isEmpty())
            ? String.join(",", contentGroupData)
            : "";
    sb.append(key).append(contentGrpStr);
  }

  private static Map<String, Object> buildAggregateKey(
      boolean explicitBatching,
      String imageType,
      String endpointPath,
      String batchSizeTag,
      String uiFlavor) {

    var request = APIRequest.getCurrentRequest();
    var status = getMembershipStatus(request);

    // keep key names in line with NIL  tags, to make it easier to grok
    return Map.ofEntries(
        entry("IsBatchRequest", explicitBatching),
        entry("ImageType", imageType != null ? imageType : NA),
        entry("BatchSize", batchSizeTag),
        entry("endpoint", endpointPath),
        entry("UIFlavor", uiFlavor != null ? uiFlavor : NA),
        entry("membershipStatus", status));
  }

  private static String getMembershipStatus(APIRequest request) {
    if (request == null) return NA;

    var user = request.getUser();
    if (user == null) return NA;

    var status = user.getMembershipStatus();
    if (status == null) return NA;

    return status.name();
  }

  public Single<ImageCriteria> asyncSetRequestLevelCriteria() {
    Single<ImageCriteria> criteria = cachedRequestLevelCriteria.get().get();
    if (criteria != null) {
      return criteria;
    }
    criteria =
        // Single.cache does not exist in this rx version...
        Single.defer(() -> setRequestLevelCriteria().cache().toSingle());
    return cachedRequestLevelCriteria.get().compareAndSet(null, criteria)
        ? criteria
        : cachedRequestLevelCriteria.get().get();
  }

  private Observable<ImageCriteria> setRequestLevelCriteria() {

    ImageCriteria.Builder newCriteria = buildImageCriteria();
    // filter out cell1
    Single<AllocResponse> allocations = abAdapterBase.getAllocationsAsProto(true, true).toSingle();
    Single<String> hostname = streamingClientServiceAdapter.getHostName();
    Single<ContentGrants> pacsContentGrants =
        CompletionStageAdapter.toSingle(productAccessContext.currentContentGrants());
    return Single.zip(
            allocations,
            hostname,
            pacsContentGrants,
            (a, h, contentGrants) -> {
              if (a != null) {
                a.getAllocMap().forEach(newCriteria::putAbAllocations);
              }
              if (h != null) {
                newCriteria.putParams("hostname", h);
              }
              if (contentGrants != null) {
                newCriteria.setContentGrants(contentGrants);
              }
              return newCriteria.build();
            })
        .onErrorReturn(
            e -> {
              logger.debug("Caught exception when populating image criteria {}", e.getMessage());
              return newCriteria.build();
            })
        .toObservable();
  }

  public static ImageCriteria.Builder buildImageCriteria() {
    var builder = ImageCriteria.newBuilder();

    var apiRequest = APIRequest.getCurrentRequest();
    setCriteria(builder, apiRequest);

    // these are client capabilities ... some or all of them will affect which artwork is
    // returned.
    APIClientCapabilitiesInternal.get(apiRequest)
        .ifPresent(
            capabilities ->
                capabilities
                    .asMap()
                    .forEach(
                        (key, values) -> {
                          var list =
                              ClientCapabilityList.newBuilder()
                                  .setKey(key)
                                  .addAllCapabilities(values);
                          builder.addClientCapabilities(list);
                        }));

    var ctxInternal = RequestContextInternal.getCurrent();
    if (ctxInternal != null) {
      var preferences = ctxInternal.getLocalePreferences();
      for (var language : preferences) {
        var locale = language.toLocale();
        builder.addLocales(Locales.toProtobuf(locale));
      }
    }

    var ctx = RequestContextWrapper.get();
    if (ctx != null) {
      var country = ctx.getCountry();
      builder.setCountry(Countries.toProtobuf(country));

      var deviceTypeId = getDeviceTypeIdAsString(ctx);
      builder.putParams("deviceTypeId", deviceTypeId);
    }

    var visitor = CurrentVisitor.get();
    if (visitor != null && visitor.getId() != null) {
      builder.setVisitor(Visitors.toProtobuf(visitor));
    }

    return builder;
  }

  private static void setCriteria(ImageCriteria.Builder builder, APIRequest request) {
    if (request == null) return;

    var ctx = request.getRequestContext();
    var flavor = ctx.getUIFlavor().name();
    builder.setUiFlavor(flavor);

    var user = request.getUser();
    if (user != null) {
      builder.putParams("userPreferredLocale", user.getLanguage());

      if (user.isTestAccount()) {
        builder.putParams("isTesterAccount", "true");
      }
    }

    var servletRequest = request.getServletRequest();
    setCriteria(builder, servletRequest);
  }

  private static void setCriteria(ImageCriteria.Builder builder, HttpServletRequest request) {
    if (request == null) return;

    if (request.getPathInfo() != null) {
      builder.putParams("endpoint", request.getPathInfo());
    }

    if (ENABLE_NQ_DIMENSION.get()) {
      var name = request.getHeader("x-netflix.nqserviceinfo.name");
      builder.putParams("nqserviceinfo.name", value(name));

      var org = request.getHeader("x-netflix.nqserviceinfo.org");
      builder.putParams("nqserviceinfo.org", value(org));

      var platform = request.getHeader("x-netflix.nqserviceinfo.platformversion");
      builder.putParams("nqserviceinfo.platformversion", value(platform));

      var version = request.getHeader("x-netflix.nqserviceinfo.version");
      builder.putParams("nqserviceinfo.version", value(version));
    }
  }

  private static String value(String s) {
    return s != null ? s : "n/a";
  }

  private static String getDeviceTypeIdAsString(ImmutableRequestContext ctx) {
    var type = ctx.getDeviceType();

    if (type != null) {
      var id = type.getId();
      if (id != null) {
        return String.valueOf(id);
      }
    }

    return NA;
  }

  public static ImageCriteria.Builder setParams(
      Map<String, Object> params, final ImageCriteria.Builder builder) {
    if (params == null) return builder;

    params.forEach(
        (name, value) -> {
          if (name != null && value != null && !"clientCapabilities".equals(name)) {
            builder.putParams(name, String.valueOf(value));
          }
        });

    return builder;
  }
}
