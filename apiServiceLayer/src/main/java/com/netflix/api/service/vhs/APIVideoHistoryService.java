package com.netflix.api.service.vhs;

import com.netflix.api.service.identity.APIUser;
import java.util.Collection;
import rx.Completable;
import rx.Observable;

public interface APIVideoHistoryService {

  /**
   * Get all the videos being watched currently for the account that this customer belongs to. This
   * is not a complete viewing history.
   *
   * @param user customer for whom you want current viewings
   * @return an <code>Observable</code> that emits <code>APIViewings</code> objects
   */
  Observable<APIViewings> getCurrentViewings(APIUser user);

  /**
   * Collects video play information outside of Netflix. This information is used to seed
   * information about the user at (or close to) sign up time so we can create a better initial
   * experience for new members.
   *
   * @param user The user to store the preferences
   * @param videoIds The videos to add to the preferences
   * @return an {@link Observable} that emits an instance of the current state of preferences for
   *     this user; note that the list of APIPreNetflixItem-s is ALWAYS empty but kept for
   *     compatibility reasons! see DNA-1288
   */
  Completable addFakePlays(APIUser user, Collection<Integer> videoIds);

  /**
   * Hide viewing history for a given video id, and user. If successful, this will return a single
   * observable of {@link Boolean#TRUE}
   *
   * @param user user for whom the viewing needs to be hidden
   * @param videoId video id
   * @param allEpisodes hide all episodes from same show if true, if videoId is not a show this will
   *     be ignored
   * @return
   */
  Observable<Boolean> hideViewing(APIUser user, int videoId, boolean allEpisodes);
}
