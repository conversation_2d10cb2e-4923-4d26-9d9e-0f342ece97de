package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class GpsCategoryCapabilities {
  /** Signifies that section should be populated with category entities */
  private Boolean shouldPopulateEntities;

  private Boolean canHandle;

  public Boolean getShouldPopulateEntities() {
    return shouldPopulateEntities;
  }

  public Boolean getCanHandle() {
    return canHandle;
  }

  public GpsCategoryCapabilities setShouldPopulateEntities(Boolean shouldPopulateEntities) {
    this.shouldPopulateEntities = shouldPopulateEntities;
    return this;
  }

  public GpsCategoryCapabilities setCanHandle(Boolean canHandle) {
    this.canHandle = canHandle;
    return this;
  }
}
