package com.netflix.api.service;

import com.netflix.api.service.device.APIDeviceType;
import java.util.Collection;
import java.util.List;
import rx.Observable;

/** Common types of data per request. */
public interface APIRequestContext {

  /**
   * AppId for the requesting device.
   *
   * @return the application ID
   */
  Integer getAppId();

  /**
   * Application name of the requesting device. This value is sent by the device in the request; no
   * logic or processing is applied to it.
   *
   * @return the application name or null
   */
  String getAppName();

  /**
   * Environment in which this request is being served
   *
   * @return the environment name as string, current values are prod and test
   */
  String getEnvironment();

  /**
   * Stack in which this request is being served
   *
   * @return the stack name as a string, current values are test, int, staging and prod
   */
  String getStack();

  /**
   * Country of current request.
   *
   * @return an {@link APICountry} object representing the country of the current request
   */
  APICountry getCountry();

  /**
   * ESN (Electronic Serial Number) of requesting device. API.Next resolves the ESN from the
   * following request inputs (in the order of precedence):
   *
   * <ol>
   *   <li>request parameter "<code>esn</code>" (e.g. <code>esn=NFPS3-001-<i>blah</i></code>)
   *   <li>request header "<code>X-Netflix.esn</code>"
   *   <li>device or double bound cookies (<code>NetflixId</code> and <code>SecureNetflixId</code>
   *       ones)
   * </ol>
   *
   * @return the requesting device's ESN, as a String
   */
  String getESN();

  /**
   * ESN Bucket - Enables callers to bucket requests via ESN value.
   *
   * @return on success, an integer between 0 and 99 that represents the 'bucket' that the ESN fits
   *     into. On error, -1 will be returned. Errors will occur if the ESN is null or empty, for
   *     example.
   */
  int getESNBucket();

  /**
   * Gets the device type id for this request, resolved from the following request inputs in this
   * order of precedence:
   *
   * <ol>
   *   <li>device or double bound cookies
   *   <li>a DTS lookup of the ESN
   * </ol>
   *
   * @return the device type id or null if none was found
   */
  Integer getDeviceTypeId();

  /**
   * The device type for the requesting device, as determined by a lookup of the ESN
   *
   * @return the device type for the requesting device, or null if it could not be determined
   */
  APIDeviceType getDeviceType();

  /** Gets the first value from {@link #getPreferredLocales()} */
  APILocale getLocale();

  /**
   * Returns an <code>Observable</code> that emits the preferred locales specified in the request.
   * These are wrapped in a <code>ListItem</code> where <code>ListItem.getIndex()</code> returns the
   * index of the locale preference.
   *
   * @return an <code>Observable</code> that emits list items of {@link APILocale}
   */
  Observable<ListItem<APILocale>> getPreferredLocales();

  /**
   * Set (override) the preferred locale list for this request. Preferred locales are used by
   * underlying dependencies to localize metadata that they expose to the API. In certain cases
   * (non-member experience, for instance), clients need to override the locale list that was
   * derived based on supported and accepted locales. This gives clients the ability to do so.
   *
   * <p>API.Next will automatically make the data it provides to you location-appropriate, based on
   * where it believes your client is making its requests from - this is the automatically-assigned
   * locale. You can manually override this automatically-assigned locale if it is incorrect or
   * inappropriate.
   *
   * <p>To do this, your script will call the <code>setPreferredLocales</code> method of the <code>
   * APIRequestContext</code> object. Your script can obtain a handle for this object by calling
   * <code>api.getRequestContext</code>.
   *
   * <p>Pass this method the list of locale identifying strings that represent the locales you want
   * API.Next to use when it localizes the data it returns to your client. Order the items in this
   * list by preference, with the most preferred locale first in the list, and subsequent locales as
   * fallbacks in case the earlier ones in the list are not available for localization.
   *
   * @deprecated pass in locales as request parameter
   * @param locales list of locales (as Strings). If you pass in null or an empty list, the current
   *     setting is <em>not</em> overridden.
   */
  @Deprecated
  void setPreferredLocales(List<String> locales);

  /**
   * URI path (in template form) of the endpoint. This is the name of the endpoint as it was
   * uploaded.
   *
   * @return the path given to the endpoint when it was uploaded. For example,
   *     /tvui/pathEvaluator/{command}
   */
  String getEndpointPath();

  /**
   * The endpoint group associated with this request (this is usually the first path segment in the
   * request, i.e. /shakti/foo would be shakti)
   *
   * @return the endpoint group
   */
  String getEndpointGroup();

  /**
   * Request ID - A UUID assigned to the request, used by clients for presentation tracking.
   *
   * @return the request ID
   */
  String getRequestId();

  /** AKA VDID. The next version of clientId and nrmid? I don't know, ask the subscriber team */
  String getVisitorDeviceId();

  /** Returns whether or not the Geo data of this request has been overridden. */
  boolean isGeoOverriden();

  /**
   * UIs can announce their client capabilities. Instead of passing cross-cutting UI/device
   * properties to several interested services, UI's set it at one place in their request flow, and
   * Edge will pass it to interested mid-tier services. This method is additive, can be called
   * repeatedly.
   *
   * @param clientCapabilities List of tags. Each client capability is agreed upon by UI teams and
   *     services, so enumerating the list is not possible. Not null!
   */
  void addClientCapabilities(String featureSet, Collection<String> clientCapabilities);

  /**
   * Mid tier services, specifically MAP and Netflix Image Library, need a UI flavor to vary the
   * behavior across different UI platforms.
   *
   * <p>Note: UI flavor indicates the UI server side (groovy) codebase, not the UI experience. The
   * discrete set of values is defined by MAP team.
   *
   * @see UIFlavor
   * @param uiFlavor - A broad identifier of the UI (groovy) codebase - enumerated in {@link
   *     UIFlavor}
   */
  void setUIFlavor(UIFlavor uiFlavor);

  /**
   * Returns the UI flavor (that was either explicitly set using or determined based on the
   * request).
   *
   * @return the UI flavor. Can be null.
   */
  UIFlavor getUIFlavor();

  /**
   * Returns the OCA hostname (url segment) that's used by UI bootloaders to create js/css URIs that
   * point to OCA servers instead of Akamai (non-netflix CDNs).
   */
  Observable<String> getSteeredCdnHostname();

  boolean deviceSupportsLocalizedKidsProfile();
}
