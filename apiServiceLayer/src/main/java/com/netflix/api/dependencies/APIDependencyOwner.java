package com.netflix.api.dependencies;

import com.netflix.platform.dependencycommand.NFDependencyOwner;
import net.jcip.annotations.ThreadSafe;

/**
 * A list of "owners" of various dependencies so that we can mark services, NFDependencyCommand
 * objects etc with their appropriate owners for logging and error handling.
 */
@ThreadSafe
public enum APIDependencyOwner implements NFDependencyOwner {
  UNKNOWN("Unknown Owner"),
  EDGE_INTERNAL_INSIGHTS("Edge internal insights"),
  SUBSCRIBER("SubscriptionData"),
  CRYPTEX("Token creation failed"),
  DISCOVERY("Discovery"),
  SOCIAL("Social"),
  AUTHSERVICE("Identity/Authentication"),
  NFI18NRESOURCES("NFI18NRESOURCES"),
  GEO("Geo"),
  MERCHAGG("Merch Aggregation Layer"),
  EMAIL("Email Manager cannot send email"),
  CUST_EVENTS("Customer event logger"),
  REVIEW("Reviews"),
  T<PERSON><PERSON><PERSON><PERSON>("Tracking"),
  PLAYBACK_CONTENT("Playback Services - Content"),
  ACCOUNT("Account Service"),
  PROFILESERVICE("Profile Service");

  private final String description;

  APIDependencyOwner(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  @Override
  public String toString() {
    return name() + " => " + description;
  }
}
