package dna.api.service.model;

import com.netflix.gpsproto.page.capability.dimension.protogen.MaxRows;
import com.netflix.gpsproto.page.capability.dimension.protogen.MaxTitlesPerFeed;
import com.netflix.gpsproto.page.capability.dimension.protogen.MaxTitlesPerRow;
import com.netflix.gpsproto.page.capability.dimension.protogen.MinTitlesPerRow;
import com.netflix.gpsproto.page.capability.dimension.protogen.ViewPortHeight;
import com.netflix.gpsproto.page.capability.dimension.protogen.ViewPortWidth;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class GpsPageDimensions {
  private MaxRows maxRows;

  private MaxTitlesPerFeed maxTitlesPerFeed;

  private MaxTitlesPerRow maxTitlesPerRow;

  private MinTitlesPerRow minTitlesPerRow;

  private ViewPortHeight viewPortHeight;

  private ViewPortWidth viewPortWidth;

  public MaxRows getMaxRows() {
    return maxRows;
  }

  public MaxTitlesPerFeed getMaxTitlesPerFeed() {
    return maxTitlesPerFeed;
  }

  public MaxTitlesPerRow getMaxTitlesPerRow() {
    return maxTitlesPerRow;
  }

  public MinTitlesPerRow getMinTitlesPerRow() {
    return minTitlesPerRow;
  }

  public ViewPortHeight getViewPortHeight() {
    return viewPortHeight;
  }

  public ViewPortWidth getViewPortWidth() {
    return viewPortWidth;
  }

  public GpsPageDimensions setMaxRows(MaxRows maxRows) {
    this.maxRows = maxRows;
    return this;
  }

  public GpsPageDimensions setMaxTitlesPerFeed(MaxTitlesPerFeed maxTitlesPerFeed) {
    this.maxTitlesPerFeed = maxTitlesPerFeed;
    return this;
  }

  public GpsPageDimensions setMaxTitlesPerRow(MaxTitlesPerRow maxTitlesPerRow) {
    this.maxTitlesPerRow = maxTitlesPerRow;
    return this;
  }

  public GpsPageDimensions setMinTitlesPerRow(MinTitlesPerRow minTitlesPerRow) {
    this.minTitlesPerRow = minTitlesPerRow;
    return this;
  }

  public GpsPageDimensions setViewPortHeight(ViewPortHeight viewPortHeight) {
    this.viewPortHeight = viewPortHeight;
    return this;
  }

  public GpsPageDimensions setViewPortWidth(ViewPortWidth viewPortWidth) {
    this.viewPortWidth = viewPortWidth;
    return this;
  }
}
