package com.netflix.api.service.batch.model;

import org.apache.commons.lang3.EnumUtils;

public enum APIAvatarType {
  AVATAR_TYPE_DEFAULT("AVATAR"),
  AVATAR_TYPE_MEDLEY("AVATAR_MEDLEY"),
  AVATAR_TYPE_WITH_CHARACTERS("AVATAR_WITH_CHARACTERS");

  private final String imageServiceName;

  APIAvatarType(String imageServiceName) {
    this.imageServiceName = imageServiceName;
  }

  public String getImageServiceName() {
    return this.imageServiceName;
  }

  public static APIAvatarType from(String name) {
    if (EnumUtils.isValidEnum(APIAvatarType.class, name)) {
      return APIAvatarType.valueOf(name);
    }

    return null;
  }
}
