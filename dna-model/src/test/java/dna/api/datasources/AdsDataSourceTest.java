package dna.api.datasources;

import static com.netflix.adsnativeadarbiter.protogen.ImageFormat.FORMAT_ASTC;
import static com.netflix.adsnativeadarbiter.protogen.LinkFormat.HTTP;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.params.provider.Arguments.argumentSet;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.netflix.adsnativeadarbiter.protogen.AdsNativeAdArbiterServiceGrpc.AdsNativeAdArbiterServiceStub;
import com.netflix.adsnativeadarbiter.protogen.DisplayAdReply;
import com.netflix.adsnativeadarbiter.protogen.DisplayAdRequest;
import com.netflix.adsnativeadarbiter.protogen.ImageDimensionCriteria;
import com.netflix.api.platform.MockPropertyRepositoryConfig;
import com.netflix.demograph.Context;
import dna.api.service.model.DisplayAdImageRecipe;
import dna.api.service.model.DisplayAdImageRecipeFormat;
import dna.api.service.model.DisplayAdOptions;
import io.grpc.stub.StreamObserver;
import java.util.List;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.FieldSource;

class AdsDataSourceTest {

  @BeforeAll
  static void beforeClass() {
    MockPropertyRepositoryConfig config = new MockPropertyRepositoryConfig();
    config.init();
  }

  @ParameterizedTest
  @FieldSource
  void displayAdDataSource(DisplayAdOptions opts, DisplayAdRequest expectedRequest) {
    // setup
    var mockReply = DisplayAdReply.newBuilder().setImageUrl("doesnt-matter").build();
    var stub = mock(AdsNativeAdArbiterServiceStub.class);
    doAnswer(
            args -> {
              StreamObserver<DisplayAdReply> observer = args.getArgument(1);
              observer.onNext(mockReply);
              observer.onCompleted();
              return null;
            })
        .when(stub)
        .requestDisplayAd(any(), any());

    var ads = new AdsDataSource(stub);
    var datasource = ads.displayAd();
    assertNotNull(datasource);
    var ctx = Context.builder().withAttribute("foo", "bar").build();

    // test
    var stage = datasource.lookup(ctx, opts);
    var future = stage.toCompletableFuture();
    var reply = future.join();

    // assert
    assertEquals(mockReply, reply);
    verify(stub).requestDisplayAd(eq(expectedRequest), any());
  }

  private static final int WIDTH = 100;
  private static final int HEIGHT = 25;
  private static final ImageDimensionCriteria CRITERIA =
      ImageDimensionCriteria.newBuilder().setWidth(WIDTH).setHeight(HEIGHT).build();

  private static DisplayAdImageRecipe newRecipe() {
    return new DisplayAdImageRecipe().setWidth(WIDTH).setHeight(HEIGHT);
  }

  static final List<Arguments> displayAdDataSource =
      List.of(
          argumentSet(
              "calls ads native arbiter",
              new DisplayAdOptions().setImageRecipe(newRecipe()),
              DisplayAdRequest.newBuilder().setImageDimensionCriteria(CRITERIA).build()),
          argumentSet(
              "calls with playback context id",
              new DisplayAdOptions()
                  .setPlaybackContextID("some-pbc-id")
                  .setImageRecipe(newRecipe()),
              DisplayAdRequest.newBuilder()
                  .setPbcId("some-pbc-id")
                  .setImageDimensionCriteria(CRITERIA)
                  .build()),
          argumentSet(
              "calls with image format",
              new DisplayAdOptions()
                  .setImageRecipe(newRecipe().setFormat(DisplayAdImageRecipeFormat.ASTC)),
              DisplayAdRequest.newBuilder()
                  .setImageDimensionCriteria(CRITERIA)
                  .setImageFormat(FORMAT_ASTC)
                  .build()),
          argumentSet(
              "calls with HTTP option",
              new DisplayAdOptions().setImageRecipe(newRecipe().setUseHTTP(true)),
              DisplayAdRequest.newBuilder()
                  .setImageDimensionCriteria(CRITERIA)
                  .setLinkFormat(HTTP)
                  .build()));
}
