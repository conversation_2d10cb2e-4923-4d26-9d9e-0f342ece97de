package com.netflix.httputil.service;

import jakarta.servlet.ServletContextEvent;
import jakarta.servlet.ServletContextListener;

public class EpicInstaller implements ServletContextListener {

  // Public constructor is required by servlet spec
  public EpicInstaller() {}

  // -------------------------------------------------------
  // ServletContextListener implementation
  // -------------------------------------------------------
  public void contextInitialized(ServletContextEvent sce) {
    // nada!
  }

  public void contextDestroyed(ServletContextEvent sce) {}
}
