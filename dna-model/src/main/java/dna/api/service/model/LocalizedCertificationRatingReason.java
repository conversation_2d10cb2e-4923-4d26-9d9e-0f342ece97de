package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class LocalizedCertificationRatingReason {
  /** @dotnext com.netflix.api.service.video.APIMovieRatingReason#getLocalizedReasonDescription */
  private String description;

  /** @dotnext com.netflix.api.service.video.APIMovieRatingReason#getLocalizedReasonLevel */
  private String level;

  public String getDescription() {
    return description;
  }

  public String getLevel() {
    return level;
  }

  public LocalizedCertificationRatingReason setDescription(String description) {
    this.description = description;
    return this;
  }

  public LocalizedCertificationRatingReason setLevel(String level) {
    this.level = level;
    return this;
  }
}
