package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

/**
 * implementing only members required by TVUI and iOS at this point. future use-cases should be
 * evaluated and added as needed
 *
 * @dotnext com.netflix.api.service.video.APIGenre#APIGenre
 */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class Genre implements GraphData {
  public Long genreId;

  public String name;

  public String navigationMenuName;

  public Atom<List<CertificationRating>> galleryCertifications;

  public Boolean hasNonMemberType;

  public Atom<List<String>> types;

  public String unifiedEntityId;

  public Map<String, Map<String, AnnotatedGenre>> subgenres;

  public Map<String, AnnotatedList> personalizedVideos;

  public Map<String, Map<String, Atom<GenreAttribute>>> attributes;

  public Map<String, Atom<Image>> artworks;

  public Map<String, Atom<SeoCollectionData>> seoData;

  public UnifiedEntity unifiedEntity;

  public Long getGenreId() {
    return genreId;
  }

  public String getName() {
    return name;
  }

  public String getNavigationMenuName() {
    return navigationMenuName;
  }

  public Atom<List<CertificationRating>> getGalleryCertifications() {
    return galleryCertifications;
  }

  public Boolean getHasNonMemberType() {
    return hasNonMemberType;
  }

  public Atom<List<String>> getTypes() {
    return types;
  }

  public String getUnifiedEntityId() {
    return unifiedEntityId;
  }

  public Genre setGenreId(Long genreId) {
    this.genreId = genreId;
    return this;
  }

  public Genre setName(String name) {
    this.name = name;
    return this;
  }

  public Genre setNavigationMenuName(String navigationMenuName) {
    this.navigationMenuName = navigationMenuName;
    return this;
  }

  public Genre setGalleryCertifications(Atom<List<CertificationRating>> galleryCertifications) {
    this.galleryCertifications = galleryCertifications;
    return this;
  }

  public Genre setHasNonMemberType(Boolean hasNonMemberType) {
    this.hasNonMemberType = hasNonMemberType;
    return this;
  }

  public Genre setTypes(Atom<List<String>> types) {
    this.types = types;
    return this;
  }

  public Genre setUnifiedEntityId(String unifiedEntityId) {
    this.unifiedEntityId = unifiedEntityId;
    return this;
  }
}
