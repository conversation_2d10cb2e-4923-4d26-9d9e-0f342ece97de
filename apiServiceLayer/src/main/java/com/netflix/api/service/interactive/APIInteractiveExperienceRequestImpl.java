package com.netflix.api.service.interactive;

import com.netflix.api.service.interactive.APIInteractiveExperienceService.APIInteractiveExperiencePlatform;
import com.netflix.api.service.interactive.APIInteractiveExperienceService.APIInteractiveExperienceRequest;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;

public class APIInteractiveExperienceRequestImpl implements APIInteractiveExperienceRequest {
  private String serviceVersion;

  // platforms are stable
  private APIInteractiveExperiencePlatform platform;

  private int uiSpecVersion;

  // experiences might change more often
  private Set<String> supportedExperiences;

  // these do not exactly match NQ headers
  private String appSdkVersion;
  private String uiVersion;
  private String osVersion;

  private Integer gmtOffsetMs;
  private String timeZoneId;

  @Override
  public APIInteractiveExperiencePlatform getPlatform() {
    return platform;
  }

  @Override
  public APIInteractiveExperienceRequest setPlatform(APIInteractiveExperiencePlatform platform) {
    this.platform = platform;
    return this;
  }

  @Override
  public int getUiSpecVersion() {
    return uiSpecVersion;
  }

  @Override
  public APIInteractiveExperienceRequest setUiSpecVersion(int uiSpecVersion) {
    this.uiSpecVersion = uiSpecVersion;
    return this;
  }

  @Override
  public Set<String> getSupportedExperiences() {
    return supportedExperiences;
  }

  @Override
  public APIInteractiveExperienceRequest setSupportedExperiences(Set<String> supportedExperiences) {
    this.supportedExperiences = supportedExperiences;
    return this;
  }

  @Override
  public String getAppSdkVersion() {
    return appSdkVersion;
  }

  @Override
  public APIInteractiveExperienceRequest setAppSdkVersion(String appSdkVersion) {
    this.appSdkVersion = appSdkVersion;
    return this;
  }

  @Override
  public String getUiVersion() {
    return uiVersion;
  }

  @Override
  public APIInteractiveExperienceRequest setUiVersion(String uiVersion) {
    this.uiVersion = uiVersion;
    return this;
  }

  @Override
  public String getOsVersion() {
    return osVersion;
  }

  @Override
  public APIInteractiveExperienceRequest setOsVersion(String osVersion) {
    this.osVersion = osVersion;
    return this;
  }

  @Override
  public Integer getGmtOffsetMs() {
    return gmtOffsetMs;
  }

  @Override
  public APIInteractiveExperienceRequest setGmtOffsetMs(Integer gmtOffsetMs) {
    this.gmtOffsetMs = gmtOffsetMs;
    return this;
  }

  @Override
  public String getTimeZoneId() {
    return timeZoneId;
  }

  @Override
  public APIInteractiveExperienceRequest setTimeZoneId(String timeZoneId) {
    this.timeZoneId = timeZoneId;
    return this;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    APIInteractiveExperienceRequestImpl that = (APIInteractiveExperienceRequestImpl) o;
    return uiSpecVersion == that.uiSpecVersion
        && Objects.equals(serviceVersion, that.serviceVersion)
        && Objects.equals(platform, that.platform)
        && Objects.equals(supportedExperiences, that.supportedExperiences)
        && Objects.equals(appSdkVersion, that.appSdkVersion)
        && Objects.equals(uiVersion, that.uiVersion)
        && Objects.equals(osVersion, that.osVersion)
        && Objects.equals(gmtOffsetMs, that.gmtOffsetMs)
        && Objects.equals(timeZoneId, that.timeZoneId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(
        serviceVersion,
        platform,
        uiSpecVersion,
        supportedExperiences,
        appSdkVersion,
        uiVersion,
        osVersion,
        gmtOffsetMs,
        timeZoneId);
  }

  @Override
  public String toString() {
    return new StringJoiner(
            ", ", APIInteractiveExperienceRequestImpl.class.getSimpleName() + "[", "]")
        .add("serviceVersion='" + serviceVersion + "'")
        .add("platform=" + platform)
        .add("uiSpecVersion=" + uiSpecVersion)
        .add("supportedExperiences=" + supportedExperiences)
        .add("appSdkVersion='" + appSdkVersion + "'")
        .add("uiVersion='" + uiVersion + "'")
        .add("osVersion='" + osVersion + "'")
        .add("gmtOffsetMs=" + gmtOffsetMs)
        .add("timeZoneId='" + timeZoneId + "'")
        .toString();
  }
}
