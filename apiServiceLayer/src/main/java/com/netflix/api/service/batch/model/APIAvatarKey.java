package com.netflix.api.service.batch.model;

import java.util.Objects;

public class APIAvatarKey {

  private final String key;
  private final boolean kids;
  private final String locale;

  private APIAvatarKey(String key, boolean kids, String locale) {
    this.key = key;
    this.kids = kids;
    this.locale = locale;
  }

  public String getKey() {
    return key;
  }

  public boolean isKids() {
    return kids;
  }

  public String getLocale() {
    return locale;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIAvatarKey that)) {
      return false;
    }
    return isKids() == that.isKids()
        && Objects.equals(getKey(), that.getKey())
        && Objects.equals(getLocale(), that.getLocale());
  }

  @Override
  public int hashCode() {
    return Objects.hash(getKey(), isKids());
  }

  public static APIAvatarKey of(String key) {
    return new APIAvatarKey(key, false, null);
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {

    private String key;
    private boolean kids;
    private String locale;

    private Builder() {}

    public Builder key(String key) {
      this.key = key;
      return this;
    }

    public Builder kids(boolean kids) {
      this.kids = kids;
      return this;
    }

    public Builder locale(String locale) {
      this.locale = locale;
      return this;
    }

    public APIAvatarKey build() {
      if (key == null) {
        throw new IllegalArgumentException("Must provide Avatar Key");
      }
      return new APIAvatarKey(key, kids, locale);
    }

    public String toString() {
      return "APIAvatarKey.Builder(key="
          + this.key
          + ", kids="
          + this.kids
          + ", locale="
          + this.locale
          + ")";
    }
  }
}
