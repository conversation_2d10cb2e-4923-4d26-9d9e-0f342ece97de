package com.netflix.api.service.profiles;

import com.netflix.api.service.batch.model.APIAnnotated;
import com.netflix.api.service.video.APIImage;

public interface APIProfileIcon extends APIAnnotated {

  /**
   * @return the id for this image
   */
  String getId();

  /**
   * @return the url for this image
   */
  String getUrl();

  /**
   * @return true if in the default icon set, false otherwise
   */
  boolean isInDefaultSet();

  /**
   * @return the image object
   */
  APIImage getImage();
}
