package com.netflix.api.service.identity;

import com.netflix.api.service.account.APIPlan;

/**
 * A representation of a user's subscription plan
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/********/APIUser.class.png"
 * alt="APIUser class diagram">
 */
public interface APIPlanInfo {

  /**
   * Indicates whether a user is on a plan that allows streaming in HD
   *
   * @deprecated use {@link APIPlan#getVideoQuality()}
   * @return {@code} true if allowed, {@code} false otherwise
   */
  @Deprecated
  boolean isHDEnabled();

  /**
   * Indicates whether a user is on a plan that allows streaming in UHD
   *
   * @deprecated use {@link APIPlan#getVideoQuality()}
   * @return {@code} true if allowed, {@code} false otherwise
   */
  @Deprecated
  boolean isUHDEnabled();
}
