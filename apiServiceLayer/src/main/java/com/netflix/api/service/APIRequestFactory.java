package com.netflix.api.service;

import com.google.inject.Provider;
import com.netflix.api.service.identity.APIUser;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public interface APIRequestFactory extends Provider<APIRequest> {

  APIRequest newRequestFromRequest(
      HttpServletRequest request,
      HttpServletResponse response,
      String endpointURI,
      String endpointGroup);

  APIRequest newRequestWithUser(APIRequest apiRequest, APIUser user);
}
