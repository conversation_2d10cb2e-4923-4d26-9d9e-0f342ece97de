package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PushMessage {
  private Boolean isSecure;

  private String notificationGuid;

  private String payload;

  private String senderApp;

  /**
   * Sends message asynchronously. Response will not contain any details on whether or not the
   * request was successful at the benefit of lower latency.
   *
   * <p>This value is ignored when calling `sendDirect` route.
   */
  private Boolean async;

  public Boolean getIsSecure() {
    return isSecure;
  }

  public String getNotificationGuid() {
    return notificationGuid;
  }

  public String getPayload() {
    return payload;
  }

  public String getSenderApp() {
    return senderApp;
  }

  public Boolean getAsync() {
    return async;
  }

  public PushMessage setIsSecure(Boolean isSecure) {
    this.isSecure = isSecure;
    return this;
  }

  public PushMessage setNotificationGuid(String notificationGuid) {
    this.notificationGuid = notificationGuid;
    return this;
  }

  public PushMessage setPayload(String payload) {
    this.payload = payload;
    return this;
  }

  public PushMessage setSenderApp(String senderApp) {
    this.senderApp = senderApp;
    return this;
  }

  public PushMessage setAsync(Boolean async) {
    this.async = async;
    return this;
  }
}
