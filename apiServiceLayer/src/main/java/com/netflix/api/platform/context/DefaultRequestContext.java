package com.netflix.api.platform.context;

import com.netflix.server.context.CurrentRequestContext;
import com.netflix.type.DeviceType;
import com.netflix.type.ISOCountry;

public class DefaultRequestContext extends AbstractImmutableRequestContext
    implements ImmutableRequestContext {

  private static final DefaultRequestContext instance = new DefaultRequestContext();

  public static DefaultRequestContext getInstance() {
    return instance;
  }

  private DefaultRequestContext() {}

  @Override
  public String getRequestId() {
    return CurrentRequestContext.get().getRequestId();
  }

  @Override
  public ISOCountry getCountry() {
    return CurrentRequestContext.get().getCountry();
  }

  @Override
  public String getLocale() {
    return CurrentRequestContext.get().getLocale();
  }

  @Override
  public String getLocaleList() {
    return CurrentRequestContext.get().getLocaleList();
  }

  @Override
  public String getDeviceId() {
    return CurrentRequestContext.get().getDeviceId();
  }

  @Override
  public DeviceType getDeviceType() {
    return CurrentRequestContext.get().getDeviceType();
  }
}
