package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PulseImage {
  private String url;

  private Integer width;

  private Integer height;

  public String getUrl() {
    return url;
  }

  public Integer getWidth() {
    return width;
  }

  public Integer getHeight() {
    return height;
  }

  public PulseImage setUrl(String url) {
    this.url = url;
    return this;
  }

  public PulseImage setWidth(Integer width) {
    this.width = width;
    return this;
  }

  public PulseImage setHeight(Integer height) {
    this.height = height;
    return this;
  }
}
