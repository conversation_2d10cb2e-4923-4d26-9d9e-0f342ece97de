package dna.api.datasources.bookmark;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.netflix.api.service.identity.APIUser;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Test;

class BookmarkDataSourceBatcherTest {

  @Test
  void byProfileGUID_returnsMapOfProfileGUIDsAndVideosRequestedByThatProfileGUID() {
    var mockUser = mock(APIUser.class);
    when(mockUser.getCustomerGUID()).thenReturn("profile-3-guid");

    var batcher = new BookmarkDataSourceBatcher();
    Map<String, Set<Integer>> actual =
        batcher.byProfileGUID(
            Set.of(
                new BookmarkWithOptionsRequest(1, null),
                new BookmarkWithOptionsRequest(101, "    "),
                new BookmarkWithOptionsRequest(1, "profile-1-guid"),
                new BookmarkWithOptionsRequest(2, "profile-1-guid"),
                new BookmarkWithOptionsRequest(3, "profile-1-guid"),
                new BookmarkWithOptionsRequest(1, "profile-2-guid"),
                new BookmarkWithOptionsRequest(3, "profile-2-guid"),
                new BookmarkWithOptionsRequest(3, "profile-3-guid"),
                new BookmarkWithOptionsRequest(1, "profile-4-guid"),
                new BookmarkWithOptionsRequest(3, "profile-4-guid")));

    assertNotNull(actual);
    assertEquals(4, actual.size());

    // confirm all profile guids are in the map
    assertTrue(actual.containsKey("profile-1-guid"));
    assertTrue(actual.containsKey("profile-2-guid"));
    assertTrue(actual.containsKey("profile-3-guid"));
    assertTrue(actual.containsKey("profile-4-guid"));

    // confirm each profile guid has the correct video list
    Set<Integer> profile1Videos = actual.get("profile-1-guid");
    assertNotNull(profile1Videos);
    assertEquals(3, profile1Videos.size());
    assertTrue(profile1Videos.contains(1));
    assertTrue(profile1Videos.contains(2));
    assertTrue(profile1Videos.contains(3));

    Set<Integer> profile2Videos = actual.get("profile-2-guid");
    assertNotNull(profile2Videos);
    assertEquals(2, profile2Videos.size());
    assertTrue(profile2Videos.contains(1));
    assertTrue(profile2Videos.contains(3));

    Set<Integer> profile3Videos = actual.get("profile-3-guid");
    assertNotNull(profile3Videos);
    assertEquals(1, profile3Videos.size());
    assertTrue(profile3Videos.contains(3));

    Set<Integer> profile4Videos = actual.get("profile-4-guid");
    assertNotNull(profile4Videos);
    assertEquals(2, profile4Videos.size());
    assertTrue(profile4Videos.contains(1));
    assertTrue(profile4Videos.contains(3));
  }
}
