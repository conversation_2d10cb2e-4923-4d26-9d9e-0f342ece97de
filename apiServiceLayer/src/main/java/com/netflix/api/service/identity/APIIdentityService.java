package com.netflix.api.service.identity;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Set;
import rx.Observable;

/**
 * Service for user authentication and metadata retrieval.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/********/APIUser.class.png"
 * alt="Class Diagram: APIUser">
 */
public interface APIIdentityService {

  /**
   * Checks to see if the entered PIN matches the account PIN of the given APIUser. This also
   * assumes that the client is not capable of handling age verification and returns false if a user
   * requires age verification in the country, but has not been age verified yet. Do not use. This
   * API has been retrofitted to support parental controls on legacy devices.
   *
   * @deprecated Use {@link #isR21PinValid(String, APIUser)}
   * @param userEnteredPin PIN entered into form in UI
   * @param user currently logged in APIUser
   * @return true if user has been age verified and pin matches, else false
   */
  @Deprecated
  Observable<Boolean> isUserPinValid(final String userEnteredPin, final APIUser user);

  /**
   * This method retains existing behavior of pin validation, while isUserPinValid will be modified
   * to additionally validate against profileLockPin as well for supporting parental controls on
   * legacy devices. Checks to see if the entered PIN matches the account PIN of the given APIUser.
   * This also assumes that the client is not capable of handling age verification and returns false
   * if a user requires age verification in the country, but has not been age verified yet.
   *
   * @param userEnteredPin PIN entered into form in UI
   * @param user currently logged in APIUser
   * @return true if user has been age verified and pin matches, else false
   */
  Observable<Boolean> isR21PinValid(final String userEnteredPin, final APIUser user);

  /**
   * Checks to see if the entered PIN matches the profile lock PIN of the given APIUser.
   *
   * @param userEnteredPin PIN entered into form in UI
   * @param user currently logged in APIUser
   * @return true if user pin matches, else false
   */
  Observable<Boolean> isProfileLockPinValid(final String userEnteredPin, final APIUser user);

  /**
   * /** Checks to see if the entered PIN matches the account PIN of the given APIUser. If client is
   * age verification capable, then PIN match suffices, if not, then this method returns true only
   * if user's age has been verified AND pin matches. Do not use. This API has been retrofitted to
   * support parental controls on legacy devices.
   *
   * @deprecated Use {@link #isR21PinValid(String, APIUser, boolean)}
   * @param userEnteredPin PIN entered into form in UI
   * @param user currently logged in APIUser
   * @return true if (isClientAgeVerificationCapable is true or user has been age verified) and pin
   *     matches, else false
   */
  @Deprecated
  Observable<Boolean> isUserPinValid(
      final String userEnteredPin,
      final APIUser user,
      final boolean isClientAgeVerificationCapable);

  /**
   * This method retains existing behavior of pin validation, while isUserPinValid will be modified
   * to additionally validate against profileLockPin as well for supporting parental controls on
   * legacy devices. Checks to see if the entered PIN matches the account PIN of the given APIUser.
   * If client is age verification capable, then PIN match suffices, if not, then this method
   * returns true only if user's age has been verified AND pin matches.
   *
   * @param userEnteredPin PIN entered into form in UI
   * @param user currently logged in APIUser
   * @return true if (isClientAgeVerificationCapable is true or user has been age verified) and pin
   *     matches, else false
   */
  Observable<Boolean> isR21PinValid(
      final String userEnteredPin,
      final APIUser user,
      final boolean isClientAgeVerificationCapable);

  APIUser authenticateRequest(
      HttpServletRequest request,
      HttpServletResponse response,
      boolean needSecureCookies,
      boolean userAuthRequired)
      throws APIAuthorizationException;

  /**
   * Creates a short lived autologin token which can be used to authenticate a user in lieu of
   * email/password or any other credentials. This is typically used for password reset cases but
   * can be leveraged for other short term authentication use cases.
   *
   * @deprecated use {@link #createAutoLoginToken(Set)}
   * @return a string representing the autologin token or an Observable with an {@link
   *     APIAuthorizationException} if there was a problem in the request
   */
  @Deprecated
  Observable<String> createAutoLoginToken();

  /**
   * Creates a short lived autologin token which can be used to authenticate a user in lieu of
   * email/password or any other credentials. This is typically used for password reset cases but
   * can be leveraged for other short term authentication use cases. This API allows the tokens to
   * be scoped. If you are working on a new use-case and unsure what scope you should use, contact
   * PEAS team (#peas-<NAME_EMAIL>)
   *
   * @param tokenScopes The scopes that the token should have. These limit the amount of access that
   *     a token has.
   * @return a string representing the autologin token or an Observable with an {@link
   *     APIAuthorizationException} if there was a problem in the request
   */
  Observable<String> createAutoLoginToken(Set<APIAuthTokenScope> tokenScopes);

  /**
   * Begin the process of building an {@code UpdateBuilder}.
   *
   * <p>In API.Next there are some instances of the Builder pattern that require an explicit build()
   * call, like the update builder, which is part of {@code APIIdentityService}. You create it with
   * {@link #getUpdateBuilder}, modify it with a variety of methods, and then call {@link
   * APIIdentityService.UpdateBuilder#build} to get the {@link APIUserHolder} object that your
   * custom-crafted update builder creates.
   *
   * @throws APIAuthorizationException if the credentials in the request are not valid
   * @see <a
   *     href="https://confluence.netflix.com/display/API/3.+How+to+write+API.Next+scripts+in+Groovy#3.HowtowriteAPI.NextscriptsinGroovy-Buildingspecializedmethodsfromthosethatusethe%E2%80%9Cbuilder%E2%80%9Dpattern">API.Next
   *     Programmer's Guide: Building specialized methods from those that use the "builder"
   *     pattern</a>
   */
  UpdateBuilder getUpdateBuilder() throws APIAuthorizationException;

  /**
   * FIXME FIXME FIXME
   *
   * @see <a
   *     href="https://confluence.netflix.com/display/API/3.+How+to+write+API.Next+scripts+in+Groovy#3.HowtowriteAPI.NextscriptsinGroovy-Buildingspecializedmethodsfromthosethatusethe%E2%80%9Cbuilder%E2%80%9Dpattern">API.Next
   *     Programmer's Guide: Building specialized methods from those that use the "builder"
   *     pattern</a>
   */
  interface UpdateBuilder {

    /**
     * @deprecated email updates are no longer support, use dynecom to change emails
     */
    @Deprecated
    UpdateBuilder setEmail(String email);

    /**
     * FIXME FIXME FIXME.
     *
     * @param signOutOfAllDevices this will break all associations and deactivates all devices
     * @return the same {@code UpdateBuilder} modified to FIXME FIXME FIXME
     */
    UpdateBuilder setSignOutOfAllDevices(boolean signOutOfAllDevices);

    /**
     * @deprecated password updates are no longer support, use dynecom to change password
     */
    @Deprecated
    UpdateBuilder setNewPassword(String newPassword);

    /**
     * @deprecated password updates are no longer support, use dynecom to change password
     */
    @Deprecated
    UpdateBuilder setCurrentPassword(String currentPassword);

    /**
     * Updates the pin setting on this account, the state of this value can be access via {@link
     * APIUser#getPin()}.
     *
     * @param pin the value must be 4 numeric characters, set this to {@code null} to unset the
     *     value
     * @return the same {@code UpdateBuilder} modified with an updated pin value
     */
    UpdateBuilder setPin(String pin);

    /**
     * Updates whether or not pin protection is enabled for this account
     *
     * @param enabled true if pin protection is required, false if it is not
     * @return the same {@code UpdateBuilder} modified with an updated pin enabled setting
     */
    UpdateBuilder setPinEnabled(boolean enabled);

    /**
     * Updates age/adult verification for this account
     *
     * @param verified true if the account is verified and false otherwise
     * @return the same {@code UpdateBuilder} modified with an updated adult verified setting
     */
    UpdateBuilder setAdultVerified(boolean verified);

    /**
     * Invoke your constructed update builder and get the {@link APIUserHolder} it creates.
     *
     * @throws APIRegistrationException if there is an issue with any of the values in the builder
     * @return the {@link APIUserHolder} object that your customized update builder creates
     */
    APIUserHolder build() throws APIRegistrationException;
  }

  /**
   * Returns whether the user's account has a password set. Some signup flows (e.g. Comcast) can
   * create accounts without a password.
   *
   * @param user Netflix user whose account will be checked
   * @return an observable that emits a single {@link Boolean#TRUE} if the account has a password
   *     set, else {@link Boolean#FALSE}
   */
  Observable<Boolean> accountHasPwd(APIUser user);
}
