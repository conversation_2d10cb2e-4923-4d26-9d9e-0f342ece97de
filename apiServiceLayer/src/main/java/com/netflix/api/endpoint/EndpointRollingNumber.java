package com.netflix.api.endpoint;

import com.netflix.api.platform.stats.RollingNumber;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;

public class EndpointRollingNumber extends RollingNumber {

  public EndpointRollingNumber(Property<Integer> timeInMilliseconds) {
    super(
        timeInMilliseconds,
        PropertyRepositoryHolder.getPropertyRepository()
            .get("api.endpoint.counter.defaultBuckets", Integer.class)
            .orElse(10));
  }

  public enum EndpointRollingNumberType implements Type {
    SCRIPT_SUCCESS,
    SCRIPT_401,
    SCRIPT_500,
    THREAD_POOL_REJECTED,
    REQUEST_THREAD_POOL_REJECTED,
    THREAD_EXECUTION,
    THREAD_MAX_ACTIVE,
    THREAD_TIME_OUT,
    COMMAND_STARTED,
    COMMAND_SUCCESS,
    COMMAND_CANCELLED,
    COMMAND_REJECTED,
    COMMAND_TIMEOUT,
    COMMAND_FAILURE
  }

  @Override
  public Type[] getAllTypes() {
    return EndpointRollingNumberType.values();
  }
}
