package com.netflix.api.platform.stats;

import com.netflix.api.platform.http.APIAsyncFilterResponseHelper;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Capture Tracer/Counter statistics for all request/responses to the app.
 *
 * <p>This is the central location for all request/response metrics/logging/stats so that we have a
 * clear picture of the entire application and all requests that flow through either RESTlet or
 * API.Next paths.
 *
 * <p>http://jira.netflix.com/browse/API-5137 Synchronize APIStats and StatsFilter
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE + 1) // This filter should run even before throttling
public class StatsFilter implements Filter {
  private static final Logger logger = LoggerFactory.getLogger(StatsFilter.class);

  /* allow turning off stats completely in case they are thought to be causing problems */
  private static final Property<Boolean> STATS_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.stats-filter.enabled", Boolean.class)
          .orElse(true);

  private static final Property<Boolean> ALLOW_EMPTY_PATH =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.stats-filter.allow.empty.path", Boolean.class)
          .orElse(true);

  // don't gather stats for healthcheck and root url requests by default
  private static final Property<List<String>> STATS_DENYLIST =
      PropertyRepositoryHolder.getPropertyRepository()
          .getList("api.stats-filter.uri.denylist", String.class)
          .orElse(List.of("/healthcheck", "/"));

  private final APIStats apiStats;

  @Autowired
  public StatsFilter(APIStats stats) {
    this.apiStats = stats;
  }

  @Override
  public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
      throws IOException, ServletException {
    final HttpServletRequest req = (HttpServletRequest) request;
    final HttpServletResponse res = (HttpServletResponse) response;
    String uri = getCleanUrlFromRequest(req);
    int statusCode = getStatusCodeFromResponse(res);
    if (gatherStats(uri)) {
      // capture stats
      long startTime = System.currentTimeMillis();
      try {
        // perform real work
        chain.doFilter(request, response);

        // log metrics that cannot be enqueued in the finally clause
        apiStats.emitDeviceRetirementMetrics(statusCode, req, CurrentMicrocontext.get());
      } finally {
        APIAsyncFilterResponseHelper.enqueueAfterRequestFinally(
            request,
            error -> {
              // the total execution time as a response header for per-request debugging
              ((HttpServletResponse) response)
                  .setHeader(
                      "X-Netflix.execution-time",
                      String.valueOf((System.currentTimeMillis() - startTime)));
            });
      }
    } else {
      // no stats, just forward
      chain.doFilter(request, response);
    }
  }

  private boolean gatherStats(String uri) {
    if (!STATS_ENABLED.get()) {
      return false;
    }
    // optionally ignore empty path
    if (!ALLOW_EMPTY_PATH.get() && uri.isEmpty()) {
      return false;
    }
    // ignore uri's configured to be ignored
    return !STATS_DENYLIST.get().contains(uri);
  }

  private static int getStatusCodeFromResponse(HttpServletResponse response) {
    int statusCode = response.getStatus();
    if (statusCode == -1) {
      // this means it was never set, so we'll default to 200 since we know that's what we default
      // to
      // http://jira/browse/API-4639 StatsFilter: Status -1 should be aliased to 200
      // I'm not pushing this logic into the APIHttpServletResponse.getStatus() method so that we
      // can know if it
      // was set or not using the -1
      statusCode = 200;
    }
    return statusCode;
  }

  private static String getCleanUrlFromRequest(HttpServletRequest request) {
    String url = "NULL";
    try {
      url = request.getRequestURI();
      if (url != null) {
        url = getCleanPath(url);
      }
    } catch (Exception e) {
      logger.warn("Unable to get clean URL={}", url);
      url = "Unknown";
    }
    return url;
  }

  static String getCleanPath(String url) {
    if (org.apache.commons.lang3.StringUtils.isBlank(url)) {
      return "";
    }
    url = url.trim();
    try {
      String cleanUrl = url;
      if (cleanUrl.endsWith("/")) {
        cleanUrl = cleanUrl.substring(0, cleanUrl.length() - 1);
      } else if (cleanUrl.indexOf('?') > -1) {
        // this should never actually happen if the argument comes from request.getRequestURI()
        // but I'm putting this as a precaution so that if it gets used by something
        // with query parameters in it this method will still work
        cleanUrl = cleanUrl.substring(0, cleanUrl.indexOf('?'));
      }

      // strip userID so we don't flood Counters with millions of unique URLs
      String USERS = "/users/";
      int usersIndex = url.indexOf(USERS);
      if (usersIndex > -1) {
        int nextPathElement = cleanUrl.indexOf("/", usersIndex + USERS.length());
        final String substring = cleanUrl.substring(0, usersIndex + USERS.length());
        if (nextPathElement == -1) {
          cleanUrl = substring + "USERID";
        } else {
          cleanUrl = substring + "USERID" + cleanUrl.substring(nextPathElement);
        }
      }
      return cleanUrl;

    } catch (Exception e) {
      // ignore if the above logic fails
      return url;
    }
  }
}
