package com.netflix.api.platform.http.throttling;

import com.netflix.api.platform.AppClientContext;
import com.netflix.api.platform.http.APIAsyncFilterResponseHelper;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.servo.monitor.Monitors;
import com.netflix.servo.util.VisibleForTesting;
import com.netflix.spectator.api.DistributionSummary;
import com.netflix.spectator.api.Gauge;
import com.netflix.spectator.api.Registry;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.concurrent.atomic.AtomicBoolean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Throttle Requests with HTTP 503 if concurrent Request count exceeds a threshold This class is
 * also responsible for gathering metrics on concurrent HTTP requests
 */
@Component
@Order(
    Ordered.HIGHEST_PRECEDENCE
        + 2) // This filter should run as early as possible so that no unnecessary work is done, but
// after StatsFilter.
public class APIRequestThrottle implements Filter {

  private final ThrottleStats stats = new ThrottleStats();
  private final DistributionSummary concurrentRequests;
  private final Gauge serverUtilization;
  private final ConcurrentRequestThrottler concurrentRequestThrottler;

  private final Property<Boolean> enableBackgroundThrottling =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.throttle.background.enable", Boolean.class)
          .orElse(true);
  private final Property<Integer> backgroundThrottleStatus =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.throttle.background.status", Integer.class)
          .orElse(429);
  private final Property<Boolean> allowJsonThrottleMessage =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.throttle.allowJson", Boolean.class)
          .orElse(true);

  // set for the whole instance
  public static final AtomicBoolean BROWNOUT = new AtomicBoolean(false);

  // DecimalFormat is not threadsafe, and is slightly costly to construct, so cache it per-thread.
  private final ThreadLocal<DecimalFormat> headerDecimalFormat =
      ThreadLocal.withInitial(() -> new DecimalFormat("#.##"));

  public static final String UTILIZATION_HEADER_NAME = "X-Netflix.server.utilization";
  public static final String FORCE_THROTTLE_HEADER_NAME = "X-Netflix.server.force-throttle";
  private static final String CONTENT_TYPE_JSON = "application/json";

  @Autowired
  public APIRequestThrottle(
      Registry registry, ConcurrentRequestThrottler concurrentRequestThrottler) {
    this(
        concurrentRequestThrottler,
        registry.distributionSummary("api.server.inflight"),
        registry.gauge("api.server.utilization"));
  }

  @VisibleForTesting
  APIRequestThrottle(
      ConcurrentRequestThrottler concurrentRequestThrottler,
      DistributionSummary concurrentRequests,
      Gauge serverUtilization) {
    this.concurrentRequestThrottler = concurrentRequestThrottler;
    this.concurrentRequests = concurrentRequests;
    this.serverUtilization = serverUtilization;
  }

  private void sendError(
      int status, String message, HttpServletRequest request, HttpServletResponse response)
      throws IOException {
    if ((CONTENT_TYPE_JSON.equalsIgnoreCase(request.getContentType())
            || CONTENT_TYPE_JSON.equalsIgnoreCase(request.getHeader("Accept")))
        && allowJsonThrottleMessage.get()) {
      response.setStatus(status);
      response.setContentType(CONTENT_TYPE_JSON);
      response.setCharacterEncoding("UTF-8");
      response.getWriter().write(String.format("{\"error\": \"%s\"}", message));
      response.getWriter().flush();
    } else {
      response.sendError(status, message);
    }
  }

  @Override
  public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
      throws IOException, ServletException {
    HttpServletRequest request = (HttpServletRequest) req;
    HttpServletResponse response = (HttpServletResponse) res;
    // Ignore health checks
    if (skipFilter(request)) {
      chain.doFilter(request, response);
      return;
    }
    // should already be set by StatsFilter but getOrCreate just in case
    final AppClientContext appContext = AppClientContext.getOrCreate(request);
    if (appContext.isBackground()) {
      stats.concurrentBackgroundRequestCount.increment();
    }
    stats.requestReceived.increment();
    stats.concurrentRequestCount.increment();
    final int concurrentRequestCount = stats.concurrentRequestCount.intValue();
    concurrentRequests.record(concurrentRequestCount);
    double percentUtilized = percentageUtilized(concurrentRequestCount);
    serverUtilization.set(percentUtilized);
    try {
      addServerUtilizationHeader(response, percentUtilized);
      ThrottleResult result =
          concurrentRequestThrottler.shouldThrottle(
              percentUtilized, concurrentRequestCount, appContext.isBackground());
      if (result.isThrottled()
          || "true".equalsIgnoreCase(request.getHeader(FORCE_THROTTLE_HEADER_NAME))) {
        if (result.equals(ThrottleResult.BACKGROUND)) {
          stats.throttleBackgroundCount.increment();
          if (enableBackgroundThrottling.get()) {
            sendError(backgroundThrottleStatus.get(), "Too many requests", request, response);
            return;
          }
        } else {
          stats.throttleCount.increment();
          if (appContext.isBackground()) {
            sendError(backgroundThrottleStatus.get(), "Too many requests", request, response);
          } else {
            sendError(503, "Service Unavailable", request, response);
          }
          return;
        }
      }
      chain.doFilter(request, response);
    } finally {
      APIAsyncFilterResponseHelper.enqueueAfterRequestFinally(
          request,
          error -> {
            if (appContext.isBackground()) {
              stats.concurrentBackgroundRequestCount.decrement();
            }
            stats.concurrentRequestCount.decrement();
            stats.responseSent.increment();
          });
    }
  }

  public static boolean skipFilter(HttpServletRequest request) {
    String uri = request.getRequestURI();
    return uri == null
        || "/favicon.ico".equals(uri)
        || uri.contains("/admin")
        || "/healthcheck".equals(uri);
  }

  /**
   * Add a http header to the response which indicates to Zuul and other clients the current
   * percentage utilization of this server.
   *
   * <p>See https://docs.google.com/document/d/1uguoLPAh1JcMycEsxDpbM6qqtQLFdlxhrDyWvCkYhN8/edit
   */
  private void addServerUtilizationHeader(ServletResponse response, double utilizationPercent) {
    double targetPercent = concurrentRequestThrottler.getTargetPercentage();

    // Write out the percentages to 2 decimal places.
    String headerValue =
        headerDecimalFormat.get().format(utilizationPercent)
            + ", target="
            + headerDecimalFormat.get().format(targetPercent);

    HttpServletResponse httpResponse = (HttpServletResponse) response;
    httpResponse.setHeader(UTILIZATION_HEADER_NAME, headerValue);
  }

  /** Calculate the percentage of max concurrent threads currently in use. */
  private double percentageUtilized(int concurrentRequestCount) {
    double maxAllowed = concurrentRequestThrottler.getThrottleLimit();
    return (concurrentRequestCount / maxAllowed) * 100d;
  }

  @Override
  public void init(FilterConfig fConfig) throws ServletException {
    /* register a metric gauge */
    Monitors.registerObject(stats);
  }

  @Override
  public void destroy() {
    Monitors.unregisterObject(stats);
  }
}
