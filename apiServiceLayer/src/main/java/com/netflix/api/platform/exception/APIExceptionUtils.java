package com.netflix.api.platform.exception;

import com.google.common.base.Preconditions;
import com.google.common.base.Throwables;
import com.netflix.servo.DefaultMonitorRegistry;
import com.netflix.servo.monitor.Counter;
import com.netflix.servo.monitor.Monitors;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.exceptions.CompositeException;

public class APIExceptionUtils {

  private APIExceptionUtils() {}

  private static final Counter INFINITE_CAUSAL_CHAIN =
      Monitors.newCounter("com.netflix.api.infiniteCausalChain");
  private static final Counter LONG_CAUSAL_CHAIN =
      Monitors.newCounter("com.netflix.api.longCausalChain");

  static {
    DefaultMonitorRegistry.getInstance().register(INFINITE_CAUSAL_CHAIN);
    DefaultMonitorRegistry.getInstance().register(LONG_CAUSAL_CHAIN);
  }

  static final Integer MAX_CHAIN_LENGTH = 20;

  private static final Logger logger = LoggerFactory.getLogger(APIExceptionUtils.class);

  /**
   * Converts the causal chain into a collection for ease of use, does not traverse circular
   * dependencies thus making it more safe than {@link Throwables#getCausalChain(Throwable)}
   *
   * <p>It also has special knowledge of CompositeException and handles this by iterating through
   * all the composed exceptions and adding all causal chains together
   *
   * @param throwable
   * @return
   */
  public static Collection<Throwable> getCausalChain(Throwable throwable) {
    Preconditions.checkNotNull(throwable);
    Set<Throwable> causes = LinkedHashSet.newLinkedHashSet(4);

    if (throwable instanceof CompositeException e) {
      List<Throwable> roots = e.getExceptions();
      for (Throwable ex : roots) {
        causes.addAll(getCausalChain(ex));
      }
    } else {
      int i = 0;
      while (throwable != null) {
        if (i++ >= MAX_CHAIN_LENGTH) {
          logger.error("Found long chain!!!!", throwable);
          LONG_CAUSAL_CHAIN.increment();
          break;
        }
        if (!causes.add(throwable)) {
          logger.error("Found circular causal chain!!!!", throwable);
          INFINITE_CAUSAL_CHAIN.increment();
          break;
        }

        throwable = throwable.getCause();
      }
    }
    return Collections.unmodifiableCollection(causes);
  }
}
