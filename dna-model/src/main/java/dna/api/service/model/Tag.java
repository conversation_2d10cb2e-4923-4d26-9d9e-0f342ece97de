package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.video.APITag */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class Tag {
  private Integer id;

  private String displayName;

  private Boolean isDisplayable;

  private Boolean isMood;

  private TagSource source;

  public Integer getId() {
    return id;
  }

  public String getDisplayName() {
    return displayName;
  }

  public Boolean getIsDisplayable() {
    return isDisplayable;
  }

  public Boolean getIsMood() {
    return isMood;
  }

  public TagSource getSource() {
    return source;
  }

  public Tag setId(Integer id) {
    this.id = id;
    return this;
  }

  public Tag setDisplayName(String displayName) {
    this.displayName = displayName;
    return this;
  }

  public Tag setIsDisplayable(Boolean isDisplayable) {
    this.isDisplayable = isDisplayable;
    return this;
  }

  public Tag setIsMood(Boolean isMood) {
    this.isMood = isMood;
    return this;
  }

  public Tag setSource(TagSource source) {
    this.source = source;
    return this;
  }
}
