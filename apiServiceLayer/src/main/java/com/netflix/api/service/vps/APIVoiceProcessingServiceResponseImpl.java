package com.netflix.api.service.vps;

import com.netflix.vps.protogen.Command;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class APIVoiceProcessingServiceResponseImpl implements APIVoiceProcessingServiceResponse {

  private final List<APIVoiceCommand> voiceCommands;
  private final Map<String, String> additionalProperties;

  public APIVoiceProcessingServiceResponseImpl(
      List<Command> voiceCommands, Map<String, String> additionalProperties) {
    this.voiceCommands = new ArrayList<>();
    for (Command command : voiceCommands == null ? List.<Command>of() : voiceCommands) {
      this.voiceCommands.add(
          new APIVoiceCommandImpl(
              APIVoiceCommandType.valueOf(command.getType().toString()),
              command.getIntent(),
              command.getResource(),
              command.getAdditionalAttributesMap()));
    }
    this.additionalProperties = additionalProperties;
  }

  public List<APIVoiceCommand> getVoiceCommands() {
    return voiceCommands;
  }

  public Map<String, String> getAdditionalProperties() {
    return additionalProperties;
  }
}
