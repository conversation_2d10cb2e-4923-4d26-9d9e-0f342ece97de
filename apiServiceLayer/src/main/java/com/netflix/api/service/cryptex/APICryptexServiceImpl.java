package com.netflix.api.service.cryptex;

import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.platform.util.CompletionStageAdapter;
import com.netflix.api.util.Cryptex2Manager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
public class APICryptexServiceImpl implements APICryptexService {
  private final Cryptex2Manager cryptex2;

  @Autowired
  public APICryptexServiceImpl(Cryptex2Manager cryptex2) {
    this.cryptex2 = cryptex2;
  }

  @Override
  public Observable<KeyPair> generateAes128Cbc(byte[] data) {
    Observable<byte[]> result = CompletionStageAdapter.toObservable(cryptex2.deriveKeyCbc(data));
    return result.map(
        keys -> {

          // Split keys.
          final byte[] aesKey = new byte[16];
          final byte[] hmacKey = new byte[32];
          System.arraycopy(keys, 0, aesKey, 0, aesKey.length);
          System.arraycopy(keys, aesKey.length, hmacKey, 0, hmacKey.length);

          return new KeyPairImpl(aesKey, hmacKey);
        });
  }

  private static class KeyPairImpl implements KeyPair {
    private final byte[] aesKey;
    private final byte[] hmacKey;

    /**
     * @param aesKey AES-128 key.
     * @param hmacKey HMAC-SHA256 key.
     */
    public KeyPairImpl(final byte[] aesKey, final byte[] hmacKey) {
      this.aesKey = aesKey;
      this.hmacKey = hmacKey;
    }

    @Override
    public byte[] aesKey() {
      return aesKey;
    }

    @Override
    public byte[] hmacKey() {
      return hmacKey;
    }
  }
}
