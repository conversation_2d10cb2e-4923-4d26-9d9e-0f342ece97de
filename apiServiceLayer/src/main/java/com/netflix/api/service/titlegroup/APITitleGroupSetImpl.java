package com.netflix.api.service.titlegroup;

import java.util.List;

public class APITitleGroupSetImpl implements APITitleGroupSet {

  private final APITitleGroup _group;
  private final List<APITitleGroupSetMember> _members;
  private APITitleGroupSetMember _relativeToMember;

  public APITitleGroupSetImpl(APITitleGroup group, List<APITitleGroupSetMember> members) {
    this._group = group;
    this._members = members;
  }

  @Override
  public APITitleGroup getGroup() {
    return _group;
  }

  @Override
  public List<APITitleGroupSetMember> getMembers() {
    return _members;
  }

  public void setRelativeToMember(APITitleGroupSetMember member) {
    this._relativeToMember = member;
  }

  public APITitleGroupSetMember getRelativeToMember() {
    return this._relativeToMember;
  }
}
