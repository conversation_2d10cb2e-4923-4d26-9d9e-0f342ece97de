package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class ThumbVideoRating implements GraphData {
  public Atom<ThumbRatingData> rating;

  public Video video;

  public Atom<ThumbRatingData> getRating() {
    return rating;
  }

  public ThumbVideoRating setRating(Atom<ThumbRatingData> rating) {
    this.rating = rating;
    return this;
  }
}
