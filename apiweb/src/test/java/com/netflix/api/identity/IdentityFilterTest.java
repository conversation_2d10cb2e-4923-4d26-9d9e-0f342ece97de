package com.netflix.api.identity;

import static com.netflix.api.identity.IdentityFilter.OAUTH_CONSUMER_KEY;
import static com.netflix.api.identity.IdentityFilter.SWITCH_PROFILE_GUID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNotNull;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.netflix.api.adapters.MicrocontextAdapter;
import com.netflix.api.identity.IdentityFilter.ContextManager;
import com.netflix.api.identity.IdentityFilter.ProfileSwitcher;
import com.netflix.api.platform.MockPropertyRepositoryConfig;
import com.netflix.api.platform.identity.IdentityConfig;
import com.netflix.api.platform.identity.IdentityPassportResult;
import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.api.util.PassportActionHelper;
import com.netflix.api.util.eas.EASSloarClientWrapper;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.introspect.PassportIdentityFactory;
import com.netflix.passport.introspect.SingletonPassportIntrospectorFactory;
import com.netflix.passport.protobuf.DeviceAction;
import com.netflix.passport.protobuf.Source;
import com.netflix.passport.protobuf.UserAction;
import com.netflix.passport.test.TestDeviceInfo;
import com.netflix.passport.test.TestPassport;
import com.netflix.passport.test.TestUserInfo;
import com.netflix.peas.test.SpectatorAssert;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.DefaultRegistry;
import com.netflix.spectator.api.NoopRegistry;
import com.netflix.spectator.api.Registry;
import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Set;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class IdentityFilterTest {

  HttpServletRequest request;
  HttpServletResponse response;
  FilterChain chain;
  ProfileSwitcher profileSwitcher;
  ContextManager contextManager;
  SecureNetflixIdEnforcementConfig secureNetflixIdEnforcementConfig;

  private final PassportIdentityFactory identityFactory =
      new PassportIdentityFactory(new NoopRegistry());
  private Registry registry;
  private final SingletonPassportIntrospectorFactory introspectorFactory =
      new SingletonPassportIntrospectorFactory(new NoopRegistry());
  private IdentityFilter filter;

  @BeforeAll
  static void beforeClass() {
    var config = new MockPropertyRepositoryConfig();
    config.init();
  }

  private void path(String path) {
    when(request.getPathInfo()).thenReturn(path);
  }

  private void defaultPath() {
    path("/users/current");
  }

  @BeforeEach
  void before() {
    request = mock(HttpServletRequest.class);
    response = mock(HttpServletResponse.class);
    chain = mock(FilterChain.class);
    profileSwitcher = mock(ProfileSwitcher.class);
    contextManager = mock(ContextManager.class);
    secureNetflixIdEnforcementConfig = mock(SecureNetflixIdEnforcementConfig.class);

    registry = new DefaultRegistry();
    var easSloarClientWrapper = mock(EASSloarClientWrapper.class);
    var microcontextAdapter = mock(MicrocontextAdapter.class);
    filter =
        spy(
            new IdentityFilter(
                profileSwitcher,
                contextManager,
                introspectorFactory,
                identityFactory,
                registry,
                secureNetflixIdEnforcementConfig,
                easSloarClientWrapper,
                microcontextAdapter));

    var config = mock(IdentityConfig.class);
    doReturn(config).when(filter).getConfig();
    when(config.shouldProcess(any())).thenCallRealMethod();

    CurrentRequestContext.set(new RequestContext(request));
  }

  @Test
  void detectsOAuthParam() {
    when(request.getParameter(OAUTH_CONSUMER_KEY)).thenReturn("hi");
    assertTrue(filter.isOAuth(request));
  }

  @Test
  void detectsOAuthHeader() {
    when(request.getHeader("authorization")).thenReturn(String.format("%s=hi", OAUTH_CONSUMER_KEY));
    assertTrue(filter.isOAuth(request));
  }

  @Test
  void detectsOAuthAttribute() {
    when(request.getAttribute(OAUTH_CONSUMER_KEY)).thenReturn("hi");
    assertTrue(filter.isOAuth(request));
  }

  @Test
  void switchProfileRequestFailsWith500Result() {
    defaultPath();

    try {
      filter.doFilter(request, response, chain);
      verify(response, times(0)).setStatus(500);
      verify(contextManager, times(1))
          .setIdentity(any(), nullable(IdentityResult.class), any(), any());
    } catch (Exception e) {
      fail("Should not catch an exception");
    }
  }

  @Test
  void switchProfileRequestByParamSucceedsWithPassport() {
    defaultPath();
    PassportIdentity currentPi = TestPassport.createRandomTestPassport().toPassportIdentity();
    PassportIdentity switchedPi = TestPassport.createRandomTestPassport().toPassportIdentity();

    when(request.getHeader("X-Passport")).thenReturn(currentPi.getPassportAsString());
    when(request.getParameter(SWITCH_PROFILE_GUID)).thenReturn("newGuid");

    final IdentityResult<PassportIdentity> newCookiesResult =
        new IdentityPassportResult(switchedPi);
    newCookiesResult.setModifiedPassportString(switchedPi.getPassportAsString());

    when(profileSwitcher.switchTo("newGuid", currentPi.getPassportAsString()))
        .thenReturn(newCookiesResult);
    when(filter.getCurrentIdentity()).thenReturn(newCookiesResult);

    try {
      filter.doFilter(request, response, chain);
      verify(contextManager, times(1))
          .setIdentity(eq(request), eq(newCookiesResult), isNotNull(), any());
    } catch (Exception e) {
      fail(e.getMessage());
    }

    verify(response)
        .setHeader(PassportActionHelper.PASSPORT_HEADER, switchedPi.getPassportAsString());
  }

  @Test
  void validatePassportExtractionAndPassportIdentity() throws Exception {
    path("/test/passport");
    TestPassport testpassport = TestPassport.createRandomTestPassport(false, true);
    testpassport.userInfo.setCustomerId(2112L);
    testpassport.userInfo.setSource(Source.MSL);
    PassportIdentity identity = testpassport.toPassportIdentity();

    when(request.getHeader("X-Passport")).thenReturn(identity.getPassportAsString());

    filter.doFilter(request, response, chain);
    var result = ArgumentCaptor.forClass(IdentityResult.class);
    verify(contextManager, times(1)).setIdentity(eq(request), result.capture(), any(), any());
    assertEquals(2112L, (long) result.getValue().getCustomerId());
  }

  @Test
  void testPassportHasActions_EmptyPassport() {
    PassportIdentity emptyPassport = TestPassport.builder().build().toPassportIdentity();

    boolean hasActions = filter.passportHasActions(emptyPassport);

    assertFalse(hasActions);
  }

  @Test
  void testPassportHasActions_NullPassport() {
    boolean hasActions = filter.passportHasActions(null);
    assertFalse(hasActions);
  }

  @Test
  void testPassportHasActions_PassportWithUserActions() {
    UserAction userAction = UserAction.newBuilder().build();
    PassportIdentity passportWithUserAction =
        TestPassport.builder()
            .userInfo(TestUserInfo.builder().build())
            .userAction(userAction)
            .build()
            .toPassportIdentity();

    boolean hasActions = filter.passportHasActions(passportWithUserAction);

    assertTrue(hasActions);
  }

  @Test
  void testPassportHasActions_PassportWithDeviceActions() {
    DeviceAction deviceAction = DeviceAction.newBuilder().build();
    PassportIdentity passportWithDeviceAction =
        TestPassport.builder()
            .deviceInfo(TestDeviceInfo.builder().build())
            .deviceAction(deviceAction)
            .build()
            .toPassportIdentity();

    boolean hasActions = filter.passportHasActions(passportWithDeviceAction);

    assertTrue(hasActions);
  }

  @Test
  void testVdidIsExtractedFromPassport() throws Exception {
    TestPassport randomTestPassport = TestPassport.createRandomTestPassport(true, true);
    randomTestPassport.getUserInfo().setVisitorDeviceId("ABCD");
    PassportIdentity identity = randomTestPassport.toPassportIdentity();

    String passportAsString = identity.getPassportAsString();
    when(request.getHeader("X-Passport")).thenReturn(passportAsString);

    filter.doFilter(request, response, chain);

    // then
    var result = ArgumentCaptor.forClass(IdentityResult.class);
    verify(contextManager, times(1)).setIdentity(eq(request), result.capture(), any(), eq("ABCD"));
    var identityResult = result.getValue();
    assertNotNull(identityResult);
    assertInstanceOf(IdentityPassportResult.class, identityResult);
  }

  @Test
  void testDeviceInformationSourceMatches() {
    TestPassport testPassport = TestPassport.createRandomTestPassport(true, true);
    testPassport.getDeviceInfo().setSource(Source.COOKIE);
    PassportIdentity identity = testPassport.toPassportIdentity();

    boolean sourceMatches = filter.deviceInformationSourceMatches(identity, Source.COOKIE);

    assertTrue(sourceMatches, "Device information source in passport is COOKIE");
  }

  @Test
  void insecureCookiesAreBlockedByDefault() {
    when(secureNetflixIdEnforcementConfig.enabled()).thenReturn(true);
    when(secureNetflixIdEnforcementConfig.blockDefault()).thenReturn(true);
    TestPassport testPassport = TestPassport.createRandomTestPassport(true, true);
    testPassport.getUserInfo().setSource(Source.COOKIE_INSECURE);
    PassportIdentity identity = testPassport.toPassportIdentity();

    boolean blocked = filter.blockProtoHttpsInsecureCookie(identity, "/abc");

    assertTrue(blocked, "request is blocked");
    assertCookieEnforcementTag("isBlocked", "true");
    assertCookieEnforcementTag("reason", "default");
  }

  @Test
  void insecureCookiesAreAllowedByConfig() {
    when(secureNetflixIdEnforcementConfig.enabled()).thenReturn(true);
    when(secureNetflixIdEnforcementConfig.allowedDevices())
        .thenReturn(Set.of(1252)); // device allowed

    TestPassport testPassport = TestPassport.createRandomTestPassport(true, true);
    testPassport.getUserInfo().setSource(Source.COOKIE_INSECURE);
    testPassport.getDeviceInfo().setDeviceTypeId(1252);
    PassportIdentity identity = testPassport.toPassportIdentity();

    boolean blocked = filter.blockProtoHttpsInsecureCookie(identity, "/abc");

    assertFalse(blocked, "request is NOT blocked");
    assertCookieEnforcementTag("isBlocked", "false");
    assertCookieEnforcementTag("reason", "allPathsAllowed");
  }

  @Test
  void insecureCookiesAreBlockedByConfig() {
    when(secureNetflixIdEnforcementConfig.enabled()).thenReturn(true);
    when(secureNetflixIdEnforcementConfig.allowedDevices())
        .thenReturn(Set.of(1252)); // device allowed
    when(secureNetflixIdEnforcementConfig.allowedPathsForDevice(1252))
        .thenReturn(Set.of("/abc")); // path allowed

    TestPassport testPassport = TestPassport.createRandomTestPassport(true, true);
    testPassport.getUserInfo().setSource(Source.COOKIE_INSECURE);
    testPassport.getDeviceInfo().setDeviceTypeId(1252);
    PassportIdentity identity = testPassport.toPassportIdentity();

    boolean blocked = filter.blockProtoHttpsInsecureCookie(identity, "/xyz");

    assertTrue(blocked, "request is blocked");
    assertCookieEnforcementTag("isBlocked", "true");
    assertCookieEnforcementTag("reason", "pathBlocked");
  }

  @Test
  void insecureCookiesAreAllowedByPathBasedConfig() {
    when(secureNetflixIdEnforcementConfig.enabled()).thenReturn(true);
    when(secureNetflixIdEnforcementConfig.allowedDevices())
        .thenReturn(Set.of(1252)); // device allowed
    when(secureNetflixIdEnforcementConfig.allowedPathsForDevice(1252))
        .thenReturn(Set.of("/abc")); // path allowed

    TestPassport testPassport = TestPassport.createRandomTestPassport(true, true);
    testPassport.getUserInfo().setSource(Source.COOKIE_INSECURE);
    testPassport.getDeviceInfo().setDeviceTypeId(1252);
    PassportIdentity identity = testPassport.toPassportIdentity();

    boolean blocked = filter.blockProtoHttpsInsecureCookie(identity, "/abc");

    assertFalse(blocked, "request is NOT blocked");
    assertCookieEnforcementTag("isBlocked", "false");
    assertCookieEnforcementTag("reason", "pathAllowed");
  }

  @Test
  void insecureCookiesAreBlockedWhenUserBound() {
    when(secureNetflixIdEnforcementConfig.enabled()).thenReturn(true);
    when(secureNetflixIdEnforcementConfig.blockUserBoundButNoDeviceInfo()).thenReturn(true);
    TestPassport testPassport = TestPassport.createRandomTestPassport(false, true);
    testPassport.getUserInfo().setSource(Source.COOKIE_INSECURE);
    PassportIdentity identity = testPassport.toPassportIdentity();

    boolean blocked = filter.blockProtoHttpsInsecureCookie(identity, "/abc");

    assertTrue(blocked, "request is blocked");
    assertCookieEnforcementTag("isBlocked", "true");
    assertCookieEnforcementTag("reason", "userBound");
  }

  private void assertCookieEnforcementTag(String name, String value) {
    SpectatorAssert.assertTag(registry, "eas.secure.cookie.enforcement", name, value);
  }
}
