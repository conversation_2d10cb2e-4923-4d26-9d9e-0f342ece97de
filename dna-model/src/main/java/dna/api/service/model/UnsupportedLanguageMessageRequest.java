package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class UnsupportedLanguageMessageRequest {
  private String size;

  private List<String> devicePreferredLanguages;

  private List<String> deviceSupportedLanguages;

  private List<String> deviceSupportedFontGroups;

  private Boolean secure;

  public String getSize() {
    return size;
  }

  public List<String> getDevicePreferredLanguages() {
    return devicePreferredLanguages;
  }

  public List<String> getDeviceSupportedLanguages() {
    return deviceSupportedLanguages;
  }

  public List<String> getDeviceSupportedFontGroups() {
    return deviceSupportedFontGroups;
  }

  public Boolean getSecure() {
    return secure;
  }

  public UnsupportedLanguageMessageRequest setSize(String size) {
    this.size = size;
    return this;
  }

  public UnsupportedLanguageMessageRequest setDevicePreferredLanguages(
      List<String> devicePreferredLanguages) {
    this.devicePreferredLanguages = devicePreferredLanguages;
    return this;
  }

  public UnsupportedLanguageMessageRequest setDeviceSupportedLanguages(
      List<String> deviceSupportedLanguages) {
    this.deviceSupportedLanguages = deviceSupportedLanguages;
    return this;
  }

  public UnsupportedLanguageMessageRequest setDeviceSupportedFontGroups(
      List<String> deviceSupportedFontGroups) {
    this.deviceSupportedFontGroups = deviceSupportedFontGroups;
    return this;
  }

  public UnsupportedLanguageMessageRequest setSecure(Boolean secure) {
    this.secure = secure;
    return this;
  }
}
