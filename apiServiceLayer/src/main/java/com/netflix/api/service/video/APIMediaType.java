package com.netflix.api.service.video;

import com.netflix.api.annotations.ExcludeFromCoverage;

@ExcludeFromCoverage
public enum APIMediaType {
  ED("instant"),
  DD("DVD"),
  BR("Blu-ray"),
  HD("HD DVD");

  private final String formattedString;

  APIMediaType(String formattedString) {
    this.formattedString = formattedString;
  }

  @Override
  public String toString() {
    return getFormattedString();
  }

  @ExcludeFromCoverage
  public String getFormattedString() {
    return formattedString;
  }
}
