package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class Similars implements GraphData {
  public Tracker tracker;

  public Map<String, Video> videos;

  public Map<String, Map<String, Video>> videosWithContext;
}
