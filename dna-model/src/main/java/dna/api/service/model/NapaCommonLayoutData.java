package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaCommonLayoutData {
  private List<String> debugData;

  public List<String> getDebugData() {
    return debugData;
  }

  public NapaCommonLayoutData setDebugData(List<String> debugData) {
    this.debugData = debugData;
    return this;
  }
}
