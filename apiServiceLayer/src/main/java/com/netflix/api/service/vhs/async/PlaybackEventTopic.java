package com.netflix.api.service.vhs.async;

/** enum representing the active playback event topics in kafka */
public enum PlaybackEventTopic {
  PLAYBACK_EVENT_US_EAST_1("playback_event_useast1"),
  PLAYBACK_EVENT_US_EAST_2("playback_event_useast2"),
  PLAYBACK_EVENT_US_WEST_2("playback_event_uswest2"),
  PLAYBACK_EVENT_EU_WEST_1("playback_event_euwest1"),
  PLAYBACK_EVENT_SPILLOVER("playback_event_spillover"),
  PLAYBACK_EVENT_KEEP_ALIVE_US_EAST_1("playback_event_keepalive_useast1"),
  PLAYBACK_EVENT_KEEP_ALIVE_US_EAST_2("playback_event_keepalive_useast2"),
  PLAYBACK_EVENT_KEEP_ALIVE_US_WEST_2("playback_event_keepalive_uswest2"),
  PLAY<PERSON><PERSON>K_EVENT_KEEP_ALIVE_EU_WEST_1("playback_event_keepalive_euwest1"),
  PLAYBACK_EVENT_KEEP_ALIVE_SPILLOVER("playback_event_keepalive_spillover"),
  PLAYBACK_REPORT_PROBLEM_REQUEST("playback_report_problem_request"),
  PLAYBACK_HIDE_VIEWING_HISTORY_REQUEST("hide_viewinghistory_request");

  private final String name;

  PlaybackEventTopic(String name) {
    this.name = name;
  }

  public String getName() {
    return name;
  }
}
