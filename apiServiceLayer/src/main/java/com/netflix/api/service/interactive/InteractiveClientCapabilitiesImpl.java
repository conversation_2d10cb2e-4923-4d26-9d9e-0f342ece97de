package com.netflix.api.service.interactive;

import java.util.List;

public class InteractiveClientCapabilitiesImpl implements InteractiveClientCapabilities {

  private List<Integer> interactivePackageVideos;

  private List<Integer> interactiveMerchVideos;

  private String errorCode;

  private String message;

  public InteractiveClientCapabilitiesImpl() {}

  @Override
  public List<Integer> getInteractivePackageVideos() {
    return interactivePackageVideos;
  }

  public void setInteractivePackageVideos(List<Integer> interactivePackageVideos) {
    this.interactivePackageVideos = interactivePackageVideos;
  }

  @Override
  public List<Integer> getInteractiveMerchVideos() {
    return interactiveMerchVideos;
  }

  public void setInteractiveMerchVideos(List<Integer> interactiveMerchVideos) {
    this.interactiveMerchVideos = interactiveMerchVideos;
  }

  @Override
  public String getErrorCode() {
    return errorCode;
  }

  public void setErrorCode(String errorCode) {
    this.errorCode = errorCode;
  }

  @Override
  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  @Override
  public Boolean isError() {
    return errorCode != null && !errorCode.isEmpty();
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((errorCode == null) ? 0 : errorCode.hashCode());
    result =
        prime * result + ((interactiveMerchVideos == null) ? 0 : interactiveMerchVideos.hashCode());
    result =
        prime * result
            + ((interactivePackageVideos == null) ? 0 : interactivePackageVideos.hashCode());
    result = prime * result + ((message == null) ? 0 : message.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    InteractiveClientCapabilitiesImpl other = (InteractiveClientCapabilitiesImpl) obj;
    if (errorCode == null) {
      if (other.errorCode != null) return false;
    } else if (!errorCode.equals(other.errorCode)) return false;
    if (interactiveMerchVideos == null) {
      if (other.interactiveMerchVideos != null) return false;
    } else if (!interactiveMerchVideos.equals(other.interactiveMerchVideos)) return false;
    if (interactivePackageVideos == null) {
      if (other.interactivePackageVideos != null) return false;
    } else if (!interactivePackageVideos.equals(other.interactivePackageVideos)) return false;
    if (message == null) {
      if (other.message != null) return false;
    } else if (!message.equals(other.message)) return false;
    return true;
  }

  @Override
  public String toString() {
    return "InteractiveClientCapabilitiesImpl [interactivePackageVideos="
        + interactivePackageVideos
        + ", interactiveMerchVideos="
        + interactiveMerchVideos
        + ", errorCode="
        + errorCode
        + ", message="
        + message
        + "]";
  }
}
