package com.netflix.api.service.video;

import com.google.common.base.Preconditions;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.videometadata.type.SupplementalVideo;
import com.netflix.vms.provider.VideoFormatDescriptorProvider;

@EnableDeprecatedMetrics
public class APICachedSupplementalVideoImpl extends APICachedViewableImpl
    implements APISupplementalVideo {
  private final String subType;

  APICachedSupplementalVideoImpl(
      SupplementalVideo supplementalVideo,
      CompleteVideo supplementalCompleteVideo,
      APIVideoFactory videoFactory,
      USTVideoMetadataAdapter videoMetadataAdapter,
      VideoFormatDescriptorProvider videoFormatDescriptorProvider) {
    super(
        supplementalCompleteVideo,
        videoFactory,
        videoMetadataAdapter,
        videoFormatDescriptorProvider);
    this.subType = Preconditions.checkNotNull(supplementalVideo.getSubType());
  }

  @Override
  public String getSubType() {
    return subType;
  }
}
