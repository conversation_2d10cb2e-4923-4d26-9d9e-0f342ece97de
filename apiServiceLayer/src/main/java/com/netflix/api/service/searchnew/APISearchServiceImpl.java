package com.netflix.api.service.searchnew;

import static com.netflix.napa.protogen.SearchServiceGrpc.*;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.searchnew.APISearchConstants.SearchItemType;
import com.netflix.api.service.searchnew.APISearchConstants.SearchListType;
import com.netflix.api.service.video.APIVideoMetadataCacheHelper;
import com.netflix.api.util.ObservableFactory;
import com.netflix.napa.protogen.SessionStoreServiceGrpc.SessionStoreServiceBlockingStub;
import com.netflix.search.pash.napacaller.NapaCaller;
import com.netflix.search.pash.protogen.PashServiceGrpc.PashServiceStub;
import com.netflix.search.service.LegacyContext;
import com.netflix.search.service.LegacySearchRequest;
import com.netflix.search.service.LegacySearchRequestByReference;
import com.netflix.search.service.QueryType;
import com.netflix.search.service.SearchList;
import com.netflix.search.service.SearchQueryParams;
import com.netflix.search.service.SearchResult;
import com.netflix.search.service.common.exception.InvalidSearchListException;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
public class APISearchServiceImpl implements APISearchService {

  private final APIVideoMetadataCacheHelper videoMetadataCacheHelper;
  private final PashServiceStub pashServiceStub;
  private final NapaCaller napaCaller;
  private final ObservableFactory observableFactory;

  @Autowired
  public APISearchServiceImpl(
      final APIVideoMetadataCacheHelper videoMetadataCacheHelper,
      @GrpcSpringClient("pash") final PashServiceStub pashServiceStub,
      @GrpcSpringClient("napa") final SessionStoreServiceBlockingStub sessionStore,
      @GrpcSpringClient("napa") final SearchServiceBlockingStub napa,
      final ObservableFactory observableFactory) {
    this.pashServiceStub = pashServiceStub.withWaitForReady();
    this.videoMetadataCacheHelper = videoMetadataCacheHelper;
    this.napaCaller = new NapaCaller(napa, sessionStore);
    this.observableFactory = observableFactory;
  }

  @Override
  public Observable<APISearchList> getSearchResults(final APISearchRequest request) {
    Preconditions.checkNotNull(request);
    Preconditions.checkNotNull(CurrentRequestContext.get());

    LegacySearchRequest pashRequest = transformToLegacySearchRequest(request);
    Observable<SearchList> listCaller =
        NapaCaller.supportsRequest(pashRequest).isSupported()
            ? observableFactory.from(() -> napaCaller.getListFromNapa(pashRequest))
            : RxObservable.call(pashServiceStub::legacySearch, pashRequest);

    return listCaller
        .filter(Objects::nonNull)
        .filter(searchList -> !Strings.isNullOrEmpty(searchList.getReferenceId()))
        .map(this::transformLegacyResponse)
        .map(
            apiSearchList -> {
              registerWithMetadataCache(apiSearchList);
              return apiSearchList;
            });
  }

  @Override
  public Observable<APISearchList> getListByReference(
      final String referenceId, final int from, final int count) {
    Preconditions.checkNotNull(referenceId);
    Preconditions.checkArgument(from >= 0);
    Preconditions.checkArgument(count >= 0);
    Preconditions.checkNotNull(CurrentRequestContext.get());

    LegacySearchRequestByReference pashByIdRequest =
        transformReferenceRequest(referenceId, from, count);

    return observableFactory
        .from(() -> napaCaller.getListByIdFromNapa(pashByIdRequest))
        .filter(Objects::nonNull)
        .filter(searchList -> !Strings.isNullOrEmpty(searchList.getReferenceId()))
        .switchIfEmpty(
            Observable.error(
                new InvalidSearchListException(
                    "Invalid list. Either list expired or invalid reference id. Please send a new request")))
        .map(this::transformLegacyResponse);
  }

  private LegacySearchRequest transformToLegacySearchRequest(APISearchRequest input) {
    LegacySearchRequest.Builder builder = LegacySearchRequest.newBuilder();
    builder.putAllOtherParams(input.getOtherParams());
    Optional.ofNullable(input.getCount()).ifPresent(builder::setCount);

    switch (input) {
      case APISearchRequestByQuery typedInput -> {
        builder.setQueryType(QueryType.StringQuery);
        builder.setListType(
            Enum.valueOf(SearchQueryParams.SearchListType.class, typedInput.getListType().name()));
        builder.setQ((typedInput.getTerm()));
      }
      case APISearchPreQueryRequest typedInput -> {
        builder.setQueryType(QueryType.Prequery);
        if (typedInput.getListType().compareTo(SearchListType.empty) != 0)
          builder.setListType(
              Enum.valueOf(
                  SearchQueryParams.SearchListType.class, typedInput.getListType().name()));
      }
      default -> {
        APISearchRequestByEntity typedInput = (APISearchRequestByEntity) input;
        builder.setQueryType(QueryType.EntityQuery);
        builder.setQ(typedInput.getEntityId());
      }
    }

    // Search is leaning towards a world where things are explicitly asked for rather than read from
    // the requestContext
    setSearchRequestContext(builder.getContextBuilder());

    return builder.build();
  }

  private LegacySearchRequestByReference transformReferenceRequest(
      final String referenceId, final int from, final int count) {
    LegacySearchRequestByReference.Builder builder = LegacySearchRequestByReference.newBuilder();

    builder.setReferenceId(referenceId);
    builder.setCount(count);
    builder.setFrom(from);

    setSearchRequestContext(builder.getContextBuilder());

    return builder.build();
  }

  private APISearchList transformLegacyResponse(SearchList searchList) {
    APISearchListImpl result = new APISearchListImpl();

    result.setLength(searchList.getOriginalListLength());
    result.setDisplayString(searchList.getDisplayString());
    result.setOptionalAttributes(searchList.getLegacyLdtsMap());
    result.setReferenceId(searchList.getReferenceId());

    result.setType(APISearchConstants.SearchListType.valueOf(searchList.getListType().name()));

    List<APISearchItem> itemsList =
        searchList.getResultsList().stream().map(this::transformToSearchItem).toList();

    result.setItems(itemsList);
    return result;
  }

  private void setSearchRequestContext(LegacyContext.Builder ctx) {
    ctx.setCountry(CurrentRequestContext.get().getCountry().getId());
    ctx.addAllLocales(getPreferredLocales());
    if (CurrentVisitor.get() != null) {
      ctx.setCurrentVisitor(CurrentVisitor.get().getId());
    }
  }

  private void registerWithMetadataCache(APISearchList searchList) {
    Set<Integer> searchResultVideoIds =
        searchList.getItems().stream()
            .filter(item -> item.getType() != SearchItemType.person)
            .map(APISearchItem::getId)
            .collect(Collectors.toSet());
    videoMetadataCacheHelper.registerBatch(searchResultVideoIds);
  }

  private APISearchItem transformToSearchItem(SearchResult searchResult) {
    APISearchItemImpl result = new APISearchItemImpl();
    result.setDisplayString(searchResult.getDisplayString());
    result.setEntityId(searchResult.getUniqueId());
    result.setId((int) searchResult.getId());
    result.setOptionalAttributes(searchResult.getLegacyLdtsMap());
    result.setType(SearchItemType.valueOf(searchResult.getType().name()));
    return result;
  }

  // Copied from LocaleResolver to match what Napa uses for locales.
  private static List<String> getPreferredLocales() {
    String locales = RequestContextWrapper.get().getLocaleList();
    if (locales == null || locales.isEmpty()) {
      return Collections.emptyList();
    } else {
      return Arrays.asList(locales.split(","));
    }
  }
}
