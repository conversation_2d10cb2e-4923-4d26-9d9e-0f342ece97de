package com.netflix.api.service.account;

import java.util.Objects;

public class APIProfileConfigurationImpl implements APIProfileConfiguration {

  private final boolean disableVideoMerch;

  APIProfileConfigurationImpl(boolean disableVideoMerch) {
    this.disableVideoMerch = disableVideoMerch;
  }

  @Override
  public boolean isVideoMerchDisabled() {
    return disableVideoMerch;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIProfileConfiguration)) {
      return false;
    }
    APIProfileConfiguration that = (APIProfileConfiguration) o;
    return isVideoMerchDisabled() == that.isVideoMerchDisabled();
  }

  @Override
  public int hashCode() {
    return Objects.hash(isVideoMerchDisabled());
  }

  @Override
  public String toString() {
    return "APIProfileConfiguration{" + "disableVideoMerch=" + disableVideoMerch + '}';
  }
}
