package dna.api.service.model;

import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.interactive.APIInteractiveUserState */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class IxUserState {
  private Map<String, Object> currentUserState;

  private List<Map<String, Object>> previousStateSnapshots;

  private Long playbackPositionMs;

  public Map<String, Object> getCurrentUserState() {
    return currentUserState;
  }

  public List<Map<String, Object>> getPreviousStateSnapshots() {
    return previousStateSnapshots;
  }

  public Long getPlaybackPositionMs() {
    return playbackPositionMs;
  }

  public IxUserState setCurrentUserState(Map<String, Object> currentUserState) {
    this.currentUserState = currentUserState;
    return this;
  }

  public IxUserState setPreviousStateSnapshots(List<Map<String, Object>> previousStateSnapshots) {
    this.previousStateSnapshots = previousStateSnapshots;
    return this;
  }

  public IxUserState setPlaybackPositionMs(Long playbackPositionMs) {
    this.playbackPositionMs = playbackPositionMs;
    return this;
  }
}
