package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * adding only members needed by tvUI and tvOS for now; more likely to be needed later
 *
 * @dotnext com.netflix.api.service.identity.APIPlanInfo
 */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PlanInfo {
  @Deprecated private Long billingPeriodEndDate;

  @Deprecated private String formattedCurrentPrice;

  @Deprecated private Boolean isSDOnly;

  private Boolean isHDEnabled;

  private Boolean isUHDEnabled;

  private Boolean is3DEnabled;

  private Long subscriptionId;

  private List<PlanDeviceType> allowedDeviceTypes;

  @Deprecated
  public Long getBillingPeriodEndDate() {
    return billingPeriodEndDate;
  }

  @Deprecated
  public String getFormattedCurrentPrice() {
    return formattedCurrentPrice;
  }

  @Deprecated
  public Boolean getIsSDOnly() {
    return isSDOnly;
  }

  public Boolean getIsHDEnabled() {
    return isHDEnabled;
  }

  public Boolean getIsUHDEnabled() {
    return isUHDEnabled;
  }

  public Boolean getIs3DEnabled() {
    return is3DEnabled;
  }

  public Long getSubscriptionId() {
    return subscriptionId;
  }

  public List<PlanDeviceType> getAllowedDeviceTypes() {
    return allowedDeviceTypes;
  }

  @Deprecated
  public PlanInfo setBillingPeriodEndDate(Long billingPeriodEndDate) {
    this.billingPeriodEndDate = billingPeriodEndDate;
    return this;
  }

  @Deprecated
  public PlanInfo setFormattedCurrentPrice(String formattedCurrentPrice) {
    this.formattedCurrentPrice = formattedCurrentPrice;
    return this;
  }

  @Deprecated
  public PlanInfo setIsSDOnly(Boolean isSDOnly) {
    this.isSDOnly = isSDOnly;
    return this;
  }

  public PlanInfo setIsHDEnabled(Boolean isHDEnabled) {
    this.isHDEnabled = isHDEnabled;
    return this;
  }

  public PlanInfo setIsUHDEnabled(Boolean isUHDEnabled) {
    this.isUHDEnabled = isUHDEnabled;
    return this;
  }

  public PlanInfo setIs3DEnabled(Boolean is3DEnabled) {
    this.is3DEnabled = is3DEnabled;
    return this;
  }

  public PlanInfo setSubscriptionId(Long subscriptionId) {
    this.subscriptionId = subscriptionId;
    return this;
  }

  public PlanInfo setAllowedDeviceTypes(List<PlanDeviceType> allowedDeviceTypes) {
    this.allowedDeviceTypes = allowedDeviceTypes;
    return this;
  }
}
