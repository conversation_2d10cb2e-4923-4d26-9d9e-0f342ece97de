package com.netflix.api.platform.util;

import com.google.common.reflect.TypeParameter;
import com.google.common.reflect.TypeToken;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import jakarta.annotation.Nonnull;
import java.util.Set;

public class Properties {

  private Properties() {}

  @Nonnull
  private static <T> TypeToken<T> typeToken(@Nonnull final Class<T> klass) {
    return TypeToken.of(klass);
  }

  @Nonnull
  private static <T> TypeToken<Set<T>> setType(@Nonnull final TypeToken<T> elementType) {
    return new TypeToken<Set<T>>() {}.where(new TypeParameter<>() {}, elementType);
  }

  @Nonnull
  private static <T> TypeToken<Set<T>> setType(@Nonnull final Class<T> elementClass) {
    return setType(typeToken(elementClass));
  }

  @Nonnull
  private static <T> Property<Set<T>> setProperty(
      @Nonnull final PropertyRepository properties,
      @Nonnull final Class<T> elementClass,
      @Nonnull final String key) {
    return properties.get(key, setType(elementClass).getType());
  }

  @Nonnull
  public static Property<Set<Integer>> intSetProperty(
      @Nonnull final PropertyRepository properties, @Nonnull final String key) {
    return setProperty(properties, Integer.class, key);
  }
}
