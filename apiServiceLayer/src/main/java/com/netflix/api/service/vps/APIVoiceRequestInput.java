package com.netflix.api.service.vps;

import com.netflix.api.service.identity.APIMaturity;
import java.util.Map;

/**
 * Interface representing request input POJO {@link APIVoiceService} can be used to create request
 * input
 */
public interface APIVoiceRequestInput {
  String getRawTextUtterance();

  String getLanguageCode();

  APIMaturity getProfileMaturity();

  String getAppName();

  boolean isOdpAware();

  Map<String, String> getAdditionalContext();

  void setRawTextUtterance(final String rawTextUtterance);

  void setLanguageCode(final String languageCode);

  void setProfileMaturity(final APIMaturity profileMaturity);

  void setAppName(final String isInApp);

  void setIsOdpAware(final boolean isOdpAware);

  void setAdditionalContext(Map<String, String> additionalContext);
}
