package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class YellowSquareNamespaceByKey implements GraphData {
  public Atom<Map<String, String>> tags;

  public Atom<Map<String, String>> getTags() {
    return tags;
  }

  public YellowSquareNamespaceByKey setTags(Atom<Map<String, String>> tags) {
    this.tags = tags;
    return this;
  }
}
