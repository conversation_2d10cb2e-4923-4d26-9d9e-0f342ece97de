package dna.api.datasources;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.netflix.textevidence.protogen.GetSynopsisReply;
import com.netflix.textevidence.protogen.Hook;
import com.netflix.textevidence.protogen.HookType;
import org.junit.jupiter.api.Test;

class VideoHookListTest {
  @Test
  void toDNA_returnsDNARepresentationOfHookList() {
    var list =
        new VideoHookList(
            GetSynopsisReply.HookList.newBuilder()
                .addHookList(
                    Hook.newBuilder()
                        .setHookText("1. some text to hook members!")
                        .setType(HookType.AWARDS_HOOK))
                .addHookList(
                    Hook.newBuilder()
                        .setHookText("2. some text to hook members!")
                        .setType(HookType.BOX_OFFICE_HOOK))
                .addHookList(
                    Hook.newBuilder()
                        .setHookText("3. some text to hook members!")
                        .setType(HookType.TALENT_HOOK))
                .addHookList(
                    Hook.newBuilder()
                        .setHookText("4. some text to hook members!")
                        .setType(HookType.TV_RATINGS_HOOK))
                .addHookList(
                    Hook.newBuilder()
                        .setHookText("5. some text to hook members!")
                        .setType(HookType.UNKNOWN_HOOK))
                .build());

    assertNotNull(list);

    var dnaHookList = list.toDNA();
    assertNotNull(dnaHookList);
    assertEquals(5, dnaHookList.size());

    var hook1 = dnaHookList.getFirst();
    assertEquals("1. some text to hook members!", hook1.getText());
    assertEquals(dna.api.service.model.HookType.Awards, hook1.getType());

    var hook2 = dnaHookList.get(1);
    assertEquals("2. some text to hook members!", hook2.getText());
    assertEquals(dna.api.service.model.HookType.BoxOffice, hook2.getType());

    var hook3 = dnaHookList.get(2);
    assertEquals("3. some text to hook members!", hook3.getText());
    assertEquals(dna.api.service.model.HookType.Talent, hook3.getType());

    var hook4 = dnaHookList.get(3);
    assertEquals("4. some text to hook members!", hook4.getText());
    assertEquals(dna.api.service.model.HookType.TvRatings, hook4.getType());

    var hook5 = dnaHookList.get(4);
    assertEquals("5. some text to hook members!", hook5.getText());
    assertEquals(dna.api.service.model.HookType.Unknown, hook5.getType());
  }
}
