package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

/** @dotnext com.netflix.api.service.ums.APIFilterNotifications */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class FilterNotifications implements GraphData {
  public String eTag;

  public Long latestMessageTimestamp;

  public Map<String, UmsNotification> messages;

  public String getETag() {
    return eTag;
  }

  public Long getLatestMessageTimestamp() {
    return latestMessageTimestamp;
  }

  public FilterNotifications setETag(String eTag) {
    this.eTag = eTag;
    return this;
  }

  public FilterNotifications setLatestMessageTimestamp(Long latestMessageTimestamp) {
    this.latestMessageTimestamp = latestMessageTimestamp;
    return this;
  }
}
