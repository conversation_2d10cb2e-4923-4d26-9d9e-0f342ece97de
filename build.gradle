apply plugin: 'netflix.nebula'

gradleLint {
    alwaysRun = false
}

buildscript {
    dependencies {
        classpath 'com.diffplug.spotless:spotless-plugin-gradle:7.1.0'
        classpath 'com.netflix.spring.gradle:spring-boot-netflix-gradle-plugin:3.+'

        if (project.hasProperty("includeProto2api")) {
          classpath 'netflix:proto2api'
        }
        //todo(eonuoha): remove when flacor-schema works with nebula-gradle-9.11.2
        classpath ("com.fasterxml.jackson.module:jackson-module-kotlin") {
            version {
                strictly '2.17.2'
            }
        }

        // 8/5 kkelani: seeing an issue with v0.25.0 and global locking. See
        // https://netflix.slack.com/archives/C0N913649/p1754436389894279?thread_ts=1754420281.093519&cid=C0N913649
        classpath ('com.netflix.nebula:gradle-jakartaee-migration-plugin:0.24.0') {
            version {
                strictly '0.24.0'
            }
        }
    }
}

allprojects {
    nebula {
        moduleOwner = 'TLI'
        moduleEmail = '<EMAIL>'
    }

    apply plugin: 'nebula.resolution-rules'
    apply plugin: 'netflix.nebula'
    apply plugin: 'netflix.spring-boot-netflix'
    apply plugin: 'netflix.jvm-library'
    apply plugin: 'jacoco'
    apply plugin: 'dependency-lock'
    apply plugin: 'eclipse'
    apply plugin: 'eclipse-wtp'
    apply plugin: 'idea'
    apply plugin: 'nebula.source-jar'
    apply plugin: 'com.diffplug.spotless'

    nebulaJavaVersionCheck {
        acceptResponsibilityForPossiblyPoisoningTheNetflixGraphByUsingNewerJDK = true
    }

    java {
        toolchain {
           languageVersion = JavaLanguageVersion.of(25)
        }
    }

    if (project.hasProperty("includeProto2api")) {
      configurations {
        proto2apiconfig
      }

      dependencies {
        proto2apiconfig "netflix:proto2api"
      }
    }

    dependencyReport.enabled = false

    dependencyLock.includeTransitives = true

    spotless {
        java {
            targetExclude '**/build/generated/**'
            googleJavaFormat().skipJavadocFormatting()
        }
    }

    tasks.withType(Test).configureEach({
        maxParallelForks = 1
    })

    tasks.withType(JavaCompile).configureEach({
        javaCompile ->
            javaCompile.options.fork = true
            javaCompile.options.incremental = true
            javaCompile.options.deprecation = false
            javaCompile.options.warnings = false
    })

    tasks.withType(Javadoc) {
        options {
            tags = ['ExcludeFromSDKJavadoc']
            addStringOption('Xdoclint:none', '-quiet')
        }
    }

    dependencies {
        modules {
            module('com.google.inject:guice-assistedinject') {
                replacedBy('com.google.inject.extensions:guice-assistedinject')
            }
            module('com.google.inject:guice-throwingproviders') {
                replacedBy('com.google.inject.extensions:guice-throwingproviders')
            }
            module('netflix:json') {
                replacedBy('org.json:json')
            }
        }
    }

    configurations.configureEach({
        // force to use io.reactivex
        exclude group: 'io.reactivex', module: 'rxnetty'

        // prefer bcprov-jdk16, important to leave this as a global exclude or else different
        // versions could get pulled in at runtime see https://jira.netflix.com/browse/DNA-1817
        exclude group: 'org.bouncycastle', module: 'bcprov-jdk15on'

        // stay away from groovy-all
        exclude group: 'org.codehaus.groovy', module: 'groovy-all'

        exclude group: "com.netflix.loadshedding", module: "netflix-load-shedding"
        exclude group: "com.netflix.loadshedding", module: "netflix-load-shedding-spring"
    })

    tasks.named('test', Test).configure {
        finalizedBy jacocoTestReport // report is always generated after tests run
    }

    jacocoTestReport {
        dependsOn test // tests are required to run before generating the report
        reports {
            xml.required.set(false)
            csv.required.set(false)
            html.outputLocation = layout.buildDirectory.dir('jacocoHtml')
        }
    }

    testCoverage {
        excludes.addAll([
                '**/dna/api/service/model/**',
        ])
    }
}


apply from: file('dependency-properties.gradle')
apply from: file('dependency-overrides.gradle')
apply from: file('git-hooks.gradle')

afterEvaluate {
    tasks['clean'].dependsOn installGitHooks
    tasks['assemble'].dependsOn installGitHooks
//    compileJava.dependsOn trackChanges
}

task directDependencies {
    group = 'Help'
    description = 'Lists all direct dependencies'
    doLast {
        def prjs = rootProject.subprojects.collect { it.name }.toSet()
        def deps = [] as Set
        rootProject.allprojects.each { p ->
            p.configurations.each { c ->
                c.dependencies.each { dep ->
                    if (!prjs.contains(dep.name)) {
                        deps << dep.group + ':' + dep.name
                    }
                }
            }
        }
        deps.each { println it }
    }
}

def factory = org.slf4j.impl.StaticLoggerBinder.singleton.loggerFactory
def loggers = factory.loggers
def name = "org.apache.http.headers"
loggers.put(name, new org.gradle.internal.logging.slf4j.OutputEventListenerBackedLogger(name, factory, factory.clock))
