package com.netflix.api.service.identity;

import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.subscriber.types.protogen.ProfileCreation;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.type.Visitor;
import java.util.Date;
import java.util.List;
import rx.Observable;
import rx.Single;

public interface APIUserInternal extends APIUser {

  long getRealCustomerId();

  long getRealAccountOwnerId();

  Visitor getVisitor();

  Single<List<Long>> getAllProfileIds();

  Single<List<AccountProfileRemote>> getAllProfilesSubscriber();

  Observable<AccountProfileRemote> getProfileByGuid(String guid);

  ProfileReader getProfileReader();

  String getAvatarImage();

  Date getMemberSinceDate();

  ProfileCreation.Method getProfileCreationMethod();

  Date getProfileCreationDate();

  boolean isJfk();

  void invalidateInternalState();

  /** For the love of all things good use {@link #getAccountProfileAsObservable()} */
  @Deprecated
  AccountProfileRemote getAccountProfile();

  Observable<AccountProfileRemote> getAccountProfileAsObservable();

  IdentityResult<?> getIdentityResult();

  APIUserPersonalData getPersonal();

  Long getSubscriptionId();
}
