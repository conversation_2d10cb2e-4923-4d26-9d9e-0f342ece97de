package com.netflix.api.endpoint;

import com.netflix.lang.ContextCallable;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.UnaryOperator;

/**
 * This is a safer alternative to calling future.cancel(true) directly
 * if future.cancel() is called before the future start executing, the runnable
 * provided will not be executed. This is problematic because it's not safe to
 * drop a runnable on the floor. This postpone calling future.cancel()
 * until the future is started
 */
public class CancellableCallable<V> extends ContextCallable<V> {
  private enum State {
    NEW,
    STARTED,
    CANCEL_POSTPONED,
    CANCELLED
  }

  private volatile Future<?> future = null;
  private final AtomicReference<State> state = new AtomicReference<>(State.NEW);

  public CancellableCallable(final Callable<V> userCallable) {
    super(userCallable);
  }

  @Override
  public V call() throws Exception {
    updateAndCancel(
        currState ->
            switch (currState) {
              case NEW -> State.STARTED;
              case CANCEL_POSTPONED -> State.CANCELLED;
              default -> currState;
            });

    return super.call();
  }

  public void setFuture(final Future<?> future) {
    this.future = future;
  }

  public void cancel() {
    updateAndCancel(
        currState ->
            switch (currState) {
              case STARTED -> State.CANCELLED;
              case NEW -> State.CANCEL_POSTPONED;
              default -> currState;
            });
  }

  private void updateAndCancel(final UnaryOperator<State> updateFunction) {
    State updatedState = state.updateAndGet(updateFunction);
    if (updatedState == State.CANCELLED) {
      future.cancel(true);
    }
  }
}
