package com.netflix.api.service.obelix;

import rx.Observable;

/**
 * Allows access to the Global String Repository Service (Obelix).
 *
 * @see <a
 *     href="https://docs.google.com/a/netflix.com/document/d/19Us3OLDcPjrcdRZFdF_xrj2sNjOOcijRWD7OLAXCS0g">Obelix
 *     Client APIs</a>
 */
public interface APIObelixService {
  /**
   * This function returns the value for given key in specified namespace, bundle and language. This
   * data is returned from cache which is loaded when any for load or get functions are called. If
   * the bundle is not loaded this will load the bundle and gets the value. In this case this might
   * be slower.
   *
   * <p>Fall back: If the specified language is not present, then it uses nfi18n fallback list to go
   * over all the languages.
   *
   * @param namespaceName Namespace name, if not provided will be fetch from "Default" namespace
   * @param bundleName Bundle Name
   * @param languageCode Language Code. If the given key is not found for this language, then the
   *     key is searched in the fallback languages.
   * @param stringDataKey String key
   * @param defaultValue Default Value
   * @return Value for the key
   */
  Observable<String> getSimpleStringProperty(
      String namespaceName,
      String bundleName,
      String languageCode,
      String stringDataKey,
      String defaultValue);
}
