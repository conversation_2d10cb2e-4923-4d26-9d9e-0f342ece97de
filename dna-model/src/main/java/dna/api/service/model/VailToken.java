package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class VailToken {
  private String token;

  private String credential;

  private Long expirationTimestamp;

  private Map<String, String> attributes;

  public String getToken() {
    return token;
  }

  public String getCredential() {
    return credential;
  }

  public Long getExpirationTimestamp() {
    return expirationTimestamp;
  }

  public Map<String, String> getAttributes() {
    return attributes;
  }

  public VailToken setToken(String token) {
    this.token = token;
    return this;
  }

  public VailToken setCredential(String credential) {
    this.credential = credential;
    return this;
  }

  public VailToken setExpirationTimestamp(Long expirationTimestamp) {
    this.expirationTimestamp = expirationTimestamp;
    return this;
  }

  public VailToken setAttributes(Map<String, String> attributes) {
    this.attributes = attributes;
    return this;
  }
}
