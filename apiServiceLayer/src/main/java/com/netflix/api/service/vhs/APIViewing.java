package com.netflix.api.service.vhs;

import rx.Observable;

/** An object representing a VHS viewing event */
public interface APIViewing {

  /**
   * Get the ESN (Electronic Serial Number) of the device on which the video was viewed.
   *
   * @return the ESN of the device, as a String
   */
  String getEsn();

  int getDeviceTypeId();

  /**
   * A flag that indicates if this viewing event is being done so in a mode in which the information
   * about the view should be supressed.
   *
   * @return a flag indicated whether this viewing should be hidden from display in other systems
   */
  boolean isHidden();

  Observable<Integer> getTopNode();

  long getCustomerId();
}
