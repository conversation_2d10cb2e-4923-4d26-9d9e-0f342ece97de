package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class GpsInstalledGamesInfo {
  private List<String> installedGamesPackageNames;

  private List<String> installedGamesUrlSchemes;

  public List<String> getInstalledGamesPackageNames() {
    return installedGamesPackageNames;
  }

  public List<String> getInstalledGamesUrlSchemes() {
    return installedGamesUrlSchemes;
  }

  public GpsInstalledGamesInfo setInstalledGamesPackageNames(
      List<String> installedGamesPackageNames) {
    this.installedGamesPackageNames = installedGamesPackageNames;
    return this;
  }

  public GpsInstalledGamesInfo setInstalledGamesUrlSchemes(List<String> installedGamesUrlSchemes) {
    this.installedGamesUrlSchemes = installedGamesUrlSchemes;
    return this;
  }
}
