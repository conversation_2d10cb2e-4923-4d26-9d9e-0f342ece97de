package com.netflix.api.service.vps;

import jakarta.annotation.Nonnull;
import java.util.List;

/**
 * Interface representing request payload consisting of {@link APIVoiceResource} &amp; {@link
 * APIVoiceIntent} {@link APIVoiceService} can be used to create request payload, resource &amp; intent
 */
public interface APIVoiceRequestPayload {
  List<APIVoiceResource> getResources();

  List<APIVoiceIntent> getIntents();

  void addIntent(@Nonnull final APIVoiceIntent intent);

  void addResource(@Nonnull final APIVoiceResource resource);
}
