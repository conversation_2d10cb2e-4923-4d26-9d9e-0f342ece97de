package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** Event for recording impression of the start of a display ad. */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class DisplayAdStartEvent {
  /** Token to record impression */
  private String token;

  public String getToken() {
    return token;
  }

  public DisplayAdStartEvent setToken(String token) {
    this.token = token;
    return this;
  }
}
