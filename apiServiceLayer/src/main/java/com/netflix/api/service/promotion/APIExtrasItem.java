package com.netflix.api.service.promotion;

import java.util.List;
import java.util.Map;

public interface APIExtrasItem {

  String getDataSource();

  String getPostId();

  String getPostCategoryType();

  String getPostCategoryLabel();

  String getPostType();

  int getMetadataVideoId();

  String getImageType();

  int getTopNodeVideoId();

  int getPlayableVideoId();

  int getTagsVideoId();

  String getAspectRatio();

  int getNumImages();

  boolean isBurnedInDialog();

  boolean isShouldLoop();

  boolean isSilent();

  List<String> getValidLanguages();

  String getPostText();

  String getPostTitle();

  String getPostSubtitle();

  Long getPublishDate();

  String getPostState();

  Integer getPostVideoId();

  Integer getImageRequestVideo();

  boolean isPushNotification();

  boolean isFeaturedPost();

  enum ExtrasCTAName {
    UNRECOGNIZED(-1),
    remindMe(0),
    addToMyList(1),
    share(2),
    play(3),
    playEpisode(4),
    playMovie(5),
    moreInfo(6),
    homePage(7);
    private final int value;

    ExtrasCTAName(int value) {
      this.value = value;
    }

    public int getValue() {
      return value;
    }
  }

  Map<String, String> getAttributes();

  Boolean getSilent();

  List<ExtrasCTAName> getActions();
}
