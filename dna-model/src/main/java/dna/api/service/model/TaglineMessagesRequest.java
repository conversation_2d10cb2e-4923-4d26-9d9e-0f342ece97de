package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class TaglineMessagesRequest {
  /**
   * Value that indicates to evidence systems which page/context the UI is requesting this for. For
   * example, ODP is used to indicate that this value will be displayed on a details page (ODP =
   * original display page).
   *
   * <p>Consult with Evidence Engineering (#evengers) for more info.
   */
  private String uiContext;

  private Boolean withExplanationMessage;

  /** Metadata to forward to Text Evidence */
  private Map<String, String> annotations;

  public String getUiContext() {
    return uiContext;
  }

  public Boolean getWithExplanationMessage() {
    return withExplanationMessage;
  }

  public Map<String, String> getAnnotations() {
    return annotations;
  }

  public TaglineMessagesRequest setUiContext(String uiContext) {
    this.uiContext = uiContext;
    return this;
  }

  public TaglineMessagesRequest setWithExplanationMessage(Boolean withExplanationMessage) {
    this.withExplanationMessage = withExplanationMessage;
    return this;
  }

  public TaglineMessagesRequest setAnnotations(Map<String, String> annotations) {
    this.annotations = annotations;
    return this;
  }
}
