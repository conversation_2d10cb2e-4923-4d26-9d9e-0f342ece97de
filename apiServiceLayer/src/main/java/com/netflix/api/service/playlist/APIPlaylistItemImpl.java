package com.netflix.api.service.playlist;

import com.netflix.api.service.video.APIVideo;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;

public class APIPlaylistItemImpl implements APIPlaylistItem {
  private final APIVideo video;
  private final Map<String, String> metadata;

  public APIPlaylistItemImpl(@Nonnull APIVideo video, Map<String, String> metadata) {
    this.video = video;
    this.metadata = Optional.ofNullable(metadata).orElse(Collections.emptyMap());
  }

  @Nonnull
  @Override
  public APIVideo getVideo() {
    return video;
  }

  @Nullable
  @Override
  public String getMetadata(String key) {
    return metadata.get(key);
  }

  @Nonnull
  @Override
  public Set<String> getMetadataKeys() {
    return metadata.keySet();
  }

  @Override
  public boolean isInRemindMe() {
    return StringUtils.isNotEmpty(getMetadata("REMIND_ME"));
  }

  public Map<String, String> getMetadata() {
    return Collections.unmodifiableMap(metadata);
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    APIPlaylistItemImpl that = (APIPlaylistItemImpl) o;
    return Objects.equals(video.getId(), that.video.getId())
        && Objects.equals(metadata, that.metadata);
  }

  @Override
  public int hashCode() {
    return Objects.hash(video, metadata);
  }

  @Override
  public String toString() {
    return "APIPlaylistItem{" + "videoId=" + video.getId() + ", metadata=" + metadata + '}';
  }
}
