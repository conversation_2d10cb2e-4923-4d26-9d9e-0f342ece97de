package com.netflix.api.service.list;

import com.netflix.api.service.APICountry;
import com.netflix.api.service.identity.APIUser;
import java.util.Map;

/**
 * Encapsulates common request data sent to MAP services. Includes user, country and esn (device),
 * that are automatically set from the request context, but can be overridden if desired. In
 * addition, request annotations (String: Object) can be added; this allows clients to send
 * arbitrary request data to MAP services without Edge service layer having to maintain the
 * contract.
 */
public interface MapRequest<I extends MapRequest> {

  /**
   * Return the user for whom to request recommendations
   *
   * @return the user for recommendations
   */
  APIUser getUser();

  /**
   * Set the user for whom to request recommendations. This is set by default to the user in the
   * request context. Clients need only use it to override to a different value.
   *
   * @param user the user for recommendations
   * @return this request
   */
  I forUser(APIUser user);

  /**
   * Return the country of the user to request recommendations for.
   *
   * @return the country of the user
   */
  APICountry getCountry();

  /**
   * Set the country of the user to request recommendations for. This is set by default to the
   * country in the request context. Clients need only use it to override to a different value.
   *
   * @param country the country of the user
   * @return this request
   */
  I forCountry(APICountry country);

  /**
   * Get ESN of the user device
   *
   * @return the ESN of the user device
   */
  String getEsn();

  /**
   * Set ESN of the user device. This is set by default to the country in the request context.
   * Clients need only use it to override to a different value.
   *
   * @param esn of the user device
   * @return this request
   */
  I forEsn(String esn);

  /**
   * Return optional annotations attached to this request
   *
   * @return the annotations
   */
  Map<String, Object> getAnnotations();
}
