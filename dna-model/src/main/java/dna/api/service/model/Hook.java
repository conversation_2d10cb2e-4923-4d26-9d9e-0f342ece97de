package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class Hook {
  private String text;

  private HookType type;

  public String getText() {
    return text;
  }

  public HookType getType() {
    return type;
  }

  public Hook setText(String text) {
    this.text = text;
    return this;
  }

  public Hook setType(HookType type) {
    this.type = type;
    return this;
  }
}
