package com.netflix.api.service.playlist;

import static com.netflix.api.grpc.GrpcCallHelpers.RxSingle.defer;

import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserInternal;
import com.netflix.api.service.video.APIVideo;
import com.netflix.api.service.video.APIVideoFactory;
import com.netflix.api.util.ServiceUtils;
import com.netflix.type.TypeManager;
import com.netflix.type.Visitor;
import com.netflix.type.proto.Videos;
import com.netflix.type.proto.Visitors;
import com.netflix.type.protogen.BasicTypes.Video;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Single;
import ust.video.playlist.v1.AddRemindMeFlagRequest;
import ust.video.playlist.v1.AddVideoRequest;
import ust.video.playlist.v1.BlockVideoRequest;
import ust.video.playlist.v1.DeleteRemindMeFlagRequest;
import ust.video.playlist.v1.DeleteVideoRequest;
import ust.video.playlist.v1.GetPlaylistActionsRequest;
import ust.video.playlist.v1.GetPlaylistRequest;
import ust.video.playlist.v1.Location;
import ust.video.playlist.v1.MoveVideoRequest;
import ust.video.playlist.v1.MylistResponse;
import ust.video.playlist.v1.PlaylistActions;
import ust.video.playlist.v1.PlaylistBaseContext;
import ust.video.playlist.v1.PlaylistBaseUpdateContext;
import ust.video.playlist.v1.PlaylistBlockContext;
import ust.video.playlist.v1.PlaylistDeleteContext;
import ust.video.playlist.v1.PlaylistUpdateContext;
import ust.video.playlist.v1.VideoPlaylistAccess;
import ust.video.v1.VideoRequest;

@Component
public class PlaylistHandler {

  private final VideoPlaylistAccess videoPlaylistAccess;
  private final APIVideoFactory videoFactory;

  @Autowired
  public PlaylistHandler(VideoPlaylistAccess videoPlaylistAccess, APIVideoFactory videoFactory) {
    this.videoPlaylistAccess = videoPlaylistAccess;
    this.videoFactory = videoFactory;
  }

  public Single<APIPlaylist> editRemindMeFlag(
      Integer trackId,
      Integer targetVideoId,
      String profileGUID,
      Map<String, String> metadata,
      PlaylistRemindMeFlagOperation flagOperation,
      boolean includeUnreleasedTitles,
      String signature) {
    if (PlaylistRemindMeFlagOperation.ADD == flagOperation) {
      return addRemindMeFlag(
          trackId, targetVideoId, profileGUID, metadata, includeUnreleasedTitles, signature);
    } else {
      return deleteRemindMeFlag(
          trackId, targetVideoId, profileGUID, metadata, includeUnreleasedTitles);
    }
  }

  private Single<APIPlaylist> addRemindMeFlag(
      Integer trackId,
      Integer targetVideoId,
      String profileGUID,
      Map<String, String> metadata,
      boolean includeUnreleasedTitles,
      String signature) {
    return buildPlaylistUpdateContext(trackId, profileGUID, includeUnreleasedTitles)
        .flatMap(
            request -> {
              AddRemindMeFlagRequest.Builder addRemindMeFlagRequest =
                  AddRemindMeFlagRequest.newBuilder();
              if (targetVideoId != null)
                addRemindMeFlagRequest.setTargetVideo(Videos.toProtobuf(targetVideoId));
              if (metadata != null) addRemindMeFlagRequest.putAllMetadata(metadata);
              addRemindMeFlagRequest.setUpdateContext(request);
              if (signature != null) {
                addRemindMeFlagRequest.setProfileGuid(getProfileGUID(profileGUID));
                addRemindMeFlagRequest.setSignature(signature);
              }
              return defer(videoPlaylistAccess::addRemindMeFlag, addRemindMeFlagRequest.build())
                  .map(this::getAPIPlaylist);
            });
  }

  private Single<APIPlaylist> deleteRemindMeFlag(
      Integer trackId,
      Integer targetVideoId,
      String profileGUID,
      Map<String, String> metadata,
      boolean includeUnreleasedTitles) {
    return buildPlaylistUpdateContext(trackId, profileGUID, includeUnreleasedTitles)
        .flatMap(
            request -> {
              DeleteRemindMeFlagRequest.Builder deleteRemindMeFlagRequest =
                  DeleteRemindMeFlagRequest.newBuilder();
              if (targetVideoId != null)
                deleteRemindMeFlagRequest.setTargetVideo(Videos.toProtobuf(targetVideoId));
              if (metadata != null) deleteRemindMeFlagRequest.putAllMetadata(metadata);
              deleteRemindMeFlagRequest.setUpdateContext(request);
              return defer(
                      videoPlaylistAccess::deleteRemindMeFlag, deleteRemindMeFlagRequest.build())
                  .map(this::getAPIPlaylist);
            });
  }

  public Single<APIPlaylist> addVideo(
      Integer anchorVideoId,
      Location location,
      Integer trackId,
      Integer targetVideoId,
      String profileGUID,
      Map<String, String> metadata,
      boolean updateIfExists,
      boolean addRemindMeFlag,
      boolean includeUnreleasedTitles,
      String signature) {
    final Location _location;
    if (location == null) {
      _location = (anchorVideoId == null) ? Location.TOP : Location.AFTER;
    } else {
      _location = Location.UNDEFINED;
    }
    return buildPlaylistUpdateContext(trackId, profileGUID, includeUnreleasedTitles)
        .flatMap(
            request -> {
              final AddVideoRequest.Builder addVideoRequest = AddVideoRequest.newBuilder();
              if (targetVideoId != null)
                addVideoRequest.setTargetVideo(Videos.toProtobuf(targetVideoId));
              if (anchorVideoId != null)
                addVideoRequest.setAnchorVideo(Videos.toProtobuf(anchorVideoId));
              if (_location != Location.UNDEFINED) addVideoRequest.setLocation(_location);
              if (metadata != null) addVideoRequest.putAllMetadata(metadata);
              addVideoRequest.setAddRemindMeFlag(addRemindMeFlag);
              addVideoRequest.setUpdateIfExists(updateIfExists);
              addVideoRequest.setUpdateContext(request);

              if (signature != null) {
                addVideoRequest.setProfileGuid(getProfileGUID(profileGUID));
                addVideoRequest.setSignature(signature);
              }
              return defer(videoPlaylistAccess::addVideo, addVideoRequest.build())
                  .map(this::getAPIPlaylist);
            });
  }

  public Single<APIPlaylist> deleteVideo(
      Integer targetVideoId,
      Integer trackId,
      String profileGUID,
      Map<String, String> metadata,
      boolean deleteRemindMeFlag,
      boolean includeUnreleasedTitles) {
    return buildPlaylistDeleteContext(trackId, profileGUID, includeUnreleasedTitles)
        .flatMap(
            request -> {
              DeleteVideoRequest.Builder deleteVideoRequest = DeleteVideoRequest.newBuilder();
              if (targetVideoId != null)
                deleteVideoRequest.setTargetVideo(Videos.toProtobuf(targetVideoId));
              if (metadata != null) deleteVideoRequest.putAllMetadata(metadata);
              deleteVideoRequest.setDeleteRemindMeFlag(deleteRemindMeFlag);
              deleteVideoRequest.setDeleteContext(request).build();
              return defer(videoPlaylistAccess::deleteVideo, deleteVideoRequest.build())
                  .map(this::getAPIPlaylist);
            });
  }

  public Single<Void> blockVideo(
      List<Integer> videoIds, Map<String, String> metadata, boolean includeUnreleasedTitles) {
    BlockVideoRequest.Builder blockVideoRequest = BlockVideoRequest.newBuilder();
    if (videoIds != null) {
      List<Video> videos = videoIds.stream().map(Videos::toProtobuf).toList();
      blockVideoRequest.addAllBlockedVideos(videos);
    }
    if (metadata != null) blockVideoRequest.putAllMetadata(metadata);
    return buildPlaylistBlockContext(includeUnreleasedTitles)
        .flatMap(
            request ->
                defer(
                        videoPlaylistAccess::blockVideo,
                        blockVideoRequest.setBlockContext(request).build())
                    .map(res -> null));
  }

  public Single<APIPlaylist> moveVideo(
      Integer anchorVideoId,
      Location location,
      Integer trackId,
      Integer targetVideoId,
      String profileGUID,
      boolean includeUnreleasedTitles) {
    MoveVideoRequest.Builder moveVideoRequest = MoveVideoRequest.newBuilder();
    if (targetVideoId != null) moveVideoRequest.setTargetVideo(Videos.toProtobuf(targetVideoId));
    if (anchorVideoId != null) moveVideoRequest.setAnchorVideo(Videos.toProtobuf(anchorVideoId));
    if (location != Location.UNDEFINED) moveVideoRequest.setLocation(location);
    return buildPlaylistUpdateContext(trackId, profileGUID, includeUnreleasedTitles)
        .flatMap(
            request ->
                defer(
                        videoPlaylistAccess::moveVideo,
                        moveVideoRequest.setUpdateContext(request).build())
                    .map(this::getAPIPlaylist));
  }

  /**
   * getPlaylist returns the playlist items (including unreleased items) belonging the requesting
   * visitor. By default, items are grouped by top node ID.
   *
   * @param includeNonTopNodes flags to include playlist items that are not top-level nodes (i.e.
   *     episodes).
   * @return the APIPlaylist object wrapping the list of playlist items.
   */
  public Single<APIPlaylist> getPlaylist(boolean includeNonTopNodes) {
    Visitor visitor = ServiceUtils.getVisitorForCurrentUser();

    GetPlaylistRequest request =
        GetPlaylistRequest.newBuilder()
            .setListContext(buildPlaylistBaseContext(visitor, true))
            .setIncludeNonTopNodes(includeNonTopNodes)
            .build();

    return defer(videoPlaylistAccess::getPlaylist, request).map(this::getAPIPlaylist);
  }

  public Single<Map<Integer, Set<String>>> getPlaylistActions(
      Set<Integer> videoIds, Set<String> capabilitiesSet) {
    Visitor visitor = ServiceUtils.getVisitorForCurrentUser();
    GetPlaylistActionsRequest.Builder playlistActionsBuilder =
        GetPlaylistActionsRequest.newBuilder();
    playlistActionsBuilder.addAllDeviceCapabilities(capabilitiesSet);
    List<Video> videoList = videoIds.stream().map(Videos::toProtobuf).toList();

    playlistActionsBuilder
        .setVideos(VideoRequest.newBuilder().addAllVideos(videoList))
        .setCustomer(Visitors.toProtobuf(visitor.getId()));
    if (capabilitiesSet != null) playlistActionsBuilder.addAllDeviceCapabilities(capabilitiesSet);

    return defer(videoPlaylistAccess::getPlaylistActions, playlistActionsBuilder.build())
        .map(
            response -> {
              Map<Integer, PlaylistActions> actions = response.getResponseMap();
              return actions.entrySet().stream()
                  .filter(entry -> videoIds.contains(entry.getKey()))
                  .collect(
                      Collectors.toMap(
                          Entry::getKey,
                          entry -> convertPlaylistActions(entry.getValue()),
                          (u, v) -> u));
            });
  }

  private APIPlaylist getAPIPlaylist(MylistResponse response) {
    if (response.getItemsCount() == 0) return new APIPlaylistImpl(List.of());

    var items =
        response.getItemsList().stream()
            .map(
                val -> {
                  APIVideo apiVideo = videoFactory.getInstance(val.getVideo().getId());
                  if (apiVideo == null) return null;
                  return new APIPlaylistItemImpl(apiVideo, val.getMetadataMap());
                })
            .filter(Objects::nonNull)
            .map(APIPlaylistItem.class::cast)
            .toList();

    return new APIPlaylistImpl(items);
  }

  private Single<PlaylistUpdateContext> buildPlaylistUpdateContext(
      Integer trackId, String profileGUID, boolean includeUnreleasedTitles) {
    return buildPlaylistBaseUpdateContext(trackId, profileGUID, includeUnreleasedTitles)
        .map(request -> PlaylistUpdateContext.newBuilder().setBaseUpdateContext(request).build());
  }

  private Single<PlaylistBaseUpdateContext> buildPlaylistBaseUpdateContext(
      Integer trackId, String profileGUID, boolean includeUnreleasedTitles) {
    return getVisitor(profileGUID)
        .map(
            visitor -> {
              PlaylistBaseUpdateContext.Builder updateContextBuilder =
                  PlaylistBaseUpdateContext.newBuilder();
              updateContextBuilder.setListContext(
                  buildPlaylistBaseContext(visitor, includeUnreleasedTitles));
              if (trackId != null) updateContextBuilder.setTrackId(String.valueOf(trackId));
              APIRequestContext requestContext = APIRequest.getCurrentRequest().getRequestContext();
              if (requestContext != null && requestContext.getDeviceTypeId() != null) {
                updateContextBuilder.setDeviceTypeId(requestContext.getDeviceTypeId());
              }
              return updateContextBuilder.build();
            });
  }

  private Single<PlaylistDeleteContext> buildPlaylistDeleteContext(
      Integer trackId, String profileGUID, boolean includeUnreleasedTitles) {
    return buildPlaylistBaseUpdateContext(trackId, profileGUID, includeUnreleasedTitles)
        .map(request -> PlaylistDeleteContext.newBuilder().setBaseUpdateContext(request).build());
  }

  private Single<PlaylistBlockContext> buildPlaylistBlockContext(boolean includeUnreleasedTitles) {
    return buildPlaylistBaseUpdateContext(null, null, includeUnreleasedTitles)
        .map(request -> PlaylistBlockContext.newBuilder().setBaseUpdateContext(request).build());
  }

  private Single<Visitor> getVisitor(String profileGUID) {
    APIUser user = APIRequest.getCurrentRequest().getUser();
    if (profileGUID == null || profileGUID.equals(user.getCustomerGUID())) {
      return Single.just(ServiceUtils.getVisitor(user));
    } else {
      APIUserInternal internal = (APIUserInternal) user;
      return internal
          .getAllProfilesSubscriber()
          .map(
              profiles ->
                  profiles.stream()
                      .filter(
                          accountProfile ->
                              profileGUID.equals(accountProfile.getBoxedProfileGuid()))
                      .findFirst()
                      .map(
                          accountProfile ->
                              TypeManager.findObject(
                                  Visitor.class, accountProfile.getBoxedProfileId()))
                      .orElseThrow(
                          () -> new IllegalArgumentException("Profile not found: " + profileGUID)));
    }
  }

  private static String getProfileGUID(String profileGUID) {
    APIUser user = APIRequest.getCurrentRequest().getUser();
    return profileGUID == null ? user.getCustomerGUID() : profileGUID;
  }

  public PlaylistBaseContext buildPlaylistBaseContext(
      Visitor visitor, boolean includeUnreleasedTitles) {
    return PlaylistBaseContext.newBuilder()
        .setVisitor(Visitors.toProtobuf(visitor.getId()))
        .setFilterUnavailableVideos(!includeUnreleasedTitles)
        .build();
  }

  private static Set<String> convertPlaylistActions(PlaylistActions playlistActions) {
    if (playlistActions != null && playlistActions.getActionsCount() > 0) {
      return new HashSet<>(playlistActions.getActionsList());
    }

    return Set.of();
  }
}
