package com.netflix.api.service.video;

import com.netflix.api.service.APICountry;
import com.netflix.api.service.identity.APIUser;
import java.util.Map;

/**
 * An {@code APICharacterDataRequest} defines how lists of videos associated with particular {@code
 * APICharacter}s will be generated by such methods as {@code APICharacter#getGallery}, {@code
 * APICharacter#getRecentlyWatchedVideos}, and {@code APICharacter#getWatchNextVideos}. The country,
 * user, and ESN are set to sensible values automatically, but you have the option to override these
 * defaults by using the methods in this interface.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APICharacter.class.png"
 * alt="APICharacter class diagram">
 */
public interface APICharacterDataRequest {
  /**
   * Get the ESN of the request. API.Next resolves this ESN from the following sources (in the order
   * of precedence):
   *
   * <ol>
   *   <li>the ESN you have explicitly set via {@link #overrideEsn}
   *   <li>request parameter "{@code esn}" (e.g. <code>esn=NFPS3-001-<i>blah</i></code>)
   *   <li>request header "{@code X-Netflix.esn}"
   *   <li>device or double bound cookies ({@code NetflixId} and {@code SecureNetflixId} ones)
   * </ol>
   *
   * @return the ESN of the request, as a {@code String}
   */
  String getEsn();

  /**
   * Override the ESN of this request for the purpose of generating character data.
   *
   * @param esn ESN you want the Character Data Request to use
   * @return the same {@code APICharacterDataRequest} modified to apply to the specified ESN
   */
  APICharacterDataRequest overrideEsn(String esn);

  /**
   * Get the customer for whom the request is being made. API.Next normally determines the customer
   * via the Netflix DCR cookies.
   *
   * @return the customer for whom the request is being made, as an {@link APIUser}
   */
  APIUser getUser();

  /**
   * Override the customer for whom the request is being made. API.Next normally determines the
   * customer via the Netflix DCR cookies. If a client cannot indicate the customer in the cookies
   * or request parameters, then they can set it here.
   *
   * @param user the customer for whom the request is being made, as an {@link APIUser}
   * @return the same {@code APICharacterDataRequest} modified to apply to the specified customer
   */
  APICharacterDataRequest overrideUser(APIUser user);

  /**
   * Get the country that was determined from the geo data in the request.
   *
   * @return the country for which the request is being made, as an {@link APICountry}
   */
  APICountry getCountry();

  /**
   * Override the country that is already set from the geo data in the request.
   *
   * @param country the country for which the request is being made, as an {@link APICountry}
   * @return the same {@code APICharacterDataRequest} modified to apply to the specified country
   */
  APICharacterDataRequest overrideCountry(APICountry country);

  /**
   * DEPRECATED - Previously, behavior of this method was: setting the flag to true returns the
   * show/movie that has been most recently watched. It does not advance to the next movie/show once
   * a movie/show has been been completely watched. Setting this to false returns the next
   * "viewable" to watch -- essentially supports the existing use case for UIs.
   *
   * <p>Current behavior of this method is a no-op
   *
   * @param continueWatching
   * @return the same {@code APICharacterDataRequest} modified to apply to the specified setting for
   *     continue watching.
   * @deprecated
   */
  @Deprecated
  APICharacterDataRequest setContinueWatching(boolean continueWatching);

  /**
   * Returns the builder option set via {@link #setContinueWatchingIncludeIncomplete(boolean)}.
   * Default is false.
   */
  boolean isContinueWatchingIncludeIncomplete();

  /**
   * Defines the strategy of how MAP would recommend the next episode to watch.
   *
   * @param continueWatchingIncludeIncomplete {@code true} means that MAP will recommend the same
   *     episode to watch next if it has not been watched entirely (beyond the threshold) yet;
   *     {@code false} means that MAP will skip to the next episode regardless of how long the
   *     current episode has been watched.
   * @return the same {@code APICharacterDataRequest} modified to apply to the specified setting for
   *     continue watching.
   */
  APICharacterDataRequest setContinueWatchingIncludeIncomplete(
      boolean continueWatchingIncludeIncomplete);

  /**
   * Get annotations added to this request
   *
   * @return the Map of key-value pairs added to this request
   */
  Map<String, Object> getAnnotations();

  /**
   * Adds generic annotation key value pairs for this request
   *
   * @param key the value of the annotation
   * @param value the value of the annotation
   * @return the same {@code APICharacterDataRequest} modified with added annotation
   */
  APICharacterDataRequest addAnnotation(String key, Object value);
}
