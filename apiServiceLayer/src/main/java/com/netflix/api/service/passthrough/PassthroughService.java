package com.netflix.api.service.passthrough;

import com.fasterxml.jackson.databind.JsonNode;
import java.util.Set;
import rx.Observable;

public interface PassthroughService {

  @Deprecated
  Set<String> getAvailableMethods();

  @Deprecated
  JsonNode getInputSchema(String name);

  @Deprecated
  JsonNode getOutputSchema(String name);

  @Deprecated
  Observable<Object> invoke(String clazz, String name, Object input);
}
