package com.netflix.api.service.abtest;

import static com.netflix.servo.monitor.DynamicCounter.increment;

import com.google.common.annotations.VisibleForTesting;
import com.netflix.aballocator.client.DefaultCellProvider;
import com.netflix.aballocator.client.SubscriberHelper;
import com.netflix.aballocator.protogen.ABAllocatorServiceGrpc.ABAllocatorServiceStub;
import com.netflix.aballocator.protogen.AbCtx;
import com.netflix.aballocator.protogen.Alloc;
import com.netflix.aballocator.protogen.AllocId;
import com.netflix.aballocator.protogen.AllocIdType;
import com.netflix.aballocator.protogen.AllocResponse;
import com.netflix.aballocator.protogen.AllocStatus;
import com.netflix.aballocator.protogen.AllocateRequest;
import com.netflix.aballocator.protogen.EmptyResponse;
import com.netflix.aballocator.protogen.ForceAllocateRequest;
import com.netflix.aballocator.protogen.ForceDeallocateRequest;
import com.netflix.aballocator.protogen.GetRequest;
import com.netflix.aballocator.protogen.TestCell;
import com.netflix.api.dependencies.subscriber.AccountProfileHandler;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.grpc.GrpcCallHelpers.RxSingle;
import com.netflix.api.platform.identity.CurrentIdentityResult;
import com.netflix.api.platform.util.CompletionStageAdapter;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.platform.util.RequestScopedObservable;
import com.netflix.api.platform.util.RequestScopedSingle;
import com.netflix.api.util.ServiceRequestUtils;
import com.netflix.archaius.api.Property;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.utils.ResolverContextUtils;
import com.netflix.microcontext.resolver.MicrocontextResolver;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;
import netflix.context.Context;
import netflix.context.experimentation.Allocations;
import netflix.context.experimentation.ExperimentationContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Single;

@Component
public class ABAdapterBase {
  private static final Logger logger = LoggerFactory.getLogger(ABAdapterBase.class);

  private final RequestScopedSingle<AllocResponse> cachedAllAllocations =
      new RequestScopedSingle<>();

  private final RequestScopedSingle<AllocResponse> cachedExplicitVisitorAllocations =
      new RequestScopedSingle<>();

  private final RequestScopedSingle<AllocResponse> cachedExplicitAccountAllocations =
      new RequestScopedSingle<>();

  private final RequestScopedObservable<AllocResponse> cachedProtoAllocationsExplicit =
      new RequestScopedObservable<>();

  private final RequestScopedObservable<AllocResponse> cachedProtoAllocationsDefaults =
      new RequestScopedObservable<>();

  private final RequestScopedObservable<AllocResponse> cachedProtoAllocationsDefaultsNoCell1 =
      new RequestScopedObservable<>();

  /**
   * Fast Property that determines whether to use the native AB Hystrix/NIWS client, or the one
   * wrapped by the legacy Membership client. The former returns VDID-based allocations, while the
   * latter does not.
   *
   * @see <a href="https://jira.netflix.com/browse/EDGE-4172">EDGE-4172</a>
   */
  private static final Property<Boolean> useLegacyMembershipClientWrapper =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.dependencies.ab.useLegacyMembershipClientWrapper", Boolean.class)
          .orElse(true);

  private final ABAllocatorServiceStub serviceStub;
  private final DefaultCellProvider defaultCellProvider;
  private final SubscriberHelper subscriberHelper;
  private final MicrocontextResolver microcontextResolver;
  private final AccountProfileHandler handler;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public ABAdapterBase(
      @GrpcSpringClient("aballocator") ABAllocatorServiceStub serviceStub,
      DefaultCellProvider defaultCellProvider,
      SubscriberHelper subscriberHelper,
      MicrocontextResolver microcontextResolver,
      AccountProfileHandler handler) {
    this.serviceStub = serviceStub.withWaitForReady();
    this.defaultCellProvider = defaultCellProvider;
    this.subscriberHelper = subscriberHelper;
    this.microcontextResolver = microcontextResolver;
    this.handler = handler;
  }

  public Single<AllocResponse> allocateAsProto(
      Map<String, String> context,
      boolean allocateAccount,
      boolean allocateVisitor,
      boolean mergeDefaultAllocations) {

    AccountProfileRemote accountProfile = handler.currentAccountProfile();
    String visitorDeviceId = CurrentIdentityResult.getVisitorDeviceId();

    if (accountProfile == null && visitorDeviceId == null) {
      return Single.error(
          new IllegalArgumentException(
              "Cannot call allocate when user credentials are not available"));
    }

    Map<String, String> augmentedContext =
        appContext(ServiceRequestUtils.fillGeoDetailsInABContext(context));

    Single<AllocResponse> accountAllocations =
        allocateAccount && accountProfile != null
            ? allocateAccount(accountProfile, augmentedContext)
            : emptyAllocations();

    Single<AllocResponse> visitorAllocations =
        allocateVisitor && visitorDeviceId != null
            ? allocateVisitor(visitorDeviceId, augmentedContext, accountProfile)
            : emptyAllocations();

    Single<AllocResponse> allocations =
        mergeAllocations(accountAllocations, visitorAllocations, mergeDefaultAllocations).cache();

    flushCaches();
    cachedAllAllocations.get().set(allocations);
    return allocations;
  }

  public Single<EmptyResponse> forceAllocateAsProto(
      boolean allocateAccount,
      boolean allocateVisitor,
      int testId,
      int cell,
      Map<String, String> context) {

    AccountProfileRemote accountProfile = handler.currentAccountProfile();
    String visitorDeviceId = CurrentIdentityResult.getVisitorDeviceId();

    if (accountProfile == null && visitorDeviceId == null) {
      return Single.error(
          new IllegalArgumentException(
              "Cannot call allocate when user credentials are not available"));
    }

    // in practice, only one of the flags can be true at any given time
    if (allocateAccount) {
      return forceAllocateAccountAsProto(accountProfile, testId, cell, context)
          .doOnSuccess(ignore -> flushCaches());
    }
    if (allocateVisitor) {
      return forceAllocateVisitorAsProto(visitorDeviceId, testId, cell, context)
          .doOnSuccess(ignore -> flushCaches());
    }
    return Single.just(EmptyResponse.getDefaultInstance());
  }

  public Single<EmptyResponse> forceDeallocateAsProto(
      boolean deallocateAccount, boolean deallocateVisitor, Collection<Integer> testIds) {

    AccountProfileRemote accountProfile = handler.currentAccountProfile();
    String visitorDeviceId = CurrentIdentityResult.getVisitorDeviceId();

    // in practice, only one of the flags can be true at any given time
    if (deallocateAccount) {
      return forceDeallocateAccountAsProto(accountProfile, testIds)
          .doOnSuccess(ignore -> flushCaches());
    }
    if (deallocateVisitor) {
      return forceDeallocateVisitorAsProto(visitorDeviceId, testIds)
          .doOnSuccess(ignore -> flushCaches());
    }
    return Single.just(EmptyResponse.getDefaultInstance());
  }

  public Single<AllocResponse> getAllocationsForTestsAsProto(
      final Collection<Integer> testIds, Map<String, String> context) {
    if (testIds == null || testIds.isEmpty()) {
      return emptyAllocations();
    }
    return getAllAllocationsAsProto(context)
        .map(
            allocResponse -> {
              Map<Integer, Alloc> allocMap = allocResponse.getAllocMap();
              Map<Integer, Alloc> filteredAllocations = new HashMap<>();
              for (Integer testId : testIds) {
                if (testId != null) {
                  if (allocMap.containsKey(testId)) {
                    filteredAllocations.put(testId, allocMap.get(testId));
                  } else {
                    filteredAllocations.put(
                        testId,
                        Alloc.newBuilder()
                            .setAllocStatus(AllocStatus.NOT_ALLOCATED) // not explicit
                            .setCell(defaultCellProvider.getDefaultAllocForTestId(testId).getCell())
                            .build());
                  }
                }
              }
              return AllocResponse.newBuilder()
                  .setIsFallback(allocResponse.getIsFallback())
                  .putAllAlloc(filteredAllocations)
                  .build();
            });
  }

  private Single<AllocResponse> getAllAllocationsAsProto(Map<String, String> context) {
    Single<AllocResponse> allocations = cachedAllAllocations.get().get();
    if (allocations != null) {
      return allocations;
    }
    allocations = Single.defer(() -> getAllocationsAsProto(context)).cache();
    return cachedAllAllocations.get().compareAndSet(null, allocations)
        ? allocations
        : cachedAllAllocations.get().get();
  }

  private Single<AllocResponse> getAllocationsAsProto(Map<String, String> context) {
    AccountProfileRemote accountProfile = handler.currentAccountProfile();
    String visitorDeviceId = CurrentIdentityResult.getVisitorDeviceId();

    Single<AllocResponse> accountAllocations =
        accountProfile == null
            ? emptyAllocations()
            : getAccountAllocationsAsProto(accountProfile, context, true);

    Single<AllocResponse> visitorAllocations =
        useLegacyMembershipClientWrapper.get() || visitorDeviceId == null
            ? emptyAllocations()
            : getVisitorAllocationsAsProto(visitorDeviceId, accountProfile, context, true);

    return mergeAllocations(accountAllocations, visitorAllocations, true);
  }

  public Single<AllocResponse> detAllocateAsProto(
      String abTestType, String visitorDeviceId, Map<String, String> context) {

    Map<String, String> augmentedContext =
        appContext(ServiceRequestUtils.fillGeoDetailsInABContext(context));

    AllocId allocId = detAllocId(abTestType, visitorDeviceId);
    if (allocId == null) {
      return emptyAllocations();
    }
    return RxSingle.defer(
        serviceStub::allocate,
        AllocateRequest.newBuilder()
            .setAbCtx(abContext(augmentedContext))
            .setAllocId(allocId)
            .build());
  }

  public Single<AllocResponse> getExplicitAllocationsAsProto(
      boolean vdidTestType, Map<String, String> context) {
    return vdidTestType
        ? getExplicitVisitorAllocationsAsProto(context)
        : getExplicitAccountAllocationsAsProto(context);
  }

  private Single<AllocResponse> getExplicitAccountAllocationsAsProto(Map<String, String> context) {
    Single<AllocResponse> allocations = cachedExplicitAccountAllocations.get().get();
    if (allocations != null) {
      return allocations;
    }
    // no account allocations if no account
    AccountProfileRemote accountProfile = handler.currentAccountProfile();
    if (accountProfile == null) {
      allocations = emptyAllocations();
    } else {
      allocations =
          Single.defer(() -> getAccountAllocationsAsProto(accountProfile, context, false)).cache();
    }
    return cachedExplicitAccountAllocations.get().compareAndSet(null, allocations)
        ? allocations
        : cachedExplicitAccountAllocations.get().get();
  }

  private Single<AllocResponse> getExplicitVisitorAllocationsAsProto(Map<String, String> context) {
    Single<AllocResponse> allocations = cachedExplicitVisitorAllocations.get().get();
    if (allocations != null) {
      return allocations;
    }
    // no visitor allocations if no vdid
    String visitorDeviceId = CurrentIdentityResult.getVisitorDeviceId();
    if (visitorDeviceId == null) {
      allocations = emptyAllocations();
    } else {
      allocations =
          Single.defer(
                  () ->
                      getVisitorAllocationsAsProto(
                          visitorDeviceId, handler.currentAccountProfile(), context, false))
              .cache();
    }
    return cachedExplicitVisitorAllocations.get().compareAndSet(null, allocations)
        ? allocations
        : cachedExplicitVisitorAllocations.get().get();
  }

  private Single<AllocResponse> getAccountAllocationsAsProto(
      AccountProfileRemote accountProfile,
      Map<String, String> context,
      boolean includeFallbackAllocations) {
    increment("api.aballocator.call", "service", "getaccount");
    return RxSingle.defer(
            serviceStub::getAllocations,
            GetRequest.newBuilder()
                .setAllocId(accountAllocId(accountProfile))
                .setAbCtx(abContext(context, accountProfile))
                .build())
        .doOnSuccess(ABAdapterBase::populateAccountMicrocontext)
        .map(
            allocResponse ->
                includeFallbackAllocations && allocResponse.getIsFallback()
                    ? getFallbackResponse()
                    : allocResponse);
  }

  private Single<AllocResponse> getVisitorAllocationsAsProto(
      @Nonnull String visitorDeviceId,
      @Nullable AccountProfileRemote accountProfile,
      Map<String, String> context,
      boolean includeFallbackAllocations) {
    increment("api.aballocator.call", "service", "getvisitor");
    return RxSingle.defer(
            serviceStub::getAllocations,
            GetRequest.newBuilder()
                .setAllocId(vdidAllocId(visitorDeviceId))
                .setAbCtx(abContext(context, accountProfile))
                .build())
        .doOnSuccess(ABAdapterBase::populateVisitorMicrocontext)
        .map(
            allocResponse ->
                includeFallbackAllocations && allocResponse.getIsFallback()
                    ? getFallbackResponse()
                    : allocResponse);
  }

  @VisibleForTesting
  static Single<AllocResponse> emptyAllocations() {
    return Single.just(AllocResponse.getDefaultInstance());
  }

  private Single<AllocResponse> allocateAccount(
      AccountProfileRemote accountProfile, Map<String, String> context) {
    DynamicCounter.increment("api.aballocator.call", "service", "allocateaccount");
    return RxSingle.defer(
            serviceStub::allocate,
            AllocateRequest.newBuilder()
                .setAllocId(accountAllocId(accountProfile))
                .setAbCtx(abContext(context, accountProfile))
                .build())
        .map(response -> response.getIsFallback() ? getFallbackResponse() : response);
  }

  private Single<AllocResponse> allocateVisitor(
      String visitorDeviceId, Map<String, String> context, AccountProfileRemote accountProfile) {
    DynamicCounter.increment("api.aballocator.call", "service", "allocatevisitor");
    return RxSingle.defer(
            serviceStub::allocate,
            AllocateRequest.newBuilder()
                .setAllocId(vdidAllocId(visitorDeviceId))
                .setAbCtx(abContext(context, accountProfile))
                .build())
        .map(response -> response.getIsFallback() ? getFallbackResponse() : response);
  }

  /**
   * Returns the default account-based <em>and</em> visitor-based allocations.
   *
   * <p>Default allocations should all be marked as non-explicit. The caller is responsible for
   * setting the fallback flag.
   *
   * @return the default allocations
   */
  private AllocResponse getFallbackResponse() {
    return AllocResponse.newBuilder()
        .setIsFallback(true)
        .putAllAlloc(defaultCellProvider.getDefaultAllocs())
        .build();
  }

  @VisibleForTesting
  Single<AllocResponse> mergeAllocations(
      Single<AllocResponse> accountAllocations,
      Single<AllocResponse> visitorAllocations,
      boolean mergeDefaultAllocations) {
    return Single.zip(
        accountAllocations,
        visitorAllocations,
        (accAllocations, vdidAllocations) -> {
          AllocResponse.Builder builder = AllocResponse.newBuilder();
          builder.setIsFallback(accAllocations.getIsFallback() || vdidAllocations.getIsFallback());
          builder.putAllAlloc(accAllocations.getAllocMap());
          for (Entry<Integer, Alloc> visitorAllocation : vdidAllocations.getAllocMap().entrySet()) {
            // explicit allocations must override non-explicit ones
            Alloc existingAllocation = builder.getAllocMap().get(visitorAllocation.getKey());
            if (existingAllocation == null
                || (visitorAllocation.getValue().getAllocStatus() == AllocStatus.ALLOCATED
                    && existingAllocation.getAllocStatus() != AllocStatus.ALLOCATED))
              builder.putAlloc(visitorAllocation.getKey(), visitorAllocation.getValue());
          }

          if (mergeDefaultAllocations) {
            for (Entry<Integer, Alloc> defaultAllocation :
                defaultCellProvider.getDefaultAllocs().entrySet()) {
              // Add default if no explicit alloc is present
              if (!builder.containsAlloc(defaultAllocation.getKey())) {
                builder.putAlloc(defaultAllocation.getKey(), defaultAllocation.getValue());
              }
            }
          }
          return builder.build();
        });
  }

  private Single<EmptyResponse> forceAllocateAccountAsProto(
      AccountProfileRemote accountProfile, int testId, int cell, Map<String, String> context) {
    increment("api.aballocator.call", "service", "allocateforceaccount");
    return RxSingle.defer(
        serviceStub::forceAllocate,
        ForceAllocateRequest.newBuilder()
            .setAllocId(accountAllocId(accountProfile))
            .setAbCtx(abContext(context, accountProfile))
            .addTestCell(TestCell.newBuilder().setTestId(testId).setCell(cell).build())
            .build());
  }

  private Single<EmptyResponse> forceAllocateVisitorAsProto(
      String visitorDeviceId, int testId, int cell, Map<String, String> context) {
    increment("api.aballocator.call", "service", "allocateforcevisitor");
    return RxSingle.defer(
        serviceStub::forceAllocate,
        ForceAllocateRequest.newBuilder()
            .setAllocId(vdidAllocId(visitorDeviceId))
            .setAbCtx(abContext(context))
            .addTestCell(TestCell.newBuilder().setTestId(testId).setCell(cell).build())
            .build());
  }

  private Single<EmptyResponse> forceDeallocateAccountAsProto(
      AccountProfileRemote accountProfile, Collection<Integer> testIds) {
    increment("api.aballocator.call", "service", "deallocate");
    return RxSingle.defer(
        serviceStub::forceDeallocate,
        ForceDeallocateRequest.newBuilder()
            .setAllocId(accountAllocId(accountProfile))
            .addAllTestIds(testIds)
            .build());
  }

  private Single<EmptyResponse> forceDeallocateVisitorAsProto(
      String visitorDeviceId, Collection<Integer> testIds) {
    increment("api.aballocator.call", "service", "deallocate");
    return RxSingle.defer(
        serviceStub::forceDeallocate,
        ForceDeallocateRequest.newBuilder()
            .setAllocId(vdidAllocId(visitorDeviceId))
            .addAllTestIds(testIds)
            .build());
  }

  private static void populateAccountMicrocontext(AllocResponse response) {
    populateMicrocontext(true, response);
  }

  private static void populateVisitorMicrocontext(AllocResponse response) {
    populateMicrocontext(false, response);
  }

  private static void populateMicrocontext(boolean member, AllocResponse response) {
    populateAccountMicrocontext(
        experimentation(CurrentMicrocontext.get().toProto().toBuilder(), member, response));
  }

  private static void populateAccountMicrocontext(Context.Builder context) {
    DynamicCounter.increment("api.abresolution", "mode", "lazy");
    CurrentMicrocontext.set(context.build());
  }

  public Observable<AllocResponse> getAllocationsAsProto(
      boolean mergeDefaults, boolean excludeCell1) {
    AccountProfileRemote accountProfile = handler.currentAccountProfile();
    final RequestScopedObservable<AllocResponse> allocationsRequestScopedObservable;
    if (excludeCell1) {
      allocationsRequestScopedObservable = cachedProtoAllocationsDefaultsNoCell1;
    } else if (mergeDefaults) {
      allocationsRequestScopedObservable = cachedProtoAllocationsDefaults;
    } else {
      allocationsRequestScopedObservable = cachedProtoAllocationsExplicit;
    }

    Observable<AllocResponse> allocations = allocationsRequestScopedObservable.get().get();
    if (allocations != null) {
      return allocations;
    }
    allocations =
        getAllocationsAsProto(accountProfile, mergeDefaults, excludeCell1)
            .doOnNext(ABAdapterBase::populateAccountMicrocontext)
            .cache();
    return allocationsRequestScopedObservable.get().compareAndSet(null, allocations)
        ? allocations
        : allocationsRequestScopedObservable.get().get();
  }

  private Observable<AllocResponse> getAllocationsAsProto(
      @Nullable AccountProfileRemote accountProfile, boolean mergeDefaults, boolean excludeCell1) {
    Observable<AllocResponse> explicitAccountAllocations = Observable.empty();
    if (accountProfile != null) {
      // get explicit allocations for account
      explicitAccountAllocations =
          RxObservable.defer(
              serviceStub::getAllocations,
              GetRequest.newBuilder()
                  .setAllocId(accountAllocId(accountProfile))
                  .setAbCtx(abContext(accountProfile))
                  .build());
    }
    String visitorDeviceId = CurrentIdentityResult.getVisitorDeviceId();
    Observable<AllocResponse> explicitVisitorAllocations = Observable.empty();
    if (useLegacyMembershipClientWrapper.get() || visitorDeviceId == null) {
      return getAllocResponseObservable(
          mergeDefaults, excludeCell1, explicitAccountAllocations, explicitVisitorAllocations);
    }
    // get explicit allocations for visitor
    explicitVisitorAllocations =
        RxObservable.defer(
            serviceStub::getAllocations,
            GetRequest.newBuilder()
                .setAllocId(vdidAllocId(visitorDeviceId))
                .setAbCtx(abContext(defaultAppContext(), accountProfile))
                .build());

    // account takes precedence over visitor
    return getAllocResponseObservable(
        mergeDefaults, excludeCell1, explicitAccountAllocations, explicitVisitorAllocations);
  }

  private Observable<AllocResponse> getAllocResponseObservable(
      boolean mergeDefaults,
      boolean excludeCell1,
      Observable<AllocResponse> explicitAccountAllocations,
      Observable<AllocResponse> explicitVisitorAllocations) {
    return explicitVisitorAllocations
        .concatWith(explicitAccountAllocations)
        // we may have empty account or visitor allocations
        .toList()
        .map(
            list -> {
              AllocResponse.Builder builder = AllocResponse.newBuilder();
              for (AllocResponse response : list) {
                builder.mergeFrom(response);
              }
              return builder.build();
            })
        .map(
            explicit -> {
              // nothing to do
              if (mergeDefaults) {
                AllocResponse.Builder finalBuilder = AllocResponse.newBuilder();

                // call DefaultCellProvider to merge defaults, with/without cell1
                Map<Integer, Alloc> finalMap =
                    excludeCell1
                        ? defaultCellProvider.getDefaultMergedAllocsMapWithNonCellOneDefault(
                            explicit.getAllocMap())
                        : defaultCellProvider.getDefaultMergedAllocsMap(explicit.getAllocMap());
                finalBuilder.putAllAlloc(finalMap);
                return finalBuilder.build();
              } else {
                return explicit;
              }
            });
  }

  public Single<ExperimentationContext> experimentationContext() {
    return CompletionStageAdapter.toSingle(microcontextResolver.resolveExperimentation());
  }

  private AbCtx abContext(AccountProfileRemote accountProfile) {
    return abContext(null, accountProfile);
  }

  private AbCtx abContext(Map<String, String> context) {
    return abContext(context, null);
  }

  private AbCtx abContext(
      @Nullable Map<String, String> context, @Nullable AccountProfileRemote accountProfile) {
    AbCtx.Builder builder =
        AbCtx.newBuilder().putAllExtraDataMap(context != null ? context : defaultAppContext());
    if (context != null && !context.isEmpty()) {
      context.keySet().forEach(key -> DynamicCounter.increment("api.ab.context.map", "key", key));
      logger.info("abcontextmap {}", context);
      MantisPublishContext.getCurrent().add("abcontextmap", context.toString());
    }
    if (accountProfile != null) {
      builder.setSubscriberInfo(subscriberHelper.getSubscriberInfo(accountProfile));
    }
    return builder.build();
  }

  private AllocId accountAllocId(@Nonnull AccountProfileRemote accountProfile) {
    return AllocId.newBuilder()
        .setId(Long.toString(accountProfile.getBoxedAcctId()))
        .setIdType(AllocIdType.ACCOUNT_ID)
        .build();
  }

  private AllocId vdidAllocId(@Nonnull String visitorDeviceId) {
    return AllocId.newBuilder()
        .setId(visitorDeviceId)
        .setIdType(AllocIdType.VISITOR_DEVICE_ID)
        .build();
  }

  private AllocId detAllocId(String abTestType, String visitorDeviceId) {
    AllocId.Builder builder = AllocId.newBuilder().setId(visitorDeviceId);
    switch (abTestType) {
      case "VISITOR_DEVICE_ID" -> builder.setIdType(AllocIdType.VISITOR_DEVICE_ID);
      case "PARTNER_VISITOR_DEVICE_ID" -> builder.setIdType(AllocIdType.PARTNER_VISITOR_DEVICE_ID);
      case "PARTNER_ACCOUNT_ID" -> builder.setIdType(AllocIdType.PARTNER_ACCOUNT_ID);
      default -> {
        return null;
      }
    }
    return builder.build();
  }

  /**
   * Returns the app-specific context used for rule-based allocations and/or inclusion rules.
   *
   * <p>A {@code currentClientType} key is required to identify the caller so the {@code
   * ABAllocator} service can uniquely target users coming from specific integrations.
   *
   * <p>Not that null keys and values which may have been inserted by the caller MUST be removed to
   * avoid running into a {@code NullPointerException} down the protobuf stack.
   *
   * <blockquote>
   *
   * <pre>
   *    at com.google.protobuf.Internal.checkNotNull(Internal.java:67)
   *    at com.google.protobuf.MapField$MutatabilityAwareMap.putAll(MapField.java:350)
   * </pre>
   *
   * </blockquote>
   *
   * @param context app-specific context
   * @return the app-specific context without any null keys and/or values that includes the expected
   *     {@code currentClientType} key
   */
  private static Map<String, String> appContext(Map<String, String> context) {
    return new HashMap<>(context)
        .entrySet().stream()
            .filter(entry -> Objects.nonNull(entry.getKey()) && Objects.nonNull(entry.getValue()))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
  }

  /**
   * Returns the default app-specific context used for rule-based allocations and/or inclusion
   * rules. See {@link #appContext(Map)}
   *
   * @return the default app-specific context that includes the expected {@code currentClientType}
   *     key
   */
  public static Map<String, String> defaultAppContext() {
    return appContext(Collections.emptyMap());
  }

  private static Context.Builder experimentation(
      Context.Builder current, boolean member, @Nonnull AllocResponse response) {
    Map<Integer, Integer> map = allocations(response);
    if (map.isEmpty()) {
      logger.debug("Setting empty map allocs {}", response.getAllocMap());
    }
    Allocations.Builder builder = Allocations.newBuilder().putAllTestToCell(map);
    ExperimentationContext.Builder experimentationBuilder =
        ResolverContextUtils.resolvedExperimentation(current)
            .map(ExperimentationContext::toBuilder)
            .orElse(ExperimentationContext.newBuilder());
    if (member) {
      experimentationBuilder.setAccountAllocations(builder);
    } else {
      experimentationBuilder.setVdidAllocations(builder);
    }
    return ResolverContextUtils.setExperimentation(current, experimentationBuilder.build());
  }

  private static Map<Integer, Integer> allocations(AllocResponse response) {
    Map<Integer, Integer> m = HashMap.newHashMap(response.getAllocCount());
    for (var entry : response.getAllocMap().entrySet()) {
      var alloc = entry.getValue();
      if (alloc.getAllocStatus() == AllocStatus.ALLOCATED && alloc.getCell() > 1) {
        m.put(entry.getKey(), alloc.getCell());
      }
    }
    return m;
  }

  private void flushCaches() {
    cachedAllAllocations.get().set(null);
    cachedExplicitVisitorAllocations.get().set(null);
    cachedExplicitAccountAllocations.get().set(null);
  }
}
