package com.netflix.api.service.list;

import rx.Single;

/**
 * A row in a lolomo. Has an ID, a (display) name, annotations, and a list context.
 *
 * <p><img
 * src="https://confluence.netflix.com/download/attachments/52216234/APILolomoService.class.png"
 * alt="Class Diagram: APILolomoService">
 */
public interface APILolomoList<T extends APIEntity> extends APIList<T>, APIEntity {
  enum ListContext {
    home,
    @Deprecated
    criticsPicks,
    @Deprecated
    expiringSoon,
    @Deprecated
    device,
    @Deprecated
    oldGenreRecs,
    @Deprecated
    genres,
    kids,
    tvHome,
    continueWatching,
    tvHero,
    popularMovies,
    queue,
    rateMovies,
    recommendations,
    topTen,
    watchAgain,
    featuredPicks,
    holiday,
    character,
    watchNext,
    contentSource,
    recentlyWatched,
    socialPopular,
    socialFriendsRecentlyWatched,
    socialFriendsHighlyRatedPlus,
    socialFriendsRecentOverlap,
    socialComments,
    socialCommentsCommented,
    socialCommentsNonCommented,
    socialCommentsPopular,
    u2uPopular,
    popularTitles,
    popularTvTitles,
    popularMovieTitles,
    billboard,
    postcard,
    bluRay,
    subtitles,
    hd,
    watchNow,
    oldGenre,
    genre,
    similars,
    time,
    newRelease,
    localFavs,
    popularCharacters,
    facet,
    recentlyAdded,
    recentlyAired,
    unknown,
    nonMember,
    search,
    social,
    suggestions,
    tvshows,
    episodes,
    hierarchicalnewreleases,
    threeD,
    genrenewrelease,
    genrerecommendation,
    hierarchicalJfk,
    genresuggestions,
    randomRecommendations,
    actorBar,
    actorPivot,
    actorRow,
    snacks,
    socialFriendLike,
    socialHiddenGems,
    socialFriendHighlyRated,
    socialGenresMashup,
    socialTrending,
    becauseYouAdded,
    becauseYouLiked,
    becauseYouSearched,
    newReleaseMovies,
    recentActivity,
    recentlyAddedMovies,
    recentlyAddedTv,
    ultraHD,
    badMovies,
    maxTalkingUi,
    onrampVideos,
    trendingNow,
    netflixOriginals,
    contentRefreshRecentlyAdded,
    interactiveDiscovery, // Test 5556
    originalsCharacter,
    socialProviderRatings,
    socialProviderViewingHistory,
    socialRecommendations,
    originalPostPlay,
    characterVideos,
    categories,
    suggestionsForYouGallery,
    contextualRecommendations,
    aprilFools2015,
    aprilFools2015Recommendations,
    kidsBatchPostplay,
    kidsOriginalsMoment,
    kidsOriginalsMomentMidSeason,
    spokenAudio,
    assistiveAudio,
    recapPrePlay,
    originalPrePlay,
    contentPreview,
    instantPlay,
    contentRefresh,
    inlinePromotionVideo,
    expandedPromotionVideo,
    characterData,
    episodicTeaser,
    cwDiscovery,
    instructionalVideoForWP,
    netflixOriginalsCollection,
    downloadable, // https://stash.corp.netflix.com/projects/MAP/repos/map/commits/13d3612d18efba8a8045727ee472c8eb16ee2852#datamodel/src/main/java/com/netflix/map/annotation/MapAnnotationConstants.java
    mock, // https://jira.netflix.com/browse/EDGE-3573
    bigRow,
    promoRow,
    originalsPreRoll,
    trailers,
    playlist,
    popularEpisodes,
    randomEpisodes, // https://jira.netflix.com/browse/DNA-805
    mobileTrailers,
    thirtySecondPreview,
    mixes,
    watchlist,
    interactiveOriginalsCollectibles, // https://jira.netflix.com/browse/EDGE-4136
    comingSoon,
    kidsAgingUp,
    shortForm, // https://jira.netflix.com/browse/MAP-3947
    embedded,
    watchathon,
    recentlyUsedIcons,
    legacyIcons,
    videoIcons,
    catalog,
    newContents,
    netflixOriginalsGenre,
    flashBack,
    extras,
    windowedComingSoon,
    windowedNewReleases,
    downloads,
    hiddenGems,
    predictedThumbsUp,
    clips,
    showAsARow,
    mostWatched,
    newThisWeek,
    myNetflix,
    mobileCollections,
    titleGroup,
    yourFavorites,
    watchNowGallery,
    myEntertainment,
    downloadsForYou,
    personalizedCategories,
    partnerExclusive,
    categoryCravers,
    evergreenCollection,
    screensaver,
    bulkRater,
    kidsFavorites,
    netflixRecommendations,
    favoriteTitles,
    reminders,
    trifecta,
    cancelFlow,
    rewatcher,
    popularOnNetflix,
    fastLaughs,
    mostThumbed,
    splashScreen,
    topRewatchPicks,
    popularGames,
    basedOnLove,
    gamesBillboard,
    gamesGenre,
    gamesTrailer,
    readyToPlay,
    watchNextGallery,
    yourDailyPicks,
    quickDiscovery,
    featureEducation,
    containerPageEvidence,
    catalogFilters,
    mostBinged,
    mostRewatched,
    staffPicks,
    sameCast,
    socialProof,
    talentVideos,
    pageVideoContainerEvidence,
    topSearches,
    talents,
    riskAversePivots,
    localContentMosaic,
    blastFromThePast,
    localLanguageMosaic,
    movieLoversPoster,
    posterControl,
    basedOnRecentlyInteracted,
    recentlyBrowsed,
    becauseYouSelected,
    guidedPivots,
    tudum
  }

  /**
   * Indicates the "type" of this list, for instance recentlyWatched row, playlist row, genre row,
   * etc. Clients can do some special processing (different rendering) based on this context, for
   * instance refresh rows more frequently if they are recentlyWatched or playlist, show social
   * aspects (friend images) for social rows, etc.
   *
   * @return an enumeration value indicating the context (type) of this row. May be null; root
   *     lolomo row has a null context.
   */
  Single<ListContext> getContext();
}
