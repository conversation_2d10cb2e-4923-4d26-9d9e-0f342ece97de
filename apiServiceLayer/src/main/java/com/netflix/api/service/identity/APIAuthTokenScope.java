package com.netflix.api.service.identity;

public enum APIAuthTokenScope {
  UNRECOGNIZED(-1),
  UNKNOWN_SCOPE(0),
  /** denotes default access, i.e. the token access is not limited. */
  DEFAULT(1),
  /** Allows the user to change the current password on the account */
  CHANGE_PASSWORD(2),
  /** Allows the user to proceed without verifying the current password. E.g. forgot password. */
  BYPASS_CURRENT_PASSWORD_CHALLENGE(3),
  /**
   * Allows the Netflix streaming app on Android platform to use the ALT minted by the stub app to
   * log the user in specifically using the MSL user auth.
   */
  MSL_LOGIN_ANDROID_SIGNUP_BRIDGE(4),
  /** Freemium customer can upgrade via an in-app link */
  FREEMIUM_UPGRADE_VIA_INAPP_LINK(5),
  /** Freemium customer can upgrade via email */
  FREEMIUM_UPGRADE_VIA_EMAIL(6),
  /** Allows the user to verify the account email. */
  EMAIL_VERIFICATION(7),
  /**
   * For MHU project, issue a nudge for account owner to access a webpage which shows the active OTP
   * challenge.
   */
  ACCESS_OTP_WEBPAGE(8),
  /** Turn on the profile transfer feature, or enable it instantly. */
  ENABLE_PROFILE_TRANSFER(9),
  /** Disable the profile transfer feature. */
  DISABLE_PROFILE_TRANSFER(10),
  /** Callback URL for banks to redirect back to confirm payment has been processed. */
  WEB_PAYMENTS_CALLBACK(11),
  /** Payment replays a request with ALT to handle multiple callbacks issued from bank. */
  WEB_PAYMENTS_REPLAY(12),
  /** Transfers the payer's session from TV and other places to web in MHU persuasion flow. */
  MHU_PERSUASION_PAYER_SESSION_TRANSFER(13),
  /** Transfers the borrower's session from TV and other places to web in MHU persuasion flow. */
  MHU_PERSUASION_BORROWER_SESSION_TRANSFER(14),
  /** Creates a beneficiary account in MHU addon flow. */
  MHU_ADDON_BENEFICIARY_ACCOUNT_CREATION(15),
  /** Signs out of all devices. */
  SIGN_OUT_OF_ALL_DEVICES(16),
  /**
   * This token can/should be used by an OCGA, and there should be corresponding OCGA claims
   * present.
   */
  OCGA_USAGE(17),
  /**
   * Transfer the session from mobile app to web for rejoin members to select a different pay plan.
   */
  REJOIN_RECHARGE_PLAN_ON_WEB(18),
  /** Transfers the session from TV to to web for updating payment. */
  TVUI_PAYMENT_UPDATE_SESSION_TRANSFER(19),
  /** Starts the process to verify new TV is in the same home as the streamable TV. */
  MHU_HOME_MISDETECTION_VERIFY_TV(20),
  /** Transfers the session from email to to web for updating household. */
  MHU_HOUSEHOLD_UPDATE_SESSION_TRANSFER(21),
  /** Starts a customer support session via in-app link in browser. */
  CUSTOMER_SUPPORT_VIA_INAPP_LINK(22),
  /** Launches account lite UI in a webview from the mobile apps. */
  ACCOUNT_LITE_SESSION_TRANSFER(23),
  /** Starts a web session to allow user to change plan. */
  CHANGE_PLAN_VIA_INAPP_LINK(24),
  /** Transfers the session from TV to to web for changing plan. */
  CHANGE_PLAN_SESSION_TRANSFER(25),
  /** Sets up the beneficiary password in the MHU invite borrower flow. */
  SET_BENEFICIARY_PASSWORD(26),
  /** deprecating to change name to MHU_UPDATE_PRIMARY_LOCATION. */
  MHU_EBI_SESSION_CONTINUITY(27),
  /** Allows user to manage the primary location to continue TVUI session */
  MHU_UPDATE_PRIMARY_LOCATION(28),
  /**
   * Logs a user in on an upgraded device based on the credentials transferred from the old device
   */
  MOBILE_DEVICE_UPGRADE(29),
  /** Confirms isolated set-top box devices for MHU. */
  MHU_CONFIRM_ISOLATED_SET_TOP_BOX(30),
  /**
   * Launches a browser session from the mobile apps to allow users to kick off the password reset
   * flow.
   */
  FORGET_PASSWORD_SESSION_TRANSFER(31),
  /**
   * Launches a browser session from the mobile apps to allow users to reset their profile lock PIN.
   */
  PROFILE_LOCK_RESET_SESSION_TRANSFER(32),
  /**
   * Allows an android user to signup using an in-app token (which they copy to the clipboard) or a
   * token delivered to them via SMS.
   */
  ANDROID_SIGNUP_VIA_SMS_OR_IN_APP_LINK(33),
  /** Allows an android user to signup using a token delivered to them via email. */
  ANDROID_SIGNUP_VIA_EMAIL(34),
  /**
   * Allows a mobile app user to start a browser session to confirm their email, phone number,
   * payment using an in-app token.
   */
  MHU_CONFIRM_ACCOUNT_VIA_INAPP_LINK(35),
  /**
   * Allows legacy Windows app users to login to the new PWA app. See:
   * https://docs.google.com/document/d/1i_zirlK5xO3tbK6b0EjilVgJRB46w_IJPMtY7QaiYv8/edit
   */
  WINDOWS_PWA_MIGRATION(36),
  /**
   * Allows a mobile app user to start a browser session to upgrade plan to support concurrent stream. See:
   *  https://docs.google.com/document/d/1jGoLPCyZyC62LwnPmXJl9yzyT0wznQ_AgE7XFSsEArs/edit
   */
  ANDROID_CONCURRENT_STREAMS_UPSELL_VIA_INAPP_LINK(48);

  private final int value;

  APIAuthTokenScope(int value) {
    this.value = value;
  }

  public int getValue() {
    return this.value;
  }
}
