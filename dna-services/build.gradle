apply plugin: 'netflix.lombok'

dependencies {
    // DO NOT ADD NON-PROJECT DEPENDENCIES HERE
    implementation project(':api-service-layer')
    implementation project(":api-global-dependencies")

    testImplementation(platform("org.junit:junit-bom:${junitBomVersion}"))
    testImplementation("org.junit.jupiter:junit-jupiter")
    testImplementation("junit:junit")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    testImplementation("org.mockito:mockito-core")
    testImplementation("com.netflix.microcontext:microcontext-test:latest.release")
}

tasks.withType(Test).configureEach { 
    useJUnitPlatform()
}

apply from: file('../dependency-properties.gradle')
apply from: file('../dependency-overrides.gradle')


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring:spring-boot-netflix-bom:latest.release'
}
