package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class GpsGameInput {
  private GpsInstalledGamesInfo installedGamesInfo;

  public GpsInstalledGamesInfo getInstalledGamesInfo() {
    return installedGamesInfo;
  }

  public GpsGameInput setInstalledGamesInfo(GpsInstalledGamesInfo installedGamesInfo) {
    this.installedGamesInfo = installedGamesInfo;
    return this;
  }
}
