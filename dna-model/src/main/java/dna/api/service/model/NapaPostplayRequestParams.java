package dna.api.service.model;

import com.netflix.nes.section.protogen.SectionKind;
import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaPostplayRequestParams {
  /** Viewable watched by visitor */
  private Integer watchedVideoId;

  /** Number of recommendations desired in END Postplay scenarios (default is 3) */
  private Integer recommendationsLimit;

  /**
   * Track Id pertaining to where play was originated from to allow for Postplay to differentiate
   * responses based on play location
   */
  private Integer trackId;

  /**
   * Videos and Sections shown to the user in this postplay session, as defined by the client.
   * Session shoudl be cleared upong visitor leaving postplay (i.e. going back to lolomo)
   */
  private List<NapaSessionHistoryEntry> sessionHistory;

  /** NES-based sections supported by client */
  private List<SectionKind> supportedSectionKinds;

  /** Postplay Features supported by client */
  private List<NapaPostPlayFeatures> supportedFeatures;

  public Integer getWatchedVideoId() {
    return watchedVideoId;
  }

  public Integer getRecommendationsLimit() {
    return recommendationsLimit;
  }

  public Integer getTrackId() {
    return trackId;
  }

  public List<NapaSessionHistoryEntry> getSessionHistory() {
    return sessionHistory;
  }

  public List<SectionKind> getSupportedSectionKinds() {
    return supportedSectionKinds;
  }

  public List<NapaPostPlayFeatures> getSupportedFeatures() {
    return supportedFeatures;
  }

  public NapaPostplayRequestParams setWatchedVideoId(Integer watchedVideoId) {
    this.watchedVideoId = watchedVideoId;
    return this;
  }

  public NapaPostplayRequestParams setRecommendationsLimit(Integer recommendationsLimit) {
    this.recommendationsLimit = recommendationsLimit;
    return this;
  }

  public NapaPostplayRequestParams setTrackId(Integer trackId) {
    this.trackId = trackId;
    return this;
  }

  public NapaPostplayRequestParams setSessionHistory(List<NapaSessionHistoryEntry> sessionHistory) {
    this.sessionHistory = sessionHistory;
    return this;
  }

  public NapaPostplayRequestParams setSupportedSectionKinds(
      List<SectionKind> supportedSectionKinds) {
    this.supportedSectionKinds = supportedSectionKinds;
    return this;
  }

  public NapaPostplayRequestParams setSupportedFeatures(
      List<NapaPostPlayFeatures> supportedFeatures) {
    this.supportedFeatures = supportedFeatures;
    return this;
  }
}
