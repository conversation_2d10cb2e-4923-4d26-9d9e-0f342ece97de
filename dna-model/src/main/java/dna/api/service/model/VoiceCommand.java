package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.vps.APIVoiceCommand */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class VoiceCommand {
  private VoiceCommandType commandType;

  private VoiceIntent intent;

  private VoiceResource resource;

  private Map<String, String> additionalAttributes;

  public VoiceCommandType getCommandType() {
    return commandType;
  }

  public VoiceIntent getIntent() {
    return intent;
  }

  public VoiceResource getResource() {
    return resource;
  }

  public Map<String, String> getAdditionalAttributes() {
    return additionalAttributes;
  }

  public VoiceCommand setCommandType(VoiceCommandType commandType) {
    this.commandType = commandType;
    return this;
  }

  public VoiceCommand setIntent(VoiceIntent intent) {
    this.intent = intent;
    return this;
  }

  public VoiceCommand setResource(VoiceResource resource) {
    this.resource = resource;
    return this;
  }

  public VoiceCommand setAdditionalAttributes(Map<String, String> additionalAttributes) {
    this.additionalAttributes = additionalAttributes;
    return this;
  }
}
