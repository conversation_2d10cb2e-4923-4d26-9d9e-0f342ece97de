package com.netflix.api.platform.util;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.netflix.mantis.publish.api.MantisPublishContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Mantis {
  private static final Logger LOG = LoggerFactory.getLogger(Mantis.class);
  private static final JsonFormat.Printer JSON =
      JsonFormat.printer().omittingInsignificantWhitespace();

  private Mantis() {}

  /**
   * Adds a JSON string representing msg on the Mantis event at the name.
   *
   * @param name the key on the event
   * @param msg the protobuf message (or builder) to serialize as a JSON string.
   */
  public static void addJSON(String name, MessageOrBuilder msg) {
    try {
      MantisPublishContext.getCurrent().add(name, JSON.print(msg));
    } catch (InvalidProtocolBufferException e) {
      LOG.error("transforming proto to json name={}", name, e);
    }
  }
}
