package com.netflix.api.service.plan;

import com.netflix.api.dependencies.subscriber.AccountProfileHandler;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.identity.APIPlanInfo;
import com.netflix.i18n.NFLocale;
import com.netflix.lang.RequestVariable;
import com.netflix.memberprice.protogen.GetMemberPriceInfoRequest;
import com.netflix.memberprice.protogen.MemberPriceServiceGrpc.MemberPriceServiceStub;
import com.netflix.membership.skuservice.protogen.GetPlanRequest;
import com.netflix.membership.skuservice.protogen.Plan;
import com.netflix.membership.skuservice.protogen.SkuServiceGrpc.SkuServiceStub;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
public class APIPlanInfoFactory {

  private final AccountProfileHandler accountProfileHandler;
  private final SkuServiceStub skuServiceStub;
  private final MemberPriceServiceStub memberPriceServiceStub;
  private final RequestVariable<Observable<Plan>> planCache = new RequestVariable<>();
  private final RequestVariable<Observable<APIAccountPlanInfo>> accountPlanCache =
      new RequestVariable<>();

  @Autowired
  public APIPlanInfoFactory(
      AccountProfileHandler accountProfileHandler,
      @GrpcSpringClient("skuservice") SkuServiceStub skuServiceStub,
      @GrpcSpringClient("memberprice") MemberPriceServiceStub memberPriceServiceStub) {
    this.accountProfileHandler = accountProfileHandler;
    this.skuServiceStub = skuServiceStub;
    this.memberPriceServiceStub = memberPriceServiceStub;
  }

  public Observable<Plan> getPlan(long planId) {
    Observable<Plan> o = planCache.get();
    if (o == null) {
      o =
          RxObservable.defer(
                  skuServiceStub::getPlan,
                  GetPlanRequest.newBuilder()
                      .setId(planId)
                      .setLocale(RequestContextWrapper.get().getLocale())
                      .build())
              .cache();
      planCache.set(o);
    }
    return o;
  }

  public Observable<PlanData> getPlanData(AccountProfileRemote accountProfile) {
    final Long planId = accountProfile.getBoxedPlanId();
    if (planId == null || planId == 0) {
      return Observable.empty();
    }
    return getPlan(planId).map(plan -> new PlanDataSku(planId, plan));
  }

  public Observable<APIPlanInfo> create(AccountProfileRemote accountProfile) {
    if (accountProfile == null) {
      return Observable.empty();
    }

    final Long planId = accountProfile.getBoxedPlanId();
    if (planId == null || planId == 0) {
      return Observable.empty();
    }
    return getPlan(planId).map(plan -> new APIPlanInfoImpl(planId, new PlanDataSku(planId, plan)));
  }

  public Observable<APISubscriptionData> getMemberData(long accountOwnerId) {
    return accountProfileHandler.getMemberData(accountOwnerId).map(APISubscriptionDataImpl::new);
  }

  public Observable<APIAccountPlanInfo> getAccountPlanInfo(AccountProfileRemote accountProfile) {
    if (accountProfile == null || accountProfile.hasAccountOwnerId()) {
      return Observable.error(new IllegalArgumentException("Must provide valid user"));
    }
    Observable<APIAccountPlanInfo> o = accountPlanCache.get();
    if (o == null) {
      o =
          RxObservable.defer(
                  memberPriceServiceStub::getPriceInfo,
                  GetMemberPriceInfoRequest.newBuilder()
                      .setCustomerId(accountProfile.getBoxedAccountOwnerId())
                      .setLocale(getLocale(accountProfile))
                      .setNglVersion("NGL_LATEST_RELEASE")
                      .build())
              .map(
                  response ->
                      new APIAccountPlanInfo(
                          convert(response.getPriceFormatted()),
                          convert(response.getNextPrice().getPriceFormatted())));
      accountPlanCache.set(o);
    }
    return o;
  }

  private static String getLocale(AccountProfileRemote accountProfile) {
    return accountProfile
        .getOptionalPrimaryLang()
        .map(NFLocale::createFromString)
        .map(NFLocale::getId)
        .orElse(NFLocale.getAvailableLocalesForScope().getFirst().getId());
  }

  private static String convert(String price) {
    return price == null || price.isEmpty() ? null : price;
  }
}
