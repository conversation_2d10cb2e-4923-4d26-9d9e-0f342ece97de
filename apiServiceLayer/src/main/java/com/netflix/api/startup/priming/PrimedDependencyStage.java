package com.netflix.api.startup.priming;

import com.google.inject.ImplementedBy;
import com.google.inject.Provider;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

@ImplementedBy(PrimedDependency.class)
public interface PrimedDependencyStage<T> extends Provider<T>, CompletionStage<T> {

  /**
   * Returns a new FutureProvider that, when this stage completes normally, is executed with this
   * stage's result as the argument to the supplied function.
   *
   * <p>See the {@link PrimedDependencyStage} documentation for rules covering exceptional
   * completion.
   *
   * @param fn the function to use to compute the value of the returned FutureProvider
   * @param <U> the function's return type
   * @return the new FutureProvider
   */
  <U> PrimedDependencyStage<U> thenApply(Function<? super T, ? extends U> fn);

  /**
   * Returns a new FutureProvider that, when this stage completes normally, is executed using this
   * stage's default asynchronous execution facility, with this stage's result as the argument to
   * the supplied function.
   *
   * <p>See the {@link PrimedDependencyStage} documentation for rules covering exceptional
   * completion.
   *
   * @param fn the function to use to compute the value of the returned FutureProvider
   * @param <U> the function's return type
   * @return the new FutureProvider
   */
  <U> PrimedDependencyStage<U> thenApplyAsync(Function<? super T, ? extends U> fn);

  /**
   * Returns a new FutureProvider that, when this stage completes normally, is executed using the
   * supplied Executor, with this stage's result as the argument to the supplied function.
   *
   * <p>See the {@link PrimedDependencyStage} documentation for rules covering exceptional
   * completion.
   *
   * @param fn the function to use to compute the value of the returned FutureProvider
   * @param executor the executor to use for asynchronous execution
   * @param <U> the function's return type
   * @return the new FutureProvider
   */
  <U> PrimedDependencyStage<U> thenApplyAsync(
      Function<? super T, ? extends U> fn, Executor executor);

  /**
   * Returns a new FutureProvider that, when this stage completes normally, is executed with this
   * stage's result as the argument to the supplied action.
   *
   * <p>See the {@link PrimedDependencyStage} documentation for rules covering exceptional
   * completion.
   *
   * @param action the action to perform before completing the returned FutureProvider
   * @return the new FutureProvider
   */
  PrimedDependencyStage<Void> thenAccept(Consumer<? super T> action);

  /**
   * Returns a new FutureProvider that, when this stage completes normally, is executed using this
   * stage's default asynchronous execution facility, with this stage's result as the argument to
   * the supplied action.
   *
   * <p>See the {@link PrimedDependencyStage} documentation for rules covering exceptional
   * completion.
   *
   * @param action the action to perform before completing the returned FutureProvider
   * @return the new FutureProvider
   */
  PrimedDependencyStage<Void> thenAcceptAsync(Consumer<? super T> action);

  /**
   * Returns a new FutureProvider that, when this stage completes normally, is executed using the
   * supplied Executor, with this stage's result as the argument to the supplied action.
   *
   * <p>See the {@link PrimedDependencyStage} documentation for rules covering exceptional
   * completion.
   *
   * @param action the action to perform before completing the returned FutureProvider
   * @param executor the executor to use for asynchronous execution
   * @return the new FutureProvider
   */
  PrimedDependencyStage<Void> thenAcceptAsync(Consumer<? super T> action, Executor executor);

  /**
   * Returns a new FutureProvider that, when this stage completes normally, executes the given
   * action.
   *
   * <p>See the {@link PrimedDependencyStage} documentation for rules covering exceptional
   * completion.
   *
   * @param action the action to perform before completing the returned FutureProvider
   * @return the new FutureProvider
   */
  PrimedDependencyStage<Void> thenRun(Runnable action);

  /**
   * Returns a new FutureProvider that, when this stage completes normally, executes the given
   * action using this stage's default asynchronous execution facility.
   *
   * <p>See the {@link PrimedDependencyStage} documentation for rules covering exceptional
   * completion.
   *
   * @param action the action to perform before completing the returned FutureProvider
   * @return the new FutureProvider
   */
  PrimedDependencyStage<Void> thenRunAsync(Runnable action);

  /**
   * Returns a new FutureProvider that, when this stage completes normally, executes the given
   * action using the supplied Executor.
   *
   * <p>See the {@link PrimedDependencyStage} documentation for rules covering exceptional
   * completion.
   *
   * @param action the action to perform before completing the returned FutureProvider
   * @param executor the executor to use for asynchronous execution
   * @return the new FutureProvider
   */
  PrimedDependencyStage<Void> thenRunAsync(Runnable action, Executor executor);

  /**
   * Returns a new FutureProvider that, when this stage completes normally, is executed with this
   * stage as the argument to the supplied function.
   *
   * <p>See the {@link PrimedDependencyStage} documentation for rules covering exceptional
   * completion.
   *
   * @param fn the function returning a new FutureProvider
   * @param <U> the type of the returned FutureProvider's result
   * @return the FutureProvider
   */
  <U> PrimedDependencyStage<U> thenCompose(Function<? super T, ? extends CompletionStage<U>> fn);

  /**
   * Returns a new FutureProvider that, when this stage completes normally, is executed using this
   * stage's default asynchronous execution facility, with this stage as the argument to the
   * supplied function.
   *
   * <p>See the {@link PrimedDependencyStage} documentation for rules covering exceptional
   * completion.
   *
   * @param fn the function returning a new FutureProvider
   * @param <U> the type of the returned FutureProvider's result
   * @return the FutureProvider
   */
  <U> PrimedDependencyStage<U> thenComposeAsync(
      Function<? super T, ? extends CompletionStage<U>> fn);

  /**
   * Returns a new FutureProvider that, when this stage completes normally, is executed using the
   * supplied Executor, with this stage's result as the argument to the supplied function.
   *
   * <p>See the {@link PrimedDependencyStage} documentation for rules covering exceptional
   * completion.
   *
   * @param fn the function returning a new FutureProvider
   * @param executor the executor to use for asynchronous execution
   * @param <U> the type of the returned FutureProvider's result
   * @return the FutureProvider
   */
  <U> PrimedDependencyStage<U> thenComposeAsync(
      Function<? super T, ? extends CompletionStage<U>> fn, Executor executor);

  /**
   * Returns a new FutureProvider that, when this stage completes exceptionally, is executed with
   * this stage's exception as the argument to the supplied function. Otherwise, if this stage
   * completes normally, then the returned stage also completes normally with the same value.
   *
   * @param fn the function to use to compute the value of the returned FutureProvider if this
   *     FutureProvider completed exceptionally
   * @return the new FutureProvider
   */
  PrimedDependencyStage<T> exceptionally(Function<Throwable, ? extends T> fn);

  /**
   * Returns a new FutureProvider with the same result or exception as this stage, that executes the
   * given action when this stage completes.
   *
   * <p>When this stage is complete, the given action is invoked with the result (or {@code null} if
   * none) and the exception (or {@code null} if none) of this stage as arguments. The returned
   * stage is completed when the action returns. If the supplied action itself encounters an
   * exception, then the returned stage exceptionally completes with this exception unless this
   * stage also completed exceptionally.
   *
   * @param action the action to perform
   * @return the new FutureProvider
   */
  PrimedDependencyStage<T> whenComplete(BiConsumer<? super T, ? super Throwable> action);

  /**
   * Returns a new FutureProvider with the same result or exception as this stage, that executes the
   * given action using this stage's default asynchronous execution facility when this stage
   * completes.
   *
   * <p>When this stage is complete, the given action is invoked with the result (or {@code null} if
   * none) and the exception (or {@code null} if none) of this stage as arguments. The returned
   * stage is completed when the action returns. If the supplied action itself encounters an
   * exception, then the returned stage exceptionally completes with this exception unless this
   * stage also completed exceptionally.
   *
   * @param action the action to perform
   * @return the new FutureProvider
   */
  PrimedDependencyStage<T> whenCompleteAsync(BiConsumer<? super T, ? super Throwable> action);

  /**
   * Returns a new FutureProvider with the same result or exception as this stage, that executes the
   * given action using the supplied Executor when this stage completes.
   *
   * <p>When this stage is complete, the given action is invoked with the result (or {@code null} if
   * none) and the exception (or {@code null} if none) of this stage as arguments. The returned
   * stage is completed when the action returns. If the supplied action itself encounters an
   * exception, then the returned stage exceptionally completes with this exception unless this
   * stage also completed exceptionally.
   *
   * @param action the action to perform
   * @param executor the executor to use for asynchronous execution
   * @return the new FutureProvider
   */
  PrimedDependencyStage<T> whenCompleteAsync(
      BiConsumer<? super T, ? super Throwable> action, Executor executor);

  /**
   * Returns a new FutureProvider that, when this stage completes either normally or exceptionally,
   * is executed with this stage's result and exception as arguments to the supplied function.
   *
   * <p>When this stage is complete, the given function is invoked with the result (or {@code null}
   * if none) and the exception (or {@code null} if none) of this stage as arguments, and the
   * function's result is used to complete the returned stage.
   *
   * @param fn the function to use to compute the value of the returned FutureProvider
   * @param <U> the function's return type
   * @return the new FutureProvider
   */
  <U> PrimedDependencyStage<U> handle(BiFunction<? super T, Throwable, ? extends U> fn);

  /**
   * Returns a new FutureProvider that, when this stage completes either normally or exceptionally,
   * is executed using this stage's default asynchronous execution facility, with this stage's
   * result and exception as arguments to the supplied function.
   *
   * <p>When this stage is complete, the given function is invoked with the result (or {@code null}
   * if none) and the exception (or {@code null} if none) of this stage as arguments, and the
   * function's result is used to complete the returned stage.
   *
   * @param fn the function to use to compute the value of the returned FutureProvider
   * @param <U> the function's return type
   * @return the new FutureProvider
   */
  <U> PrimedDependencyStage<U> handleAsync(BiFunction<? super T, Throwable, ? extends U> fn);

  /**
   * Returns a new FutureProvider that, when this stage completes either normally or exceptionally,
   * is executed using the supplied executor, with this stage's result and exception as arguments to
   * the supplied function.
   *
   * <p>When this stage is complete, the given function is invoked with the result (or {@code null}
   * if none) and the exception (or {@code null} if none) of this stage as arguments, and the
   * function's result is used to complete the returned stage.
   *
   * @param fn the function to use to compute the value of the returned FutureProvider
   * @param executor the executor to use for asynchronous execution
   * @param <U> the function's return type
   * @return the new FutureProvider
   */
  <U> PrimedDependencyStage<U> handleAsync(
      BiFunction<? super T, Throwable, ? extends U> fn, Executor executor);

  /**
   * Returns a {@link CompletableFuture} maintaining the same completion properties as this stage.
   * If this stage is already a CompletableFuture, this method may return this stage itself.
   * Otherwise, invocation of this method may be equivalent in effect to {@code thenApply(x -> x)},
   * but returning an instance of type {@code CompletableFuture}. A FutureProvider implementation
   * that does not choose to interoperate with others may throw {@code
   * UnsupportedOperationException}.
   *
   * @return the CompletableFuture
   * @throws UnsupportedOperationException if this implementation does not interoperate with
   *     CompletableFuture
   */
  CompletableFuture<T> toCompletableFuture();
}
