package com.netflix.api.service.identity;

import com.netflix.api.annotations.ExcludeFromCoverage;
import com.netflix.api.service.APIServiceException;

/** Represents an authentication exception (HTTP 401). */
public class APIAuthorizationException extends APIServiceException {

  private static final long serialVersionUID = -135325636274352199L;

  private final AuthorizationExceptionCause cause;

  /** FIXME FIXME FIXME. */
  @ExcludeFromCoverage
  public enum AuthorizationExceptionCause {
    NO_USER_CREDENTIALS,
    AUTHENTICATION_FAILURE,
    NO_ACCOUNT_ASSOCIATION,
    TOKEN_CREATION_FAILURE,
    TOKEN_RENEWAL_FAILURE,
    INVALID_TOKEN,
    INVALID_CUSTOMER,
    DEVICE_NOT_ASSOCIATED_WITH_ACCOUNT,
    UNKNOWN_MVPD_PARTNER,
    MSL_VALIDATION_FAILURE,
    TOKEN_SCOPE_NOT_SUPPLIED
  }

  /**
   * <PERSON>IXME FIXME FIXME.
   *
   * @param cause FIXME FIXME FIXME
   * @param message FIXME FIXME FIXME
   */
  public APIAuthorizationException(AuthorizationExceptionCause cause, String message) {
    super(message);
    this.cause = cause;
  }

  /**
   * FIXME FIXME FIXME.
   *
   * @param cause FIXME FIXME FIXME
   * @param message FIXME FIXME FIXME
   * @param e FIXME FIXME FIXME
   */
  public APIAuthorizationException(AuthorizationExceptionCause cause, String message, Throwable e) {
    super(message, e);
    this.cause = cause;
  }

  /**
   * FIXME FIXME FIXME.
   *
   * @return FIXME FIXME FIXME
   */
  public AuthorizationExceptionCause getAuthorizationExceptionCause() {
    return cause;
  }
}
