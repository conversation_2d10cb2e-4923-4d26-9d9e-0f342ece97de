package com.netflix.api.service;

import java.util.HashMap;
import java.util.Map;

/**
 * Set of valid UI flavors.
 *
 * <p>UI flavors (a misnomer) indicates the UI (server-side groovy) codebase flavor. It is used by
 * backend services to debug issues and migrate UIs to new backend features iteratively.
 *
 * <p>This list is compiled from <a href="http://go.netflix.com/clientlist">clientlist
 * spreadsheet</a>.
 */
public enum UIFlavor {
  akira,
  argo,
  atv_fuji,
  atv_hopper,
  darwin,
  ios_legacy,
  android,
  tv_other,
  windows_cougar,
  windows_gotham,
  windows_px,
  windows_wildcat,
  legacy,
  partnerCatalog,
  det;

  private static final Map<String, UIFlavor> nameMap = new HashMap<>();

  static {
    for (UIFlavor value : UIFlavor.values()) {
      nameMap.put(value.name(), value);
    }
  }

  public static UIFlavor lookup(final String name) {
    if (name != null) {
      if ("uibootmember".equalsIgnoreCase(name)) return darwin;

      if ("det".equalsIgnoreCase(name)) {
        return det;
      }

      UIFlavor value = nameMap.get(name);
      if (value != null) return value;
    }
    return legacy;
  }
}
