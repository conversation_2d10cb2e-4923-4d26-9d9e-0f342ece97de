package com.netflix.api.service.vps;

import com.netflix.api.service.identity.APIMaturity;
import java.util.List;
import java.util.Map;
import rx.Observable;
import rx.Single;

/** Voice Service - service to process voice (search) commands directed by partners to Netflix */
public interface APIVoiceService {

  /**
   * @param input Non-null input built from {@link APIVoiceRequestInput}
   * @param header Non-Null header built from {@link APIVoiceRequestHeader}
   * @param voicePayload Optional/Nullable payload built from {@link APIVoiceRequestPayload}
   * @param messageId This is expected to be Non-null. PlaceHolder while we evolve a good story
   *     around messageID, the requestId (source: requestContext) will work
   * @return Observable of {@link APIVoiceProcessingServiceResponse}
   */
  Observable<APIVoiceProcessingServiceResponse> getVoiceCommands(
      final APIVoiceRequestInput input,
      final APIVoiceRequestHeader header,
      final APIVoiceRequestPayload voicePayload,
      final String messageId);

  /**
   * Log voice command for offline analysis. See <a
   * href="https://docs.google.com/document/d/1VVUmZm9niGUSGwi3qFHBnK6xh_AaM_Q6XRUn_K0Wnps">VUI
   * Processing &amp; Utterance Logging for Partner Deep-Links</a>
   */
  Single<APIVoiceProcessingServiceResponse> logVoiceCommand(
      final APIVoiceRequestInput input,
      final APIVoiceRequestHeader header,
      final APIVoiceRequestPayload voicePayload,
      final String messageId);

  /**
   * @param commandDirective {@link APIVoiceCommandType}
   * @param payLoadVersion Non-null payloadversion
   * @return {@link APIVoiceRequestHeader} header to be used in VPS requests
   */
  APIVoiceRequestHeader createRequestHeader(
      final APIVoiceCommandType commandDirective, final String payLoadVersion);

  /**
   * @param rawTextUtterance
   * @param languageCode
   * @param profileMaturity {@link APIMaturity}
   * @param appName
   * @param odpAware
   * @return {@link APIVoiceRequestInput} input for VPS requests
   */
  APIVoiceRequestInput createRequestInput(
      final String rawTextUtterance,
      final String languageCode,
      final APIMaturity profileMaturity,
      final String appName,
      final boolean odpAware);

  /**
   * @param rawTextUtterance
   * @param languageCode
   * @param profileMaturity {@link APIMaturity}
   * @param appName
   * @param odpAware
   * @param additionalContext - This Map is available to enable experimentation
   * @return {@link APIVoiceRequestInput} input for VPS requests
   */
  APIVoiceRequestInput createRequestInput(
      final String rawTextUtterance,
      final String languageCode,
      final APIMaturity profileMaturity,
      final String appName,
      final boolean odpAware,
      final Map<String, String> additionalContext);

  /**
   * @param voiceResourceType {@link APIVoiceResourceType}
   * @param id
   * @param value
   * @return {@link APIVoiceResource} voice resource which can be part of VPS request payload {@link
   *     APIVoiceRequestPayload}
   */
  APIVoiceResource createVoiceResource(
      final APIVoiceResourceType voiceResourceType, final String id, final String value);

  /**
   * @param voiceIntentType {@link APIVoiceIntentType}
   * @param id
   * @param value
   * @return {@link APIVoiceIntent} voice intent which can be part of VPS request payload {@link
   *     APIVoiceRequestPayload}
   */
  APIVoiceIntent createVoiceIntent(
      final APIVoiceIntentType voiceIntentType, final String id, final String value);

  /**
   * @param resources list of {@link APIVoiceResource}
   * @param intents list of {@link APIVoiceIntent}
   * @return {@link APIVoiceRequestPayload} request payload for VPS requests
   */
  APIVoiceRequestPayload createRequestPayload(
      final List<APIVoiceResource> resources, final List<APIVoiceIntent> intents);
}
