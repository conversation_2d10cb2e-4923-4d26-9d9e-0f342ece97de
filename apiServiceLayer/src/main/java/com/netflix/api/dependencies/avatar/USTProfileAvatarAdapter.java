package com.netflix.api.dependencies.avatar;

import com.netflix.api.adapters.StreamingClientServiceAdapter;
import com.netflix.api.grpc.GrpcCallHelpers.RxSingle;
import com.netflix.api.service.abtest.ABAdapterBase;
import com.netflix.ust.adapters.USTStatus;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Single;
import ust.images.v1.Image;
import ust.profile.avatar.v1.AvatarResult;
import ust.profile.avatar.v1.AvatarSpec;
import ust.profile.avatar.v1.GetAvatarRequest;
import ust.profile.avatar.v1.ProfileAvatarAccess;

@Component
public class USTProfileAvatarAdapter {

  private final ABAdapterBase abAdapterBase;
  private final StreamingClientServiceAdapter streamingClientServiceAdapter;
  private final ProfileAvatarAccess profileAvatarAccess;

  @Autowired
  public USTProfileAvatarAdapter(
      ABAdapterBase abAdapterBase,
      StreamingClientServiceAdapter streamingClientServiceAdapter,
      ProfileAvatarAccess profileAvatarAccess) {
    this.abAdapterBase = abAdapterBase;
    this.streamingClientServiceAdapter = streamingClientServiceAdapter;
    this.profileAvatarAccess = profileAvatarAccess;
  }

  public Single<Map<AvatarSpec, Image>> getAvatars(
      Collection<AvatarSpec> avatarSpecs, AvatarCriteria criteria) {

    // Calling AB will populate Microcontext with AB allocations
    return Single.zip(
            abAdapterBase.experimentationContext(),
            streamingClientServiceAdapter.getHostName(),
            (experimentationContext, ocaHostName) -> ocaHostName) // ignore experimentationContext
        .flatMap(
            ocaHostName ->
                RxSingle.defer(
                        profileAvatarAccess::getAvatar,
                        GetAvatarRequest.newBuilder()
                            .addAllSpecs(avatarSpecs)
                            .setSize(criteria.getDimensions())
                            .setSecure(criteria.isSecure())
                            .setTopAligned(criteria.isTopAligned())
                            .setSupportsLocalizedKidsProfile(
                                criteria.isSupportsLocalizedKidsProfile())
                            .addAllRecipes(
                                criteria.getRecipePreferences() == null
                                    ? Collections.emptyList()
                                    : criteria.getRecipePreferences())
                            .setOcaHostName(ocaHostName)
                            .build())
                    .map(
                        reply ->
                            reply.getResultsList().stream()
                                .filter(result -> USTStatus.isOk(result.getStatus()))
                                .collect(
                                    Collectors.toMap(
                                        AvatarResult::getKey,
                                        result -> result.getResult().getImage()))));
  }
}
