package dna.api.converters;

import static com.netflix.membercommerce.protogen.BillingPartnerType.BPT_BUNDLE;
import static com.netflix.membercommerce.protogen.BillingPartnerType.BPT_INT_PAY;
import static com.netflix.membercommerce.protogen.BillingPartnerType.BPT_ITUNES;
import static com.netflix.membercommerce.protogen.BillingPartnerType.BPT_NETFLIX;
import static com.netflix.membercommerce.protogen.BillingPartnerType.BPT_UNSET;
import static com.netflix.membercommerce.protogen.DevicePlatformCategory.DPC_ANDROID;
import static com.netflix.membercommerce.protogen.DevicePlatformCategory.DPC_APPLE_TV;
import static com.netflix.membercommerce.protogen.DevicePlatformCategory.DPC_CS;
import static com.netflix.membercommerce.protogen.DevicePlatformCategory.DPC_IOS;
import static com.netflix.membercommerce.protogen.DevicePlatformCategory.DPC_MEDIAROOM;
import static com.netflix.membercommerce.protogen.DevicePlatformCategory.DPC_TEST;
import static com.netflix.membercommerce.protogen.DevicePlatformCategory.DPC_TV;
import static com.netflix.membercommerce.protogen.DevicePlatformCategory.DPC_UNKNOWN;
import static com.netflix.membercommerce.protogen.DevicePlatformCategory.DPC_WEB;
import static com.netflix.membercommerce.protogen.DevicePlatformCategory.DPC_WINDOWS;
import static com.netflix.membercommerce.protogen.UpsellStreamQuality.USQ_HD;
import static com.netflix.membercommerce.protogen.UpsellStreamQuality.USQ_HD_720;
import static com.netflix.membercommerce.protogen.UpsellStreamQuality.USQ_SD;
import static com.netflix.membercommerce.protogen.UpsellStreamQuality.USQ_UHD;
import static com.netflix.membercommerce.protogen.UpsellStreamQuality.USQ_UNSET;
import static com.netflix.membercommerce.protogen.UpsellVideoResolution.UVR_FHD_1080;
import static com.netflix.membercommerce.protogen.UpsellVideoResolution.UVR_HD_720;
import static com.netflix.membercommerce.protogen.UpsellVideoResolution.UVR_SD_480;
import static com.netflix.membercommerce.protogen.UpsellVideoResolution.UVR_UHD_4K;
import static com.netflix.membercommerce.protogen.UpsellVideoResolution.UVR_UNSET;
import static dna.api.converters.MemberCommerceConverters.acceptQualityUpsellOptionRequest;
import static dna.api.converters.MemberCommerceConverters.acceptStreamsUpsellOptionRequest;
import static dna.api.converters.MemberCommerceConverters.qualityUpsellOptionRequest;
import static dna.api.converters.MemberCommerceConverters.qualityUpsellOptionResponse;
import static dna.api.converters.MemberCommerceConverters.streamsUpsellOptionRequest;
import static dna.api.converters.MemberCommerceConverters.streamsUpsellOptionResponse;
import static dna.api.service.model.MemberCommerceBillingPartnerType.BUNDLE;
import static dna.api.service.model.MemberCommerceBillingPartnerType.INT_PAY;
import static dna.api.service.model.MemberCommerceBillingPartnerType.ITUNES;
import static dna.api.service.model.MemberCommerceBillingPartnerType.NETFLIX;
import static dna.api.service.model.MemberCommerceBillingPartnerType.UNSET;
import static dna.api.service.model.MembershipStreamQuality.PRODUCT_CHOICE_STREAM_QUALITY_HD;
import static dna.api.service.model.MembershipStreamQuality.PRODUCT_CHOICE_STREAM_QUALITY_HD720;
import static dna.api.service.model.MembershipStreamQuality.PRODUCT_CHOICE_STREAM_QUALITY_NOT_SET;
import static dna.api.service.model.MembershipStreamQuality.PRODUCT_CHOICE_STREAM_QUALITY_SD;
import static dna.api.service.model.MembershipStreamQuality.PRODUCT_CHOICE_STREAM_QUALITY_UHD;
import static dna.api.service.model.UpsellDevicePlatformCategory.ANDROID;
import static dna.api.service.model.UpsellDevicePlatformCategory.APPLE_TV;
import static dna.api.service.model.UpsellDevicePlatformCategory.CUSTOMER_SUPPORT;
import static dna.api.service.model.UpsellDevicePlatformCategory.IOS;
import static dna.api.service.model.UpsellDevicePlatformCategory.MEDIAROOM;
import static dna.api.service.model.UpsellDevicePlatformCategory.TEST;
import static dna.api.service.model.UpsellDevicePlatformCategory.TV;
import static dna.api.service.model.UpsellDevicePlatformCategory.UNKNOWN;
import static dna.api.service.model.UpsellDevicePlatformCategory.WEB;
import static dna.api.service.model.UpsellDevicePlatformCategory.WINDOWS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.params.provider.Arguments.arguments;

import com.google.protobuf.Timestamp;
import com.netflix.lang.BindingContexts;
import com.netflix.membercommerce.protogen.AcceptQualityUpsellOptionRequest;
import com.netflix.membercommerce.protogen.AcceptQualityUpsellOptionResponse;
import com.netflix.membercommerce.protogen.AcceptStreamsUpsellOptionRequest;
import com.netflix.membercommerce.protogen.AcceptStreamsUpsellOptionResponse;
import com.netflix.membercommerce.protogen.BillingPartnerType;
import com.netflix.membercommerce.protogen.DevicePlatformCategory;
import com.netflix.membercommerce.protogen.GetQualityUpsellOptionRequest;
import com.netflix.membercommerce.protogen.GetQualityUpsellOptionResponse;
import com.netflix.membercommerce.protogen.GetStreamsUpsellOptionRequest;
import com.netflix.membercommerce.protogen.GetStreamsUpsellOptionResponse;
import com.netflix.membercommerce.protogen.UpsellFormattedOutput;
import com.netflix.membercommerce.protogen.UpsellFormattingInput;
import com.netflix.membercommerce.protogen.UpsellStreamQuality;
import com.netflix.membercommerce.protogen.UpsellVideoResolution;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.test.utils.TestCurrentMicrocontext;
import com.netflix.microcontext.test.utils.TestMicrocontext;
import com.netflix.type.protogen.BasicTypes.Locale;
import dna.api.service.model.AcceptConcurrentStreamsUpsellOptionRequest;
import dna.api.service.model.AcceptConcurrentStreamsUpsellOptionResponse;
import dna.api.service.model.AcceptStreamingQualityUpsellOptionRequest;
import dna.api.service.model.AcceptStreamingQualityUpsellOptionResponse;
import dna.api.service.model.ConcurrentStreamsUpsellOptionRequest;
import dna.api.service.model.ConcurrentStreamsUpsellOptionResponse;
import dna.api.service.model.MemberCommerceBillingPartnerType;
import dna.api.service.model.MembershipStreamQuality;
import dna.api.service.model.StreamingQualityUpsellOptionRequest;
import dna.api.service.model.UpsellDevicePlatformCategory;
import dna.api.service.model.UpsellOptionFormatted;
import java.util.Arrays;
import java.util.List;
import netflix.context.Context;
import netflix.context.device.DeviceContext;
import netflix.context.geo.GeoContext;
import netflix.context.geo.IpAddress;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.FieldSource;

class MemberCommerceConvertersTest {
  @BeforeEach
  void before() {
    BindingContexts.push();
  }

  @AfterEach
  void after() {
    BindingContexts.pop();
  }

  @Test
  void qualityUpsellOptionRequest_dnaReq_to_memberCommerceReq() {
    final StreamingQualityUpsellOptionRequest req =
        new StreamingQualityUpsellOptionRequest()
            .setDevicePlatformCategory(UpsellDevicePlatformCategory.WEB)
            .setQualityFilter(PRODUCT_CHOICE_STREAM_QUALITY_HD720)
            .setNglVersion("some-ngl-version");

    CurrentMicrocontext.set(
        Context.newBuilder().addLocales(Locale.newBuilder().setId("some-locale").build()).build());

    final GetQualityUpsellOptionRequest actual =
        qualityUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);

    assertEquals(123456L, actual.getBoxedCustomerId().longValue());
    assertEquals(USQ_HD_720, actual.getTargetStreamQuality());
    assertEquals(DevicePlatformCategory.DPC_WEB, actual.getSourceDevicePlatform());

    final UpsellFormattingInput formattingInput = actual.getFormattingInput();
    assertEquals("some-ngl-version", formattingInput.getBoxedNglVersion());
    assertEquals("some-locale", formattingInput.getBoxedLocale());
  }

  @Test
  void qualityUpsellOptionRequest_dnaReq_to_memberCommerceReq_useLocaleOverride() {
    var locale = Locale.newBuilder().setId("some-locale");
    var ctx = Context.newBuilder().addLocales(locale).build();
    CurrentMicrocontext.set(ctx);
    var req = new StreamingQualityUpsellOptionRequest().setLocaleOverride("some-locale-override");

    var actual = qualityUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);
    assertEquals("some-locale-override", actual.getFormattingInput().getBoxedLocale());
  }

  @Test
  void qualityUpsellOptionRequest_dnaReq_to_memberCommerceReq_useDefaultLocale() {
    var req = new StreamingQualityUpsellOptionRequest();

    var actual = qualityUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);
    assertEquals("en-US", actual.getFormattingInput().getBoxedLocale());
  }

  @Test
  void qualityUpsellOptionRequest_dnaReq_to_memberCommerceReq_useDefaultNGLVersion() {
    var req = new StreamingQualityUpsellOptionRequest();

    var actual = qualityUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);
    assertEquals("NGL_LATEST_RELEASE", actual.getFormattingInput().getBoxedNglVersion());
  }

  @ParameterizedTest
  @FieldSource
  void qualityUpsellOptionRequest_MembershipStreamQuality(
      MembershipStreamQuality input, UpsellStreamQuality expected) {
    var req = new StreamingQualityUpsellOptionRequest().setQualityFilter(input);

    var actual = qualityUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);
    assertEquals(expected, actual.getTargetStreamQuality());
  }

  static final List<Arguments> qualityUpsellOptionRequest_MembershipStreamQuality =
      List.of(
          arguments(PRODUCT_CHOICE_STREAM_QUALITY_SD, USQ_SD),
          arguments(PRODUCT_CHOICE_STREAM_QUALITY_HD720, USQ_HD_720),
          arguments(PRODUCT_CHOICE_STREAM_QUALITY_HD, USQ_HD),
          arguments(PRODUCT_CHOICE_STREAM_QUALITY_UHD, USQ_UHD),
          arguments(PRODUCT_CHOICE_STREAM_QUALITY_NOT_SET, USQ_UNSET));

  @ParameterizedTest
  @FieldSource
  void qualityUpsellOptionRequest_UpsellDevicePlatformCategory(
      UpsellDevicePlatformCategory input, DevicePlatformCategory expected) {
    var locale = Locale.newBuilder().setId("doesnt-matter");
    var ctx = Context.newBuilder().addLocales(locale).build();
    CurrentMicrocontext.set(ctx);
    var req = new StreamingQualityUpsellOptionRequest().setDevicePlatformCategory(input);

    var actual = qualityUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);
    assertEquals(expected, actual.getSourceDevicePlatform());
  }

  static final List<Arguments> qualityUpsellOptionRequest_UpsellDevicePlatformCategory =
      List.of(
          arguments(ANDROID, DPC_ANDROID),
          arguments(APPLE_TV, DPC_APPLE_TV),
          arguments(TEST, DPC_TEST),
          arguments(IOS, DPC_IOS),
          arguments(WEB, DPC_WEB),
          arguments(CUSTOMER_SUPPORT, DPC_CS),
          arguments(MEDIAROOM, DPC_MEDIAROOM),
          arguments(TV, DPC_TV),
          arguments(WINDOWS, DPC_WINDOWS),
          arguments(UNKNOWN, DPC_UNKNOWN));

  @Test
  void qualityUpsellOptionRequest_dnaReq_to_memberCommerceReq_noAccountOwnerId() {
    assertThrows(
        IllegalArgumentException.class,
        () ->
            qualityUpsellOptionRequest(
                null, new StreamingQualityUpsellOptionRequest(), CurrentMicrocontext.get()));
  }

  @Test
  void qualityUpsellOptionRequest_dnaReq_to_memberCommerceReq_noReq() {
    assertThrows(
        IllegalArgumentException.class,
        () -> qualityUpsellOptionRequest(1234567L, null, CurrentMicrocontext.get()));
  }

  @Test
  void qualityUpsellOptionRequest_dnaReq_to_memberCommerceReq_noMicrocontext() {
    assertThrows(
        IllegalArgumentException.class,
        () ->
            qualityUpsellOptionRequest(1234567L, new StreamingQualityUpsellOptionRequest(), null));
  }

  @Test
  void qualityUpsellOptionResponse_memberCommerceResp_to_dnaResp() {
    var ts = Timestamp.newBuilder().setSeconds(1).setNanos(2000000).build();

    var ustResp =
        GetQualityUpsellOptionResponse.newBuilder()
            .setCustomerId(654321L)
            .setIsQualityUpsellAvailable(true)
            .setStatusDetail("some-status-detail")
            .setUpsellUuid("some-upsell-uuid")
            .setBillingPartner("some-billing-partner")
            .setBillingCountry("some-billing-country")
            .setCurrencyCode("some-currency-code")
            .setNextBillingTs(ts)
            .setIsNextBillingDateApproximate(true)
            .setCurrentPlanId(123L)
            .setCurrentPriceTier("some-current-price-tier")
            .setCurrentTotalPriceInCents(999L)
            .setCurrentVideoResolution(UVR_SD_480)
            .setTargetPlanId(456L)
            .setTargetPriceTier("some-target-price-tier")
            .setTargetTotalPriceInCents(1000L)
            .setTargetVideoResolution(UVR_UHD_4K)
            .setDeltaPriceInCents(1L)
            .setCurrentMaxConcurrentStreams(3L)
            .setTargetMaxConcurrentStreams(4L)
            .setBillingPartnerType(BPT_ITUNES)
            .setBillingPartnerDisplayName("some-bpt-display-name")
            .setCurrentIsEligibleForChangePlan(true)
            .setFormattedOutput(
                UpsellFormattedOutput.newBuilder()
                    .setDeltaPriceFormatted("some-delta-price-formatted")
                    .setCurrentTotalPriceFormatted("some-current-total-price-formatted")
                    .setCurrentPlanDescription("some-current-plan-description-formatted")
                    .setCurrentPlanDescriptionShort("some-short-current-plan-desc-formatted")
                    .setCurrentPlanName("some-current-plan-name")
                    .setTargetTotalPriceFormatted("some-target-total-price-formatted")
                    .setTargetPlanDescription("some-target-plan-description-formatted")
                    .setTargetPlanDescriptionShort("some-short-target-plan-desc-formatted")
                    .setTargetPlanName("some-target-plan-name")
                    .build())
            .build();

    var actual = qualityUpsellOptionResponse(ustResp);

    assertNotNull(actual);

    assertEquals(654321L, actual.getCustomerID().longValue());
    assertTrue(actual.getIsUpsellAvailable());
    assertEquals("some-status-detail", actual.getStatusDetail());
    assertEquals("some-upsell-uuid", actual.getUpsellUUID());
    assertEquals("some-billing-partner", actual.getBillingPartner());
    assertEquals("some-billing-country", actual.getBillingCountry());
    assertEquals("some-currency-code", actual.getCurrencyCode());
    assertEquals(1002, actual.getNextBillingTS().longValue());
    assertTrue(actual.getIsNextBillingDateApproximate());
    assertEquals(123L, actual.getCurrentPlanID().longValue());
    assertEquals("some-current-price-tier", actual.getCurrentPriceTier());
    assertEquals(999L, actual.getCurrentTotalPriceInCents().longValue());
    assertEquals(3L, actual.getCurrentMaxConcurrentStreams().longValue());
    assertEquals(4L, actual.getTargetMaxConcurrentStreams().longValue());
    assertEquals(PRODUCT_CHOICE_STREAM_QUALITY_SD, actual.getCurrentVideoResolution());
    assertEquals(456L, actual.getTargetPlanID().longValue());
    assertEquals("some-target-price-tier", actual.getTargetPriceTier());
    assertEquals(1000L, actual.getTargetTotalPriceInCents().longValue());
    assertEquals(PRODUCT_CHOICE_STREAM_QUALITY_UHD, actual.getTargetVideoResolution());
    assertEquals(1L, actual.getDeltaPriceInCents().longValue());
    assertEquals(ITUNES, actual.getBillingPartnerType());
    assertEquals("some-bpt-display-name", actual.getBillingPartnerDisplayName());
    assertTrue(actual.getCurrentIsEligibleForChangePlan());

    var upsellFormatted = actual.getUpsellFormatted();
    assertEquals("some-delta-price-formatted", upsellFormatted.getDeltaPriceFormatted());
    assertEquals(
        "some-current-total-price-formatted", upsellFormatted.getCurrentTotalPriceFormatted());
    assertEquals(
        "some-current-plan-description-formatted", upsellFormatted.getCurrentPlanDescription());
    assertEquals(
        "some-short-current-plan-desc-formatted", upsellFormatted.getCurrentPlanDescriptionShort());
    assertEquals("some-current-plan-name", upsellFormatted.getCurrentPlanName());
    assertEquals(
        "some-target-total-price-formatted", upsellFormatted.getTargetTotalPriceFormatted());
    assertEquals(
        "some-target-plan-description-formatted", upsellFormatted.getTargetPlanDescription());
    assertEquals(
        "some-short-target-plan-desc-formatted", upsellFormatted.getTargetPlanDescriptionShort());
    assertEquals("some-target-plan-name", upsellFormatted.getTargetPlanName());
  }

  @ParameterizedTest
  @FieldSource
  void qualityUpsellOptionResponse_TargetVideoResolution(
      UpsellVideoResolution input, MembershipStreamQuality expected) {
    var resp = GetQualityUpsellOptionResponse.newBuilder().setTargetVideoResolution(input).build();

    var actual = qualityUpsellOptionResponse(resp);

    assertNotNull(actual);
    assertEquals(expected, actual.getTargetVideoResolution());
  }

  static final List<Arguments> qualityUpsellOptionResponse_TargetVideoResolution =
      List.of(
          arguments(UVR_SD_480, PRODUCT_CHOICE_STREAM_QUALITY_SD),
          arguments(UVR_HD_720, PRODUCT_CHOICE_STREAM_QUALITY_HD720),
          arguments(UVR_FHD_1080, PRODUCT_CHOICE_STREAM_QUALITY_HD),
          arguments(UVR_UHD_4K, PRODUCT_CHOICE_STREAM_QUALITY_UHD),
          arguments(UVR_UNSET, PRODUCT_CHOICE_STREAM_QUALITY_NOT_SET));

  @ParameterizedTest
  @FieldSource
  void qualityUpsellOptionResponse_BillingPartnerType(
      BillingPartnerType input, MemberCommerceBillingPartnerType expected) {
    var resp = GetQualityUpsellOptionResponse.newBuilder().setBillingPartnerType(input).build();

    var actual = qualityUpsellOptionResponse(resp);

    assertNotNull(actual);
    assertEquals(expected, actual.getBillingPartnerType());
  }

  static final List<Arguments> qualityUpsellOptionResponse_BillingPartnerType =
      List.of(
          arguments(BPT_ITUNES, ITUNES),
          arguments(BPT_NETFLIX, NETFLIX),
          arguments(BPT_BUNDLE, BUNDLE),
          arguments(BPT_INT_PAY, INT_PAY),
          arguments(BPT_UNSET, UNSET));

  @Test
  void qualityUpsellOptionResponse_memberCommerceResp_to_dnaResp_noResp() {
    assertThrows(IllegalArgumentException.class, () -> qualityUpsellOptionResponse(null));
  }

  @Test
  void streamsUpsellOptionRequest_dnaReq_to_memberCommerceReq() {
    var req =
        new ConcurrentStreamsUpsellOptionRequest()
            .setDevicePlatformCategory(IOS)
            .setNglVersion("some-ngl-version");

    var locale = Locale.newBuilder().setId("some-locale");
    var ctx = Context.newBuilder().addLocales(locale).build();
    CurrentMicrocontext.set(ctx);

    var actual = streamsUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);

    assertEquals(123456L, actual.getBoxedCustomerId().longValue());
    assertEquals(DPC_IOS, actual.getSourceDevicePlatform());

    var formattingInput = actual.getFormattingInput();
    assertEquals("some-ngl-version", formattingInput.getBoxedNglVersion());
    assertEquals("some-locale", formattingInput.getBoxedLocale());
  }

  @Test
  void streamsUpsellOptionRequest_dnaReq_to_memberCommerceReq_useLocaleOverride() {
    final ConcurrentStreamsUpsellOptionRequest req =
        new ConcurrentStreamsUpsellOptionRequest().setLocaleOverride("some-locale-override");

    CurrentMicrocontext.set(
        Context.newBuilder().addLocales(Locale.newBuilder().setId("some-locale").build()).build());

    final GetStreamsUpsellOptionRequest actual =
        streamsUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);

    assertEquals("some-locale-override", actual.getFormattingInput().getBoxedLocale());
  }

  @Test
  void streamsUpsellOptionRequest_dnaReq_to_memberCommerceReq_useDefaultLocale() {
    var req = new ConcurrentStreamsUpsellOptionRequest();

    var actual = streamsUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);
    assertEquals("en-US", actual.getFormattingInput().getBoxedLocale());
  }

  @Test
  void streamsUpsellOptionRequest_dnaReq_to_memberCommerceReq_useDefaultNGLVersion() {
    final ConcurrentStreamsUpsellOptionRequest req = new ConcurrentStreamsUpsellOptionRequest();

    final GetStreamsUpsellOptionRequest actual =
        streamsUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);

    assertEquals("NGL_LATEST_RELEASE", actual.getFormattingInput().getBoxedNglVersion());
  }

  @ParameterizedTest
  @FieldSource
  void streamsUpsellOptionRequestTest(
      UpsellDevicePlatformCategory input, DevicePlatformCategory expected) {
    var req = new ConcurrentStreamsUpsellOptionRequest().setDevicePlatformCategory(input);

    var locale = Locale.newBuilder().setId("en_us");
    var ctx = Context.newBuilder().addLocales(locale).build();
    CurrentMicrocontext.set(ctx);

    var actual = streamsUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);

    assertEquals(expected, actual.getSourceDevicePlatform());
  }

  static final List<Arguments> streamsUpsellOptionRequestTest =
      List.of(
          arguments(ANDROID, DPC_ANDROID),
          arguments(APPLE_TV, DPC_APPLE_TV),
          arguments(TEST, DPC_TEST),
          arguments(IOS, DPC_IOS),
          arguments(WEB, DPC_WEB),
          arguments(CUSTOMER_SUPPORT, DPC_CS),
          arguments(MEDIAROOM, DPC_MEDIAROOM),
          arguments(TV, DPC_TV),
          arguments(WINDOWS, DPC_WINDOWS),
          arguments(UNKNOWN, DPC_UNKNOWN));

  @Test
  void streamsUpsellOptionRequest_dnaReq_to_memberCommerceReq_noAccountOwnerId() {
    assertThrows(
        IllegalArgumentException.class,
        () ->
            streamsUpsellOptionRequest(
                null, new ConcurrentStreamsUpsellOptionRequest(), CurrentMicrocontext.get()));
  }

  @Test
  void streamsUpsellOptionRequest_dnaReq_to_memberCommerceReq_noReq() {
    assertThrows(
        IllegalArgumentException.class,
        () -> streamsUpsellOptionRequest(1234567L, null, CurrentMicrocontext.get()));
  }

  @Test
  void streamsUpsellOptionRequest_dnaReq_to_memberCommerceReq_noMicrocontext() {
    assertThrows(
        IllegalArgumentException.class,
        () ->
            streamsUpsellOptionRequest(1234567L, new ConcurrentStreamsUpsellOptionRequest(), null));
  }

  @Test
  void streamsUpsellOptionResponse_memberCommerceResp_to_dnaResp() {
    final Timestamp ts = Timestamp.newBuilder().setSeconds(1).setNanos(3000000).build();

    final GetStreamsUpsellOptionResponse ustResp =
        GetStreamsUpsellOptionResponse.newBuilder()
            .setCustomerId(654321L)
            .setIsStreamsUpsellAvailable(true)
            .setStatusDetail("some-status-detail")
            .setUpsellUuid("some-upsell-uuid")
            .setBillingPartner("some-billing-partner")
            .setBillingCountry("some-billing-country")
            .setCurrencyCode("some-currency-code")
            .setNextBillingTs(ts)
            .setIsNextBillingDateApproximate(true)
            .setCurrentPlanId(123L)
            .setCurrentPriceTier("some-current-price-tier")
            .setCurrentTotalPriceInCents(999L)
            .setCurrentVideoResolution(UVR_SD_480)
            .setTargetPlanId(456L)
            .setTargetPriceTier("some-target-price-tier")
            .setTargetTotalPriceInCents(1000L)
            .setTargetVideoResolution(UVR_UHD_4K)
            .setDeltaPriceInCents(1L)
            .setCurrentMaxConcurrentStreams(3L)
            .setTargetMaxConcurrentStreams(6L)
            .setBillingPartnerType(BillingPartnerType.BPT_INT_PAY)
            .setBillingPartnerDisplayName("some-bpt-display-name")
            .setCurrentIsEligibleForChangePlan(true)
            .setFormattedOutput(
                UpsellFormattedOutput.newBuilder()
                    .setDeltaPriceFormatted("some-delta-price-formatted")
                    .setCurrentTotalPriceFormatted("some-current-total-price-formatted")
                    .setCurrentPlanDescription("some-current-plan-description-formatted")
                    .setCurrentPlanDescriptionShort("some-short-current-plan-desc-formatted")
                    .setCurrentPlanName("some-current-plan-name")
                    .setTargetTotalPriceFormatted("some-target-total-price-formatted")
                    .setTargetPlanDescription("some-target-plan-description-formatted")
                    .setTargetPlanDescriptionShort("some-short-target-plan-desc-formatted")
                    .setTargetPlanName("some-target-plan-name")
                    .build())
            .build();

    final ConcurrentStreamsUpsellOptionResponse actual = streamsUpsellOptionResponse(ustResp);

    assertNotNull(actual);

    assertEquals(654321L, actual.getCustomerID().longValue());
    assertTrue(actual.getIsUpsellAvailable());
    assertEquals("some-status-detail", actual.getStatusDetail());
    assertEquals("some-upsell-uuid", actual.getUpsellUUID());
    assertEquals("some-billing-partner", actual.getBillingPartner());
    assertEquals("some-billing-country", actual.getBillingCountry());
    assertEquals("some-currency-code", actual.getCurrencyCode());
    assertEquals(1003, actual.getNextBillingTS().longValue());
    assertTrue(actual.getIsNextBillingDateApproximate());
    assertEquals(123L, actual.getCurrentPlanID().longValue());
    assertEquals("some-current-price-tier", actual.getCurrentPriceTier());
    assertEquals(999L, actual.getCurrentTotalPriceInCents().longValue());
    assertEquals(PRODUCT_CHOICE_STREAM_QUALITY_SD, actual.getCurrentVideoResolution());
    assertEquals(456L, actual.getTargetPlanID().longValue());
    assertEquals("some-target-price-tier", actual.getTargetPriceTier());
    assertEquals(1000L, actual.getTargetTotalPriceInCents().longValue());
    assertEquals(PRODUCT_CHOICE_STREAM_QUALITY_UHD, actual.getTargetVideoResolution());
    assertEquals(1L, actual.getDeltaPriceInCents().longValue());
    assertEquals(3L, actual.getCurrentMaxConcurrentStreams().longValue());
    assertEquals(6L, actual.getTargetMaxConcurrentStreams().longValue());
    assertEquals(MemberCommerceBillingPartnerType.INT_PAY, actual.getBillingPartnerType());
    assertEquals("some-bpt-display-name", actual.getBillingPartnerDisplayName());
    assertTrue(actual.getCurrentIsEligibleForChangePlan());

    final UpsellOptionFormatted upsellFormatted = actual.getUpsellFormatted();
    assertEquals("some-delta-price-formatted", upsellFormatted.getDeltaPriceFormatted());
    assertEquals(
        "some-current-total-price-formatted", upsellFormatted.getCurrentTotalPriceFormatted());
    assertEquals(
        "some-current-plan-description-formatted", upsellFormatted.getCurrentPlanDescription());
    assertEquals(
        "some-short-current-plan-desc-formatted", upsellFormatted.getCurrentPlanDescriptionShort());
    assertEquals("some-current-plan-name", upsellFormatted.getCurrentPlanName());
    assertEquals(
        "some-target-total-price-formatted", upsellFormatted.getTargetTotalPriceFormatted());
    assertEquals(
        "some-target-plan-description-formatted", upsellFormatted.getTargetPlanDescription());
    assertEquals(
        "some-short-target-plan-desc-formatted", upsellFormatted.getTargetPlanDescriptionShort());
    assertEquals("some-target-plan-name", upsellFormatted.getTargetPlanName());
  }

  @Test
  void streamsUpsellOptionResponse_memberCommerceResp_to_dnaResp_noResp() {
    assertThrows(IllegalArgumentException.class, () -> streamsUpsellOptionResponse(null));
  }

  @Test
  void streamsUpsellOptionResponse_memberCommerceResp_to_dnaResp_allBillingPartnerTypes() {
    final List<Pair<BillingPartnerType, MemberCommerceBillingPartnerType>> testCases =
        Arrays.asList(
            Pair.of(BPT_ITUNES, ITUNES),
            Pair.of(BillingPartnerType.BPT_NETFLIX, MemberCommerceBillingPartnerType.NETFLIX),
            Pair.of(BillingPartnerType.BPT_BUNDLE, MemberCommerceBillingPartnerType.BUNDLE),
            Pair.of(BillingPartnerType.BPT_INT_PAY, MemberCommerceBillingPartnerType.INT_PAY),
            Pair.of(BillingPartnerType.BPT_UNSET, MemberCommerceBillingPartnerType.UNSET));

    testCases.forEach(
        testCase -> {
          final BillingPartnerType input = testCase.getLeft();
          final MemberCommerceBillingPartnerType expected = testCase.getRight();

          final GetStreamsUpsellOptionResponse resp =
              GetStreamsUpsellOptionResponse.newBuilder().setBillingPartnerType(input).build();

          final ConcurrentStreamsUpsellOptionResponse actual = streamsUpsellOptionResponse(resp);

          assertNotNull(actual);

          assertEquals(expected, actual.getBillingPartnerType());
        });
  }

  @Test
  void acceptQualityUpsellOptionRequest_dnaReq_to_memberCommerceReq() {
    final AcceptStreamingQualityUpsellOptionRequest req =
        new AcceptStreamingQualityUpsellOptionRequest()
            .setUpsellUUID("some-upsell-uuid")
            .setPlanID(999L)
            .setPriceTier("some-price-tier")
            .setDevicePlatformCategory(UpsellDevicePlatformCategory.TV)
            .setMessageGUID("some-message-guid");

    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setGeo(
                GeoContext.newBuilder()
                    .setIpAddress(IpAddress.newBuilder().setAddress("some-ip"))
                    .build())
            .setContext(
                Context.newBuilder()
                    .setDevice(DeviceContext.newBuilder().setEsn("some-esn"))
                    .build())
            .build());

    final AcceptQualityUpsellOptionRequest actual =
        acceptQualityUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);

    assertEquals(123456L, actual.getBoxedCustomerId().longValue());
    assertEquals("some-upsell-uuid", actual.getBoxedUpsellUuid());
    assertEquals(999L, actual.getBoxedPlanId().longValue());
    assertEquals("some-price-tier", actual.getBoxedPriceTier());
    assertEquals(DevicePlatformCategory.DPC_TV, actual.getSourceDevicePlatform());
    assertEquals("some-esn", actual.getBoxedEsn());
    assertEquals("some-ip", actual.getBoxedClientIp());
    assertEquals("some-message-guid", actual.getBoxedMessageGuid());
  }

  @Test
  void acceptQualityUpsellOptionRequest_dnaReq_to_memberCommerceReq_noAccountOwnerId() {
    assertThrows(
        IllegalArgumentException.class,
        () ->
            acceptQualityUpsellOptionRequest(
                null, new AcceptStreamingQualityUpsellOptionRequest(), CurrentMicrocontext.get()));
  }

  @Test
  void acceptQualityUpsellOptionRequest_dnaReq_to_memberCommerceReq_noReq() {
    assertThrows(
        IllegalArgumentException.class,
        () -> acceptQualityUpsellOptionRequest(123456L, null, CurrentMicrocontext.get()));
  }

  @Test
  void acceptQualityUpsellOptionRequest_dnaReq_to_memberCommerceReq_noMicrocontext() {
    assertThrows(
        IllegalArgumentException.class,
        () ->
            acceptQualityUpsellOptionRequest(
                123456L, new AcceptStreamingQualityUpsellOptionRequest(), null));
  }

  @ParameterizedTest
  @FieldSource
  void acceptQualityUpsellOptionRequestTest(
      UpsellDevicePlatformCategory input, DevicePlatformCategory expected) {
    var actual =
        acceptQualityUpsellOptionRequest(
            123456L,
            new AcceptStreamingQualityUpsellOptionRequest().setDevicePlatformCategory(input),
            CurrentMicrocontext.get());

    assertNotNull(actual);
    assertEquals(expected, actual.getSourceDevicePlatform());
  }

  static final List<Arguments> acceptQualityUpsellOptionRequestTest =
      List.of(
          arguments(ANDROID, DPC_ANDROID),
          arguments(APPLE_TV, DPC_APPLE_TV),
          arguments(TEST, DPC_TEST),
          arguments(IOS, DPC_IOS),
          arguments(WEB, DPC_WEB),
          arguments(CUSTOMER_SUPPORT, DPC_CS),
          arguments(MEDIAROOM, DPC_MEDIAROOM),
          arguments(TV, DPC_TV),
          arguments(WINDOWS, DPC_WINDOWS),
          arguments(UNKNOWN, DPC_UNKNOWN));

  @Test
  void acceptStreamsUpsellOptionRequest_dnaReq_to_memberCommerceReq() {
    final AcceptConcurrentStreamsUpsellOptionRequest req =
        new AcceptConcurrentStreamsUpsellOptionRequest()
            .setUpsellUUID("some-upsell-uuid")
            .setPlanID(999L)
            .setPriceTier("some-price-tier")
            .setDevicePlatformCategory(UpsellDevicePlatformCategory.TV)
            .setMessageGUID("some-message-guid");

    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setGeo(
                GeoContext.newBuilder()
                    .setIpAddress(IpAddress.newBuilder().setAddress("some-ip").build())
                    .build())
            .setContext(
                Context.newBuilder()
                    .setDevice(DeviceContext.newBuilder().setEsn("some-esn"))
                    .build())
            .build());

    final AcceptStreamsUpsellOptionRequest actual =
        acceptStreamsUpsellOptionRequest(123456L, req, CurrentMicrocontext.get());

    assertNotNull(actual);

    assertEquals(123456L, actual.getBoxedCustomerId().longValue());
    assertEquals("some-upsell-uuid", actual.getBoxedUpsellUuid());
    assertEquals(999L, actual.getBoxedPlanId().longValue());
    assertEquals("some-price-tier", actual.getBoxedPriceTier());
    assertEquals(DevicePlatformCategory.DPC_TV, actual.getSourceDevicePlatform());
    assertEquals("some-esn", actual.getBoxedEsn());
    assertEquals("some-ip", actual.getBoxedClientIp());
    assertEquals("some-message-guid", actual.getBoxedMessageGuid());
  }

  @Test
  void acceptStreamsUpsellOptionRequest_dnaReq_to_memberCommerceReq_noAccountOwnerId() {
    assertThrows(
        IllegalArgumentException.class,
        () ->
            acceptStreamsUpsellOptionRequest(
                null, new AcceptConcurrentStreamsUpsellOptionRequest(), CurrentMicrocontext.get()));
  }

  @Test
  void acceptStreamsUpsellOptionRequest_dnaReq_to_memberCommerceReq_noReq() {
    assertThrows(
        IllegalArgumentException.class,
        () -> acceptStreamsUpsellOptionRequest(123456L, null, CurrentMicrocontext.get()));
  }

  @Test
  void acceptStreamsUpsellOptionRequest_dnaReq_to_memberCommerceReq_noMicrocontext() {
    assertThrows(
        IllegalArgumentException.class,
        () ->
            acceptStreamsUpsellOptionRequest(
                123456L, new AcceptConcurrentStreamsUpsellOptionRequest(), null));
  }

  @ParameterizedTest
  @FieldSource
  void acceptStreamsUpsellOptionRequestTest(
      UpsellDevicePlatformCategory input, DevicePlatformCategory expected) {
    var actual =
        acceptStreamsUpsellOptionRequest(
            123456L,
            new AcceptConcurrentStreamsUpsellOptionRequest().setDevicePlatformCategory(input),
            CurrentMicrocontext.get());

    assertNotNull(actual);
    assertEquals(expected, actual.getSourceDevicePlatform());
  }

  static final List<Arguments> acceptStreamsUpsellOptionRequestTest =
      List.of(
          arguments(ANDROID, DPC_ANDROID),
          arguments(APPLE_TV, DPC_APPLE_TV),
          arguments(TEST, DPC_TEST),
          arguments(IOS, DPC_IOS),
          arguments(WEB, DPC_WEB),
          arguments(CUSTOMER_SUPPORT, DPC_CS),
          arguments(MEDIAROOM, DPC_MEDIAROOM),
          arguments(TV, DPC_TV),
          arguments(WINDOWS, DPC_WINDOWS),
          arguments(UNKNOWN, DPC_UNKNOWN));

  @Test
  void acceptQualityUpsellOptionResponse_memberCommerceResp_to_dnaResp() {
    final AcceptStreamingQualityUpsellOptionResponse actual =
        MemberCommerceConverters.acceptQualityUpsellOptionResponse(
            AcceptQualityUpsellOptionResponse.newBuilder().setCustomerId(123456L).build());

    assertNotNull(actual);
    assertNotNull(actual.getCustomerID());
    assertEquals(123456L, actual.getCustomerID().longValue());
  }

  @Test
  void acceptQualityUpsellOptionResponse_memberCommerceResp_to_dnaResp_noResp() {
    assertThrows(
        IllegalArgumentException.class,
        () -> MemberCommerceConverters.acceptQualityUpsellOptionResponse(null));
  }

  @Test
  void acceptStreamsUpsellOptionResponse_memberCommerceResp_to_dnaResp() {
    final AcceptConcurrentStreamsUpsellOptionResponse actual =
        MemberCommerceConverters.acceptStreamsUpsellOptionResponse(
            AcceptStreamsUpsellOptionResponse.newBuilder().setCustomerId(123456L).build());

    assertNotNull(actual);
    assertNotNull(actual.getCustomerID());
    assertEquals(123456L, actual.getCustomerID().longValue());
  }

  @Test
  void acceptStreamsUpsellOptionResponse_memberCommerceResp_to_dnaResp_noResp() {
    assertThrows(
        IllegalArgumentException.class,
        () -> MemberCommerceConverters.acceptStreamsUpsellOptionResponse(null));
  }
}
