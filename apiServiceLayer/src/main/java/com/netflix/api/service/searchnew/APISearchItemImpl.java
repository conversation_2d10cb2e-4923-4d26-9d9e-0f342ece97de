package com.netflix.api.service.searchnew;

import java.util.Map;

/** Author: sgudiboina Date: 9/23/16 Time: 5:03 PM */
public class APISearchItemImpl implements APISearchItem {
  private Integer id;
  private String entityId;
  private String displayString;
  private APISearchConstants.SearchItemType type;
  private Map<String, String> optionalAttributes;

  @Override
  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  @Override
  public String getEntityId() {
    return entityId;
  }

  public void setEntityId(String entityId) {
    this.entityId = entityId;
  }

  @Override
  public String getDisplayString() {
    return displayString;
  }

  public void setDisplayString(String displayString) {
    this.displayString = displayString;
  }

  @Override
  public APISearchConstants.SearchItemType getType() {
    return type;
  }

  public void setType(APISearchConstants.SearchItemType type) {
    this.type = type;
  }

  @Override
  public Map<String, String> getOptionalAttributes() {
    return optionalAttributes;
  }

  public void setOptionalAttributes(Map<String, String> optionalAttributes) {
    this.optionalAttributes = optionalAttributes;
  }
}
