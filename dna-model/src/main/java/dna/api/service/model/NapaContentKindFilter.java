package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaContentKindFilter {
  private MediaKind mediaKind;

  public MediaKind getMediaKind() {
    return mediaKind;
  }

  public NapaContentKindFilter setMediaKind(MediaKind mediaKind) {
    this.mediaKind = mediaKind;
    return this;
  }
}
