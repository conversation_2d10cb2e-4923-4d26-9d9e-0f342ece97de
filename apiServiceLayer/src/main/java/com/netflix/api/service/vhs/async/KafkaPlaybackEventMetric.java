package com.netflix.api.service.vhs.async;

import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Registry;
import com.netflix.spectator.api.histogram.PercentileTimer;
import jakarta.inject.Inject;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class KafkaPlaybackEventMetric {

  private final Registry registry;
  private static final String NAME = "api.event.kafka.call";
  private final Id timerId;
  private static final String SUCCESS_NAME = "success";
  private static final String TOPIC_NAME = "topic";

  @Inject
  public KafkaPlaybackEventMetric(Registry registry) {
    this.registry = registry;
    this.timerId = registry.createId(NAME);
  }

  public void measure(
      boolean success,
      long durationInNanos,
      PlaybackEventTopic playbackEventTopic,
      Map<String, String> tags) {
    PercentileTimer.get(
            registry,
            timerId
                .withTag(SUCCESS_NAME, success)
                .withTag(TOPIC_NAME, playbackEventTopic.getName())
                .withTags(tags))
        .record(durationInNanos, TimeUnit.NANOSECONDS);
  }
}
