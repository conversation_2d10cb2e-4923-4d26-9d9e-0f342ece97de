package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaNonMemberInfo {
  private Boolean isKidsProfile;

  private Maturity maturity;

  private Boolean isFamilyProfile;

  public Boolean getIsKidsProfile() {
    return isKidsProfile;
  }

  public Maturity getMaturity() {
    return maturity;
  }

  public Boolean getIsFamilyProfile() {
    return isFamilyProfile;
  }

  public NapaNonMemberInfo setIsKidsProfile(Boolean isKidsProfile) {
    this.isKidsProfile = isKidsProfile;
    return this;
  }

  public NapaNonMemberInfo setMaturity(Maturity maturity) {
    this.maturity = maturity;
    return this;
  }

  public NapaNonMemberInfo setIsFamilyProfile(Boolean isFamilyProfile) {
    this.isFamilyProfile = isFamilyProfile;
    return this;
  }
}
