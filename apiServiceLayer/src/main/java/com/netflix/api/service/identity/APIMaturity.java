package com.netflix.api.service.identity;

import com.netflix.api.annotations.ExcludeFromCoverage;

@ExcludeFromCoverage
public enum APIMaturity {
  LITTLE_KIDS {
    @Override
    public int getLevel() {
      return 40;
    }
  },
  OLDER_KIDS {
    @Override
    public int getLevel() {
      return 70;
    }
  },
  TEENS {
    @Override
    public int getLevel() {
      return 96;
    }
  },
  ADULTS {
    @Override
    public int getLevel() {
      return 1000000;
    }
  };

  /**
   * Gets the numeric value of the maturity level, this is specifically for cases where the enum
   * needs to be compared to the legacy integer values
   *
   * @return an integer representing the maturitylevel
   */
  public abstract int getLevel();

  /**
   * Converts the numeric maturity level value to the enum
   *
   * @param maturity the integer value which will be used to create an {@link APIMaturity}
   * @return the resolved {@link APIMaturity} based on the maturity param
   */
  public static APIMaturity getMaturity(int maturity) {
    if (maturity <= LITTLE_KIDS.getLevel()) {
      return LITTLE_KIDS;
    } else if (maturity <= OLDER_KIDS.getLevel()) {
      return OLDER_KIDS;
    } else if (maturity <= TEENS.getLevel()) {
      return TEENS;
    } else {
      return ADULTS;
    }
  }
}
