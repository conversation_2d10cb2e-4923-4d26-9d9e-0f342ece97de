package dna.api.converters;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.netflix.api.platform.MockPropertyRepositoryConfig;
import com.netflix.api.platform.NetflixESN;
import com.netflix.api.platform.NetflixESN.Source;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.context.RequestContextWrapper.Builder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContextInternal;
import com.netflix.api.service.identity.APIUserInternal;
import com.netflix.geoclient.CurrentGeoData;
import com.netflix.geoclient.GeoData;
import com.netflix.lang.BindingContexts;
import com.netflix.noir.context.protogen.NonMemberInfo;
import com.netflix.noir.context.protogen.RequestContext;
import com.netflix.noir.request.protogen.FeedRequestParams;
import com.netflix.noir.request.protogen.FeedRequestParams.FeedType;
import com.netflix.subscriberservice.common.MaturityLevel;
import com.netflix.type.NFCountry;
import dna.api.service.model.Maturity;
import dna.api.service.model.NapaFeedRequestParams;
import dna.api.service.model.NapaFeedType;
import dna.api.service.model.NapaNonMemberInfo;
import dna.api.service.model.NapaPageRequest;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NapaConvertersTest {

  APIUserInternal user;
  APIRequestContextInternal requestContext;
  APIRequest currentRequest;
  GeoData geoData;

  List<String> preferredLocales = List.of("en-US");

  @BeforeAll
  static void beforeClass() {
    MockPropertyRepositoryConfig config = new MockPropertyRepositoryConfig();
    config.init();
  }

  @BeforeEach
  void setup() {
    BindingContexts.push();

    user = mock(APIUserInternal.class);
    requestContext = mock(APIRequestContextInternal.class);
    currentRequest = mock(APIRequest.class);
    geoData = mock(GeoData.class);

    when(user.getRealCustomerId()).thenReturn(123L);
    when(geoData.isBlockedProxy()).thenReturn(true);
    APIRequest.setCurrentRequest(currentRequest);
    when(currentRequest.getUser()).thenReturn(user);
    when(currentRequest.getRequestContext()).thenReturn(requestContext);
    when(requestContext.getClientCapabilitiesInternal()).thenReturn(null);
    CurrentGeoData.set(geoData);
    RequestContextWrapper.createAndSet(
        Builder.newInstance()
            .country(NFCountry.US)
            .requestId("foo")
            .localeList("en-US")
            .deviceId("esnfoo"));
    NetflixESN.createFromESN("esnfoo", Source.PARAM);
  }

  @AfterEach
  void teardown() {
    BindingContexts.pop();
  }

  @Test
  void testConvertPageRequestNull() {
    assertNotNull(NoirPageRequestConverter.protoRequest(null, preferredLocales));
  }

  @Test
  void testConvertPageRequestEmpty() {
    assertNotNull(NoirPageRequestConverter.protoRequest(new NapaPageRequest(), preferredLocales));
  }

  @Test
  void testConvertPageRequest() {
    NapaPageRequest pageRequest = new NapaPageRequest();
    pageRequest.setFeedRequestParams(
        new NapaFeedRequestParams().setFeedType(NapaFeedType.COMEDY_CLIPS_FEED));
    final com.netflix.noir.protogen.NoirPageRequest napaPageRequest =
        NoirPageRequestConverter.protoRequest(pageRequest, preferredLocales);
    assertNotNull(napaPageRequest);
    final RequestContext context = napaPageRequest.getContext();
    assertNotNull(context);
    final com.netflix.noir.context.protogen.GeoData geoData = context.getGeoData();
    assertNotNull(geoData);
    assertTrue(geoData.getIsBlockedProxy());
    assertEquals(123L, context.getCustomerId());
    final FeedRequestParams feedRequestParams = napaPageRequest.getFeedRequestParams();
    assertNotNull(feedRequestParams);
    assertEquals(FeedType.COMEDY_CLIPS_FEED, feedRequestParams.getFeedType());
  }

  @Test
  void testConvertNonMemberInfo() {
    NapaNonMemberInfo in = new NapaNonMemberInfo();
    in.setIsKidsProfile(true);
    in.setIsFamilyProfile(false);
    in.setMaturity(Maturity.OLDER_KIDS);
    NonMemberInfo.Builder outBuilder = NonMemberInfo.newBuilder();
    NoirSearchPageRequestConverter.convertNonMemberInfo(in, outBuilder);
    NonMemberInfo out = outBuilder.build();
    assertTrue(out.getIsKidsProfile());
    assertFalse(out.getIsFamilyProfile());
    assertEquals(
        MaturityLevel.getMaturityValue(MaturityLevel.OLDER_KIDS).intValue(),
        out.getMaturityLevel());
  }
}
