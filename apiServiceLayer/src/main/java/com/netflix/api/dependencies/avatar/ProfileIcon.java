package com.netflix.api.dependencies.avatar;

import com.netflix.api.platform.avatar.LegacyIconNameMapper;
import java.util.EnumSet;
import java.util.Set;

public enum ProfileIcon {
  Icon13("icon13", false),
  Icon14("icon14", false),
  Icon15("icon15", false),
  Icon16("icon16", false),
  Icon17("icon17", false),
  Icon18("icon18", false),
  Icon19("icon19", false),
  Icon20("icon20", false),
  Icon21("icon21", false),
  Icon22("icon22", false),
  Icon23("icon23", false),
  Icon24("icon24", false),

  Silhouette1("icon25", true),
  Silhouette2("icon26", true),
  Silhouette3("icon27", true),
  Silhouette4("icon28", true),
  Silhouette5("icon29", true),

  HatersIconTest("icon50", false),
  HatersIconReal("icon51", false),

  V1_Silhouette("icon12", true),

  DefaultKidsIcon("icon36", false),
  WatchTogetherIcon("icon38", false);

  static final Set<ProfileIcon> ALL_ICONS =
      EnumSet.of(
          Icon13,
          Icon14,
          Icon15,
          Icon16,
          Icon17,
          Icon18,
          Icon19,
          Icon20,
          Icon21,
          Icon22,
          Icon23,
          Icon24,
          Silhouette1,
          Silhouette2,
          Silhouette3,
          Silhouette4,
          Silhouette5);

  static final Set<ProfileIcon> ALL_SILHOUETTES =
      EnumSet.of(Silhouette1, Silhouette2, Silhouette3, Silhouette4, Silhouette5);

  static final Set<ProfileIcon> HATERS_ICONS = EnumSet.of(HatersIconTest, HatersIconReal);

  private final String iconName;
  private final boolean inDefaultSet;

  ProfileIcon(String iconName, boolean inDefaultSet) {
    this.iconName = iconName;
    this.inDefaultSet = inDefaultSet;
  }

  public String getIconName() {
    return iconName;
  }

  public String getMappedName() {
    return LegacyIconNameMapper.from(iconName);
  }

  public boolean isInDefaultSet() {
    return inDefaultSet;
  }
}
