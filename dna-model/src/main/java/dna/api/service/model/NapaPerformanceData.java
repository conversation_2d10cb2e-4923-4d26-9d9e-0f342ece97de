package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaPerformanceData {
  private Long overallLatency;

  private Long startTime;

  private List<NapaPerformancePhase> phases;

  public Long getOverallLatency() {
    return overallLatency;
  }

  public Long getStartTime() {
    return startTime;
  }

  public List<NapaPerformancePhase> getPhases() {
    return phases;
  }

  public NapaPerformanceData setOverallLatency(Long overallLatency) {
    this.overallLatency = overallLatency;
    return this;
  }

  public NapaPerformanceData setStartTime(Long startTime) {
    this.startTime = startTime;
    return this;
  }

  public NapaPerformanceData setPhases(List<NapaPerformancePhase> phases) {
    this.phases = phases;
    return this;
  }
}
