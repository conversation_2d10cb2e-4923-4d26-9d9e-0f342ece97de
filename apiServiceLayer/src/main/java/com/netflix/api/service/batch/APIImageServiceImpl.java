package com.netflix.api.service.batch;

import static com.google.common.collect.Lists.partition;

import com.netflix.aballocator.protogen.AllocResponse;
import com.netflix.api.adapters.StreamingClientServiceAdapter;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.facade.service.VideoLookup;
import com.netflix.api.facade.service.VideoLookupUtils;
import com.netflix.api.grpc.GrpcCallHelpers.RxSingle;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.exception.Exceptions;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIClientCapabilitiesInternal;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import com.netflix.api.service.APIRequestUtilities;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.service.abtest.ABAdapterBase;
import com.netflix.api.service.batch.model.APIAvatarKey;
import com.netflix.api.service.batch.model.APIAvatarType;
import com.netflix.api.service.batch.model.APIProfileIconCriteria;
import com.netflix.api.service.video.APIArtWorkImageImpl;
import com.netflix.api.service.video.APIImage;
import com.netflix.api.service.video.APIImageImpl;
import com.netflix.api.service.video.ImageResolverUtils;
import com.netflix.api.util.ApiStackDumper;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.images.protogen.AvatarImageKey;
import com.netflix.images.protogen.AvatarImages;
import com.netflix.images.protogen.CharacterImages;
import com.netflix.images.protogen.ClientCapabilityList;
import com.netflix.images.protogen.GetAvatarsRequest;
import com.netflix.images.protogen.GetCharacterImagesRequest;
import com.netflix.images.protogen.GetPersonImagesRequest;
import com.netflix.images.protogen.GetVideoImagesReply;
import com.netflix.images.protogen.GetVideoImagesRequest;
import com.netflix.images.protogen.Image;
import com.netflix.images.protogen.ImageCriteria;
import com.netflix.images.protogen.ImageCriteria.Builder;
import com.netflix.images.protogen.ImagesServiceGrpc.ImagesServiceStub;
import com.netflix.images.protogen.PersonImages;
import com.netflix.images.protogen.VideoImages;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.type.proto.Videos;
import com.netflix.type.protogen.BasicTypes;
import com.netflix.videometadata.type.CompleteVideo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Single;

@Component
@EnableDeprecatedMetrics
public class APIImageServiceImpl implements APIImageService {

  private static final Logger logger = LoggerFactory.getLogger(APIImageServiceImpl.class);
  private static final Property<Boolean> ARTWORK_DETAILED_METRICS_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.artwork.detailed.metrics.enabled", Boolean.class)
          .orElse(false);

  private final ImagesServiceStub imagesService;
  private final ABAdapterBase abAdapterBase;
  private final StreamingClientServiceAdapter streamingClientServiceAdapter;
  private final VideoLookup videoLookup;

  private final Property<Boolean> logNullVideos;
  private final Property<Boolean> scrubNullVideos;
  private final Property<Boolean> dedupeBatches;
  private final Property<Integer> rebatchSize;

  private final Property<Boolean> mantisEnabled;

  @Autowired
  public APIImageServiceImpl(
      StreamingClientServiceAdapter streamingClientServiceAdapter,
      @GrpcSpringClient("images") ImagesServiceStub imagesService,
      ABAdapterBase abAdapterBase,
      VideoLookup videoLookup,
      PropertyRepository properties) {

    this.streamingClientServiceAdapter = streamingClientServiceAdapter;
    this.abAdapterBase = abAdapterBase;
    this.imagesService = imagesService.withWaitForReady();
    this.videoLookup = videoLookup;

    logNullVideos = properties.get("nil-log-null-videos", Boolean.class).orElse(false);
    scrubNullVideos = properties.get("nil-scrub-null-videos", Boolean.class).orElse(true);
    dedupeBatches = properties.get("api.artwork.dedupe.batches", Boolean.class).orElse(false);
    rebatchSize = properties.get("api.artwork.dedupe.batches", Integer.class).orElse(500);
    mantisEnabled = properties.get("api.artwork.mantis.enabled", Boolean.class).orElse(false);
  }

  private static final ApiStackDumper batchSizeOneStackDumper =
      new ApiStackDumper("nil-batchsize-1");

  public Single<Map<Integer, List<APIImage>>> getMultipleVideoArtwork(
      Collection<Integer> videoIds, Single<GetVideoImagesRequest.Builder> imageCriteria) {
    return imageCriteria.flatMap(
        c ->
            RxSingle.defer(imagesService::getVideoImages, c.build())
                .map(reply -> convertImagesToAPIImages(videoIds, reply))
                .onErrorReturn(
                    e -> {
                      logger.info("error getting image results: ", e);
                      return Map.of();
                    }));
  }

  private Map<Integer, List<APIImage>> convertImagesToAPIImages(
      Collection<Integer> videoIds, GetVideoImagesReply reply) {
    Map<Integer, List<APIImage>> imageMap = HashMap.newHashMap(videoIds.size());
    List<VideoImages> videoImages = reply.getVideoImagesList();

    if (!videoImages.isEmpty()) {
      for (VideoImages videoImage : videoImages) {
        int key = videoImage.getVideo().getId();
        List<APIImage> imageList = new LinkedList<>();

        if (videoImage.getImagesList().isEmpty()) {
          logger.debug("no images returned by client/service for videoId = {}", key);
        }
        for (com.netflix.images.protogen.Image img : videoImage.getImagesList()) {
          imageList.add(new APIArtWorkImageImpl(img));
        }
        imageMap.put(key, imageList);
      }
    } else {
      logger.debug("no images returned by client/service for videoIds = {}", videoIds);
    }

    return imageMap;
  }

  public Single<Map<Integer, APIImage>> getVideoArtwork(
      Collection<Integer> videoIds, Single<GetVideoImagesRequest.Builder> imageCriteria) {
    return imageCriteria.flatMap(
        builder -> {
          // brownout mode?
          boolean brownout = APIRequestUtilities.isInBrownoutMode();
          if (brownout) {
            builder.mergeCriteria(
                ImageCriteria.newBuilder().putParams("force_smoky", "true").build());
          }

          if (ARTWORK_DETAILED_METRICS_ENABLED.get()) {
            MantisPublishContext.getCurrent()
                .add(
                    "api.artwork.request.explicit.batching."
                        + builder.getCriteria().getType()
                        + ".brownout."
                        + brownout
                        + "."
                        + System.nanoTime(),
                    videoIds);
          }

          // track call
          ImageResolverUtils.trackCall(
              true,
              builder.getCriteria().getType().getValue(),
              videoIds.size(),
              builder.getCriteria().getUiFlavor().getValue(),
              builder.getCriteria().getContentGrants());

          DynamicCounter.increment(
              "api.artwork.request", "source", "batched", "brownout", String.valueOf(brownout));

          if (builder.getVideosList() == null || builder.getVideosList().isEmpty()) {
            return Single.just(Map.of());
          }

          // should we rebatch?
          if (builder.getVideosList().size() > rebatchSize.get()) {
            return getRebatchedImages(builder);
          }
          var criteria = builder.build();
          var now = System.nanoTime();
          if (mantisEnabled.get()) {
            MantisPublishContext.getCurrent()
                .add("api.artwork.request." + now, USTListUtils.toJson(criteria));
          }
          return RxSingle.defer(imagesService::getVideoImages, criteria)
              .map(
                  reply -> {
                    if (mantisEnabled.get()) {
                      MantisPublishContext.getCurrent()
                          .add("api.artwork.response." + now, USTListUtils.toJson(reply));
                    }
                    return extractImageResults(videoIds, reply);
                  })
              .onErrorReturn(
                  e -> {
                    if (mantisEnabled.get()) {
                      MantisPublishContext.getCurrent()
                          .add("api.artwork.error." + now, Exceptions.getStackTrace(e));
                    }
                    return Map.of();
                  });
        });
  }

  private Single<? extends Map<Integer, APIImage>> getRebatchedImages(
      GetVideoImagesRequest.Builder c) {
    List<List<BasicTypes.Video>> partitions = partition(c.getVideosList(), rebatchSize.get());
    DynamicCounter.increment(
        "api.artwork.request.rebatched", "finalBatches", "" + partitions.size());
    GetVideoImagesRequest original = c.build();
    var obs =
        partitions.stream().map(partition -> buildRebatchedRequest(original, partition)).toList();

    Observable<? extends Map<Integer, APIImage>> result = Observable.merge(obs);

    // TODO use a transformer
    return result
        .toList()
        .map(
            listOfMaps -> {
              Map<Integer, APIImage> all = new HashMap<>();
              listOfMaps.forEach(all::putAll);
              return all;
            })
        .toSingle();
  }

  private Observable<? extends Map<Integer, APIImage>> buildRebatchedRequest(
      GetVideoImagesRequest original, List<BasicTypes.Video> partition) {
    return sendRebatchedRequest(original, partition).toObservable();
  }

  private Single<? extends Map<Integer, APIImage>> sendRebatchedRequest(
      GetVideoImagesRequest original, List<BasicTypes.Video> partition) {
    GetVideoImagesRequest.Builder partitionCriteria = GetVideoImagesRequest.newBuilder();

    // all other criteria come from original batch
    partitionCriteria.mergeFrom(original);

    // overwrite videos with partition
    partitionCriteria.clearVideos();
    partitionCriteria.addAllVideos(partition);

    return RxSingle.defer(imagesService::getVideoImages, partitionCriteria.build())
        .map(reply -> extractImageResults(getVideoIds(partition), reply))
        .onErrorReturn(e -> Map.of());
  }

  private Collection<Integer> getVideoIds(List<BasicTypes.Video> videos) {
    return videos.stream().map(BasicTypes.Video::getId).collect(Collectors.toSet());
  }

  private Map<Integer, APIImage> extractImageResults(
      Collection<Integer> videoIds, GetVideoImagesReply reply) {
    Map<Integer, APIImage> imageMap = HashMap.newHashMap(videoIds.size());
    List<VideoImages> imagesList = reply.getVideoImagesList();
    if (!imagesList.isEmpty()) {
      for (VideoImages videoImages : imagesList) {
        try {
          int key = videoImages.getVideo().getId();
          List<Image> imgs = videoImages.getImagesList();
          // only one image at most
          if (!imgs.isEmpty()) {
            imageMap.put(key, new APIArtWorkImageImpl(imgs.getFirst()));
          }
        } catch (Exception t) {
          logger.debug("error extracting image", t);
        }
      }
    }
    return imageMap;
  }

  public Single<Map<Integer, APIImage>> getCharacterArtWorkRemote(
      Collection<Integer> characterIds, Single<Builder> finalCriteria) {
    return finalCriteria.flatMap(
        criteria -> {
          try {
            return getCharacterArtWorkRemote(characterIds, criteria.build())
                .map(
                    images -> {
                      Map<Integer, APIImage> artWorkMap = HashMap.newHashMap(characterIds.size());
                      for (Entry<Integer, Image> entry : images.entrySet()) {
                        artWorkMap.put(entry.getKey(), new APIArtWorkImageImpl(entry.getValue()));
                      }
                      return artWorkMap;
                    });
          } catch (Exception e) {
            logger.error("error returning character artwork", e);
            return Single.just(Map.of());
          }
        });
  }

  private Single<Map<Integer, Image>> getCharacterArtWorkRemote(
      Collection<Integer> characterIds, ImageCriteria finalCriteria) {

    GetCharacterImagesRequest.Builder builder = GetCharacterImagesRequest.newBuilder();
    builder.addAllCharacters(characterIds);
    builder.setCriteria(finalCriteria);
    return RxSingle.defer(imagesService::getCharacterImages, builder.build())
        .map(
            reply -> {
              Map<Integer, Image> characterMap = HashMap.newHashMap(characterIds.size());
              List<CharacterImages> characterImagesList = reply.getCharacterImagesList();
              if (!characterImagesList.isEmpty()) {
                for (CharacterImages characterImages : characterImagesList) {
                  int key = characterImages.getCharacter();
                  List<Image> images = characterImages.getImagesList();

                  // only one character image ever supported by mid-tier
                  if (!images.isEmpty()) {
                    characterMap.put(key, images.getFirst());
                  }
                }
              }
              return characterMap;
            })
        .onErrorReturn(e -> Map.of());
  }

  public Single<Map<Long, APIImage>> getPersonArtWorkRemote(
      Collection<Long> personIds, Single<Builder> finalCriteria) {
    return finalCriteria.flatMap(
        criteria -> {
          try {
            return getPersonArtWorkRemote(personIds, criteria.build())
                .map(
                    images -> {
                      Map<Long, APIImage> artWorkMap = HashMap.newHashMap(personIds.size());
                      for (Entry<Long, Image> entry : images.entrySet()) {
                        artWorkMap.put(entry.getKey(), new APIArtWorkImageImpl(entry.getValue()));
                      }
                      return artWorkMap;
                    });
          } catch (Exception e) {
            logger.error("error returning person artwork", e);
            return Single.just(Map.of());
          }
        });
  }

  private Single<Map<Long, Image>> getPersonArtWorkRemote(
      Collection<Long> personIds, ImageCriteria finalCriteria) {

    GetPersonImagesRequest.Builder builder = GetPersonImagesRequest.newBuilder();
    builder.addAllPersonIds(personIds);
    builder.setCriteria(finalCriteria);
    return RxSingle.defer(imagesService::getPersonImages, builder.build())
        .map(
            reply -> {
              Map<Long, Image> personMap = HashMap.newHashMap(personIds.size());
              List<PersonImages> personImagesList = reply.getPersonImagesList();
              if (!personImagesList.isEmpty()) {
                for (PersonImages personImages : personImagesList) {
                  long key = personImages.getPersonId();
                  List<Image> images = personImages.getImagesList();

                  // only one person image
                  if (!images.isEmpty()) {
                    personMap.put(key, images.getFirst());
                  }
                }
              }
              return personMap;
            })
        .onErrorReturn(e -> Map.of());
  }

  public Single<Map<Integer, APIImage>> getStillImages(
      Collection<Integer> videoIds, Single<ImageCriteria> criteria) {
    // video ids to complete videos
    Collection<CompleteVideo> videos = VideoLookupUtils.convert(videoLookup.getVideos(videoIds));

    Single<GetVideoImagesRequest.Builder> builder =
        criteria.map(
            finalCriteria -> {
              try {
                if (videos.size() == 1) {
                  if (batchSizeOneStackDumper.watch()) {
                    MantisPublishContext.getCurrent()
                        .add("nil-batchsize-1.criteria", finalCriteria.toString());
                  }
                }

                if (ARTWORK_DETAILED_METRICS_ENABLED.get()) {
                  MantisPublishContext.getCurrent()
                      .add(
                          "api.artwork.request.explicit.batching."
                              + finalCriteria.getType()
                              + "."
                              + System.nanoTime(),
                          videoIds);
                }

                Collection<CompleteVideo> finalVideos =
                    scrubNullVideos.get() && videos.contains(null)
                        ? getScrubbedVideos(videos)
                        : videos;
                ImageResolverUtils.trackCall(
                    true,
                    finalCriteria.getType().getValue(),
                    finalVideos.size(),
                    finalCriteria.getUiFlavor().getValue(),
                    finalCriteria.getContentGrants());

                // Build request
                final GetVideoImagesRequest.Builder requestBuilder =
                    GetVideoImagesRequest.newBuilder();
                finalVideos.forEach(video -> requestBuilder.addVideos(Videos.toProtobuf(video)));
                requestBuilder.setCriteria(finalCriteria);
                return requestBuilder;
              } catch (Exception e) {
                logger.error("error getting images for criteria {}", finalCriteria, e);
                throw new APIServiceRuntimeException(
                    "error getting images for criteria " + finalCriteria, e);
              }
            });
    return getVideoArtwork(videoIds, builder);
  }

  private Collection<CompleteVideo> getScrubbedVideos(Collection<CompleteVideo> videos) {

    // dedupe and remove null videos; log metrics
    if (dedupeBatches.get()) {
      Set<Integer> uniqueVideoIds = new HashSet<>();
      List<CompleteVideo> finalVideos = new ArrayList<>();
      for (CompleteVideo video : videos) {
        if (video == null && logNullVideos.get()) {
          MantisPublishContext.getCurrent().add("nil-null-videos", true);
        }
        if (video != null) {
          if (!uniqueVideoIds.contains(video.getId())) {
            finalVideos.add(video);
          } else {
            DynamicCounter.increment("api.artwork.request.deduped", "source", "explicitBatching");
          }
          uniqueVideoIds.add(video.getId());
        }
      }
      return finalVideos;
    } else {
      List<CompleteVideo> finalVideos = videos.stream().filter(Objects::nonNull).toList();
      if (logNullVideos.get() && finalVideos.size() != videos.size()) {
        MantisPublishContext.getCurrent().add("nil-null-videos", true);
      }
      return finalVideos;
    }
  }

  @Override
  public Single<Map<APIAvatarKey, APIImage>> getProfileIconsByKey(
      Collection<APIAvatarKey> keys, APIProfileIconCriteria critera) {
    if (keys == null || keys.isEmpty() || critera == null) {
      return Single.error(new IllegalArgumentException("Must provide non-null keys and criteria"));
    }
    return abAdapterBase
        .getAllocationsAsProto(false, false)
        .toSingle()
        .flatMap(
            allocations ->
                streamingClientServiceAdapter
                    .getHostName()
                    .flatMap(ocaHostname -> getImages(keys, critera, allocations, ocaHostname)));
  }

  private Single<Map<APIAvatarKey, APIImage>> getImages(
      Collection<APIAvatarKey> keys,
      APIProfileIconCriteria critera,
      AllocResponse allocations,
      String ocaHostname) {
    Set<APIAvatarKey> unprocessed = new HashSet<>(keys);
    final Map<APIAvatarKey, APIImage> imageMap = HashMap.newHashMap(keys.size());
    Single<Map<APIAvatarKey, APIImage>> local = Single.just(imageMap);

    // fetch requested images
    Single<Map<APIAvatarKey, APIImage>> remote =
        getImagesRemoteWithFallbacks(critera, allocations, ocaHostname, unprocessed);

    return local.zipWith(
        remote,
        (imageMap1, imageMap2) -> {
          imageMap1.putAll(imageMap2);
          return imageMap1;
        });
  }

  private Single<Map<APIAvatarKey, APIImage>> getImagesRemoteWithFallbacks(
      APIProfileIconCriteria critera,
      AllocResponse allocations,
      String ocaHostname,
      final Set<APIAvatarKey> unprocessed) {

    Set<String> kidsLookup = HashSet.newHashSet(unprocessed.size());
    Map<String, String> localeLookup = HashMap.newHashMap(unprocessed.size());
    final GetAvatarsRequest.Builder builder = GetAvatarsRequest.newBuilder();
    for (APIAvatarKey key : unprocessed) {
      final AvatarImageKey.Builder keyBuilder =
          AvatarImageKey.newBuilder().setKey(key.getKey()).setKids(key.isKids());
      if (key.getLocale() != null) {
        keyBuilder.setLocale(key.getLocale());
        localeLookup.put(key.getKey(), key.getLocale());
      }
      builder.addKeys(keyBuilder.build());
      if (key.isKids()) {
        DynamicCounter.increment("api.imageservice.avatar.kids");
        kidsLookup.add(key.getKey());
      }
    }
    GetAvatarsRequest avatarsRequest =
        builder.setCriteria(getImageCriteria(critera, allocations, ocaHostname)).build();
    return RxSingle.defer(imagesService::getAvatars, avatarsRequest)
        .map(
            reply -> {
              Map<APIAvatarKey, APIImage> avatarMap = new HashMap<>();
              boolean secure = critera.isSecure();
              List<AvatarImages> avatarImagesList = reply.getAvatarImagesList();
              if (!avatarImagesList.isEmpty()) {
                for (AvatarImages avatarImages : avatarImagesList) {
                  List<Image> images = avatarImages.getImagesList();
                  String key = avatarImages.getImageKey();
                  boolean kids = kidsLookup.contains(key);
                  if (avatarImages.getImagesCount() > 0) {
                    Image image = images.getFirst();
                    String url = secure ? image.getSecureUrl() : image.getInsecureUrl();
                    if (url.isEmpty()) {
                      MantisPublishContext.getCurrent()
                          .add("emptyAvatarListRequest", key + "_" + avatarsRequest);
                      DynamicCounter.increment(
                          "api.imageservice.url.empty",
                          "kids",
                          Boolean.toString(kids),
                          "country",
                          RequestContextWrapper.get().getCountry().getId());
                    } else {
                      avatarMap.put(
                          APIAvatarKey.builder()
                              .key(key)
                              .kids(kids)
                              .locale(localeLookup.get(key))
                              .build(),
                          new APIImageImpl(image, key, secure));
                    }
                  } else {
                    MantisPublishContext.getCurrent()
                        .add("emptyAvatarListRequest", key + "_" + avatarsRequest);
                    DynamicCounter.increment(
                        "api.imageservice.avatar.empty",
                        "kids",
                        Boolean.toString(kids),
                        "country",
                        RequestContextWrapper.get().getCountry().getId());
                    logger.error(
                        "Empty avatar list, skipping key {} request {}", key, avatarsRequest);
                  }
                }
              }

              return avatarMap;
            });
  }

  private ImageCriteria getImageCriteria(
      APIProfileIconCriteria apiProfileIconCriteria,
      AllocResponse allocations,
      String ocaHostname) {

    APIAvatarType avatarType = APIAvatarType.AVATAR_TYPE_DEFAULT;
    if (apiProfileIconCriteria.getAvatarType() != null) {
      avatarType = apiProfileIconCriteria.getAvatarType();
    }

    Builder builder =
        ImageResolverUtils.buildImageCriteria()
            .setType(avatarType.getImageServiceName())
            .putParams("top_align", Boolean.toString(apiProfileIconCriteria.isTopAligned()))
            .putParams(
                "supports_localized_kids_profile",
                Boolean.toString(isLocalizedKidsProfileSupported()))
            .setWidth(apiProfileIconCriteria.getWidth())
            .setHeight(apiProfileIconCriteria.getHeight())
            .putAllAbAllocations(allocations.getAllocMap());
    Map<String, Object> params = apiProfileIconCriteria.getParams();
    if (params != null) {
      for (Entry<String, Object> entry : params.entrySet()) {
        builder.putParams(entry.getKey(), entry.getValue().toString());
      }
    }
    if (ocaHostname != null) {
      builder.putParams("hostname", ocaHostname);
    }
    if (apiProfileIconCriteria.getRecipePreferences() != null) {
      builder.addAllRecipes(apiProfileIconCriteria.getRecipePreferences());
    }

    // DNA-3051 pass image capabilities same as for other image calls
    Optional<APIClientCapabilitiesInternal> apiClientCapabilitiesInternal =
        APIClientCapabilitiesInternal.get(APIRequest.getCurrentRequest());
    if (apiClientCapabilitiesInternal.isPresent()) {
      Map<String, Collection<String>> map = apiClientCapabilitiesInternal.get().asMap();

      for (String key : map.keySet()) {
        ClientCapabilityList.Builder capabilitiesList =
            ClientCapabilityList.newBuilder().setKey(key);
        Collection<String> values = map.get(key);
        capabilitiesList.addAllCapabilities(values);
        builder.addClientCapabilities(capabilitiesList);
      }
    }
    return builder.build();
  }

  private static boolean isLocalizedKidsProfileSupported() {
    return Optional.ofNullable(APIRequest.getCurrentRequest())
        .map(APIRequest::getRequestContext)
        .map(APIRequestContext::deviceSupportsLocalizedKidsProfile)
        .orElse(false);
  }
}
