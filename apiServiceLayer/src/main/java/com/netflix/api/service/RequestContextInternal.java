package com.netflix.api.service;

import com.netflix.api.platform.context.ImmutableRequestContext;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.device.APIDeviceTypeImpl;
import com.netflix.i18n.NFLocale;
import com.netflix.lang.RequestVariable;
import com.netflix.streaming.dts.common.model.DeviceType;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;
import org.apache.commons.lang3.StringUtils;

/**
 * (<PERSON>zily) creates and holds request variables that are directly consumable by underlying libraries
 * (as opposed to {@link com.netflix.api.service.APIRequestContextImpl} that holds variables in the
 * format consumable by clients of API).
 *
 * <p>For instance, {@link com.netflix.server.context.RequestContext} represents locale preferences
 * as a comma separated string, libraries need a list of {@link com.netflix.i18n.NFLocale} objects,
 * and the conversion is done several times whenever a mid-tier library method needs it.
 */
public class RequestContextInternal {
  private static final RequestVariable<RequestContextInternal> current =
      new RequestVariable<>() {
        @Override
        public RequestContextInternal initialValue() {
          return new RequestContextInternal(
              RequestContextWrapper.get(), APIRequest.getCurrentRequest());
        }
      };

  private final ImmutableRequestContext requestContext;
  private final APIRequest apiRequest;

  private final ConcurrentHashMap<String, Object> variables = new ConcurrentHashMap<>();

  private RequestContextInternal(ImmutableRequestContext requestContext, APIRequest request) {
    this.requestContext = requestContext;
    this.apiRequest = request;
  }

  public static RequestContextInternal getCurrent() {
    return current.get();
  }

  private volatile List<NFLocale> localePreferences = null;
  private static final AtomicReferenceFieldUpdater<RequestContextInternal, List>
      localePreferencesUpdater =
          AtomicReferenceFieldUpdater.newUpdater(
              RequestContextInternal.class, List.class, "localePreferences");

  public List<NFLocale> getLocalePreferences() {
    if (localePreferences == null)
      localePreferencesUpdater.compareAndSet(this, null, loadLocalePreferences());
    return localePreferences;
  }

  public DeviceType getDeviceType() {
    if (apiRequest == null
        || apiRequest.getRequestContext() == null
        || apiRequest.getRequestContext().getDeviceType() == null) {
      return null;
    }
    return ((APIDeviceTypeImpl) apiRequest.getRequestContext().getDeviceType()).getDeviceType();
  }

  private List<NFLocale> loadLocalePreferences() {
    // locale preferences
    String localeList = requestContext.getLocaleList();

    List<NFLocale> nfLocales = new ArrayList<>();
    if (StringUtils.isNotBlank(localeList)) {
      for (String s : localeList.split(",")) {
        NFLocale locale = NFLocale.findInstance(s);
        if (locale != null) nfLocales.add(NFLocale.findInstance(s));
      }
    }
    if (nfLocales.isEmpty()) nfLocales.add(NFLocale.ENGLISH); // default
    return nfLocales;
  }

  public void put(String key, Object value) {
    variables.putIfAbsent(key, value);
  }

  public Object get(String key) {
    if (key != null) {
      return variables.get(key);
    }
    return null;
  }
}
