package com.netflix.api.service.account;

import java.util.Set;

public interface APIPlanDetail {

  long getPlanId();

  boolean isSDOnly();

  boolean isHDEnabled();

  boolean isUHDEnabled();

  /**
   * @return Max concurrent streams
   */
  int getMaxStreams();

  /**
   * @return The max number of devices members can download content to
   */
  int getMaxDownloadDevices();

  /** use PACS Experience */
  @Deprecated
  boolean isFreemium();

  /** use allowed device types */
  @Deprecated
  boolean isMobileOnly();

  Set<APIPlanDeviceType> getAllowedDeviceTypes();
}
