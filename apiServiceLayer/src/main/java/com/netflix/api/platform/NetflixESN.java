package com.netflix.api.platform;

import com.google.common.base.Strings;
import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.api.platform.util.MessageDigestStore;
import com.netflix.api.platform.util.ParamConstants;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.lang.RequestVariable;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.servo.monitor.DynamicCounter;
import jakarta.annotation.Nonnull;
import jakarta.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Optional;

/** Type representing Netflix device Electronic Serial Number (ESN) */
public class NetflixESN {

  private final String esn;
  private final Source source;

  private static final Property<Boolean> ENABLE_MICROCONTEXT_ESN =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.nfesn.microcontextEnabled", Boolean.class)
          .orElse(true);

  static final RequestVariable<NetflixESN> current = new RequestVariable<>();

  private static final Property<Boolean> PASSPORT_ESN_PRECEDENCE =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("eas.api.passportESNPrecedence", Boolean.class)
          .orElse(true);

  public enum Source {
    PARAM,
    CREDENTIALS,
    PASSPORT,
    HEADER,
    FALLBACK,
    MICROCONTEXT
  }

  NetflixESN(@Nonnull String esn, @Nonnull Source source) {
    this.esn = esn;
    this.source = source;
  }

  public static NetflixESN getCurrent() {
    return current.get();
  }

  public static NetflixESN create(
      HttpServletRequest request, IdentityResult<?> result, PassportIdentity identity) {

    current.set(null);

    Microcontext microcontext = CurrentMicrocontext.get();
    String microcontextEsn = microcontext.getEsn().orElse(null);

    NetflixESN oldEsn = getEsnLegacy(request, result, identity);

    if (oldEsn == null) {
      DynamicCounter.increment("api.esn.create", "source", "missing-old");
    }
    if (microcontextEsn == null) {
      DynamicCounter.increment("api.esn.create", "source", "missing-mc");
    }

    boolean areEsnsEqual =
        microcontextEsn != null && oldEsn != null && microcontextEsn.equals(oldEsn.getESN());
    DynamicCounter.increment("api.esn.create", "esn.equal", String.valueOf(areEsnsEqual));

    if (ENABLE_MICROCONTEXT_ESN.get() && microcontextEsn != null) {
      DynamicCounter.increment("api.esn.create", "source", "microcontext");
      return createFromESN(microcontextEsn, Source.MICROCONTEXT);
    } else {
      if (oldEsn != null) {
        return oldEsn;
      }
    }

    DynamicCounter.increment("api.esn.create", "source", "notfound");
    return null;
  }

  private static NetflixESN getEsnLegacy(
      HttpServletRequest request, IdentityResult<?> result, PassportIdentity identity) {

    if (PASSPORT_ESN_PRECEDENCE.get()) {
      if (identity != null) {
        Optional<String> esn = identity.getEsn();
        if (esn.isPresent()) {
          DynamicCounter.increment("api.esn.create", "source", "passport");
          return createFromESN(esn.get(), Source.PASSPORT);
        }
      }
    }

    if (result != null && result.getESN() != null) {
      DynamicCounter.increment("api.esn.create", "source", "credentials");
      return createFromESN(result.getESN(), Source.CREDENTIALS);
    }

    if (request != null) {
      String param = request.getParameter(ParamConstants.ESN);
      if (param != null) {
        DynamicCounter.increment("api.esn.create", "source", "paramesn");
        return createFromESN(param, Source.PARAM);
      }

      String paramLetter = request.getParameter(ParamConstants.E);
      if (paramLetter != null) {
        DynamicCounter.increment("api.esn.create", "source", "parame");
        return createFromESN(paramLetter, Source.PARAM);
      }

      String header = request.getHeader(ParamConstants.X_NETFLIX_ESN);
      if (header != null) {
        DynamicCounter.increment("api.esn.create", "source", "header");
        return createFromESN(header, Source.HEADER);
      }

      String fallbackEsn = request.getParameter("fallbackEsn");
      if (fallbackEsn != null) {
        DynamicCounter.increment("api.esn.create", "source", "fallback");
        return createFromESN(fallbackEsn, Source.FALLBACK);
      }
    }

    return null;
  }

  public static NetflixESN createFromESN(String esn, Source source) {
    NetflixESN nfESN = null;
    if (!Strings.isNullOrEmpty(esn)) {
      nfESN = new NetflixESN(esn, source);
    }
    current.set(nfESN);
    return nfESN;
  }

  public String getESN() {
    return esn;
  }

  public Source getSource() {
    return source;
  }

  /**
   * @deprecated this won't return an ESN prefix that uniquely identifies a device type, use the
   *     device type id instead
   */
  @Deprecated
  public String getPrefix() {
    // using the old way since that's what TrackerHandlerTest wants and I don't know enough about
    // the requirements
    // to make a change
    return extractPrefixBeforeDash(esn);
  }

  /**
   * Older approach to grabbing ESN prefixes.
   *
   * @deprecated this won't return an ESN prefix that uniquely identifies a device type, use the
   *     device type id instead
   */
  @Deprecated
  public static String extractPrefixBeforeDash(String esn) {
    if (esn == null) {
      return null;
    }
    int nIndex = esn.indexOf('-');
    if (nIndex == -1) return esn;
    return esn.substring(0, nIndex);
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + esn.hashCode();
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    NetflixESN other = (NetflixESN) obj;
    return esn.equals(other.esn);
  }

  public int getBucket() {
    return getBucket(100);
  }

  public int getBucket(int bucketSize) {
    int bucket = -1;
    if (!esn.isEmpty()) {
      int result = 0;
      MessageDigest local = MessageDigestStore.getInstance().getSha1();
      local.update(esn.getBytes(StandardCharsets.ISO_8859_1));
      byte[] baDigest = local.digest();
      for (byte aBaDigest : baDigest) {
        result += (aBaDigest & 0xff) + 0x100;
      }
      bucket = Math.abs(result % bucketSize);
    }
    return bucket;
  }

  @Override
  public String toString() {
    return "NetflixESN{" + "esn='" + esn + '\'' + ", source=" + source + '}';
  }
}
