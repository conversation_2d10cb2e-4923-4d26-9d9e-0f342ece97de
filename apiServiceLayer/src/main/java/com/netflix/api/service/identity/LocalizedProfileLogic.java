package com.netflix.api.service.identity;

import static java.util.concurrent.CompletableFuture.completedFuture;

import com.netflix.api.grpc.GrpcCallHelpers;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import com.netflix.dloc.protogen.DlocServiceGrpc.DlocServiceStub;
import com.netflix.dloc.protogen.ObelixRequestSimpleLanguage;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import jakarta.annotation.Nonnull;
import java.util.Optional;
import java.util.concurrent.CompletionStage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class LocalizedProfileLogic {

  private static final String DEFAULT_KIDS_PROFILE_NAME = "Kids";
  private static final String DEFAULT_FAMILY_PROFILE_NAME = "Family";

  private final DlocServiceStub stub;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public LocalizedProfileLogic(@GrpcSpringClient("dloc") DlocServiceStub stub) {
    this.stub = stub;
  }

  private static boolean isLocalizedKidsProfileSupported() {
    return Optional.ofNullable(APIRequest.getCurrentRequest())
        .map(APIRequest::getRequestContext)
        .map(APIRequestContext::deviceSupportsLocalizedKidsProfile)
        .orElse(false);
  }

  private boolean isDefaultKidsProfileName(AccountProfileRemote accountProfile) {
    return DEFAULT_KIDS_PROFILE_NAME.equals(accountProfile.getBoxedProfileName());
  }

  /** show localized experience? */
  public boolean showLocalizedDefaultKidsProfileName(AccountProfileRemote accountProfile) {
    return isLocalizedKidsProfileSupported() && isDefaultKidsProfileName(accountProfile);
  }

  /**
   * Calls DlocService to get the localized kids profile name for the given locale.
   *
   * @param locale the locale of the requesting profile.
   * @return the localized kids profile name, or the default name "Kids" if the call failed.
   */
  public CompletionStage<String> getDefaultKidsProfileName(String locale) {
    if (!isLocalizedKidsProfileSupported()) return completedFuture(DEFAULT_KIDS_PROFILE_NAME);

    return getLocalizedProfileName("extensibility_kids_title", locale, DEFAULT_KIDS_PROFILE_NAME);
  }

  private CompletionStage<String> getLocalizedProfileName(
      String key, String locale, String defaultValue) {
    var request =
        ObelixRequestSimpleLanguage.newBuilder()
            .setNamespace("Default")
            .setBundle("tvui-darwin-ql")
            .setLanguage(locale)
            .setKey(key)
            .build();

    return GrpcCallHelpers.Future.call(stub::getSimpleStringPropertyWithLanguage, request)
        .thenApply(reply -> reply.getOptionalValue().orElse(defaultValue));
  }

  /**
   * Calls DlocService to get the localized family profile name for the given profile.
   *
   * @param accountProfile the profile associated with the request. Their primary language is used
   *     to localize.
   * @return the localized family profile name, or the default name "Family" if the call failed.
   */
  public CompletionStage<String> getFamilyProfileName(
      @Nonnull AccountProfileRemote accountProfile) {
    var profileName = accountProfile.getBoxedProfileName();
    if (!DEFAULT_FAMILY_PROFILE_NAME.equals(profileName)) return completedFuture(profileName);

    var locale = accountProfile.getBoxedPrimaryLang();
    return getLocalizedProfileName("family_profile_name", locale, DEFAULT_FAMILY_PROFILE_NAME);
  }
}
