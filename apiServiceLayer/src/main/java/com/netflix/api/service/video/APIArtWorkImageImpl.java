package com.netflix.api.service.video;

import com.google.common.base.Preconditions;
import com.ibm.icu.util.ULocale;
import com.netflix.images.protogen.Image;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * Encapsulates an image - width, height and url of the image. note that this is backed by proto
 * definition (images service)
 */
public class APIArtWorkImageImpl extends APIImageImpl {

  private final Image source;

  public APIArtWorkImageImpl(Image source) {
    super(
        null,
        0,
        0,
        "unknown",
        Preconditions.checkNotNull(source).getKey(),
        source.getBoxedSourceFileID());
    this.source = source;
  }

  @Override
  public String getUrl() {
    // most devices ask for insecure urls, default to that.
    return source.getInsecureUrl();
  }

  @Override
  public int getWidth() {
    return source.getWidth();
  }

  @Override
  public int getHeight() {
    return source.getHeight();
  }

  @Override
  public ImageExtension getExtension() {
    return fromString(source.getBoxedRecipeName(), getUrl());
  }

  @Override
  public String getUrl(Boolean secure) {
    return secure ? source.getSecureUrl() : source.getInsecureUrl();
  }

  @Override
  public Map<String, String> getAttributes() {
    return this.source.getAttributesMap();
  }

  @Override
  public Map<String, Collection<String>> getMultiValueAttributes() {
    Map<String, Collection<String>> result =
        HashMap.newHashMap(this.source.getMultiValueAttributesMap().size());
    this.source.getMultiValueAttributesMap().forEach((v, i) -> result.put(v, i.getValueList()));
    return result;
  }

  @Override
  public boolean isRightToLeft() {
    return new ULocale(source.getLocale().getId()).isRightToLeft();
  }

  public Image getSource() {
    return source;
  }

  @Override
  public boolean isSmoky() {
    return source.getIsSmoky();
  }
}
