package com.netflix.api.service.list;

import static com.netflix.map.annotation.MapAnnotationConstants.CHARACTERIDV2;

import com.netflix.map.datatypes.MapAnnotations;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
public class MapResponseUtils {

  private MapResponseUtils() {}

  public static <T> T getAnnotation(String name, MapAnnotations annotations, Class<T> type) {
    if (annotations == null) return null;
    Object o = annotations.get(name);
    return getTypedObject(type, o);
  }

  private static <T> T getTypedObject(Class<T> type, Object o) {
    if (o == null) return null;
    if (type.isInstance(o)) {
      return type.cast(o);
    } else if (type == String.class) {
      return type.cast(o.toString());
    } else {
      throw new RuntimeException(
          String.format(
              "Cannot convert %s of type %s to %s", o, o.getClass().getName(), type.getName()));
    }
  }

  static final Map<String, String> MAP_ANNOTATIONS_DEPRECATED_TO_CURRENT =
      Map.of("characterId", CHARACTERIDV2);
}
