package dna.api.service.model;

import static java.util.Map.entry;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.google.protobuf.StringValue;
import com.google.protobuf.util.JsonFormat;
import dna.api.util.JsonUtil;
import java.util.Map;
import org.junit.jupiter.api.Test;

class UstSerdeTest {

  @Test
  void testSerde() throws Exception {
    var builder =
        ust.variant.v1.Variant.newBuilder()
            .setId(StringValue.of("foo"))
            .setCorrelationId("bar")
            .setTemplate("mytemplate")
            .putArgumentData("imakey", "imavalue")
            .putArgumentData("nutherkey", "nuthervalue");

    var protoJson = JsonFormat.printer().print(builder.build());
    var variant = JsonUtil.mapper.readValue(protoJson, Variant.class);

    var data = Map.ofEntries(entry("imakey", "imavalue"), entry("nutherkey", "nuthervalue"));

    var expected =
        new Variant()
            .setId("foo")
            .setCorrelationId("bar")
            .setTemplate("mytemplate")
            .setArgumentData(data);

    assertEquals(expected, variant);
  }
}
