package com.netflix.api.service.identity;

import com.netflix.api.service.profiles.APILolopi;
import java.util.Map;
import rx.Completable;
import rx.Observable;
import rx.Single;

/**
 * This API service interface provides access to all profile related information (and CRUD
 * operations). Each subscription account may have 1 or more profiles.
 *
 * <p>Profiles allow different members of a household to set different taste preferences. Also, each
 * profile's viewing history is tracked separately. It allows Netflix to merchandise content
 * tailored to each member of a household.
 *
 * <p>A primary profile corresponds to the account owner. A configured number (4) of other profiles
 * can be created and edited. Switching between profiles is frictionless, i.e., different profiles
 * do not have access restrictions to other profiles under the same account.
 *
 * <p>To switch between profiles, users simply pass the guid of the profile to switch to as request
 * parameter or a request header named "{@code switchProfileGuid}". Since switching and
 * authorization needs to happen before control is given to the client groovy script, there is no
 * API provided for switching between profiles, much like user authentication.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/********/APIUser.class.png"
 * alt="Class Diagram: APIUser">
 */
@SuppressWarnings("unused")
public interface APIUserProfileService {

  /**
   * Create a new profile. This ignores the profile creation lock on the owner Callers are expected
   * to check profileCreationLocked flag before calling this API
   *
   * @param accountOwner account owner
   * @return an {@code Observable} that emits a {@code APIUserProfile} representing the customer
   *     profile to create
   */
  @Deprecated
  APIUserProfileBuilder createUserProfileWithOwner(final APIUser accountOwner);

  /**
   * Create a new profile.
   *
   * @deprecated use {@link #createUserProfileWithOwner(APIUser)} (APIUser)} This API should solely
   *     used only by legacy devices. This call will fail if the profileCreationLock flag is set on
   *     the account owner in order to support parental control setting on legacy device.
   * @param accountOwner account owner
   * @return an {@code Observable} that emits a {@code APIUserProfile} representing the customer
   *     profile to create
   */
  @Deprecated
  APIUserProfileBuilder createUserWithOwner(final APIUser accountOwner);

  /**
   * Edit an existing customer profile.
   *
   * @param userToEdit user to edit
   * @return an {@code Observable} that emits a {@code APIUserProfile} representing the edited
   *     customer profile
   * @deprecated use {@link #editUserProfile(APIUser)} This API will be do additional checks for
   *     parental control support on devices
   */
  @Deprecated
  APIUserProfileBuilder editUser(final APIUser userToEdit);

  /**
   * Edit an existing customer profile.
   *
   * @param userToEdit user to edit
   * @return an {@code Observable} that emits a {@code APIUserProfile} representing the edited
   *     customer profile
   */
  @Deprecated
  APIUserProfileBuilder editUserProfile(final APIUser userToEdit);

  /**
   * Delete a customer profile. It will fail if the {@code userToDelete} does not share the same
   * owner as the currently-logged in user, or is the owner's profile. Primary profiles may not be
   * deleted.
   *
   * @param guid the guid of the profile to delete
   * @return complete if success and error if failed
   */
  @Deprecated
  Completable deleteProfileCompletable(String guid);

  /**
   * Discover whether or not more profiles can be created under this account. Typically, number of
   * profiles is limited to 5. This API will also check profileCreationLock flag on subscriber for
   * parental control on legacy device
   *
   * @param user the customer whose account you are querying
   * @return an {@code Observable} that emits a single Boolean value @Deprecated use {@link
   *     #canCreateUserProfile(APIUser)}
   */
  @Deprecated
  Observable<Boolean> canCreateProfile(final APIUser user);

  /**
   * Discover whether or not more profiles can be created under this account. Typically, number of
   * profiles is limited to 5.
   *
   * @param user the customer whose account you are querying
   * @return an {@code Observable} that emits a single Boolean value
   */
  @Deprecated
  Observable<Boolean> canCreateUserProfile(final APIUser user);

  /**
   * Fetches the lolopi (list of list of profile icons)
   *
   * @param user context for the lolopi
   * @param requestAnnotations a map of annotations for the lolopi call
   * @return the lolopi as a single or error
   */
  @Deprecated
  Single<APILolopi> getLolopi(APIUser user, Map<String, Object> requestAnnotations);
}
