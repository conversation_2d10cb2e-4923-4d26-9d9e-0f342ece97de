package com.netflix.api.dependencies.vhs;

import com.netflix.vhs.protogen.GetViewingHistoryRequest;
import java.time.Duration;
import java.time.Instant;

public final class ViewingHistoryRequestBuilder {

  private final GetViewingHistoryRequest.Builder requestBuilder;

  public ViewingHistoryRequestBuilder(Long profileId) {
    if (profileId == null) {
      throw new IllegalArgumentException("visitor is required.");
    }
    requestBuilder = GetViewingHistoryRequest.newBuilder().setProfileId(profileId);
  }

  public void forLast(int numDays) {
    requestBuilder
        .getFilterBuilder()
        .getDetailConstraintsBuilder()
        .setAfterTimeInMillisSinceEpoch(
            Instant.now().minus(Duration.ofDays(numDays)).toEpochMilli());
  }

  public void includingSupplementalVideos() {
    requestBuilder.setIncludingSupplementalPlays(true);
  }

  public void includingAutoplays() {
    // IncludeUIAutoPlaysConstraint is not supported; use request builder directly
    requestBuilder.setIncludingUIAutoPlays(true);
  }

  public void forTotalWatched(int numSeconds) {
    requestBuilder
        .getFilterBuilder()
        .getDetailConstraintsBuilder()
        .setMinimumDurationInSecs(numSeconds);
  }

  public void forVideo(int videoId) {
    requestBuilder.getFilterBuilder().getMovieIdsBuilder().addMovieId(videoId);
  }

  public GetViewingHistoryRequest getRequest() {
    return requestBuilder.build();
  }
}
