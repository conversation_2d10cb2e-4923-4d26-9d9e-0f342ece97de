package com.netflix.api.service.identity;

import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.api.service.APIListBuilderBehaviorImpl;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.service.identity.APIUser.Experience;
import com.netflix.api.service.identity.exceptions.BadProfileNameException;
import com.netflix.api.service.identity.exceptions.ProfileNameTooLongException;
import com.netflix.api.service.identity.exceptions.ProfileNameTooShortException;
import com.netflix.subscriberservice.common.MaturityLevel;
import java.util.regex.Matcher;

abstract class APIUserManagementBehavior
    extends APIListBuilderBehaviorImpl<APIUser, IdentityResult> {

  protected String profileName;
  protected Experience experience;
  protected String iconName;
  protected MaturityLevel maturityLevel;
  protected Boolean firstUse;
  protected String language;
  protected Boolean autoplayEnabled;
  protected Boolean cookieDisclosureAccepted;
  protected final APIProfileErrors.Localizer localizer;
  protected final ContextManager contextManager;
  protected final APIUserFactory userFactory;
  protected String profileLockPin;
  protected Boolean profileCreationLocked;
  protected Boolean removeProfileLockPin;
  protected Integer rawMaturityValue;

  public APIUserManagementBehavior(
      APIUserFactory userFactory, APIProfileErrors.Localizer localizer) {
    this.localizer = localizer;
    this.contextManager = new ProductionContextManager();
    this.userFactory = userFactory;
  }

  public void setProfileName(String s) {
    if (s != null) {
      this.profileName = s;
    }
  }

  public void setExperience(Experience experience) {
    if (experience != null) {
      this.experience = experience;
    }
  }

  public void setAvatarName(String s) {
    if (s != null) {
      this.iconName = s;
    }
  }

  public void setMaturity(APIMaturity apiMaturity) {
    if (apiMaturity != null) {
      this.maturityLevel = MaturityLevel.valueOf(apiMaturity.name());
    }
  }

  public void setMaturity(Integer rawMaturityValue) {
    if (rawMaturityValue != null) {
      this.rawMaturityValue = rawMaturityValue;
    }
  }

  public void setAutoplayEnabled(Boolean autoplayEnabled) {
    if (autoplayEnabled != null) {
      this.autoplayEnabled = autoplayEnabled;
    }
  }

  public void setCookieDisclosureAccepted(Boolean cookieDisclosureAccepted) {
    if (cookieDisclosureAccepted != null) {
      this.cookieDisclosureAccepted = cookieDisclosureAccepted;
    }
  }

  public void setFirstUse(boolean isFirstUse) {
    this.firstUse = isFirstUse;
  }

  public void setLanguage(String s) {
    if (s != null) {
      this.language = s;
    }
  }

  public void setProfileLockPin(String profileLockPin) {
    if (profileLockPin != null) {
      this.profileLockPin = profileLockPin;
    }
  }

  public void removeProfileLockPin(Boolean removeProfileLockPin) {
    if (removeProfileLockPin != null) {
      this.removeProfileLockPin = removeProfileLockPin;
    }
  }

  public void setProfileCreationLocked(Boolean profileCreationLocked) {
    if (profileCreationLocked != null) {
      this.profileCreationLocked = profileCreationLocked;
    }
  }

  protected void validateName() throws APIServiceRuntimeException {
    if (profileName == null || profileName.isEmpty()) {
      String errorMsg =
          localizer.localize(
              APIProfileErrors.PROFILE_NAME_TOO_SHORT_MESSAGE_KEY,
              APIProfileErrors.DEFAULT_PROFILE_NAME_TOO_SHORT_MESSAGE);
      throw new ProfileNameTooShortException(errorMsg);
    }
    if (profileName.length() > APIProfileErrors.MAX_ALLOWED_PROFILE_NAME_LENGTH) {
      String errorMsg =
          localizer.localize(
              APIProfileErrors.PROFILE_NAME_TOO_LONG_MESSAGE_KEY,
              APIProfileErrors.DEFAULT_PROFILE_NAME_TOO_LONG_MESSAGE);
      throw new ProfileNameTooLongException(errorMsg);
    }
    Matcher invalidCharMatcher = APIProfileErrors.invalidCharRegex.matcher(profileName);
    if (invalidCharMatcher.find()) {
      String errorMsg =
          localizer.localize(
              APIProfileErrors.PROFILE_NAME_CONTAINS_INVALID_CHARS_MESSAGE_KEY,
              APIProfileErrors.DEFAULT_PROFILE_NAME_CONTAINS_INVALID_CHARACTER_MESSAGE);
      throw new BadProfileNameException(errorMsg);
    }
  }

  /**
   * These 2 methods are really hacky and will change once Subscriber is more explicit about
   * distinguishing errors at its level
   */
  protected boolean isDuplicateIconException(Exception ex) {
    if (ex != null && ex.getMessage() != null) {
      String errorMsg = ex.getMessage();
      return errorMsg.contains("Profile Avatar Image must be unique");
    }
    return false;
  }

  protected boolean isDuplicateNameException(Exception ex) {
    if (ex != null && ex.getMessage() != null) {
      String errorMsg = ex.getMessage();
      return errorMsg.contains("Profile names must be unique");
    }
    return false;
  }

  protected String getDuplicatedIconErrorMessage() {
    return localizer.localize(
        APIProfileErrors.DUPLICATE_PROFILE_ICON_MESSAGE_KEY,
        APIProfileErrors.DEFAULT_DUPLICATE_PROFILE_ICON_MESSAGE);
  }

  protected String getDuplicateNameErrorMessage() {
    return localizer.localize(
        APIProfileErrors.DUPLICATE_PROFILE_NAME_MESSAGE_KEY,
        APIProfileErrors.DEFAULT_DUPLICATE_PROFILE_NAME_MESSAGE);
  }

  interface CacheInvalidator {
    void invalidateForUser(APIUser user);
  }

  class ProductionCacheInvalidator implements CacheInvalidator {

    @Override
    public void invalidateForUser(APIUser user) {
      APIUser refreshedUser =
          userFactory.createUser(
              APIUserUtil.getCustomerId(user), APIUserUtil.getAccountOwnerId(user), true);
      // must invoke a method to trigger subscriber lookup
      refreshedUser.getCustomerGUID();
    }
  }

  interface ContextManager {
    void refreshUserOnApiRequest();
  }

  static class ProductionContextManager implements ContextManager {

    @Override
    public void refreshUserOnApiRequest() {
      APIRequest currentRequest = APIRequest.getCurrentRequest();
      if (currentRequest != null) {
        APIUser user = currentRequest.getUser();
        if (user instanceof APIUserInternal) {
          ((APIUserInternal) user).invalidateInternalState();
        }
      }
    }
  }
}
