package com.netflix.api.service.identity;

import com.google.common.base.Preconditions;
import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.membership.MembershipStatus;
import java.util.Optional;

public class IdentityUserResult extends IdentityResult<APIUser> {

  private final APIUserInternal user;
  private final String esn;

  public IdentityUserResult(APIUserInternal user) {
    this(user, null);
  }

  public IdentityUserResult(APIUserInternal user, String esn) {
    this.user = Preconditions.checkNotNull(user, "User cannot be null");
    this.esn = esn;
  }

  @Override
  public APIUser getData() {
    return user;
  }

  @Override
  public boolean isSuccess() {
    return true;
  }

  @Override
  public Long getCustomerId() {
    return APIUserUtil.getCustomerId(user);
  }

  @Override
  public Long getAccountOwnerId() {
    return APIUserUtil.getAccountOwnerId(user);
  }

  @Override
  public String getSignupCountry() {
    return user.getCountryOfSignup();
  }

  @Override
  public String getRegistrationCountry() {
    return user.getCountryOfRegistration();
  }

  @Override
  public String getESN() {
    return esn;
  }

  @Override
  public Optional<MembershipStatus> getMembershipStatus() {
    return Optional.of(MembershipStatus.valueOf(user.getMembershipStatus().name()));
  }

  @Override
  public String getCustomerGuid() {
    return user.getCustomerGUID();
  }

  @Override
  public Integer getDeviceType() {
    return null;
  }

  @Override
  public IdentitySource getDeviceInformationSource() {
    return IdentitySource.NONE;
  }

  @Override
  public IdentitySource getUserInformationSource() {
    return IdentitySource.NONE;
  }
}
