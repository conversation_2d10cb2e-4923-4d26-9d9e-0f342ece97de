package com.netflix.api.service.video;

import com.netflix.videometadata.l10n.VMSL10NProviderDelegate;
import com.netflix.videometadata.type.Certification;

public class APICertificationImpl implements APICertification {
  private final Certification certification;
  private final VMSL10NProviderDelegate vmsl10NProviderDelegate;

  APICertificationImpl(
      Certification certification, VMSL10NProviderDelegate vmsl10NProviderDelegate) {
    this.certification = certification;
    this.vmsl10NProviderDelegate = vmsl10NProviderDelegate;
  }

  @Override
  public String getValue() {
    if (certification == null || certification.getMovieCert() == null) {
      return null;
    }
    return certification.getMovieCert().getName();
  }

  @Override
  public String getBoard() {
    if (certification == null || certification.getCertSystem() == null) {
      return null;
    }
    return certification.getCertSystem().getName();
  }

  @Override
  public Integer getBoardId() {
    if (certification == null || certification.getCertSystem() == null) {
      return null;
    }
    return certification.getCertSystem().getId();
  }

  @Override
  public int getMaturityLevel() {
    if (certification == null
        || certification.getMovieCert() == null
        || certification.getMovieCert().getMaturityLevel() == null) {
      return -1;
    }

    return certification.getMovieCert().getMaturityLevel();
  }

  @Override
  public String getMaturityDescription() {
    if (certification == null
        || certification.getMovieCert() == null
        || certification.getMovieCert().getMaturityLevel() == null) {
      return null;
    }

    return certification.getMovieCert().getDescription();
  }

  @Override
  public String getVideoSpecificRatingReason() {
    if (certification == null
        || certification.getMovieCert() == null
        || certification.getMovieCert().getMaturityLevel() == null) {
      return null;
    }

    return certification.getMovieCert().getVideoSpecificRatingReason();
  }

  @Override
  public APIMovieRatingReason getRatingReason() {
    if (certification == null
        || certification.getMovieCert() == null
        || certification.getMovieCert().getRatingReason() == null) {
      return null;
    }

    return new APIMovieRatingReasonImpl(
        certification.getMovieCert().getRatingReason(), vmsl10NProviderDelegate);
  }

  @Override
  public String getCertSystemConfirmationId() {
    if (certification == null || certification.getMovieCert() == null) {
      return null;
    }
    return certification.getMovieCert().getCertSystemConfirmationId();
  }

  @Override
  public Integer getRatingId() {
    if (certification == null || certification.getMovieCert() == null) {
      return null;
    }
    return certification.getMovieCert().getRatingId();
  }
}
