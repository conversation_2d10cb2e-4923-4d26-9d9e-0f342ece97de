package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SearchVideoCarousel {
  private String displayString;

  private List<SearchPageVideo> videos;

  public String getDisplayString() {
    return displayString;
  }

  public List<SearchPageVideo> getVideos() {
    return videos;
  }

  public SearchVideoCarousel setDisplayString(String displayString) {
    this.displayString = displayString;
    return this;
  }

  public SearchVideoCarousel setVideos(List<SearchPageVideo> videos) {
    this.videos = videos;
    return this;
  }
}
