package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PlaybackVideoResolution {
  private Integer height;

  private Integer width;

  public Integer getHeight() {
    return height;
  }

  public Integer getWidth() {
    return width;
  }

  public PlaybackVideoResolution setHeight(Integer height) {
    this.height = height;
    return this;
  }

  public PlaybackVideoResolution setWidth(Integer width) {
    this.width = width;
    return this;
  }
}
