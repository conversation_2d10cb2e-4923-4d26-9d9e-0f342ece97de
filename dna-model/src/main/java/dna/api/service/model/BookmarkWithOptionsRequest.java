package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class BookmarkWithOptionsRequest {
  /** Get bookmark for the profile that belongs to the given profile GUID. */
  private String profileGUID;

  public String getProfileGUID() {
    return profileGUID;
  }

  public BookmarkWithOptionsRequest setProfileGUID(String profileGUID) {
    this.profileGUID = profileGUID;
    return this;
  }
}
