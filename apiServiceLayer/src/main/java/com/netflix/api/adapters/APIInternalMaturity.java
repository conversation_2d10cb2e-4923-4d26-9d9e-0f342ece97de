package com.netflix.api.adapters;

import java.util.Objects;
import javax.annotation.Nonnull;

public record APIInternalMaturity(Integer maturityLevel, Boolean excludeFromParentalControls) {

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIInternalMaturity(Integer level, Boolean fromParentalControls))) {
      return false;
    }
    return Objects.equals(maturityLevel(), level)
        && Objects.equals(excludeFromParentalControls(), fromParentalControls);
  }

  @Override
  public int hashCode() {
    return Objects.hash(maturityLevel(), excludeFromParentalControls());
  }

  @Nonnull
  public String toString() {
    return "APIInternalMaturity(maturityLevel="
        + this.maturityLevel()
        + ", excludeFromParentalControls="
        + this.excludeFromParentalControls()
        + ")";
  }
}
