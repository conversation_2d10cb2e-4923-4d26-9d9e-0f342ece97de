apply plugin: 'netflix.spring-boot-netflix-application'
apply plugin: 'netflix.lombok'
apply plugin: 'nebula.facet'

buildscript {
    dependencies {
    }
}

configurations {
    apiAgent.transitive = false
}

configurations.configureEach({
//    exclude group: 'org.slf4j', module: 'log4j-over-slf4j'
    exclude group: "org.apache.servicemix.bundles", module: "org.apache.servicemix.bundles.bcprov-jdk16"
    exclude group: "netflix:nf-admin-plugins"

    resolutionStrategy {
        force "org.slf4j:slf4j-api:2.0.9"
    }
})

apply from: file('../dependency-properties.gradle')
apply from: file('../dependency-overrides.gradle')

// Set up for the smoke test so we can execute ./gradlew smokeTest
facets {
    smokeTest {
        parentSourceSet = 'test'
    }
}
dependencies {
    implementation project(':api-service-layer')
    implementation project(':api-global-dependencies')
    implementation project(':dna-router')
    implementation 'org.apache.logging.log4j:log4j-slf4j2-impl:2.21.1'

    testImplementation 'com.netflix.spring:spring-boot-netflix-starter-test'
    testImplementation('com.netflix.passport.test:passport-test-core:latest.release')
    testImplementation('com.netflix.peas:peas-test-spectator:latest.release')
    testImplementation("org.springframework:spring-test")
    testImplementation("org.springframework:spring-web")

    // corretto crypto
    implementation 'software.amazon.cryptools:AmazonCorrettoCryptoProvider:1.+:linux-x86_64'

    // SBN
    implementation 'com.netflix.spring:spring-boot-netflix-starter-rest-server'
    implementation 'com.netflix.spring:spring-boot-netflix-starter-security-authn-embedded'
    implementation 'com.netflix.spring:spring-boot-netflix-starter-grpc-client'
    implementation 'com.netflix.spring:spring-boot-netflix-starter-grpc-client-evcache'
    implementation 'com.netflix.spring:spring-boot-netflix-starter-niws'
    implementation 'com.netflix.spring:spring-boot-netflix-starter-aws'
    implementation 'org.springframework:spring-test'
    implementation 'com.netflix.spring:spring-boot-netflix-starter-governator-guice-bridge'
    implementation 'com.netflix.ksclient:ksclient-api'
    implementation 'com.netflix.ksclient:ksclient-spring-boot'

    // Cryptex 2
    implementation 'netflix:cryptex-agent-sbn:latest.release'
    implementation 'netflix:server-context'
    implementation 'netflix:nfi18n-core'
    implementation 'org.json:json'

    // compilation breaks because we rely on some of the base server classes in our filters, we should remove those references and these dependencies in the future
    implementation 'netflix:base-server'
    implementation 'netflix:base-server-explorer:latest.release'
}


task buildScriptDependencies(type: DependencyReportTask) {
    configurations = project.buildscript.configurations
}

eclipse {
    wtp {
        component {
            contextPath = '/'
        }
    }
}

tasks.withType(JavaCompile) {
//    dependsOn ':trackChanges'
    it.options.compilerArgs.add("-implicit:none")
}

task sourceProperties {
    doFirst {
        Properties props = new Properties()
        // we might get here before the build directory was created
        if (!project.layout.buildDirectory.asFile.get().exists()) {
            project.layout.buildDirectory.asFile.get().mkdir()
        }
        File propsFile = new File(project.layout.buildDirectory.asFile.get().toString() + "/sourceVersion.properties")
        if (propsFile.exists()) {
            propsFile.delete()
        } else {
            propsFile.createNewFile()
        }
        props.setProperty('source.version', System.getProperty('GIT_COMMIT') + ".h" + System.getProperty('BUILD_NUMBER', "1"))
        props.setProperty('builtBy', System.getProperty('user.name'))
        props.setProperty('buildDate', new Date().format('yyyy-MM-dd_HH:mm:ss'))
        props.setProperty('buildJob', System.getProperty('JOB_NAME', "LOCAL"))
        props.setProperty('buildNumber', System.getProperty('BUILD_NUMBER', "1"))
        props.setProperty('buildId', System.getProperty('BUILD_ID', "LOCAL-BUILD"))
        props.store(propsFile.newWriter(), null)
    }
}

tasks.named('test', Test).configure {
    testLogging.showStandardStreams = true
    useJUnitPlatform()
}

// Print out full stack traces when our tests fail to assist debugging (e.g., when scanning Jenkins console output)
tasks.withType(Test).configureEach {
    useJUnitPlatform()
    testLogging {
        exceptionFormat = 'full'
        events "PASSED", "STARTED", "FAILED", "SKIPPED"
    }
}

ospackage {
    requires('metatron-client')
    requires('cryptex-agent')

    from(configurations.apiAgent) {
        into '/apps/apiAgent'
    }
}

buildDeb {
    requires('atlas-agent')
}

task writeDebFile {
    doLast {
        buildDeb.getOutputs().getFiles().each {
            File fName ->
                def fString = fName.name
                println fString
                File rpmProps = new File("${project.layout.buildDirectory.asFile.get().toString()}/deb.properties")
                try {
                    def apiDeb = fString.substring(0, fString.lastIndexOf(".deb"))
                    def apiVersion = apiDeb.substring(4, apiDeb.lastIndexOf("_all"))
                    rpmProps.withWriter { out ->
                        out.writeLine("apiDeb=${apiDeb}")
                        out.writeLine("apiVersion=${apiVersion}")
                    }
                    new File("${project.layout.buildDirectory.asFile.get().toString()}/${apiDeb}.txt").withWriter { out -> out.write(apiDeb) }
                } catch (IndexOutOfBoundsException e) {
                    println "Ignoring ${fString} " + e
                }
        }
    }
}
buildDeb.finalizedBy writeDebFile

buildDeb.enabled = true

application {
    mainClass = 'com.netflix.api.ApiServer'
}

springBootNetflixConfig {
    mesh {
        enabled = true
    }
}

// Enable local debugging on the dev laptop
bootRun {
    jvmArgs = ["-Xdebug", "-Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005", "-Djava.net.preferIPv4Stack=true"]
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring:spring-boot-netflix-bom:latest.release'
}
