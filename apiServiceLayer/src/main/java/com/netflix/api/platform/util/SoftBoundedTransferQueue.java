package com.netflix.api.platform.util;

import jakarta.annotation.Nonnull;
import java.util.Collection;
import java.util.concurrent.LinkedTransferQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TransferQueue;

/**
 * A {@link java.util.concurrent.BlockingQueue} implementation based off of {@link
 * LinkedTransferQueue} for usage in cases in which some sort of upper limit of capacity should try
 * to be enforced but avoids the excessive locking present in most hard bounded blocking queue
 * implementations such as {@link java.util.concurrent.ArrayBlockingQueue}. In highly concurrent
 * cases we may exceed the capacity limits but will eventually start to reject adding values to the
 * queue.
 *
 * @param <E> the type of elements held in this collection
 */
public class SoftBoundedTransferQueue<E> extends LinkedTransferQueue<E>
    implements TransferQueue<E> {

  private final int capacity;

  public SoftBoundedTransferQueue(int capacity) {
    if (capacity <= 0) {
      throw new IllegalArgumentException("Capacity must be greater than 0");
    }
    this.capacity = capacity;
  }

  private boolean hasCapacity(E e) {
    return e == null || remainingCapacity() - 1 >= 0;
  }

  private boolean hasCapacity(Collection<? extends E> c) {
    return c == null || remainingCapacity() - c.size() >= 0;
  }

  @Override
  public int remainingCapacity() {
    return Math.max(capacity - super.size(), 0);
  }

  @Override
  public boolean offer(@Nonnull E e) {
    return hasCapacity(e) && super.offer(e);
  }

  @Override
  public boolean offer(@Nonnull E e, long timeout, @Nonnull TimeUnit unit) {
    return hasCapacity(e) && super.offer(e, timeout, unit);
  }

  @Override
  public boolean add(@Nonnull E e) {
    return hasCapacity(e) && super.add(e);
  }

  @Override
  public boolean addAll(@Nonnull Collection<? extends E> c) {
    return hasCapacity(c) && super.addAll(c);
  }

  @Override
  public void put(@Nonnull E e) {
    if (hasCapacity(e)) {
      super.put(e);
    } else {
      // currently no acceptable way to signal the queue being full here so throw
      throw new IllegalStateException("Queue full");
    }
  }
}
