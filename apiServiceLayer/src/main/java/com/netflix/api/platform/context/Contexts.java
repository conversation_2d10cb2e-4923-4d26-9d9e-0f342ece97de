package com.netflix.api.platform.context;

import com.netflix.geoclient.CurrentGeoData;
import com.netflix.geoclient.GeoData;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.type.protogen.BasicTypes.Country;
import com.netflix.type.protogen.BasicTypes.Locale;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import netflix.context.user.User;
import netflix.context.user.UserContext;

public class Contexts {

  private Contexts() {}

  public static Optional<User> user() {
    UserContext userContext = CurrentMicrocontext.get().getUser();
    return userContext.hasCurrentUser()
        ? Optional.of(userContext.getCurrentUser())
        : Optional.empty();
  }

  public static Optional<Long> customerId() {
    return user().map(User::getId);
  }

  public static Optional<Long> accountId() {
    return user().flatMap(User::getOptionalOwnerId);
  }

  public static Optional<Locale> locale() {
    return CurrentMicrocontext.get().getLocale();
  }

  public static Country country() {
    return CurrentMicrocontext.get().getCountry();
  }

  public static Optional<Map<String, String>> geoData() {
    return Optional.ofNullable(CurrentGeoData.get())
        .map(GeoData::getAttributeMap)
        .map(HashMap::new) // to be safe
        .map(
            attrMap -> {
              attrMap.entrySet().removeIf(kv -> kv.getValue() == null);
              return attrMap;
            });
  }

  public static Optional<String> esn() {
    return CurrentMicrocontext.get().getEsn();
  }
}
