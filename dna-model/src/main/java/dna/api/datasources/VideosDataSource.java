package dna.api.datasources;

import static com.google.rpc.Code.OK_VALUE;
import static com.netflix.api.context.USTContexts.personalizedVideoContext;
import static com.netflix.api.context.USTContexts.videoRequestBuilder;
import static com.netflix.api.platform.util.CompletionStageAdapter.from;
import static dna.api.converters.EvidenceConverters.convertEvidenceTypes;
import static dna.api.datasources.DataSourceUtil.buildMultiValuedMap;
import static dna.api.datasources.utils.LoggerUtil.errorConditionally;
import static java.util.HashMap.newHashMap;
import static java.util.stream.Collectors.toMap;

import com.google.common.base.Preconditions;
import com.google.protobuf.util.Durations;
import com.netflix.api.context.USTContexts;
import com.netflix.api.facade.service.collection.CollectionLookup;
import com.netflix.api.grpc.GrpcCallHelpers.Future;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import com.netflix.api.service.UIFlavor;
import com.netflix.api.service.abtest.ABAdapterBase;
import com.netflix.api.service.batch.APIVideoServiceImpl;
import com.netflix.api.service.batch.USTVideoAdapter;
import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.api.service.batch.model.PromoVideo;
import com.netflix.api.service.cms.APICmsServiceImpl;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.video.APIColors;
import com.netflix.api.service.video.APIColorsImpl;
import com.netflix.api.service.video.APIResolutions;
import com.netflix.api.service.video.APIResolutionsImpl;
import com.netflix.api.service.video.APIVideo.PersonRole;
import com.netflix.api.service.video.ContextualMetadataUtils;
import com.netflix.api.util.ServiceUtils;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.cms.protogen.MovieCertificationData;
import com.netflix.demograph.datasource.AsyncDataSource;
import com.netflix.demograph.datasource.batch.BatchDataSource;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.patron.mostwatched.protogen.GetAllMostWatchedDataRequest;
import com.netflix.patron.mostwatched.protogen.GetAllMostWatchedDataResponse;
import com.netflix.patron.mostwatched.protogen.GetMostWatchedDataRequest;
import com.netflix.patron.mostwatched.protogen.MostWatchedDetails;
import com.netflix.patron.mostwatched.protogen.MostWatchedServiceGrpc.MostWatchedServiceStub;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.DistributionSummary;
import com.netflix.spectator.api.Registry;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.textevidence.protogen.TextEvidenceServiceGrpc.TextEvidenceServiceStub;
import com.netflix.type.ISOCountry;
import com.netflix.type.Visitor;
import com.netflix.type.proto.Videos;
import com.netflix.ust.adapters.USTGames;
import com.netflix.ust.adapters.USTStatus;
import com.netflix.ust.adapters.USTVideos;
import com.netflix.videocolor.protogen.ColorsRequest;
import com.netflix.videocolor.protogen.ColorsResponse;
import com.netflix.videocolor.protogen.VideoColorServiceGrpc.VideoColorServiceStub;
import dna.api.ApiRequestUtil;
import dna.api.converters.EvidenceConverters;
import dna.api.datasources.utils.DataSources;
import dna.api.service.model.CollectionMetadata;
import dna.api.service.model.ContextualEvidenceCriteria;
import dna.api.service.model.ContextualEvidenceResponse;
import dna.api.service.model.ContextualSynopsis;
import dna.api.service.model.Evidence;
import dna.api.service.model.EvidenceCriteria;
import dna.api.service.model.Hook;
import dna.api.service.model.MetadataType;
import dna.api.service.model.Person;
import dna.api.service.model.PuiEvidenceCriteria;
import dna.api.service.model.RemoveFromContinueWatchingRequest;
import dna.api.service.model.SocialProofEvidenceCriteria;
import dna.api.service.model.TimeCode;
import dna.api.service.model.VideoMostWatchedData;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import ust.collection.v1.CollectionAccess;
import ust.collection.v1.GameGenresResult;
import ust.collection.v1.GetGameGenresRequest;
import ust.collection.v1.GetGameGenresResponse;
import ust.collection.v1.GetVideoCollectionsRequest;
import ust.collection.v1.GetVideoCollectionsResponse;
import ust.collection.v1.GetVideoGenresRequest;
import ust.collection.v1.GetVideoGenresResponse;
import ust.collection.v1.VideoCollectionsResult;
import ust.collection.v1.VideoGenresResult;
import ust.orchestrator.v1.GetOrchestratedBadgingRequest;
import ust.orchestrator.v1.OrchestratorAccess;
import ust.video.core.v1.AncestorResult;
import ust.video.core.v1.BaseResult;
import ust.video.core.v1.BroadcastInfoResult;
import ust.video.core.v1.GetAllVideoPersonRequest;
import ust.video.core.v1.GetAncestorRequest;
import ust.video.core.v1.GetBroadcastInfoRequest;
import ust.video.core.v1.GetNewContentAvailabilityRequest;
import ust.video.core.v1.GetParentRequest;
import ust.video.core.v1.GetVideoBaseRequest;
import ust.video.core.v1.GetVideoCertificationRequest;
import ust.video.core.v1.GetVideoCertificationResponse;
import ust.video.core.v1.GetVideoCoreRequest;
import ust.video.core.v1.GetVideoFormatRequest;
import ust.video.core.v1.NewContentAvailabilityResult;
import ust.video.core.v1.ParentResult;
import ust.video.core.v1.VideoAccess;
import ust.video.core.v1.VideoCertification;
import ust.video.core.v1.VideoFormatResult;
import ust.video.core.v1.VideoPersonResult;
import ust.video.core.v2.IsPlayableRequest;
import ust.video.core.v2.IsPlayableResult;
import ust.video.personalized.v1.BadgingContextResult;
import ust.video.personalized.v1.BadgingInfo;
import ust.video.personalized.v1.ContextualSynopsisResult;
import ust.video.personalized.v1.DisplayRuntimeResult;
import ust.video.personalized.v1.EvidenceContext;
import ust.video.personalized.v1.GetLiveEventListRequest;
import ust.video.personalized.v1.GetPuiEvidenceRequest;
import ust.video.personalized.v1.GetPuiEvidenceResponse;
import ust.video.personalized.v1.GetSocialProofEvidenceRequest;
import ust.video.personalized.v1.GetSocialProofEvidenceResponse;
import ust.video.personalized.v1.GetVideoEvidenceRequest;
import ust.video.personalized.v1.GetVideoEvidenceResponse;
import ust.video.personalized.v1.LiveEvent;
import ust.video.personalized.v1.LiveEventListResult;
import ust.video.personalized.v1.PersonalizedVideoContext.Builder;
import ust.video.personalized.v1.TotalDurationResult;
import ust.video.personalized.v1.VideoCollectionMetadataResult;
import ust.video.personalized.v1.VideoPersonalizedAccess;
import ust.video.v1.BroadcastInformation;
import ust.video.v1.NewContentAvailability;
import ust.video.v1.PersonCore;
import ust.video.v1.PersonRoles;
import ust.video.v1.PersonRolesList;
import ust.video.v1.RoleType;
import ust.video.v1.Timecode;
import ust.video.v1.TimecodeType;
import ust.video.v1.VideoBase;
import ust.video.v1.VideoCore;
import ust.video.v1.VideoFormatData;
import ust.video.v1.VideoRequest;

@Component
public class VideosDataSource {

  private static final Logger logger = LoggerFactory.getLogger(VideosDataSource.class);
  private final Property<Boolean> enableTeDebug;

  // datasources (must be singletons for demograph implicit caching to work)
  private final BatchDataSource<Integer, Map<String, List<Person>>> persons;
  private final BatchDataSource<Integer, Map<TimecodeType, TimeCode>> timeCodes;
  private final BatchDataSource<Integer, List<Long>> videoGenres;
  private final BatchDataSource<Integer, Set<Long>> videoCollections;
  private final BatchDataSource<Integer, List<Long>> gameGenres;
  private final BatchDataSource<VideoNumSeasonsLabelRequest, String> numSeasonsLabel;

  private final BatchDataSource<Integer, Integer> upcomingSeason;
  private final BatchDataSource<Integer, Integer> recapVideo;
  private final BatchDataSource<Integer, Boolean> isAgeVerificationProtected;
  private final BatchDataSource<EvidenceRequest, List<Evidence>> evidence;
  private final BatchDataSource<PuiEvidenceRequest, List<Evidence>> puiEvidence;
  private final BatchDataSource<SocialProofEvidenceRequest, List<Evidence>> socialProofEvidence;
  private final BatchDataSource<ContextualEvidenceRequest, ContextualEvidenceResponse>
      contextualEvidence;
  private final BatchDataSource<Integer, VideoMostWatchedData> mostWatchedData;
  private final AsyncDataSource<Void, List<Integer>> mostWatched;
  private final AsyncDataSource<APIUser, List<Integer>> profileTitleProtectedVideos;
  private final AsyncDataSource<UserVideoId, Boolean> setProfileTitleProtectedVideos;
  private final AsyncDataSource<UserVideoId, Boolean> isProfileTitleProtected;
  private final AsyncDataSource<UserVideoId, Boolean> removeProfileTitleProtectedVideos;
  private final BatchDataSource<Integer, APIColors> colorsByVideoId;
  private final BatchDataSource<Integer, List<com.netflix.scout.protogen.Badge>> badgingContexts;
  private final BatchDataSource<Integer, Integer> ancestors;
  private final BatchDataSource<Integer, Integer> parents;
  private final BatchDataSource<Integer, Integer> episodeBatchParents;
  private final BatchDataSource<SupplementalVideosRequest, List<Integer>> supplementalVideosByType;
  private final AsyncDataSource<RemoveFromContinueWatchingRequest, Void>
      removeFromContinueWatchingList;
  private final BatchDataSource<Integer, Integer> totalDisplayRuntime;
  private final BatchDataSource<Integer, Integer> displayRuntime;
  private final BatchDataSource<Integer, VideoCore> videoCore;
  private final BatchDataSource<Integer, VideoBase> videoBase;
  private final BatchDataSource<Integer, NewContentAvailability> newContentAvailability;
  private final BatchDataSource<Integer, List<Hook>> hooks;
  private final BatchDataSource<ContextualSynopsisRequest, ContextualSynopsis> contextualSynopsis;
  private final BatchDataSource<VideoCollectionMetadataRequest, CollectionMetadata>
      collectionMetadata;
  private final BatchDataSource<Integer, APIResolutions> formats;
  private final BatchDataSource<Integer, IsPlayableResult> isPlayable;
  private final BatchDataSource<Integer, MovieCertificationData> certificationRatings;
  private final BatchDataSource<Integer, BroadcastInformation> broadcastInfo;
  private final BatchDataSource<Integer, List<LiveEvent>> liveEventsOfShowOrSeason;

  // Counts that a call happened, and captures the size of the input in a DistributionSummary.
  record CallMetrics(Counter counter, DistributionSummary sizer) {
    CallMetrics(Registry registry, String name, String method) {
      this(
          registry.counter(name + ".count", "method", method),
          registry.distributionSummary(name + ".size", "method", method));
    }

    /**
     * Observe that a call was made and record its input size.
     *
     * @param size the amount of items passed to the call.
     */
    void observe(long size) {
      counter.increment();
      sizer.record(size);
    }
  }

  // We want to track the frequency of calls and distribution of input sizes to videoCore(),
  // since we cannot rely on the existing GRPC filters like we normally would for non-UST calls.
  private final CallMetrics videoCoreCall;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  VideosDataSource(
      APIVideoServiceImpl videoService,
      APICmsServiceImpl cms,
      VideoAccess videoAccess,
      ust.video.core.v2.VideoAccess videoAccessV2,
      USTVideoAdapter videoAdapter,
      VideoPersonalizedAccess videoPersonalizedAccess,
      ABAdapterBase abAllocatorService,
      OrchestratorAccess orchestratorAccess,
      CollectionAccess collectionAccess,
      @GrpcSpringClient("ember") VideoColorServiceStub videoColorServiceStub,
      @GrpcSpringClient("textevidence") TextEvidenceServiceStub textEvidence,
      @GrpcSpringClient("patron") MostWatchedServiceStub mostWatchedServiceStub,
      Registry registry,
      PropertyRepository propertyRepository) {
    // init datasources
    timeCodes = _timeCodes(videoAdapter);
    videoGenres = _videoGenres(collectionAccess);
    videoCollections = _videoCollections(collectionAccess);
    gameGenres = _gameGenres(collectionAccess);
    numSeasonsLabel = _numSeasonsLabel(videoService);
    upcomingSeason = _upcomingSeason(videoService);
    recapVideo = _recapVideo(videoService);
    isAgeVerificationProtected = _isAgeVerificationProtected(videoService);
    evidence = _evidence(videoPersonalizedAccess, abAllocatorService);
    puiEvidence = _puiEvidence(videoPersonalizedAccess, abAllocatorService);
    socialProofEvidence = _socialProofEvidence(videoPersonalizedAccess, abAllocatorService);
    contextualEvidence = _contextualEvidence(textEvidence, abAllocatorService);
    mostWatchedData = _mostWatchedData(mostWatchedServiceStub);
    mostWatched = _mostWatched(mostWatchedServiceStub);
    profileTitleProtectedVideos = _profileTitleProtectedVideos(cms);
    setProfileTitleProtectedVideos = _setProfileTitleProtectedVideos(cms);
    isProfileTitleProtected = _isProfileTitleProtected(cms);
    removeProfileTitleProtectedVideos = _removeProfileTitleProtectedVideos(cms);

    videoCore = _videoCore(videoAccess);
    videoCoreCall = new CallMetrics(registry, "dna.videosDataSource.call", "videoCore");

    videoBase = _videoBase(videoAccess);
    colorsByVideoId = _colorsByVideoId(videoColorServiceStub);
    badgingContexts = _badgingContexts(orchestratorAccess);
    ancestors = _ancestors(videoAccess);
    parents = _parents(videoAccess);
    episodeBatchParents = _episodeBatchParents(videoService);
    supplementalVideosByType = _supplementalVideosByType(videoService);
    removeFromContinueWatchingList = _removeFromContinueWatchingList(videoService);
    totalDisplayRuntime = _totalDisplayRuntime(videoPersonalizedAccess);
    contextualSynopsis = _contextualSynopsis(videoPersonalizedAccess, abAllocatorService);
    collectionMetadata = _collectionMetadata(videoPersonalizedAccess);
    displayRuntime = _displayRuntime(videoPersonalizedAccess);
    persons = _persons(videoAccess);
    newContentAvailability = _newContentAvailability(videoAccess);
    formats = _formats(videoAccess);
    isPlayable = _isPlayable(videoAccessV2);
    certificationRatings = _certificationRatings(videoAccess);
    broadcastInfo = _broadcastInfo(videoAccess);
    liveEventsOfShowOrSeason = _liveEventsOfShowOrSeason(videoPersonalizedAccess);
    hooks = _hooks(textEvidence);
    enableTeDebug = propertyRepository.get("api.te.enableDebug", Boolean.class).orElse(false);
  }

  public BatchDataSource<Integer, VideoBase> getVideoBase() {
    return videoBase;
  }

  // datasources public accessors to support mocking / testing

  public BatchDataSource<ContextualSynopsisRequest, ContextualSynopsis> contextualSynopsis() {
    return contextualSynopsis;
  }

  public BatchDataSource<Integer, IsPlayableResult> isPlayable() {
    return isPlayable;
  }

  public BatchDataSource<Integer, Map<TimecodeType, TimeCode>> timeCodes() {
    return timeCodes;
  }

  public BatchDataSource<Integer, Map<String, List<Person>>> persons() {
    return persons;
  }

  public BatchDataSource<VideoCollectionMetadataRequest, CollectionMetadata> collectionMetadata() {
    return collectionMetadata;
  }

  public BatchDataSource<Integer, Integer> totalDisplayRuntime() {
    return totalDisplayRuntime;
  }

  public BatchDataSource<Integer, Integer> displayRuntime() {
    return displayRuntime;
  }

  public BatchDataSource<Integer, List<Long>> videoGenres() {
    return videoGenres;
  }

  public BatchDataSource<Integer, Set<Long>> videoCollections() {
    return videoCollections;
  }

  public BatchDataSource<Integer, List<Long>> gameGenres() {
    return gameGenres;
  }

  public BatchDataSource<VideoNumSeasonsLabelRequest, String> numSeasonsLabel() {
    return numSeasonsLabel;
  }

  public BatchDataSource<Integer, Integer> upcomingSeason() {
    return upcomingSeason;
  }

  public BatchDataSource<Integer, Integer> recapVideo() {
    return recapVideo;
  }

  public BatchDataSource<Integer, Boolean> isAgeVerificationProtected() {
    return isAgeVerificationProtected;
  }

  public BatchDataSource<EvidenceRequest, List<Evidence>> evidence() {
    return evidence;
  }

  public BatchDataSource<PuiEvidenceRequest, List<Evidence>> puiEvidence() {
    return puiEvidence;
  }

  public BatchDataSource<SocialProofEvidenceRequest, List<Evidence>> socialProofEvidence() {
    return socialProofEvidence;
  }

  public BatchDataSource<ContextualEvidenceRequest, ContextualEvidenceResponse>
      contextualEvidence() {
    return contextualEvidence;
  }

  public BatchDataSource<Integer, VideoMostWatchedData> mostWatchedData() {
    return mostWatchedData;
  }

  public AsyncDataSource<Void, List<Integer>> mostWatched() {
    return mostWatched;
  }

  public AsyncDataSource<APIUser, List<Integer>> profileTitleProtectedVideos() {
    return profileTitleProtectedVideos;
  }

  public AsyncDataSource<UserVideoId, Boolean> setProfileTitleProtectedVideos() {
    return setProfileTitleProtectedVideos;
  }

  public AsyncDataSource<UserVideoId, Boolean> isProfileTitleProtected() {
    return isProfileTitleProtected;
  }

  public AsyncDataSource<UserVideoId, Boolean> removeProfileTitleProtectedVideos() {
    return removeProfileTitleProtectedVideos;
  }

  public BatchDataSource<Integer, VideoCore> getVideoCore() {
    return videoCore;
  }

  public BatchDataSource<Integer, APIColors> colorsByVideoId() {
    return colorsByVideoId;
  }

  public BatchDataSource<Integer, List<com.netflix.scout.protogen.Badge>> badgingContexts() {
    return badgingContexts;
  }

  public BatchDataSource<Integer, Integer> ancestors() {
    return ancestors;
  }

  public BatchDataSource<Integer, Integer> parents() {
    return parents;
  }

  public BatchDataSource<Integer, Integer> episodeBatchParents() {
    return episodeBatchParents;
  }

  public BatchDataSource<SupplementalVideosRequest, List<Integer>> supplementalVideosByType() {
    return supplementalVideosByType;
  }

  public AsyncDataSource<RemoveFromContinueWatchingRequest, Void> removeFromContinueWatchingList() {
    return removeFromContinueWatchingList;
  }

  public BatchDataSource<Integer, NewContentAvailability> getNewContentAvailability() {
    return newContentAvailability;
  }

  public BatchDataSource<Integer, List<Hook>> hooks() {
    return hooks;
  }

  public BatchDataSource<Integer, APIResolutions> formats() {
    return formats;
  }

  public BatchDataSource<Integer, MovieCertificationData> getCertificationRatings() {
    return certificationRatings;
  }

  public BatchDataSource<Integer, BroadcastInformation> broadcastInfo() {
    return broadcastInfo;
  }

  public BatchDataSource<Integer, List<LiveEvent>> getLiveEventsOfShowOrSeason() {
    return liveEventsOfShowOrSeason;
  }

  // datasources initializers

  private BatchDataSource<Integer, Map<String, List<Person>>> _persons(VideoAccess videoAccess) {
    return DataSources.of(
        "persons",
        (context, ids) -> {
          VideoRequest.Builder builder = VideoRequest.newBuilder();
          for (Integer id : ids) {
            builder.addVideos(Videos.toProtobuf(id));
          }
          Map<Integer, Map<String, List<Person>>> videoPersons = newHashMap(ids.size());
          return Future.call(
                  videoAccess::getAllVideoPerson,
                  GetAllVideoPersonRequest.newBuilder().setVideoRequest(builder.build()).build())
              .thenApply(
                  response -> {
                    List<VideoPersonResult> videoPersonResults = response.getResultsList();
                    for (VideoPersonResult videoPersonResult : videoPersonResults) {
                      Integer videoId = videoPersonResult.getKey().getId();
                      if (videoPersonResult.hasResult()) {
                        PersonRolesList personRolesResult = videoPersonResult.getResult();
                        Map<String, List<Person>> personRoleMap =
                            convertPersonRoles(personRolesResult.getPersonRolesList());
                        videoPersons.put(videoId, personRoleMap);
                      }
                    }
                    return videoPersons;
                  });
        });
  }

  private static Map<String, List<Person>> convertPersonRoles(List<PersonRoles> personRolesList) {
    Map<String, List<Person>> result = newHashMap(personRolesList.size());
    for (PersonRoles personRoles : personRolesList) {
      List<Person> persons =
          personRoles.getPersonCoreList().stream().map(VideosDataSource::convertPerson).toList();
      result.put(convertRole(personRoles.getRole()), persons);
    }
    return result;
  }

  private static Person convertPerson(PersonCore person) {
    Person p = new Person();
    p.setName(person.getName());
    p.setPersonId(person.getId());
    return p;
  }

  private static String convertRole(RoleType roleType) {
    return switch (roleType) {
      case ROLE_TYPE_ACTOR -> PersonRole.actor.name();
      case ROLE_TYPE_WRITER -> PersonRole.writer.name();
      case ROLE_TYPE_DIRECTOR -> PersonRole.director.name();
      case ROLE_TYPE_PRODUCER -> PersonRole.producer.name();
      case ROLE_TYPE_CREATOR -> PersonRole.creator.name();
      case ROLE_TYPE_SCREENWRITER -> PersonRole.screenwriter.name();
      case ROLE_TYPE_STORY_BY -> PersonRole.story_by.name();
      case ROLE_TYPE_GUEST -> PersonRole.guest.name();
      case ROLE_TYPE_HOST -> PersonRole.host.name();
      case ROLE_TYPE_CREW -> PersonRole.crew.name();
      default -> StringUtils.EMPTY;
    };
  }

  private BatchDataSource<Integer, Map<TimecodeType, TimeCode>> _timeCodes(
      USTVideoAdapter videoAdapter) {
    return DataSources.of(
        "timeCodes",
        (context, ids) ->
            videoAdapter
                .getTimecodes(ids)
                .thenApply(
                    response ->
                        response.entrySet().stream()
                            .collect(
                                toMap(
                                    Entry::getKey, entry -> convertTimeCodes(entry.getValue())))));
  }

  private Map<TimecodeType, TimeCode> convertTimeCodes(List<Timecode> timecodesList) {
    Map<TimecodeType, TimeCode> map = newHashMap(timecodesList.size());
    for (Timecode timecode : timecodesList) {
      map.put(timecode.getType(), convertTimeCode(timecode));
    }
    return map;
  }

  private static TimeCode convertTimeCode(Timecode ustTimecode) {
    return new TimeCode()
        .setType(ustTimecode.getBoxedRawType())
        .setLabel(ustTimecode.getBoxedLabel())
        .setStart(Durations.toMillis(ustTimecode.getStart()))
        .setEnd(Durations.toMillis(ustTimecode.getEnd()))
        .setAnnotations(Map.of());
  }

  private BatchDataSource<Integer, List<Long>> _videoGenres(CollectionAccess collectionAccess) {
    return DataSources.of(
        "videoGenres",
        (context, videoIds) -> {
          GetVideoGenresRequest request =
              GetVideoGenresRequest.newBuilder().addAllVideos(USTVideos.toVideos(videoIds)).build();
          return Future.call(collectionAccess::getVideoGenres, request)
              .thenApply(GetVideoGenresResponse::getResultsList)
              .thenApply(
                  results ->
                      results.stream()
                          .filter(result -> USTStatus.isOk(result.getStatus()))
                          .collect(
                              toMap(
                                  result -> result.getVideo().getId(),
                                  VideoGenresResult::getGenreIdsList)))
              .exceptionally(
                  th -> CollectionLookup.logAndFallback(th, "video genres", videoIds, Map.of()));
        });
  }

  private BatchDataSource<Integer, Set<Long>> _videoCollections(CollectionAccess collectionAccess) {
    return DataSources.of(
        "videoCollections",
        (context, videoIds) -> {
          GetVideoCollectionsRequest request =
              GetVideoCollectionsRequest.newBuilder()
                  .addAllVideos(USTVideos.toVideos(videoIds))
                  .build();
          return Future.call(collectionAccess::getVideoCollections, request)
              .thenApply(GetVideoCollectionsResponse::getResultsList)
              .thenApply(
                  results -> {
                      int estimatedSize = (int) (results.size() * 0.75 + 1);
                      return results.stream()
                          .filter(result -> USTStatus.isOk(result.getStatus()))
                          .collect(
                              toMap(
                                  result -> result.getVideo().getId(),
                                  VideosDataSource::collectionIds,
                                  (existing, replacement) -> existing, // merge function for duplicates
                                  () -> new HashMap<>(estimatedSize)));
                  })
              .exceptionally(
                  th ->
                      CollectionLookup.logAndFallback(th, "video collections", videoIds, Map.of()));
        });
  }

  private static Set<Long> collectionIds(VideoCollectionsResult result) {
    return new HashSet<>(result.getCollectionIdsList());
  }

  private BatchDataSource<Integer, List<Long>> _gameGenres(CollectionAccess collectionAccess) {
    return DataSources.of(
        "gameGenres",
        (context, gameIds) -> {
          GetGameGenresRequest request =
              GetGameGenresRequest.newBuilder().addAllGames(USTGames.toGames(gameIds)).build();
          return Future.call(collectionAccess::getGameGenres, request)
              .thenApply(GetGameGenresResponse::getResultsList)
              .thenApply(
                  results ->
                      results.stream()
                          .filter(result -> USTStatus.isOk(result.getStatus()))
                          .collect(
                              toMap(
                                  result -> result.getGame().getId(),
                                  GameGenresResult::getGenreIdsList)));
        });
  }

  private BatchDataSource<VideoNumSeasonsLabelRequest, String> _numSeasonsLabel(
      APIVideoServiceImpl videoService) {
    return DataSources.of(
        "numSeasonsLabel",
        (context, requests) -> {
          Map<String, Set<VideoNumSeasonsLabelRequest>> batches =
              buildMultiValuedMap(
                  requests,
                  // label type forms the batch key
                  VideoNumSeasonsLabelRequest::labelType,
                  r -> r,
                  v -> new HashSet<>());
          return from(
              Observable.from(batches.entrySet())
                  .flatMap(
                      batch -> {
                        String labelType = batch.getKey();
                        Set<VideoNumSeasonsLabelRequest> reqSet = batch.getValue();
                        Map<Integer, List<Integer>> showsToSeasonsMap =
                            buildShowToSeasonsMap(reqSet);
                        return videoService
                            .getNumSeasonsLabel(labelType, showsToSeasonsMap)
                            .onErrorResumeNext(
                                e -> {
                                  errorConditionally(logger, "error getting numSeasonsLabel", e);
                                  return Observable.just(Map.of());
                                })
                            .map(
                                numSeasonslabelsByShowId ->
                                    numSeasonslabelsByShowId.entrySet().stream()
                                        .collect(
                                            toMap(
                                                entry ->
                                                    new VideoNumSeasonsLabelRequest(
                                                        labelType,
                                                        showsToSeasonsMap.get(entry.getKey()),
                                                        entry.getKey()),
                                                Entry::getValue)));
                      })
                  .reduce(
                      (a, c) -> {
                        a.putAll(c);
                        return a;
                      }));
        });
  }

  private static Map<Integer, List<Integer>> buildShowToSeasonsMap(
      Set<VideoNumSeasonsLabelRequest> requests) {
    return requests.stream()
        .collect(toMap(VideoNumSeasonsLabelRequest::showId, VideoNumSeasonsLabelRequest::seasons));
  }

  private BatchDataSource<Integer, Integer> _upcomingSeason(APIVideoServiceImpl videoService) {
    return DataSources.of(
        "upcomingSeason", (context, videoIds) -> from(videoService.getUpcomingSeasons(videoIds)));
  }

  private BatchDataSource<Integer, Integer> _recapVideo(APIVideoServiceImpl videoService) {
    return DataSources.of(
        "recapVideo",
        (context, videoIds) -> {
          Collection<Long> ids = asLongs(videoIds);
          String ctx = "recap";
          return from(
              videoService
                  .getPromoVideos(ids, ctx, null, null)
                  .map(VideosDataSource::getPromoVideos));
        });
  }

  private static Map<Integer, Integer> getPromoVideos(Map<Long, PromoVideo> reply) {
    return reply.entrySet().stream()
        .collect(toMap(entry -> entry.getKey().intValue(), entry -> entry.getValue().getId()));
  }

  private BatchDataSource<Integer, Boolean> _isAgeVerificationProtected(
      APIVideoServiceImpl videoService) {
    return DataSources.of(
        "isAgeVerificationProtected",
        (context, topVideoIds) -> from(videoService.isAgeVerificationProtected(topVideoIds)));
  }

  private BatchDataSource<Integer, Integer> _totalDisplayRuntime(
      VideoPersonalizedAccess videoPersonalizedAccess) {
    return DataSources.of(
        "totalDisplayRuntime",
        (context, videoIds) -> {
          VideoRequest.Builder videoBuilder = videoRequestBuilder(videoIds);
          try {
            return Future.call(
                    videoPersonalizedAccess::getTotalDuration,
                    ust.video.personalized.v1.GetTotalDurationRequest.newBuilder()
                        .setVideoRequest(videoBuilder)
                        .setPersonalizedVideoContext(
                            personalizedVideoContext(
                                USTVideoMetadataAdapter.ENABLE_CMS_DEBUG.get()))
                        .build())
                .thenApply(
                    r -> {
                      Map<Integer, Integer> m = newHashMap(r.getResultsCount());
                      for (TotalDurationResult result : r.getResultsList()) {
                        if (USTStatus.isOk(result.getStatus())) {
                          m.put(result.getKey().getId(), (int) result.getResult().getSeconds());
                        }
                      }
                      return m;
                    });
          } catch (Exception e) {
            return CompletableFuture.completedFuture(Map.of());
          }
        });
  }

  private BatchDataSource<ContextualSynopsisRequest, ContextualSynopsis> _contextualSynopsis(
      VideoPersonalizedAccess videoPersonalizedAccess, ABAdapterBase abAllocatorService) {
    return DataSources.of(
        "contextualSynopsis",
        (context, requests) -> {
          Map<dna.api.service.model.SynopsisContext, Set<Integer>> contextToVideoIds =
              buildMultiValuedMap(
                  requests,
                  ContextualSynopsisRequest::context,
                  ContextualSynopsisRequest::videoId,
                  v -> new HashSet<>());

          return CompletableFuture.completedFuture(contextToVideoIds.entrySet())
              .thenCompose(
                  entries -> {
                    List<CompletionStage<Map<ContextualSynopsisRequest, ContextualSynopsis>>>
                        futures =
                            entries.stream()
                                .map(
                                    entry ->
                                        processContextualSynopsisBatch(
                                            videoPersonalizedAccess, abAllocatorService, entry))
                                .toList();

                    CompletionStage<Map<ContextualSynopsisRequest, ContextualSynopsis>> result =
                        CompletableFuture.completedFuture(new HashMap<>());
                    for (CompletionStage<Map<ContextualSynopsisRequest, ContextualSynopsis>>
                        future : futures) {
                      result =
                          result.thenCombine(
                              future,
                              (resultMap, batchMap) -> {
                                resultMap.putAll(batchMap);
                                return resultMap;
                              });
                    }
                    return result;
                  });
        });
  }

  private BatchDataSource<VideoCollectionMetadataRequest, CollectionMetadata> _collectionMetadata(
      VideoPersonalizedAccess videoPersonalizedAccess) {
    return DataSources.of(
        "collectionMetadata",
        (context, requests) -> {
          Map<dna.api.service.model.MetadataType, Set<Integer>> contextToVideoIds =
              buildMultiValuedMap(
                  requests,
                  VideoCollectionMetadataRequest::metadataType,
                  VideoCollectionMetadataRequest::videoId,
                  v -> new HashSet<>());

          return CompletableFuture.completedFuture(contextToVideoIds.entrySet())
              .thenCompose(
                  entries -> {
                    List<CompletionStage<Map<VideoCollectionMetadataRequest, CollectionMetadata>>>
                        futures =
                            entries.stream()
                                .map(
                                    entry ->
                                        processCollectionMetadataBatch(
                                            videoPersonalizedAccess, entry))
                                .toList();

                    CompletionStage<Map<VideoCollectionMetadataRequest, CollectionMetadata>>
                        result = CompletableFuture.completedFuture(new HashMap<>());
                    for (CompletionStage<Map<VideoCollectionMetadataRequest, CollectionMetadata>>
                        future : futures) {
                      result =
                          result.thenCombine(
                              future,
                              (resultMap, batchMap) -> {
                                resultMap.putAll(batchMap);
                                return resultMap;
                              });
                    }
                    return result;
                  });
        });
  }

  private CompletionStage<Map<VideoCollectionMetadataRequest, CollectionMetadata>>
      processCollectionMetadataBatch(
          VideoPersonalizedAccess videoPersonalizedAccess,
          Entry<MetadataType, Set<Integer>> batch) {
    Set<Integer> videoIds = batch.getValue();
    return callCollectionMetadata(batch.getKey(), videoIds, videoPersonalizedAccess)
        .exceptionally(
            e -> {
              errorConditionally(logger, "error getting collection metadata", e);
              return Map.of();
            })
        .thenApply(
            metadataByVideoId -> {
                int size = metadataByVideoId.size();
                return metadataByVideoId.entrySet().stream()
                    .collect(
                        toMap(
                            entry -> new VideoCollectionMetadataRequest(entry.getKey(), batch.getKey()),
                            Entry::getValue,
                            (existing, replacement) -> existing, // merge function
                            () -> new HashMap<>(size)));
            });
  }

  private CompletionStage<Map<ContextualSynopsisRequest, ContextualSynopsis>>
      processContextualSynopsisBatch(
          VideoPersonalizedAccess videoPersonalizedAccess,
          ABAdapterBase abAllocatorService,
          Entry<dna.api.service.model.SynopsisContext, Set<Integer>> batch) {
    Set<Integer> videoIds = batch.getValue();
    return callContextualSynopsis(
            batch.getKey(), videoIds, videoPersonalizedAccess, abAllocatorService)
        .exceptionally(
            e -> {
              errorConditionally(logger, "error getting contextualSynopsis", e);
              return Map.of();
            })
        .thenApply(
            synopsisByVideoId ->
                synopsisByVideoId.entrySet().stream()
                    .collect(
                        toMap(
                            entry -> new ContextualSynopsisRequest(entry.getKey(), batch.getKey()),
                            Entry::getValue)));
  }

  private CompletionStage<Map<Integer, ContextualSynopsis>> callContextualSynopsis(
      dna.api.service.model.SynopsisContext synopsisContext,
      Set<Integer> videoIds,
      VideoPersonalizedAccess videoPersonalizedAccess,
      ABAdapterBase abAllocatorService) {
    return from(abAllocatorService.getAllocationsAsProto(false, false))
        .thenCompose(
            allocs -> {
              VideoRequest.Builder videoBuilder = videoRequestBuilder(videoIds);
              EvidenceContext.Builder ev = EvidenceContext.newBuilder();
              if (synopsisContext.getGroupLocator() != null) {
                ev.setGroupLocator(synopsisContext.getGroupLocator());
              }
              if (synopsisContext.getAnnotations() != null) {
                ev.putAllAnnotations(synopsisContext.getAnnotations());
              }
              if (synopsisContext.getUiContext() != null) {
                ev.setUiContext(synopsisContext.getUiContext().name());
              }
              if (synopsisContext.getUiPlatform() != null) {
                ev.setUiPlatform(
                    com.netflix.evidence.protogen.UiPlatform.valueOf(
                        (synopsisContext.getUiPlatform().name())));
              }

              Builder pc = personalizedVideoContext(enableTeDebug.get());

              if (synopsisContext.getClientCapabilities() != null) {
                pc.addAllFeatureCapabilities(
                    synopsisContext.getClientCapabilities().stream().map(Enum::name).toList());
              }
              ust.video.personalized.v1.GetContextualSynopsisRequest request =
                  ust.video.personalized.v1.GetContextualSynopsisRequest.newBuilder()
                      .setVideoRequest(videoBuilder)
                      .setEvidenceContext(ev)
                      .setPersonalizedVideoContext(pc)
                      .build();
              return Future.call(videoPersonalizedAccess::getContextualSynopsis, request)
                  .thenApply(
                      r -> {
                        Map<Integer, ContextualSynopsis> map = newHashMap(r.getResultsCount());
                        for (ContextualSynopsisResult result : r.getResultsList()) {
                          if (USTStatus.isOk(result.getStatus())) {
                            map.put(
                                result.getKey().getId(),
                                new ContextualSynopsis()
                                    .setText(result.getResult().getText())
                                    .setEvidenceKey(result.getResult().getEvidenceKey()));
                          }
                        }
                        return map;
                      });
            });
  }

  private CompletionStage<Map<Integer, CollectionMetadata>> callCollectionMetadata(
      MetadataType metadataType,
      Set<Integer> videoIds,
      VideoPersonalizedAccess videoPersonalizedAccess) {
    VideoRequest.Builder videoBuilder = videoRequestBuilder(videoIds);

    Builder pc = personalizedVideoContext(enableTeDebug.get());

    ust.video.personalized.v1.GetVideoCollectionMetadataRequest request =
        ust.video.personalized.v1.GetVideoCollectionMetadataRequest.newBuilder()
            .setVideoRequest(videoBuilder)
            .setMetadataType(convert(metadataType))
            .setPersonalizedVideoContext(pc)
            .build();
    return Future.call(videoPersonalizedAccess::getVideoCollectionMetadata, request)
        .thenApply(
            r -> {
              Map<Integer, CollectionMetadata> map = newHashMap(r.getResultsCount());
              for (VideoCollectionMetadataResult result : r.getResultsList()) {
                if (USTStatus.isOk(result.getStatus())) {
                  map.put(
                      result.getKey().getId(),
                      dna.api.converters.EvidenceConverters.convert(result.getResult()));
                }
              }
              return map;
            });
  }

  private static com.netflix.cms.protogen.MetadataType convert(MetadataType metadataType) {
    try {
      return com.netflix.cms.protogen.MetadataType.valueOf(metadataType.name());
    } catch (Exception e) {
      logger.warn("unsupported metadata type: {}", metadataType, e);
      return com.netflix.cms.protogen.MetadataType.UNKNOWN_TYPE;
    }
  }

  private BatchDataSource<Integer, Integer> _displayRuntime(
      VideoPersonalizedAccess videoPersonalizedAccess) {
    return DataSources.of(
        "totalDisplayRuntime",
        (context, videoIds) ->
            Future.call(
                    videoPersonalizedAccess::getDisplayRuntime,
                    ust.video.personalized.v1.GetDisplayRuntimeRequest.newBuilder()
                        .setPersonalizedVideoContext(
                            personalizedVideoContext(
                                USTVideoMetadataAdapter.ENABLE_CMS_DEBUG.get()))
                        .setVideoRequest(videoRequestBuilder(videoIds))
                        .build())
                .thenApply(
                    r -> {
                      Map<Integer, Integer> map = newHashMap(r.getResultsCount());
                      for (DisplayRuntimeResult result : r.getResultsList()) {
                        if (USTStatus.isOk(result.getStatus())) {
                          map.put(result.getKey().getId(), (int) result.getResult().getSeconds());
                        }
                      }
                      return map;
                    }));
  }

  private BatchDataSource<Integer, NewContentAvailability> _newContentAvailability(
      VideoAccess videoAccess) {
    return DataSources.of(
        "newContentAvailability",
        (context, args) ->
            Future.call(
                    videoAccess::getNewContentAvailability,
                    GetNewContentAvailabilityRequest.newBuilder()
                        .setVideoRequest(videoRequestBuilder(args))
                        .build())
                .thenApply(
                    r -> {
                      Map<Integer, NewContentAvailability> map = newHashMap(r.getResultsCount());
                      for (NewContentAvailabilityResult result : r.getResultsList()) {
                        if (USTStatus.isOk(result.getStatus())) {
                          map.put(result.getKey().getId(), result.getResult());
                        }
                      }
                      return map;
                    }));
  }

  private BatchDataSource<Integer, List<Hook>> _hooks(final TextEvidenceServiceStub textEvidence) {
    return DataSources.of(
        "hookData",
        (context, args) ->
            Future.call(
                    textEvidence::getSynopsis,
                    new GetSynopsisRequest(args, CurrentMicrocontext.get()).createHookRequest())
                .thenApply(
                    textEvidenceResp -> {
                      final Map<Integer, List<Hook>> map =
                          newHashMap(textEvidenceResp.getHooksCount());
                      textEvidenceResp
                          .getHooksMap()
                          .forEach(
                              (videoId, hooksForVideo) -> {
                                logger.trace(
                                    "adding {} hook(s) for {} to response",
                                    hooksForVideo.getHookListCount(),
                                    videoId);
                                map.put(videoId, new VideoHookList(hooksForVideo).toDNA());
                              });
                      return map;
                    }));
  }

  private BatchDataSource<Integer, APIResolutions> _formats(VideoAccess videoAccess) {
    return DataSources.of(
        "formats",
        (context, videoIds) ->
            Future.call(
                    videoAccess::getVideoFormat,
                    GetVideoFormatRequest.newBuilder()
                        .setVideoRequest(videoRequestBuilder(videoIds))
                        .build())
                .thenApply(
                    r -> {
                      Map<Integer, APIResolutions> map = newHashMap(r.getResultsCount());
                      for (VideoFormatResult result : r.getResultsList()) {
                        if (USTStatus.isOk(result.getStatus())) {
                          map.put(result.getKey().getId(), convertResolutions(result.getResult()));
                        }
                      }
                      return map;
                    }));
  }

  private BatchDataSource<Integer, IsPlayableResult> _isPlayable(
      ust.video.core.v2.VideoAccess videoAccess) {
    return DataSources.of(
        "isPlayable",
        (context, videoIds) -> {
          var request = IsPlayableRequest.newBuilder().addAllIds(videoIds).build();
          return Future.call(videoAccess::isPlayable, request)
              .thenApply(
                  response -> {
                    var map =
                        HashMap.<Integer, IsPlayableResult>newHashMap(response.getResultsCount());
                    for (var result : response.getResultsList()) {
                      var id = result.getId();
                      if (map.put(id, result) != null) {
                        throw new IllegalStateException("Duplicate key");
                      }
                    }
                    return map;
                  });
        });
  }

  private BatchDataSource<Integer, MovieCertificationData> _certificationRatings(
      VideoAccess videoAccess) {
    return DataSources.of(
        "certificationRatings",
        (context, videoIds) ->
            Future.call(
                    videoAccess::getVideoCertification,
                    GetVideoCertificationRequest.newBuilder()
                        .setVideoRequest(videoRequestBuilder(videoIds))
                        .setUiFlavor(USTContexts.uiFlavor())
                        .build())
                .thenApply(GetVideoCertificationResponse::getResultsList)
                .thenApply(
                    videoCertifications ->
                        videoCertifications.stream()
                            .collect(
                                toMap(
                                    videoCertification -> videoCertification.getKey().getId(),
                                    VideoCertification::getCertification))));
  }

  private BatchDataSource<Integer, BroadcastInformation> _broadcastInfo(VideoAccess videoAccess) {
    return DataSources.of(
        "broadcastInfo",
        (context, videoIds) ->
            Future.call(
                    videoAccess::getBroadcastInfo,
                    GetBroadcastInfoRequest.newBuilder()
                        .setVideoRequest(videoRequestBuilder(videoIds))
                        .build())
                .thenApply(
                    response -> {
                      Map<Integer, BroadcastInformation> m = newHashMap(videoIds.size());
                      for (BroadcastInfoResult result : response.getResultsList()) {
                        if (USTStatus.isOk(result.getStatus()) && result.hasResult()) {
                          m.put(result.getKey().getId(), result.getResult());
                        }
                      }
                      return m;
                    }));
  }

  private static APIResolutions convertResolutions(VideoFormatData result) {
    return new APIResolutionsImpl(result.getHd(), result.getSuperHd(), result.getUltraHd());
  }

  private BatchDataSource<EvidenceRequest, List<Evidence>> _evidence(
      VideoPersonalizedAccess videoPersonalizedAccess, ABAdapterBase abAllocatorService) {
    return DataSources.of(
        "evidence",
        (ctx, requests) -> {
          Map<EvidenceCriteria, Set<Integer>> batches =
              buildMultiValuedMap(
                  requests,
                  EvidenceRequest::criteria,
                  EvidenceRequest::videoId,
                  v -> new HashSet<>());

          List<Observable<Map<EvidenceRequest, List<Evidence>>>> observables =
              batches.entrySet().stream()
                  .map(
                      entry ->
                          processEvidenceBatch(
                              videoPersonalizedAccess,
                              abAllocatorService,
                              entry.getValue(),
                              entry.getKey()))
                  .toList();

          return from(
              Observable.concat(observables)
                  .reduce(
                      new HashMap<>(),
                      (a, c) -> {
                        a.putAll(c);
                        return a;
                      }));
        });
  }

  private static Observable<Map<EvidenceRequest, List<Evidence>>> processEvidenceBatch(
      VideoPersonalizedAccess videoPersonalizedAccess,
      ABAdapterBase abAllocatorService,
      Set<Integer> videoIds,
      EvidenceCriteria criteria) {
    return callEvidence(videoPersonalizedAccess, videoIds, abAllocatorService, criteria)
        .onErrorReturn(
            e -> {
              errorConditionally(logger, "error getting evidence", e);
              return Map.of();
            })
        .map(
            evByVideoId ->
                evByVideoId.entrySet().stream()
                    .collect(
                        toMap(
                            entry -> new EvidenceRequest(entry.getKey(), criteria),
                            Entry::getValue)));
  }

  private static Observable<Map<Integer, List<Evidence>>> callEvidence(
      VideoPersonalizedAccess access,
      Set<Integer> videoIds,
      ABAdapterBase abAllocatorService,
      EvidenceCriteria criteria) {

    var request =
        GetVideoEvidenceRequest.newBuilder()
            .addAllAcceptedEvidenceTypes(convertEvidenceTypes(criteria.getEvidenceTypes()))
            .setEvidenceContext(buildEvidenceContext(criteria))
            .setVideoRequest(videoRequestBuilder(videoIds));

    try {
      var visitor = ServiceUtils.getVisitorForCurrentUser();
      var enableDebug = USTVideoMetadataAdapter.ENABLE_TE_DEBUG.get();

      var ctx = personalizedVideoContext(enableDebug);
      request.setPersonalizedVideoContext(ctx);
    } catch (Exception ignored) {
      // Do an un-personalized request for evidence.
    }

    return abAllocatorService
        .getAllocationsAsProto(false, false)
        .flatMap(
            allocs ->
                RxObservable.defer(access::getVideoEvidence, request.build())
                    .map(GetVideoEvidenceResponse::getResultsList)
                    .map(EvidenceConverters::convertVideoEvidenceList));
  }

  private static EvidenceContext buildEvidenceContext(EvidenceCriteria criteria) {
    ust.video.personalized.v1.EvidenceContext.Builder builder =
        ust.video.personalized.v1.EvidenceContext.newBuilder().setUiFlavor(USTContexts.uiFlavor());

    if (criteria.getEvidenceCanvas() != null) {
      builder.setCanvas(EvidenceConverters.convertEvidenceCanvas(criteria.getEvidenceCanvas()));
    }

    if (criteria.getGroupLocator() != null) {
      builder.setGroupLocator(criteria.getGroupLocator());
    }

    if (criteria.getUiContext() != null) {
      builder.setUiContext(criteria.getUiContext());
    }

    // annotations may come from request context
    builder.putAllAnnotations(ContextualMetadataUtils.getClientCapabilities());

    return builder.build();
  }

  private BatchDataSource<PuiEvidenceRequest, List<Evidence>> _puiEvidence(
      VideoPersonalizedAccess videoPersonalizedAccess, ABAdapterBase abAllocatorService) {
    return DataSources.of(
        "puiEvidence",
        (ctx, requests) -> {
          Map<PuiEvidenceCriteria, Set<Integer>> batches =
              buildMultiValuedMap(
                  requests,
                  PuiEvidenceRequest::criteria,
                  PuiEvidenceRequest::videoId,
                  v -> new HashSet<>());

          List<Observable<Map<PuiEvidenceRequest, List<Evidence>>>> observables =
              batches.entrySet().stream()
                  .map(
                      entry ->
                          processPuiEvidenceBatch(
                              videoPersonalizedAccess,
                              abAllocatorService,
                              entry.getValue(),
                              entry.getKey()))
                  .toList();

          return from(
              Observable.concat(observables)
                  .reduce(
                      new HashMap<>(),
                      (a, c) -> {
                        a.putAll(c);
                        return a;
                      }));
        });
  }

  private static Observable<Map<PuiEvidenceRequest, List<Evidence>>> processPuiEvidenceBatch(
      VideoPersonalizedAccess videoPersonalizedAccess,
      ABAdapterBase abAllocatorService,
      Set<Integer> videoIds,
      PuiEvidenceCriteria criteria) {
    return callPuiEvidence(videoPersonalizedAccess, abAllocatorService, videoIds, criteria)
        .onErrorReturn(
            e -> {
              errorConditionally(logger, "error getting PUI evidence", e);
              return Map.of();
            })
        .map(
            evByVideoId ->
                evByVideoId.entrySet().stream()
                    .collect(
                        toMap(
                            entry -> new PuiEvidenceRequest(entry.getKey(), criteria),
                            Entry::getValue)));
  }

  private static Observable<Map<Integer, List<Evidence>>> callPuiEvidence(
      VideoPersonalizedAccess videoPersonalizedAccess,
      ABAdapterBase abAllocatorService,
      Set<Integer> videoIds,
      PuiEvidenceCriteria criteria) {
    return abAllocatorService
        .getAllocationsAsProto(false, false)
        .flatMap(
            allocs -> {
              GetPuiEvidenceRequest req =
                  GetPuiEvidenceRequest.newBuilder()
                      .setPersonalizedVideoContext(
                          personalizedVideoContext(USTVideoMetadataAdapter.ENABLE_TE_DEBUG.get()))
                      .setModuleType(criteria.getModuleType())
                      .setEvidenceContext(EvidenceConverters.buildEvidenceContext(criteria))
                      .setVideoRequest(videoRequestBuilder(videoIds))
                      .build();
              return RxObservable.defer(videoPersonalizedAccess::getPuiEvidence, req)
                  .map(GetPuiEvidenceResponse::getResultsList)
                  .map(EvidenceConverters::convertVideoEvidenceList);
            });
  }

  private BatchDataSource<SocialProofEvidenceRequest, List<Evidence>> _socialProofEvidence(
      final VideoPersonalizedAccess videoPersonalizedAccess,
      final ABAdapterBase abAllocatorService) {
    return DataSources.of(
        "socialProofEvidence",
        (ctx, requests) -> {
          final Map<SocialProofEvidenceCriteria, Set<Integer>> batches =
              buildMultiValuedMap(
                  requests,
                  SocialProofEvidenceRequest::criteria,
                  SocialProofEvidenceRequest::videoId,
                  v -> new HashSet<>());

          final List<Observable<Map<SocialProofEvidenceRequest, List<Evidence>>>> observables =
              batches.entrySet().stream()
                  .map(
                      entry ->
                          processSocialProofEvidenceBatch(
                              videoPersonalizedAccess,
                              abAllocatorService,
                              entry.getValue(),
                              entry.getKey()))
                  .toList();

          return from(
              Observable.concat(observables)
                  .reduce(
                      new HashMap<>(),
                      (a, c) -> {
                        a.putAll(c);
                        return a;
                      }));
        });
  }

  private BatchDataSource<ContextualEvidenceRequest, ContextualEvidenceResponse>
      _contextualEvidence(
          final TextEvidenceServiceStub textEvidence, final ABAdapterBase abAllocatorService) {
    return DataSources.of(
        "contextualEvidence",
        (ctx, requests) -> {
          Microcontext microcontext = CurrentMicrocontext.get();

          Visitor visitor = CurrentVisitor.get();
          Preconditions.checkArgument(visitor != null, "visitor is null");

          APIRequest currentRequest = APIRequest.getCurrentRequest();
          Preconditions.checkArgument(currentRequest != null, "current request is null");
          APIRequestContext requestContext = currentRequest.getRequestContext();
          Preconditions.checkArgument(requestContext != null, "request context is null");
          UIFlavor uiFlavor = requestContext.getUIFlavor();

          return from(
              abAllocatorService
                  .getAllocationsAsProto(false, false)
                  .flatMap(
                      abAllocs -> {
                        // Want to make a single request to TE for all videos that have the same
                        // "criteria" (route arguments). Create groupings of "criteria" to video
                        // IDs.
                        final Map<ContextualEvidenceCriteria, Set<Integer>> requestBatchesForTE =
                            buildMultiValuedMap(
                                requests,
                                ContextualEvidenceRequest::criteria,
                                ContextualEvidenceRequest::videoId,
                                v -> new HashSet<>());

                        // For each grouping, make request to TE
                        return Observable.from(requestBatchesForTE.entrySet())
                            .flatMap(
                                criteriaAndVideoIdsBatchItem -> {
                                  var criteria = criteriaAndVideoIdsBatchItem.getKey();
                                  var videoIds = criteriaAndVideoIdsBatchItem.getValue();
                                  return RxObservable.defer(
                                          textEvidence::getContextualEvidence,
                                          EvidenceConverters.convert(
                                              criteria,
                                              videoIds,
                                              microcontext,
                                              visitor,
                                              uiFlavor,
                                              abAllocs))
                                      .filter(
                                          teResponse -> {
                                            if (!teResponse.getIsSuccessful()) {
                                              logger.error(
                                                  "failed to get contextual evidence for videoIds={}: error={}",
                                                  videoIds,
                                                  teResponse.getErrorInfo());
                                              return false;
                                            }
                                            return true;
                                          })
                                      .map(
                                          teResponse ->
                                              // convert response map of "video ID -> Evidence"
                                              // back to "DNA ContextualEvidenceReq -> Evidence"
                                              teResponse
                                                  .getVideoEvidenceMapMap()
                                                  .entrySet()
                                                  .stream()
                                                  .collect(
                                                      toMap(
                                                          teVideoIdAndUIContext ->
                                                              new ContextualEvidenceRequest(
                                                                  teVideoIdAndUIContext.getKey(),
                                                                  criteria),
                                                          teVideoIdAndUIContext ->
                                                              EvidenceConverters.convert(
                                                                  teVideoIdAndUIContext
                                                                      .getValue()))));
                                })
                            .onErrorReturn(
                                th -> {
                                  errorConditionally(
                                      logger, "failed to get contextual evidence", th);
                                  return Map.of();
                                })
                            .reduce(
                                (first, second) -> {
                                  first.putAll(second);
                                  return first;
                                });
                      }));
        });
  }

  private static Observable<Map<SocialProofEvidenceRequest, List<Evidence>>>
      processSocialProofEvidenceBatch(
          VideoPersonalizedAccess videoPersonalizedAccess,
          ABAdapterBase abAllocatorService,
          Set<Integer> videoIds,
          SocialProofEvidenceCriteria criteria) {
    return callSocialProofEvidence(videoPersonalizedAccess, abAllocatorService, videoIds, criteria)
        .onErrorReturn(
            e -> {
              errorConditionally(logger, "error getting social proof evidence", e);
              return Map.of();
            })
        .map(
            evByVideoId ->
                evByVideoId.entrySet().stream()
                    .collect(
                        toMap(
                            entry -> new SocialProofEvidenceRequest(entry.getKey(), criteria),
                            Entry::getValue)));
  }

  private static Observable<Map<Integer, List<Evidence>>> callSocialProofEvidence(
      VideoPersonalizedAccess videoPersonalizedAccess,
      ABAdapterBase abAllocatorService,
      Set<Integer> videoIds,
      SocialProofEvidenceCriteria criteria) {
    return abAllocatorService
        .getAllocationsAsProto(false, false)
        .flatMap(
            allocs -> {
              final GetSocialProofEvidenceRequest req =
                  GetSocialProofEvidenceRequest.newBuilder()
                      .setPersonalizedVideoContext(
                          personalizedVideoContext(USTVideoMetadataAdapter.ENABLE_TE_DEBUG.get()))
                      .setEvidenceContext(EvidenceConverters.buildEvidenceContext(criteria))
                      .setVideoRequest(videoRequestBuilder(videoIds))
                      .build();
              return RxObservable.defer(videoPersonalizedAccess::getSocialProofEvidence, req)
                  .map(GetSocialProofEvidenceResponse::getResultsList)
                  .map(EvidenceConverters::convertVideoEvidenceList);
            });
  }

  private BatchDataSource<Integer, VideoMostWatchedData> _mostWatchedData(
      MostWatchedServiceStub mostWatchedServiceStub) {
    return DataSources.of(
        "mostWatchedData",
        (context, videoIds) ->
            mostWatchedPatron(
                mostWatchedServiceStub, videoIds, RequestContextWrapper.get().getCountry()));
  }

  private static CompletionStage<Map<Integer, VideoMostWatchedData>> mostWatchedPatron(
      MostWatchedServiceStub mostWatchedServiceStub, Set<Integer> videoIds, ISOCountry country) {
    return Future.call(
            mostWatchedServiceStub::getMostWatchedData,
            GetMostWatchedDataRequest.newBuilder()
                .addAllVideoId(videoIds)
                .setCountry(country.getId())
                .build())
        .thenApply(
            response -> {
              final Map<Integer, VideoMostWatchedData> map =
                  newHashMap(response.getResponseCount());
              for (Entry<Integer, MostWatchedDetails> entry :
                  response.getResponseMap().entrySet()) {
                final MostWatchedDetails value = entry.getValue();
                map.put(
                    entry.getKey(),
                    new VideoMostWatchedData()
                        .setCollectionId(
                            value.getOptionalCollectionId().map(Math::toIntExact).orElse(null))
                        .setLast7DaysEndDate(value.getBoxedLast7DaysEndDate())
                        .setLast7DaysStartDate(value.getBoxedLast7DaysStartDate())
                        .setPublishDate(value.getBoxedPublishDate())
                        .setRank(value.getBoxedRank()));
              }

              return map;
            });
  }

  @SuppressWarnings("DeprecatedIsStillUsed")
  @Deprecated
  private AsyncDataSource<Void, List<Integer>> _mostWatched(
      MostWatchedServiceStub mostWatchedServiceStub) {
    return new AsyncDataSource<>(
        (context, args) -> {
          var country = RequestContextWrapper.get().getCountry();
          return Future.call(
                  mostWatchedServiceStub::getAllMostWatchedData,
                  GetAllMostWatchedDataRequest.newBuilder().setCountry(country.getId()).build())
              .thenApply(GetAllMostWatchedDataResponse::getVideoIdList);
        });
  }

  private AsyncDataSource<APIUser, List<Integer>> _profileTitleProtectedVideos(
      APICmsServiceImpl cms) {
    return new AsyncDataSource<>(
        (context, apiUser) ->
            from(cms.getProfileTitleProtectedVideos(apiUser)).thenApply(ArrayList::new));
  }

  private AsyncDataSource<UserVideoId, Boolean> _setProfileTitleProtectedVideos(
      APICmsServiceImpl cms) {
    return new AsyncDataSource<>(
        (context, userVideoId) ->
            from(cms.setProfileTitleProtection(userVideoId.user(), userVideoId.videoId())));
  }

  private AsyncDataSource<UserVideoId, Boolean> _isProfileTitleProtected(APICmsServiceImpl cms) {
    return new AsyncDataSource<>(
        (context, userVideoId) ->
            from(cms.isProfileTitleProtected(userVideoId.user(), userVideoId.videoId())));
  }

  private AsyncDataSource<UserVideoId, Boolean> _removeProfileTitleProtectedVideos(
      APICmsServiceImpl cms) {
    return new AsyncDataSource<>(
        (context, userVideoId) ->
            from(cms.removeProfileTitleProtection(userVideoId.user(), userVideoId.videoId())));
  }

  private BatchDataSource<Integer, VideoCore> _videoCore(VideoAccess videoAccess) {
    return DataSources.of(
        "videoCoreById",
        (context, videoIds) -> {
          videoCoreCall.observe(videoIds.size());

          var videos = VideoRequest.newBuilder();
          for (var videoId : videoIds) {
            var video = Videos.toProtobuf(videoId);
            videos.addVideos(video);
          }

          var request = GetVideoCoreRequest.newBuilder().setVideoRequest(videos).build();
          return Future.call(videoAccess::getVideoCore, request)
              .thenApply(
                  response -> {
                    var map = HashMap.<Integer, VideoCore>newHashMap(videoIds.size());
                    for (var result : response.getResultsList()) {
                      if (OK_VALUE == result.getStatus().getCode()) {
                        map.put(result.getKey().getId(), result.getResult());
                      }
                    }
                    return map;
                  });
        });
  }

  private BatchDataSource<Integer, List<LiveEvent>> _liveEventsOfShowOrSeason(
      VideoPersonalizedAccess videoPersonalizedAccess) {
    return DataSources.of(
        "liveEvents",
        (context, videoIds) -> {
          GetLiveEventListRequest request =
              GetLiveEventListRequest.newBuilder()
                  .setVideoRequest(videoRequestBuilder(videoIds))
                  .build();

          return Future.call(videoPersonalizedAccess::getLiveEventList, request)
              .thenApply(
                  response -> {
                    Map<Integer, List<LiveEvent>> map = newHashMap(videoIds.size());

                    for (LiveEventListResult result : response.getResultsList()) {
                      if (USTStatus.isOk(result.getStatus())) {
                        map.put(result.getKey().getId(), result.getLiveEventsList());
                      }
                    }

                    return map;
                  });
        });
  }

  private BatchDataSource<Integer, VideoBase> _videoBase(VideoAccess videoAccess) {
    return DataSources.of(
        "videoBase",
        (context, videoIds) ->
            Future.call(
                    videoAccess::getVideoBase,
                    GetVideoBaseRequest.newBuilder()
                        .setVideoRequest(videoRequestBuilder(videoIds))
                        .build())
                .thenApply(
                    response -> {
                      Map<Integer, VideoBase> m = newHashMap(videoIds.size());
                      for (BaseResult result : response.getResultsList()) {
                        if (USTStatus.isOk(result.getStatus())) {
                          m.put(result.getKey().getId(), result.getResult());
                        }
                      }
                      return m;
                    }));
  }

  private BatchDataSource<Integer, APIColors> _colorsByVideoId(
      VideoColorServiceStub videoColorServiceStub) {
    return DataSources.of(
        "colorsByVideoId",
        (ctx, videoIds) -> {
          if (videoIds == null || videoIds.isEmpty()) {
            return CompletableFuture.completedFuture(Map.of());
          }
          return Future.call(
                  videoColorServiceStub::getVideosColors,
                  ColorsRequest.newBuilder().addAllVideoIds(videoIds).build())
              .thenApply(ColorsResponse::getColorsMap)
              .exceptionally(th -> Map.of())
              .thenApply(
                  map ->
                      map.entrySet().stream()
                          .collect(
                              toMap(Entry::getKey, entry -> new APIColorsImpl(entry.getValue()))));
        });
  }

  private BatchDataSource<Integer, List<com.netflix.scout.protogen.Badge>> _badgingContexts(
      OrchestratorAccess orchestratorAccess) {
    return DataSources.of(
        "badgingContexts",
        (context, videoIds) ->
            Future.call(
                    orchestratorAccess::getOrchestratedBadgingContext,
                    GetOrchestratedBadgingRequest.newBuilder()
                        .setVideoRequest(videoRequestBuilder(videoIds))
                        .build())
                .thenApply(
                    response -> {
                      Map<Integer, List<com.netflix.scout.protogen.Badge>> m =
                          newHashMap(videoIds.size());
                      for (BadgingContextResult result : response.getResultsList()) {
                        if (USTStatus.isOk(result.getStatus()) && result.hasResult()) {
                          m.put(
                              result.getKey().getId(),
                              convertBadgingContext(result.getResult().getBadgingInfoList()));
                        }
                      }
                      return m;
                    }));
  }

  private static List<com.netflix.scout.protogen.Badge> convertBadgingContext(
      List<BadgingInfo> badgingInfoList) {
    return badgingInfoList.stream()
        .filter(BadgingInfo::getAvailable)
        .map(BadgingInfo::getBadging)
        .toList();
  }

  private BatchDataSource<Integer, Integer> _ancestors(VideoAccess videoAccess) {
    return DataSources.of(
        "ancestors",
        (context, videoIds) ->
            Future.call(
                    videoAccess::getAncestor,
                    GetAncestorRequest.newBuilder()
                        .setVideoRequest(videoRequestBuilder(videoIds))
                        .build())
                .thenApply(
                    response -> {
                      Map<Integer, Integer> m = newHashMap(videoIds.size());
                      for (AncestorResult result : response.getResultsList()) {
                        if (USTStatus.isOk(result.getStatus())
                            && result.hasResult()
                            && result.getResult().hasAncestor()) {
                          m.put(result.getKey().getId(), result.getResult().getAncestor().getId());
                        }
                      }
                      return m;
                    }));
  }

  private BatchDataSource<Integer, Integer> _parents(VideoAccess videoAccess) {
    return DataSources.of(
        "parents",
        (context, videoIds) ->
            Future.call(
                    videoAccess::getParent,
                    GetParentRequest.newBuilder()
                        .setVideoRequest(videoRequestBuilder(videoIds))
                        .build())
                .thenApply(
                    response -> {
                      Map<Integer, Integer> m = newHashMap(videoIds.size());
                      for (ParentResult result : response.getResultsList()) {
                        if (USTStatus.isOk(result.getStatus())
                            && result.hasResult()
                            && result.getResult().hasParent()) {
                          m.put(result.getKey().getId(), result.getResult().getParent().getId());
                        }
                      }
                      return m;
                    }));
  }

  private BatchDataSource<Integer, Integer> _episodeBatchParents(APIVideoServiceImpl videoService) {
    return DataSources.of(
        "episodeBatchParents",
        (ctx, videoIds) -> from(videoService.getEpisodeBatchParents(videoIds)));
  }

  private BatchDataSource<SupplementalVideosRequest, List<Integer>> _supplementalVideosByType(
      APIVideoServiceImpl videoService) {
    return DataSources.of(
        "supplementalVideosByType",
        (context, requests) -> {

          // batches of videoIds keyed by uiContext and set of supp types
          Map<SupplementalBatchKey, Set<Integer>> batches =
              buildMultiValuedMap(
                  requests,
                  request -> new SupplementalBatchKey(request.getUiContext(), request.getTypes()),
                  SupplementalVideosRequest::getVideoId,
                  k -> new HashSet<>());

          APIUser user = ApiRequestUtil.apiNextUser(context);
          boolean nonMember =
              user == null
                  || user.getMembershipStatus() == null
                  || user.getMembershipStatus().isNonMember();

          return from(
              Observable.from(batches.entrySet())
                  .flatMap(
                      batch -> {
                        String uiContext = batch.getKey().uiContext();
                        Set<String> supplementalTypes = batch.getKey().supplementalTypes();

                        return videoService
                            .getSupplementalVideos(
                                batch.getValue(), // videoIds
                                supplementalTypes,
                                nonMember,
                                uiContext)
                            .map(
                                supplementalsMap ->
                                    supplementalsMap.entrySet().stream()
                                        .collect(
                                            toMap(
                                                entry ->
                                                    new SupplementalVideosRequest(
                                                        entry.getKey(),
                                                        supplementalTypes,
                                                        uiContext),
                                                entry ->
                                                    (List<Integer>)
                                                        new ArrayList<>(entry.getValue()))))
                            .toObservable();
                      })
                  .reduce(
                      (a, c) -> {
                        a.putAll(c);
                        return a;
                      }));
        });
  }

  private AsyncDataSource<RemoveFromContinueWatchingRequest, Void> _removeFromContinueWatchingList(
      APIVideoServiceImpl videoService) {
    return new AsyncDataSource<>(
        (context, request) ->
            from(
                videoService.removeFromContinueWatchingList(
                    request.getVideoIds(), request.getUiVersion(), request.getTrackId())));
  }

  public enum VideoKind {
    show,
    season,
    episode,
    movie,
    supplemental,
    titlegroup,
    game,
    episode_batch,
    generic_container,
    unknown;

    public String getPath() {
      return name();
    }

    public static VideoKind forVideo(VideoCore video) {
      if (video.hasEpisode()) {
        return episode;
      } else if (video.hasSeason()) {
        return season;
      } else if (video.hasShow()) {
        return show;
      } else if (video.hasMovie() || video.hasSupplemental()) {
        return movie;
      } else if (video.hasTitleGroup()) {
        return titlegroup;
      } else if (video.hasGameDetail()) {
        return game;
      } else if (video.hasEpisodeBatch()) {
        return episode_batch;
      } else if (video.hasGenericContainer()) {
        return generic_container;
      } else {
        return unknown;
      }
    }
  }

  private static Collection<Long> asLongs(Set<Integer> videoIds) {
    if (videoIds == null) {
      return null;
    }
    return videoIds.stream().map(Long::valueOf).collect(Collectors.toSet());
  }
}
