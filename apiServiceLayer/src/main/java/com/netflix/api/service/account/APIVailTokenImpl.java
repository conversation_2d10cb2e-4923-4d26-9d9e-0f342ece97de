package com.netflix.api.service.account;

import java.util.Map;
import java.util.Objects;

public class APIVailTokenImpl implements APIVailToken {

  private final String token;
  private final String credential;
  private final long tokenExpirationTS;
  private final Map<String, String> resultAttributes;

  public APIVailTokenImpl(
      String token,
      String credential,
      long tokenExpirationTS,
      Map<String, String> resultAttributes) {
    super();
    this.token = token;
    this.credential = credential;
    this.tokenExpirationTS = tokenExpirationTS;
    this.resultAttributes = resultAttributes;
  }

  public Map<String, String> getResultAttributes() {
    return resultAttributes;
  }

  @Override
  public String getToken() {
    return token;
  }

  @Override
  public String getCredential() {
    return credential;
  }

  @Override
  public long getTokenExpirationTS() {
    return tokenExpirationTS;
  }

  @Override
  public String toString() {
    return "APIVailToken{"
        + "token='"
        + token
        + '\''
        + ", credential='"
        + credential
        + '\''
        + ", tokenExpirationTS="
        + tokenExpirationTS
        + ", resultAttributes="
        + resultAttributes
        + '}';
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIVailTokenImpl)) {
      return false;
    }
    APIVailTokenImpl that = (APIVailTokenImpl) o;
    return Math.abs(getTokenExpirationTS() - that.getTokenExpirationTS()) < 1000
        && Objects.equals(getToken(), that.getToken())
        && Objects.equals(getCredential(), that.getCredential())
        && Objects.equals(getResultAttributes(), that.getResultAttributes());
  }

  @Override
  public int hashCode() {
    return Objects.hash(getToken(), getCredential());
  }
}
