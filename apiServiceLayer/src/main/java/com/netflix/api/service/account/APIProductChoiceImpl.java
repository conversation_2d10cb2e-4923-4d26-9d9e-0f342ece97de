package com.netflix.api.service.account;

import com.netflix.memberprice.protogen.DURATION;
import com.netflix.memberprice.protogen.PriceConsentInfo;
import com.netflix.memberprice.protogen.ProductChoice;

public class APIProductChoiceImpl implements APIProductChoice {
  private final ProductChoice productChoice;

  APIProductChoiceImpl(ProductChoice productChoice) {
    this.productChoice = productChoice;
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.price"
   */
  @Override
  public Double getPrice() {
    return this.productChoice.getPrice();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.price_formatted"
   */
  @Override
  public String getPriceFormatted() {
    return this.productChoice.getPriceFormatted();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.currency"
   */
  @Override
  public String getCurrency() {
    return this.productChoice.getCurrency();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.country"
   */
  @Override
  public String getCountry() {
    return this.productChoice.getCountry();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.price_tier"
   */
  @Override
  public String getPriceTier() {
    return this.productChoice.getPriceTier();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_id"
   */
  @Override
  public Long getPlanId() {
    return this.productChoice.getPlanId();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.additional_amount_over_current"
   */
  @Override
  public Double getAdditionalAmountOverCurrent() {
    return this.productChoice.getAdditionalAmountOverCurrent();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.additional_amount_over_current_formatted"
   */
  @Override
  public String getAdditionalAmountOverCurrentFormatted() {
    return this.productChoice.getAdditionalAmountOverCurrentFormatted();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_change_effective_date"
   */
  @Override
  public Long getPlanChangeEffectiveDate() {
    return this.productChoice.getBoxedPlanChangeEffectiveDate();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.max_streams"
   */
  @Override
  public Integer getMaxStreams() {
    return this.productChoice.getMaxStreams();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_name"
   */
  @Override
  public String getPlanName() {
    return this.productChoice.getPlanName();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_desc"
   */
  @Override
  public String getPlanDesc() {
    return this.productChoice.getPlanDesc();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_desc_short"
   */
  @Override
  public String getPlanDescShort() {
    return this.productChoice.getPlanDescShort();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.new_billing_date"
   */
  @Override
  public Long getNewBillingDate() {
    return this.productChoice.getBoxedNewBillingDate();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.revoke_uou_offer"
   */
  @Override
  public Boolean getRevokeUouOffer() {
    return this.productChoice.getRevokeUouOffer();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_status"
   */
  @Override
  public APIPlanStatus getPlanStatus() {
    ProductChoice.PlanStatus planStatus = this.productChoice.getPlanStatus();
    if (planStatus == null) {
      return null;
    }
    if (planStatus == com.netflix.memberprice.protogen.ProductChoice.PlanStatus.UNRECOGNIZED) {
      return APIPlanStatus.UNRECOGNIZED;
    } else {
      try {
        return Enum.valueOf(APIPlanStatus.class, planStatus.name());
      } catch (IllegalArgumentException e) {
        return APIPlanStatus.UNRECOGNIZED;
      }
    }
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_change_type"
   */
  @Override
  public APIPlanChangeType getPlanChangeType() {
    ProductChoice.PlanChangeType planChangeType = this.productChoice.getPlanChangeType();
    if (planChangeType == null) {
      return null;
    }
    if (planChangeType
        == com.netflix.memberprice.protogen.ProductChoice.PlanChangeType.UNRECOGNIZED) {
      return APIPlanChangeType.UNRECOGNIZED;
    } else {
      try {
        return Enum.valueOf(APIPlanChangeType.class, planChangeType.name());
      } catch (IllegalArgumentException e) {
        return APIPlanChangeType.UNRECOGNIZED;
      }
    }
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.gf_state"
   */
  @Override
  public APIGrandfatheredState getGfState() {
    ProductChoice.GrandfatheredState grandfatheredState = this.productChoice.getGfState();
    if (grandfatheredState == null) {
      return null;
    }
    if (grandfatheredState
        == com.netflix.memberprice.protogen.ProductChoice.GrandfatheredState.UNRECOGNIZED) {
      return APIGrandfatheredState.UNRECOGNIZED;
    } else {
      try {
        return Enum.valueOf(APIGrandfatheredState.class, grandfatheredState.name());
      } catch (IllegalArgumentException e) {
        return APIGrandfatheredState.UNRECOGNIZED;
      }
    }
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.quality"
   */
  @Override
  public APIStreamQuality getQuality() {
    ProductChoice.StreamQuality streamQuality = this.productChoice.getQuality();
    if (streamQuality == null) {
      return null;
    }
    if (streamQuality
        == com.netflix.memberprice.protogen.ProductChoice.StreamQuality.UNRECOGNIZED) {
      return APIStreamQuality.UNRECOGNIZED;
    } else {
      try {
        return Enum.valueOf(APIStreamQuality.class, streamQuality.name());
      } catch (IllegalArgumentException e) {
        return APIStreamQuality.UNRECOGNIZED;
      }
    }
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.price_duration"
   */
  @Override
  public APIPriceDuration getPriceDuration() {
    DURATION dURATION = this.productChoice.getPriceDuration();
    if (dURATION == null) {
      return null;
    }
    if (dURATION == DURATION.UNRECOGNIZED) {
      return APIPriceDuration.UNRECOGNIZED;
    } else {
      try {
        return Enum.valueOf(APIPriceDuration.class, dURATION.name());
      } catch (IllegalArgumentException e) {
        return APIPriceDuration.UNRECOGNIZED;
      }
    }
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.is_new_billing_date_approximate"
   */
  @Override
  public Boolean getIsNewBillingDateApproximate() {
    return this.productChoice.getIsNewBillingDateApproximate();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.price_consent"
   */
  @Override
  public APIPriceConsentInfo getPriceConsent() {
    PriceConsentInfo priceConsentInfo = this.productChoice.getPriceConsent();
    if (priceConsentInfo == null) {
      return null;
    }
    return new APIPriceConsentInfoImpl(priceConsentInfo);
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_max_download_devices"
   */
  @Override
  public Integer getPlanMaxDownloadDevices() {
    return this.productChoice.getPlanMaxDownloadDevices();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_has_ads"
   */
  @Override
  public Boolean getPlanHasAds() {
    return this.productChoice.getPlanHasAds();
  }
}
