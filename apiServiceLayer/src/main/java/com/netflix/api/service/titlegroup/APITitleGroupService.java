package com.netflix.api.service.titlegroup;

import java.util.Collection;
import java.util.List;
import rx.Observable;

/** Service to manage title groups */
public interface APITitleGroupService {

  /**
   * Find all the titles groups set that contains any of the members
   *
   * @param members List of members
   * @return
   */
  Observable<List<APITitleGroupSet>> findGroupSets(Collection<APITitleGroupSetMember> members);

  /**
   * Get all the members of a title group
   */
  Observable<List<APITitleGroupSet>> getGroupSets(Collection<APITitleGroup> groups);

  /** Find an title group from its id */
  Observable<List<APITitleGroup>> findGroups(Collection<Integer> groups);
}
