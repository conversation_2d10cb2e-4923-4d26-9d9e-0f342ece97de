package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class MembershipService implements GraphData {
  public Map<String, Atom<MembershipChoicesResponse>> choices;

  public Map<String, Atom<StreamingQualityUpsellOptionResponse>> streamingQualityUpsellOption;

  public Map<String, Atom<ConcurrentStreamsUpsellOptionResponse>> concurrentStreamsUpsellOption;

  public Map<String, Atom<LowestOrganicPricedSignupPlanResponse>> lowestOrganicPricedSignupPlan;
}
