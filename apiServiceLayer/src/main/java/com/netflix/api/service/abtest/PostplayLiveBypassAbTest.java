package com.netflix.api.service.abtest;

import com.netflix.microcontext.resolver.MicrocontextResolver;
import com.netflix.servo.monitor.DynamicCounter;
import java.util.concurrent.CompletableFuture;
import org.springframework.stereotype.Component;

@Component
public class PostplayLiveBypassAbTest {

  public static final Integer TEST_ID = 72167;
  private static final Integer CONTROL_TEST_CELL = 0;

  private final MicrocontextResolver microcontextResolver;

  public PostplayLiveBypassAbTest(final MicrocontextResolver microcontextResolver) {
    this.microcontextResolver = microcontextResolver;
  }

  public CompletableFuture<Boolean> shouldEvaluateLocalLivePostplay() {
    return microcontextResolver
        .resolveExperimentation()
        .toCompletableFuture()
        .thenApply(
            experimentationContext -> {
              final int testCell =
                  experimentationContext
                      .getAccountAllocations()
                      .getTestToCellOrDefault(TEST_ID, CONTROL_TEST_CELL);

              // counter for tracking test allocation
              DynamicCounter.increment(
                  "api.postplay.livebypass.abtest.allocation", "cell", String.valueOf(testCell));

              return testCell != 2;
            });
  }
}
