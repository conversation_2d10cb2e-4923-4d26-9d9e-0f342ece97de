package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class UserSubtitleSettingsRequest {
  private String applicationGroup;

  private SubtitleSettingsRequest subtitleSettings;

  public String getApplicationGroup() {
    return applicationGroup;
  }

  public SubtitleSettingsRequest getSubtitleSettings() {
    return subtitleSettings;
  }

  public UserSubtitleSettingsRequest setApplicationGroup(String applicationGroup) {
    this.applicationGroup = applicationGroup;
    return this;
  }

  public UserSubtitleSettingsRequest setSubtitleSettings(SubtitleSettingsRequest subtitleSettings) {
    this.subtitleSettings = subtitleSettings;
    return this;
  }
}
