package dna.api.service.model;

import com.netflix.nes.page.protogen.Page;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class GpsPageResponse {
  /** The Page resulting from the request. */
  private Page page;

  public Page getPage() {
    return page;
  }

  public GpsPageResponse setPage(Page page) {
    this.page = page;
    return this;
  }
}
