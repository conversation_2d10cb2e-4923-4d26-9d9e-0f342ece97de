package com.netflix.governator.providers;

import com.google.inject.Binding;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.spi.DefaultBindingTargetVisitor;
import com.google.inject.spi.Dependency;
import com.google.inject.spi.ProviderInstanceBinding;
import java.util.Set;
import java.util.stream.Collectors;

public class AdvicesDependenciesFinder {

  public static <T> Set<Dependency<?>> findDependencies(Injector injector, Binding<T> binding) {
    return binding.acceptTargetVisitor(
        new DefaultBindingTargetVisitor<T, Set<Dependency<?>>>() {
          public Set<Dependency<?>> visit(
              ProviderInstanceBinding<? extends T> providerInstanceBinding) {
            var provider = providerInstanceBinding.getUserSuppliedProvider();
            if (provider instanceof AdvisedProvider) {
              Key<? extends T> key = providerInstanceBinding.getKey();
              return injector.findBindingsByType(key.getTypeLiteral()).stream()
                  .map((binding) -> binding.getKey())
                  .filter((k) -> k.hasAttributes())
                  .filter((k) -> AdviceElement.class.isAssignableFrom(k.getAnnotationType()))
                  .filter(
                      (k) ->
                          ((AdviceElement) k.getAnnotation())
                              .name()
                              .equals(key.hasAttributes() ? key.getAnnotation().toString() : ""))
                  .map(Dependency::get)
                  .collect(Collectors.toSet());
            }
            return null;
          }
        });
  }
}
