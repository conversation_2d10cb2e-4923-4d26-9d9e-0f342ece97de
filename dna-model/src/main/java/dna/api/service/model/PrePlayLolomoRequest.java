package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PrePlayLolomoRequest {
  private Map<String, Object> mapAnnotations;

  private Boolean debug;

  public Map<String, Object> getMapAnnotations() {
    return mapAnnotations;
  }

  public Boolean getDebug() {
    return debug;
  }

  public PrePlayLolomoRequest setMapAnnotations(Map<String, Object> mapAnnotations) {
    this.mapAnnotations = mapAnnotations;
    return this;
  }

  public PrePlayLolomoRequest setDebug(Boolean debug) {
    this.debug = debug;
    return this;
  }
}
