package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class ServiceCancellationInfo {
  private Boolean pendingCancellation;

  private Boolean onGift;

  private String cancellationDate;

  public Boolean getPendingCancellation() {
    return pendingCancellation;
  }

  public Boolean getOnGift() {
    return onGift;
  }

  public String getCancellationDate() {
    return cancellationDate;
  }

  public ServiceCancellationInfo setPendingCancellation(Boolean pendingCancellation) {
    this.pendingCancellation = pendingCancellation;
    return this;
  }

  public ServiceCancellationInfo setOnGift(Boolean onGift) {
    this.onGift = onGift;
    return this;
  }

  public ServiceCancellationInfo setCancellationDate(String cancellationDate) {
    this.cancellationDate = cancellationDate;
    return this;
  }
}
