package com.netflix.api.service.video;

import com.netflix.api.annotations.ExcludeFromCoverage;

@ExcludeFromCoverage
public enum APIImageFormat {
  UNKNOWN(-1),
  /**
   * @deprecated use {@linkplain #W240} instead
   */
  @Deprecated
  SD(240),
  /**
   * @deprecated use {@linkplain #W320} instead
   */
  @Deprecated
  HD(320),
  W240(240),
  W320(320),
  W640(640);

  private final int width;

  APIImageFormat(int width) {
    this.width = width;
  }

  public int getWidth() {
    return width;
  }
}
