package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class UiMetadataRequest {
  private Boolean isKidsUi;

  private Boolean useSslForAssets;

  private List<String> imageExtensionPreferences;

  private String audioLocaleOverride;

  private String textLocaleOverride;

  private Boolean usePseudoLoc;

  private List<String> deviceSupportedLanguages;

  /** whether to return null or propagate service errors. defaults to true if missing. */
  private Boolean propagateErrors;

  private String schemaVersion;

  private List<String> momentIds;

  private Long playbackPositionMs;

  private String intent;

  private IxUserStateSnapshot userState;

  private Double imageScaleFactor;

  public Boolean getIsKidsUi() {
    return isKidsUi;
  }

  public Boolean getUseSslForAssets() {
    return useSslForAssets;
  }

  public List<String> getImageExtensionPreferences() {
    return imageExtensionPreferences;
  }

  public String getAudioLocaleOverride() {
    return audioLocaleOverride;
  }

  public String getTextLocaleOverride() {
    return textLocaleOverride;
  }

  public Boolean getUsePseudoLoc() {
    return usePseudoLoc;
  }

  public List<String> getDeviceSupportedLanguages() {
    return deviceSupportedLanguages;
  }

  public Boolean getPropagateErrors() {
    return propagateErrors;
  }

  public String getSchemaVersion() {
    return schemaVersion;
  }

  public List<String> getMomentIds() {
    return momentIds;
  }

  public Long getPlaybackPositionMs() {
    return playbackPositionMs;
  }

  public String getIntent() {
    return intent;
  }

  public IxUserStateSnapshot getUserState() {
    return userState;
  }

  public Double getImageScaleFactor() {
    return imageScaleFactor;
  }

  public UiMetadataRequest setIsKidsUi(Boolean isKidsUi) {
    this.isKidsUi = isKidsUi;
    return this;
  }

  public UiMetadataRequest setUseSslForAssets(Boolean useSslForAssets) {
    this.useSslForAssets = useSslForAssets;
    return this;
  }

  public UiMetadataRequest setImageExtensionPreferences(List<String> imageExtensionPreferences) {
    this.imageExtensionPreferences = imageExtensionPreferences;
    return this;
  }

  public UiMetadataRequest setAudioLocaleOverride(String audioLocaleOverride) {
    this.audioLocaleOverride = audioLocaleOverride;
    return this;
  }

  public UiMetadataRequest setTextLocaleOverride(String textLocaleOverride) {
    this.textLocaleOverride = textLocaleOverride;
    return this;
  }

  public UiMetadataRequest setUsePseudoLoc(Boolean usePseudoLoc) {
    this.usePseudoLoc = usePseudoLoc;
    return this;
  }

  public UiMetadataRequest setDeviceSupportedLanguages(List<String> deviceSupportedLanguages) {
    this.deviceSupportedLanguages = deviceSupportedLanguages;
    return this;
  }

  public UiMetadataRequest setPropagateErrors(Boolean propagateErrors) {
    this.propagateErrors = propagateErrors;
    return this;
  }

  public UiMetadataRequest setSchemaVersion(String schemaVersion) {
    this.schemaVersion = schemaVersion;
    return this;
  }

  public UiMetadataRequest setMomentIds(List<String> momentIds) {
    this.momentIds = momentIds;
    return this;
  }

  public UiMetadataRequest setPlaybackPositionMs(Long playbackPositionMs) {
    this.playbackPositionMs = playbackPositionMs;
    return this;
  }

  public UiMetadataRequest setIntent(String intent) {
    this.intent = intent;
    return this;
  }

  public UiMetadataRequest setUserState(IxUserStateSnapshot userState) {
    this.userState = userState;
    return this;
  }

  public UiMetadataRequest setImageScaleFactor(Double imageScaleFactor) {
    this.imageScaleFactor = imageScaleFactor;
    return this;
  }
}
