package com.netflix.api.facade.service;

import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Registry;
import com.netflix.type.TypeManager;
import com.netflix.type.Video;
import com.netflix.videometadata.VMSInMemory;
import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.videometadata.type.SupplementalVideo;
import com.netflix.vms.provider.SupplementalVideoProvider;
import com.netflix.vms.type.hollow.video.CompleteVideoContext;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class LocalVideoLookup extends BaseVideoLookup implements VideoLookup {

  private final Counter videoLookupCounter;
  private final Counter badVideoCounter;
  private final Counter missVideoCounter;

  private final VMSInMemory vmsInMemory;
  private final SupplementalVideoProvider supplementalVideoProvider;

  @Autowired
  public LocalVideoLookup(
      Registry registry,
      VMSInMemory vmsInMemory,
      SupplementalVideoProvider supplementalVideoProvider) {
    this.vmsInMemory = vmsInMemory;
    this.supplementalVideoProvider = supplementalVideoProvider;
    this.videoLookupCounter = registry.counter("api.videolookup.total");
    this.badVideoCounter = registry.counter("api.videolookup.bad");
    this.missVideoCounter = registry.counter("api.videolookup.miss");
  }

  @Override
  public CompletableFuture<Map<Integer, Optional<CompleteVideo>>> getVideosAsync(
      @NotNull Collection<Integer> videoIds, @NotNull LookupContext context) {
    videoLookupCounter.add(videoIds.size());
    int invalid = 0;
    int miss = 0;
    for (Integer videoId : videoIds) {
      if (videoId <= 1) {
        invalid++;
      }
    }
    badVideoCounter.add(invalid);
    // preserve order
    Map<Integer, Optional<CompleteVideo>> resultMap = HashMap.newHashMap(videoIds.size());
    for (Integer videoId : videoIds) {
      final Optional<CompleteVideo> optional;
      if (videoId <= 1) {
        optional = Optional.empty();
      } else {
        CompleteVideo video = getCompleteVideo(videoId, context);
        if (video != null) {
          if (video.isGame() && !context.includeGames()) {
            optional = Optional.empty();
          } else {
            optional = Optional.of(video);
          }
        } else {
          optional = Optional.empty();
          miss++;
        }
      }
      resultMap.put(videoId, optional);
    }
    missVideoCounter.add(miss);
    return CompletableFuture.completedFuture(resultMap);
  }

  private CompleteVideo getCompleteVideo(int videoId, LookupContext context) {
    if (context == null) {
      context = LookupContext.defaultContext();
    }
    CompleteVideoContext.Builder builder = vmsInMemory.newImplicitCompleteVideoContext();
    return vmsInMemory
        .getCompleteVideoManager(context.country())
        .getVideo(videoId, builder.build());
  }

  @Override
  public SupplementalVideo getSupplementalVideo(int videoId) {
    return supplementalVideoProvider.getSupplementalVideo(
        TypeManager.findObject(Video.class, videoId), RequestContextWrapper.get().getCountry());
  }
}
