package com.netflix.api.service.vhs;

import com.google.common.collect.Lists;
import com.netflix.vhs.protogen.ViewingHistory;
import com.netflix.vhs.protogen.ViewingHistory.Detail;
import java.util.List;
import java.util.Objects;

public class APIVideoViewingRecordImpl implements APIVideoViewingRecord {

  private final List<APIVideoViewingDetails> viewingDetails;
  private final Integer videoId;
  private final long maxDate;

  APIVideoViewingRecordImpl(ViewingHistory.ViewingRecord viewingRecord) {
    final List<Detail> detailList = viewingRecord.getDetailList();
    this.videoId = viewingRecord.getMovieId();
    viewingDetails = Lists.newArrayListWithCapacity(detailList.size());
    long maxDateTemp = 0L;
    for (Detail vd : detailList) {
      viewingDetails.add(new APIVideoViewingDetailsImpl(vd));
      maxDateTemp = Math.max(maxDateTemp, vd.getWatchedTs());
    }
    viewingDetails.sort((a, b) -> Long.compare(b.getStartTs(), a.getStartTs()));
    this.maxDate = maxDateTemp;
  }

  @Override
  public Integer getVideo() {
    return videoId;
  }

  @Override
  public APIVideoViewingDetails getMostRecentViewingDetails() {
    if (viewingDetails.isEmpty()) {
      return null;
    }
    return viewingDetails.getFirst();
  }

  @Override
  public List<APIVideoViewingDetails> getAllViewingDetails() {
    return viewingDetails;
  }

  @Override
  public long getMostRecentDate() {
    return maxDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIVideoViewingRecordImpl that)) {
      return false;
    }
    return Objects.equals(viewingDetails, that.viewingDetails);
  }

  @Override
  public int hashCode() {
    return Objects.hash(viewingDetails);
  }

  @Override
  public String toString() {
    return "Record[" + viewingDetails + ']';
  }
}
