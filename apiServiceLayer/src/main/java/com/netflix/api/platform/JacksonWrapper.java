package com.netflix.api.platform;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonStreamContext;
import com.fasterxml.jackson.core.Version;
import com.fasterxml.jackson.core.json.JsonWriteContext;
import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.module.SimpleSerializers;
import com.fasterxml.jackson.databind.ser.BeanSerializerFactory;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.guava.GuavaModule;
import com.fasterxml.jackson.module.afterburner.AfterburnerModule;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Multimap;
import com.netflix.api.platform.JsonUtility.Feature;
import com.netflix.api.platform.JsonUtility.JSONCapableObject;
import java.io.IOException;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;

public class JacksonWrapper implements JsonParserWrapper {
  // can't access the PropertyRepositoryHolder from here; hardcode
  private static final boolean USE_SAFE_SER = true;
  private static final boolean WRITE_ENUM_USING_TO_STRING = true;
  private static final List<String> NAME_PREFIX_WHITELIST = List.of();
  private static final List<String> PACKAGE_PREFIX_WHITELIST = List.of();
  private static final boolean FAIL_ON_EMPTY_BEANS = false;
  private static final boolean NON_NULL = false;

  private static class Holder {
    final ObjectMapper mapper;
    final ObjectReader reader;
    final ObjectWriter writer;

    private Holder(ObjectMapper mapper) {
      this.mapper = mapper;
      reader = mapper.readerFor(Object.class);
      writer = mapper.writer();
    }
  }

  private static final LoadingCache<Set<Feature>, Holder> mapperCache =
      CacheBuilder.newBuilder()
          .build(
              new CacheLoader<>() {
                @Override
                public Holder load(Set<Feature> features) {
                  return new Holder(createMapper(features));
                }
              });

  private final Holder holder;

  static JacksonWrapper create(Collection<Feature> features) {
    Holder holder;
    try {
      if (features == null) {
        holder = mapperCache.get(Set.of());
      } else {
        holder = mapperCache.get(new HashSet<>(features));
      }
    } catch (ExecutionException e) {
      throw new RuntimeException("Could not create JSON parser", e);
    }
    return new JacksonWrapper(holder);
  }

  private JacksonWrapper(Holder mapper) {
    this.holder = mapper;
  }

  private static ObjectMapper createMapper(Collection<Feature> features) {
    ObjectMapper mapper = new ObjectMapper();
    if (NON_NULL) {
      mapper.setSerializationInclusion(Include.NON_NULL);
    }
    // some scripts expect enum to be written as a string
    mapper.configure(SerializationFeature.WRITE_ENUMS_USING_TO_STRING, WRITE_ENUM_USING_TO_STRING);
    // TODO: Replace deprecated ESCAPE_NON_ASCII with modern Jackson configuration
    @SuppressWarnings("deprecation")
    var factory = mapper.getFactory();
    factory.configure(JsonGenerator.Feature.ESCAPE_NON_ASCII, true);
    mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, FAIL_ON_EMPTY_BEANS);
    mapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
    if (features.contains(Feature.PRETTY_PRINT)) {
      mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
    }
    SimpleModule module = new SimpleModule(Version.unknownVersion());
    if (features.contains(Feature.DO_NOT_SERIALIZE_POJOS)) {
      module.setSerializers(new UseToStringForPojoSerializers());
    } else {
      module.setSerializers(new ServiceLayerSafeSerializers());
    }
    module.addSerializer(JSONCapableObjectSerializer.instance);
    mapper.registerModule(new GuavaModule());
    mapper.registerModule(new AfterburnerModule());
    mapper.registerModule(module);
    return mapper;
  }

  @Override
  public String toJson(Object obj) throws Exception {
    return holder.writer.writeValueAsString(obj);
  }

  @Override
  public Object fromJson(String json) throws Exception {
    try {
      return holder.reader.readValue(json);
    } catch (JsonProcessingException ex) {
      ex.clearLocation();
      throw ex;
    }
  }

  @Override
  public Object toMap(Object obj) {
    return holder.mapper.convertValue(obj, Map.class);
  }

  private static class JSONCapableObjectSerializer extends StdSerializer<JSONCapableObject> {

    private static final long serialVersionUID = 7729450240086035751L;
    private static final JSONCapableObjectSerializer instance = new JSONCapableObjectSerializer();

    private JSONCapableObjectSerializer() {
      super(JSONCapableObject.class);
    }

    @Override
    public void serialize(JSONCapableObject value, JsonGenerator jgen, SerializerProvider provider)
        throws IOException {
      JsonStreamContext ctx = jgen.getOutputContext();
      JsonWriteContext jwc = (JsonWriteContext) ctx;
      StringBuilder sb = new StringBuilder();
      if (jwc.inObject()) {
        sb.append(":");
      }
      sb.append(value.getJSON());
      jgen.writeRaw(sb.toString());

      jwc.writeValue();
    }
  }

  private static class ServiceLayerSerializer<T> extends StdSerializer<Object> {

    private static final long serialVersionUID = 7561722698587194613L;
    private final JsonSerializer<T> ser;
    private final JavaType type;
    private final BeanDescription beanDesc;
    private final Class<T> raw;
    private final Package pack;

    ServiceLayerSerializer(
        JsonSerializer<T> ser,
        JavaType type,
        BeanDescription beanDesc,
        Class<T> raw,
        Package pack) {
      super(Object.class);
      this.ser = ser;
      this.type = type;
      this.beanDesc = beanDesc;
      this.raw = raw;
      this.pack = pack;
    }

    @Override
    public void serialize(
        Object value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
        throws IOException {
      if (USE_SAFE_SER) {
        final String simpleName = raw.getSimpleName();
        final String packageName = pack != null ? pack.getName() : null;
        if (!(NAME_PREFIX_WHITELIST.contains(simpleName)
            || PACKAGE_PREFIX_WHITELIST.contains(packageName))) {
          jsonGenerator.writeString(raw.cast(value).toString());
          return;
        }
      }
      if (ser != null) {
        ser.serialize(raw.cast(value), jsonGenerator, serializerProvider);
      } else {
        // TODO: Replace deprecated findBeanSerializer with SerializerProvider approach
        @SuppressWarnings("deprecation")
        JsonSerializer<Object> beanSer =
            BeanSerializerFactory.instance.findBeanSerializer(serializerProvider, type, beanDesc);
        if (beanSer != null) {
          beanSer.serialize(value, jsonGenerator, serializerProvider);
        }
      }
    }
  }

  private static class ServiceLayerSafeSerializers extends SimpleSerializers {

    private static final long serialVersionUID = -6038935578810700963L;

    @SuppressWarnings("unchecked")
    @Override
    public JsonSerializer<?> findSerializer(
        SerializationConfig config, JavaType type, BeanDescription beanDesc) {
      Package pack = type.getRawClass().getPackage();
      JsonSerializer<?> ser = super.findSerializer(config, type, beanDesc);
      if (!type.isEnumType()
          && pack != null
          && pack.getName().startsWith("com.netflix.api.service")) {
        return new ServiceLayerSerializer(ser, type, beanDesc, type.getRawClass(), pack);
      }
      return ser;
    }
  }

  private static class UseToStringForPojoSerializers extends SimpleSerializers {

    private static final long serialVersionUID = 7918255473132924797L;

    @Override
    public JsonSerializer<?> findSerializer(
        SerializationConfig config, JavaType type, BeanDescription beanDesc) {
      Class<?> raw = type.getRawClass();
      if (Multimap.class.isAssignableFrom(raw)
          || JSONCapableObject.class.isAssignableFrom(raw)
          || type.isPrimitive()
          || type.isCollectionLikeType()
          || type.isMapLikeType()
          || type.isArrayType()
          || type.isEnumType()
          || Number.class.isAssignableFrom(raw)
          || String.class.isAssignableFrom(raw)
          || Boolean.class.isAssignableFrom(raw)) {
        return super.findSerializer(config, type, beanDesc);
      } else {
        return ToStringSerializer.instance;
      }
    }
  }
}
