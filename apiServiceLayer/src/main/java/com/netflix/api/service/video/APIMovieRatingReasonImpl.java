package com.netflix.api.service.video;

import com.netflix.videometadata.l10n.VMSL10NProviderDelegate;
import com.netflix.vms.type.MovieRatingReason;
import java.util.List;
import java.util.Optional;

public class APIMovieRatingReasonImpl implements APIMovieRatingReason {
  private final MovieRatingReason ratingReason;
  private final VMSL10NProviderDelegate vmsl10NProviderDelegate;

  APIMovieRatingReasonImpl(
      MovieRatingReason ratingReason, VMSL10NProviderDelegate vmsl10NProviderDelegate) {
    this.ratingReason = ratingReason;
    this.vmsl10NProviderDelegate = vmsl10NProviderDelegate;
  }

  @Override
  public List<Integer> getReasonIds() {
    return ratingReason.getReasonIds();
  }

  @Override
  public boolean isDisplayImageOnly() {
    return ratingReason.isDisplayImageOnly();
  }

  @Override
  public boolean isDisplayOrderSpecific() {
    return ratingReason.isDisplayOrderSpecific();
  }

  @Override
  public String getLocalizedReasonDescription(Integer reasonId, String defaultString) {
    // this is a temporary hack for KR TODO have separate fields for description and level in VMS
    return Optional.ofNullable(
            LocalizedRatingReason.getInstance(vmsl10NProviderDelegate, reasonId, defaultString))
        .map(LocalizedRatingReason::getDescription)
        .orElse(null);
  }

  @Override
  public String getLocalizedReasonLevel(Integer reasonId, String defaultString) {
    // this is a temporary hack for KR TODO have separate fields for description and level in VMS
    return Optional.ofNullable(
            LocalizedRatingReason.getInstance(vmsl10NProviderDelegate, reasonId, defaultString))
        .map(LocalizedRatingReason::getLevel)
        .orElse(null);
  }
}
