package com.netflix.api.service.ums;

import com.google.common.base.Preconditions;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.api.service.ums.APIMessage.APIMessageState;
import com.netflix.api.service.ums.APIMessageImpl.APIMessageStateImpl;
import com.netflix.ums.common.CapabilityType;
import com.netflix.ums.common.requestresponse.GetSatisfactionSurveysRequest;
import java.util.ArrayList;
import java.util.List;

public class APISatisfactionSurveysRequestImpl implements APISatisfactionSurveysRequest {

  private final APIUser user;
  private APIMessageState readState;
  private Integer maxCount;
  private Long timestamp;
  private Boolean retrieveOlderThanRefTimestamp;
  private String uiPlatform;
  private String templateStringFormat;
  private String appVersion;
  private final List<CapabilityType> capabilityTypes = new ArrayList<>();

  APISatisfactionSurveysRequestImpl(APIUser user) {
    this.user = Preconditions.checkNotNull(user, "Must provide user");
  }

  @Override
  public APIUser getUser() {
    return user;
  }

  @Override
  public APISatisfactionSurveysRequest setReadState(APIMessageState readState) {
    this.readState = readState;
    return this;
  }

  @Override
  public APISatisfactionSurveysRequest setMaxCount(int maxCount) {
    this.maxCount = maxCount;
    return this;
  }

  @Override
  public APISatisfactionSurveysRequest setReferenceTimestamp(long timestamp) {
    this.timestamp = timestamp;
    return this;
  }

  @Override
  public APISatisfactionSurveysRequest setMessagesOlderThanRefTimestamp(
      boolean retrieveOlderThanRefTimestamp) {
    this.retrieveOlderThanRefTimestamp = retrieveOlderThanRefTimestamp;
    return this;
  }

  @Override
  public APISatisfactionSurveysRequest setUIPlatform(String uiPlatform) {
    this.uiPlatform = uiPlatform;
    return this;
  }

  @Override
  public APISatisfactionSurveysRequest setTemplateStringFormat(String templateStringFormat) {
    this.templateStringFormat = templateStringFormat;
    return this;
  }

  @Override
  public APISatisfactionSurveysRequest setAppVersion(String appVersion) {
    this.appVersion = appVersion;
    return this;
  }

  @Override
  public APISatisfactionSurveysRequest addCapabilityType(APICapabilityType capabilityType) {
    capabilityTypes.add(CapabilityType.valueOf(capabilityType.name()));
    return this;
  }

  public static GetSatisfactionSurveysRequest createFrom(
      APISatisfactionSurveysRequest filterNotificationMessagesRequest) {
    if (!(filterNotificationMessagesRequest instanceof APISatisfactionSurveysRequestImpl)) {
      throw new IllegalArgumentException("Unknown implementation of APISatisfactionSurveysRequest");
    }
    APISatisfactionSurveysRequestImpl request =
        (APISatisfactionSurveysRequestImpl) filterNotificationMessagesRequest;

    GetSatisfactionSurveysRequest.Builder builder = GetSatisfactionSurveysRequest.getBuilder();

    // set required values
    builder
        .withCountry(RequestContextWrapper.get().getCountry())
        .withCustomerId(APIUserUtil.getCustomerId(request.user))
        .withLocale(RequestContextWrapper.get().getNfLocale())
        .withOlderThanRefTS(request.retrieveOlderThanRefTimestamp);

    // set optional values
    if (request.maxCount != null) {
      builder.withMaxCount(request.maxCount);
    }
    if (request.readState != null) {
      builder.withReadState(APIMessageStateImpl.lookup(request.readState));
    }
    if (request.timestamp != null) {
      builder.withReferenceTS(request.timestamp);
    }
    if (request.uiPlatform != null) {
      builder.withUiPlatform(request.uiPlatform);
    }
    if (request.templateStringFormat != null) {
      builder.withTemplateStringFormat(request.templateStringFormat);
    }
    if (request.appVersion != null) {
      builder.withAppVersion(request.appVersion);
    }
    if (!request.capabilityTypes.isEmpty()) {
      builder.withCapabilityTypes(request.capabilityTypes);
    }

    // build and return request
    return builder.build();
  }
}
