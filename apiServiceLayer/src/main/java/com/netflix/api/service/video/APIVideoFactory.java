package com.netflix.api.service.video;

import com.netflix.api.facade.service.VideoLookup;
import com.netflix.api.facade.service.collection.CollectionLookup;
import com.netflix.type.Video;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletionStage;

public interface APIVideoFactory {

  VideoLookup getVideoLookup();

  CollectionLookup getCollectionLookup();

  APICachedVideoImpl getInstance(int videoId);

  APICachedVideoImpl getInstance(Video video);

  Map<Integer, Optional<APIVideo>> getInstances(Collection<Integer> videoIds);

  /** If a video cannot be looked up it will not be present in the response collection */
  Collection<APIVideo> getInstancesCollection(Collection<Integer> videoIds);

  CompletionStage<Collection<APIVideo>> getInstancesCollectionAsync(Collection<Integer> videoIds);
}
