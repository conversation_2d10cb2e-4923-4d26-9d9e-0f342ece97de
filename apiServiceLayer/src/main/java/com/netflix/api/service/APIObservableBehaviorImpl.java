package com.netflix.api.service;

import com.netflix.api.platform.util.InterruptAdvice;
import rx.Observable;
import rx.Subscriber;

/**
 * Created with IntelliJ IDEA. User: mjacobs Date: 3/3/14 Time: 11:00 AM To change this template use
 * File | Settings | File Templates.
 */
public abstract class APIObservableBehaviorImpl<Item> implements APIObservableBehavior<Item> {

  @Override
  public void call(Subscriber<? super Item> outerSubscriber) {
    final Observable.OnSubscribe<Item> onSubscribe =
        innerSubscriber -> internalSubscribe(InterruptAdvice.wrapSubscriber(innerSubscriber));
    onSubscribe.call(outerSubscriber);
  }

  protected abstract void internalSubscribe(Subscriber<? super Item> subscriber);
}
