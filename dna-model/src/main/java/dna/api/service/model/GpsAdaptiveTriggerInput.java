package dna.api.service.model;

import com.netflix.gpsproto.common.adaptive.trigger.protogen.AdaptiveTriggerCause;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class GpsAdaptiveTriggerInput {
  private AdaptiveTriggerCause adaptiveTriggerCause;

  private GpsPageSectionsInput triggerData;

  public AdaptiveTriggerCause getAdaptiveTriggerCause() {
    return adaptiveTriggerCause;
  }

  public GpsPageSectionsInput getTriggerData() {
    return triggerData;
  }

  public GpsAdaptiveTriggerInput setAdaptiveTriggerCause(
      AdaptiveTriggerCause adaptiveTriggerCause) {
    this.adaptiveTriggerCause = adaptiveTriggerCause;
    return this;
  }

  public GpsAdaptiveTriggerInput setTriggerData(GpsPageSectionsInput triggerData) {
    this.triggerData = triggerData;
    return this;
  }
}
