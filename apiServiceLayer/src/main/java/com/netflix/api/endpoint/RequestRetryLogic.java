package com.netflix.api.endpoint;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.archaius.api.Property;
import java.util.List;
import java.util.concurrent.CancellationException;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeoutException;
import org.springframework.stereotype.Component;
import rx.exceptions.CompositeException;

/**
 * Has utility methods containing logic to help translate exceptions to status codes, for instance.
 */
@Component
public class RequestRetryLogic {
  private static final Property<List<String>> endpointsToRetry =
      PropertyRepositoryHolder.getPropertyRepository()
          .getList("api.endpoints.to.503.on.exception.at.startup", String.class)
          .orElse(List.of("all"));

  private static final Property<Boolean> onlyAllowGetRetries =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.allow.only.httpget.retries.at.startup", Boolean.class)
          .orElse(true);

  public boolean isRequestRetriableOnException(Throwable e, APIRequest request) {
    // if server is warm, don't retry on any exceptions

    // if request has side effects dont retry
    if (onlyAllowGetRetries.get()
        && !"GET".equalsIgnoreCase(request.getServletRequest().getMethod())) return false;

    // selectively retry certain endpoints
    List<String> whitelistedEndpointGroups = endpointsToRetry.get();
    if (!whitelistedEndpointGroups.contains("all")
        && !whitelistedEndpointGroups.contains(request.getRequestContext().getEndpointGroup()))
      return false;

    return isRetriableException(e)
        || (e instanceof CompositeException && hasRetriableException((CompositeException) e));
  }

  private boolean isRetriableException(Throwable e) {
    return e instanceof InterruptedException
        || e instanceof RejectedExecutionException
        || e instanceof TimeoutException
        || e instanceof CancellationException;
  }

  private boolean hasRetriableException(CompositeException ce) {
    for (Throwable e : ce.getExceptions()) if (isRetriableException(e)) return true;
    return false;
  }
}
