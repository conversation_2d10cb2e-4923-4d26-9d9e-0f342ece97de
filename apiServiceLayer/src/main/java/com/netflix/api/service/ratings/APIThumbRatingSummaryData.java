package com.netflix.api.service.ratings;

import java.util.Objects;
import javax.annotation.Nonnull;

public record APIThumbRatingSummaryData(APIThumbRatingSummary ratingSummary, Long ttl) {

  public boolean equals(final Object o) {
    if (o == this) return true;
    if (!(o instanceof APIThumbRatingSummaryData(APIThumbRatingSummary summary, Long ttl1)))
      return false;
    final Object this$ratingSummary = this.ratingSummary();
    if (!Objects.equals(this$ratingSummary, summary)) return false;
    final Object this$ttl = this.ttl();
    return Objects.equals(this$ttl, ttl1);
  }

  public int hashCode() {
    final int PRIME = 59;
    int result = 1;
    final Object $ratingSummary = this.ratingSummary();
    result = result * PRIME + ($ratingSummary == null ? 43 : $ratingSummary.hashCode());
    final Object $ttl = this.ttl();
    result = result * PRIME + ($ttl == null ? 43 : $ttl.hashCode());
    return result;
  }

  @Nonnull
  public String toString() {
    return "APIThumbRatingSummaryData(ratingSummary="
        + this.ratingSummary()
        + ", ttl="
        + this.ttl()
        + ")";
  }
}
