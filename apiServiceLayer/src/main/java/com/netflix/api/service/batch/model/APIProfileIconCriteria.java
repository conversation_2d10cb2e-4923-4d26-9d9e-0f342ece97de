package com.netflix.api.service.batch.model;

import java.util.List;
import java.util.Map;

public class APIProfileIconCriteria {

  private final int width;
  private final int height;
  private final boolean secure;
  private final boolean topAligned;
  private final Map<String, Object> params;
  private final List<String> recipePreferences;
  private final APIAvatarType avatarType;

  private APIProfileIconCriteria(Builder builder) {
    this.width = builder.width;
    this.height = builder.height;
    this.secure = builder.secure;
    this.topAligned = builder.topAligned;
    this.params = builder.params;
    this.recipePreferences = builder.recipePreferences;
    this.avatarType = builder.avatarType;
  }

  public int getWidth() {
    return width;
  }

  public int getHeight() {
    return height;
  }

  public boolean isSecure() {
    return secure;
  }

  public boolean isTopAligned() {
    return topAligned;
  }

  public Map<String, Object> getParams() {
    return params;
  }

  public List<String> getRecipePreferences() {
    return recipePreferences;
  }

  public APIAvatarType getAvatarType() {
    return avatarType;
  }

  @Override
  public String toString() {
    return "APIProfileIconCriteria{"
        + "width="
        + width
        + ", height="
        + height
        + ", secure="
        + secure
        + ", topAligned="
        + topAligned
        + ", params="
        + params
        + ", recipePrefrences="
        + recipePreferences
        + '}';
  }

  public static Builder newBuilder() {
    return new Builder();
  }

  public static class Builder {
    private int width;
    private int height;
    private boolean secure = false;
    private boolean topAligned = false;
    private Map<String, Object> params;
    private List<String> recipePreferences;
    private APIAvatarType avatarType;

    protected Builder() {}

    public Builder setWidth(int width) {
      this.width = width;
      return this;
    }

    public Builder setHeight(int height) {
      this.height = height;
      return this;
    }

    public Builder setSecure(boolean secure) {
      this.secure = secure;
      return this;
    }

    public Builder setTopAligned(boolean topAligned) {
      this.topAligned = topAligned;
      return this;
    }

    public Builder setParams(Map<String, Object> params) {
      this.params = params;
      return this;
    }

    public Builder setRecipePreferences(List<String> preferences) {
      this.recipePreferences = preferences;
      return this;
    }

    public Builder setAvatarType(APIAvatarType avatarType) {
      this.avatarType = avatarType;
      return this;
    }

    /**
     * @deprecated has no effect on avatar image
     * @see <a href="https://jira.netflix.com/browse/DNA-927">DNA-927</a>
     */
    @Deprecated
    public Builder setGrayscale(boolean grayscale) {
      return this;
    }

    public APIProfileIconCriteria build() {
      return new APIProfileIconCriteria(this);
    }
  }
}
