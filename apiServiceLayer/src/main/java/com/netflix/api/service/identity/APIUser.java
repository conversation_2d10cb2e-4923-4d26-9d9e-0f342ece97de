package com.netflix.api.service.identity;

import com.netflix.api.service.APICountry;
import com.netflix.api.service.APILocale;
import com.netflix.api.service.ListItem;
import com.netflix.api.service.account.APIPlanDetail;
import com.netflix.api.service.plan.APISubscriptionData;
import com.netflix.api.service.video.APIResolutions;
import java.util.List;
import java.util.SortedMap;
import java.util.concurrent.CompletionStage;
import rx.Observable;
import rx.Single;

/**
 * Representation of all Identity/User data for the API Service Layer.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/********/APIUser.class.png"
 * alt="APIUser class diagram">
 */
public interface APIUser {

  long getCustomerId();

  /**
   * Placeholder
   *
   * @return Placeholder
   */
  APIUser getAccountOwner();

  /**
   * Is this user the primary account holder? (Used before a delete -- a primary profile cannot be
   * deleted).
   *
   * @return {@code true} if this user is the primary account holder
   */
  boolean isPrimaryAccountOwner();

  /**
   * Is the subscriber signed up for streaming services?
   *
   * @return {@code true} if the subscriber is allowed to stream movies
   */
  boolean getCanStreamNow();

  /**
   * Returns a boolean indicating if the customer has a DVD plan or not. Note this means that the
   * customer could be a hybrid customer or a DVD-only plan customer.
   *
   * @return {@code true} if the customer has a DVD plan
   * @deprecated
   */
  @Deprecated
  boolean getHasDVD();

  /**
   * A flag to indicate whether the user has opted in/out for the pre/post autoplay feature.
   *
   * @return boolean indicating whether autoplay is enabled
   */
  boolean isAutoplayEnabled();

  /**
   * The country in which the subscriber signed up.
   *
   * @return the country from which the subscriber signed up, as an {@link APICountry}
   */
  Observable<APICountry> getAPICountry();

  /**
   * Indicates if this user has previously been present and accepts cookie disclosure terms
   *
   * @return true if the user has accepted, false otherwise
   */
  boolean cookieDisclosureAccepted();

  /**
   * Returns the country in which the user signed up
   *
   * @deprecated Use {@linkplain #getCountryOfSignupObservable()} instead
   */
  @Deprecated
  String getCountryOfSignup();

  /** Returns the country in which the user signed up */
  Observable<String> getCountryOfSignupObservable();

  /** The country which the user registered */
  String getCountryOfRegistration();

  /** Returns the user's primary language */
  String getLanguage();

  /**
   * Get the preferred locales as list items of {@link APILocale}.
   *
   * @return an {@code Observable} that emits preferred locales as list items of {@link APILocale}
   * @see com.netflix.api.service.ListItem
   */
  Observable<ListItem<APILocale>> getPreferredLocales();

  /** Returns a globally unique id associated with this customer. */
  String getCustomerGUID();

  /**
   * Email address on file for this subscriber.
   *
   * @return the email address as a String
   */
  String getEncryptedEmailAddress();

  /**
   * Returns the name of the user.
   *
   * @return user's profile name
   */
  CompletionStage<String> getProfileName();

  /**
   * Returns the HTML-decoded profile name of the user
   *
   * @return HTML-decoded profile name of the user
   */
  CompletionStage<String> getHumanReadableProfileName();

  /** Gets the raw maturity value from subscriber. */
  @Deprecated
  Observable<Integer> getRawMaturityValue();

  /**
   * Get the maturity level setting for this customer.
   *
   * @return the maturity level setting for this customer
   */
  APIMaturity getMaturity();

  /**
   * Status of the membership: on hold, active, not active, etc.
   *
   * @return Placeholder
   * @see APIMembershipStatus
   */
  APIMembershipStatus getMembershipStatus();

  /**
   * Is it time to show the terms of use to this subscriber.
   *
   * @return {@code true} if it is time to show the TOU to this subscriber
   */
  boolean isTimeForTou();

  /**
   * Indicates if this account is a tester account
   *
   * @return {@code true} if this is a tester account
   */
  boolean isTestAccount();

  /**
   * @return the current subscription plan id or empty if no subscription exists
   */
  Observable<Long> getPlanId();

  /**
   * @deprecated use {@link #getPlanDetails()} or {@link #getSubscriptionDetails()}
   * @return the details of the user's current subscription plan information
   */
  @Deprecated
  Observable<APIPlanInfo> getPlanInfo();

  /**
   * @return the video resolutions available for the current plan
   */
  Observable<APIResolutions> getPlanResolutions();

  /**
   * @return the details of the user's current plan
   */
  Observable<APIPlanDetail> getPlanDetails();

  /**
   * @return the details of the user's current subscription
   */
  Observable<APISubscriptionData> getSubscriptionDetails();

  /**
   * @return the details of the user's current subscription price data
   */
  Observable<APIPriceInfo> getPriceDetails();

  /**
   * A list of reasons why the user cannot stream
   *
   * @return the list of reasons
   */
  Observable<List<APIServiceEndReasons>> getServiceEndReasons();

  /**
   * Indicating if playback is blocked for payment problems
   *
   * @return Observable of a flag
   */
  Observable<Boolean> onHoldDueToPayment();

  /**
   * Indicates if an instant queue is allowed for the customer.
   *
   * @return {@code true} if this customer can have an instant queue
   */
  boolean isInstantQueueAllowed();

  /**
   * Is this profile being used for the first time? This lets clients show first-time-user messaging
   * such as collecting taste preferences, etc. Once the first-time-user messaging has been
   * provided, clients must edit the profile and set this flag to false.
   *
   * @return {@code true} if this the profile is being used for the first time
   */
  boolean isFirstUse();

  /**
   * Get the date this profile was created, in milliseconds since epoch.
   *
   * @return date this profile was created, in milliseconds since epoch
   */
  long getDateCreated();

  /**
   * Get the number of profiles on this account.
   *
   * @return the number of profiles on this account
   */
  Single<Integer> getNumProfilesInAccount();

  /**
   * Get the name of icon associated to this user
   *
   * @return the icon name as string
   */
  String getIconName();

  /**
   * Get all icon names on the account of this user by customer ID (some may be null).
   *
   * @return all icon names, mapped to the customer ID
   */
  Single<SortedMap<Long, String>> getIconNames();

  /**
   * AB Test 4736 allows for the creation of an auto-created Kids Profile per account. This method
   * returns true if and only if the user is an auto-created Kids Profile.
   *
   * @return true iff the user is an auto-created Kids Profile
   */
  boolean isDefaultKidsProfile();

  /**
   * returns true if and only if the user is an auto-created Profile.
   *
   * @return true if the user is an auto-created Profile
   */
  boolean isAutoCreatedProfile();

  /**
   * Preferred experience of a user - currently, it is either a standard or a "JustForKids"
   * experience.
   */
  enum Experience {
    jfk,
    watch_together,
    standard,
    family
  }

  /**
   * Whether the customer wants a JustForKids or a Standard experience. There may be other
   * experiences in the future.
   *
   * <p>use the APIUser.isXXXExperience() methods to determine if this profile is non-standard
   *
   * @return the preferred experience of this customer as an enum
   */
  Experience getPreferredExperience();

  /**
   * AB Test 33404 adds a family profile experience, this flag indicates if this is a family profile
   *
   * @return true if this user has the family experience type and false otherwise
   */
  boolean isFamilyExperience();

  /**
   * @return true if this is a kids profile and false otherwise
   */
  boolean isJustForKidsExperience();

  /**
   * @return true if this is a watch together profile and false otherwise
   */
  boolean isWatchTogetherExperience();

  /**
   * Information about how subtitles should be rendered for this user.
   *
   * @return The subtitle settings the user has explicitly set
   */
  Observable<APISubtitleSettings> getSubtitleSettings();

  /**
   * Returns true if user has been verified to be an adult
   *
   * @return a Boolean indicating if user has been verified to be an adult
   */
  Observable<Boolean> isAdultVerifiedObservable();

  /**
   * Returns true if PIN is enabled for user. If signup country is "DE" or Germany, this is always
   * true
   *
   * @return an Observable of boolean indicating if this user requires PIN protection
   */
  Observable<Boolean> isPinEnabled();

  /**
   * Returns the pin for this user
   *
   * @return an Observable of string with the pin value, an empty Observable if no pin is set
   */
  Observable<String> getPin();

  /**
   * Returns true if user profile is locked.
   *
   * @return an Observable of boolean indicating if this user profile is locked
   */
  Observable<Boolean> isProfileLocked();

  /**
   * Returns true if profile creation is locked on the account.
   *
   * @return an Observable of boolean indicating if profile creation is locked
   */
  Observable<Boolean> isProfileCreationLocked();

  /**
   * Returns true if user has migrated to use Profile hub
   *
   * @return an Observable of boolean indicating if this user has migrated
   */
  Observable<Boolean> isAccountMigratedToProfileHub();

  /**
   * Returns the profile lock pin for this user
   *
   * @return an Observable of string with the profile lock pin value, an empty Observable if no
   *     profile lock pin is set
   */
  Observable<String> getProfileLockPin();

  /**
   * Retrieves whether or not this Account is a partner preview
   *
   * @return true if it is preview and false otherwise
   */
  Boolean isPartnerPreviewAccount();

  /**
   * Indicates whether or not the user has alerts available.
   *
   * @return true if alerts are available and false otherwise
   */
  boolean hasAlerts();

  /**
   * Time this customer most recently became a current member. This value changes each time the
   * customer rejoins.
   *
   * @return time this customer became a current member (in milliseconds since Unix Epoch)
   */
  Observable<Long> getMemberSince();

  Observable<APIUser> getUserByGuid(String guid);
}
