package com.netflix.api.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import rx.Observable;

public abstract class APIListBuilderImpl<Item, Behavior extends APIList<PERSON><PERSON>erBehavior<Item>>
    extends APIObservableBuilderImpl<ListItem<Item>, Behavior> implements APIListBuilder<Item> {

  protected APIListBuilderImpl(Behavior behavior) {
    super(behavior);
  }

  @Override
  public Observable<List<Item>> toOrderedList() {
    return build()
        .toList()
        .map(
            listItems -> {
              if (listItems == null) return null;
              if (listItems.isEmpty()) return List.of();

              List<ListItem<Item>> clone = new ArrayList<>(listItems);
              Collections.sort(clone);
              List<Item> items = new ArrayList<>(clone.size());

              // getIndex() can be negative, there can be gaps
              for (ListItem<Item> listItem : clone) {
                items.add(listItem.getItem());
              }
              return items;
            });
  }
}
