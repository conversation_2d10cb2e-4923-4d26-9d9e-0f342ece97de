package dna.api.service.model;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assumptions.assumeFalse;

import com.netflix.userpreferences2.protogen.CharSize;
import com.netflix.userpreferences2.protogen.CharStyle;
import com.netflix.userpreferences2.protogen.Color;
import com.netflix.userpreferences2.protogen.EdgeAttribute;
import com.netflix.userpreferences2.protogen.Opacity;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

class SubtitlesUserPrefsEnumTest {

  @ParameterizedTest
  @EnumSource(Color.Value.class)
  void color(Color.Value value) {
    // make sure that we have all the enum values that subtitles.proto does, otherwise add to
    // api.graphql and run ./gradlew generateFalcor
    assumeFalse(value == Color.Value.UNRECOGNIZED);
    assumeFalse(value == Color.Value.UNSPECIFIED);
    assertDoesNotThrow(() -> dna.api.service.model.Color.valueOf(value.toString()));
  }

  @ParameterizedTest
  @EnumSource(Opacity.Value.class)
  void opacity(Opacity.Value value) {
    // make sure that we have all the enum values that subtitles.proto does, otherwise add to
    // api.graphql and run ./gradlew generateFalcor
    assumeFalse(value == Opacity.Value.UNRECOGNIZED);
    assumeFalse(value == Opacity.Value.UNSPECIFIED);
    assertDoesNotThrow(() -> dna.api.service.model.Opacity.valueOf(value.toString()));
  }

  @ParameterizedTest
  @EnumSource(CharStyle.Value.class)
  void charStyle(CharStyle.Value value) {
    // make sure that we have all the enum values that subtitles.proto does, otherwise add to
    // api.graphql and run ./gradlew generateFalcor
    assumeFalse(value == CharStyle.Value.UNRECOGNIZED);
    assumeFalse(value == CharStyle.Value.UNSPECIFIED);
    assertDoesNotThrow(() -> CharacterStyle.valueOf(value.toString()));
  }

  @ParameterizedTest
  @EnumSource(CharSize.Value.class)
  void charSize(CharSize.Value value) {
    // make sure that we have all the enum values that subtitles.proto does, otherwise add to
    // api.graphql and run ./gradlew generateFalcor
    assumeFalse(value == CharSize.Value.UNRECOGNIZED);
    assumeFalse(value == CharSize.Value.UNSPECIFIED);
    assertDoesNotThrow(() -> CharacterSize.valueOf(value.toString()));
  }

  @ParameterizedTest
  @EnumSource(EdgeAttribute.Value.class)
  void edgeAttr(EdgeAttribute.Value value) {
    // make sure that we have all the enum values that subtitles.proto does, otherwise add to
    // api.graphql and run ./gradlew generateFalcor
    assumeFalse(value == EdgeAttribute.Value.UNRECOGNIZED);
    assumeFalse(value == EdgeAttribute.Value.UNSPECIFIED);
    assertDoesNotThrow(() -> dna.api.service.model.EdgeAttribute.valueOf(value.toString()));
  }
}
