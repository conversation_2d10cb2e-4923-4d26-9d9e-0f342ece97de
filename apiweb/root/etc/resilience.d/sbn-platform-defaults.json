[{"scope": {"cluster": "api-prod-x1nq-x2android", "env": "prod"}, "properties": {"netflix.resource.limiter.concurrency.utilization.buffer": "failure", "netflix.resource.limiter.cpu.enabled": "true", "netflix.resource.limiter.cpu_global.enabled": "true", "netflix.resource.limiter.cpu_priority.enabled": "false", "netflix.resource.limiter.latency.expectedLatencyMetricEnabled": "true", "netflix.resource.limiter.latency.mvc.ApiController.nodequarkPathEvaluator.max": "400", "netflix.resource.limiter.latency.mvc.ApiController.nodequarkPathEvaluator.target": "25", "netflix.resource.limiter.max_latency.enabled": "false", "netflix.resource.limiter.max_latency.utilization.buffer": "failure", "netflix.resource.limiter.request.metrics.enabled": "true", "netflix.resource.limiter.request.priority.microcontext": "true", "netflix.resource.limiter.target_latency.enabled": "false", "netflix.resource.limiter.target_latency.utilization.buffer": "failure", "netflix.resource.utilization.cpu.max": "90", "netflix.resource.utilization.cpu.target": "80", "netflix.resource.utilization.max_latency.allowDroppedCallsToContributeViolations": "true", "netflix.resource.utilization.max_latency.max": "50", "netflix.resource.utilization.max_latency.target": "10"}}, {"scope": {"cluster": "api-prod-x1nq-x2det", "env": "prod"}, "properties": {"netflix.resource.limiter.concurrency.utilization.buffer": "failure", "netflix.resource.limiter.cpu.enabled": "true", "netflix.resource.limiter.cpu_global.enabled": "true", "netflix.resource.limiter.cpu_priority.enabled": "false", "netflix.resource.limiter.latency.expectedLatencyMetricEnabled": "true", "netflix.resource.limiter.latency.mvc.ApiController.nodequarkPathEvaluator.max": "300", "netflix.resource.limiter.latency.mvc.ApiController.nodequarkPathEvaluator.target": "25", "netflix.resource.limiter.latency.mvc.SimpleErrorPagesController.handleError.max": "20", "netflix.resource.limiter.latency.mvc.SimpleErrorPagesController.handleError.target": "20", "netflix.resource.limiter.max_latency.enabled": "false", "netflix.resource.limiter.max_latency.utilization.buffer": "failure", "netflix.resource.limiter.request.metrics.enabled": "true", "netflix.resource.limiter.request.priority.microcontext": "true", "netflix.resource.limiter.target_latency.enabled": "false", "netflix.resource.limiter.target_latency.utilization.buffer": "failure", "netflix.resource.utilization.cpu.max": "90", "netflix.resource.utilization.cpu.target": "80", "netflix.resource.utilization.max_latency.allowDroppedCallsToContributeViolations": "true", "netflix.resource.utilization.max_latency.max": "50", "netflix.resource.utilization.max_latency.target": "10"}}]