package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PacsFeatureResponse {
  private PacsFeature featureName;

  private String featureNameNonstrict;

  private PacsResponseClassification responseClassification;

  private PacsFrictionResponse frictionResponse;

  /**
   * DEPRECATED: PACS removed campaign ID from the their proto Context:
   * https://github.netflix.net/corp/eas-pacs/pull/1973
   */
  @Deprecated private String campaignId;

  private PacsTracing tracing;

  private PacsDetail pacsDetail;

  public PacsFeature getFeatureName() {
    return featureName;
  }

  public String getFeatureNameNonstrict() {
    return featureNameNonstrict;
  }

  public PacsResponseClassification getResponseClassification() {
    return responseClassification;
  }

  public PacsFrictionResponse getFrictionResponse() {
    return frictionResponse;
  }

  @Deprecated
  public String getCampaignId() {
    return campaignId;
  }

  public PacsTracing getTracing() {
    return tracing;
  }

  public PacsDetail getPacsDetail() {
    return pacsDetail;
  }

  public PacsFeatureResponse setFeatureName(PacsFeature featureName) {
    this.featureName = featureName;
    return this;
  }

  public PacsFeatureResponse setFeatureNameNonstrict(String featureNameNonstrict) {
    this.featureNameNonstrict = featureNameNonstrict;
    return this;
  }

  public PacsFeatureResponse setResponseClassification(
      PacsResponseClassification responseClassification) {
    this.responseClassification = responseClassification;
    return this;
  }

  public PacsFeatureResponse setFrictionResponse(PacsFrictionResponse frictionResponse) {
    this.frictionResponse = frictionResponse;
    return this;
  }

  @Deprecated
  public PacsFeatureResponse setCampaignId(String campaignId) {
    this.campaignId = campaignId;
    return this;
  }

  public PacsFeatureResponse setTracing(PacsTracing tracing) {
    this.tracing = tracing;
    return this;
  }

  public PacsFeatureResponse setPacsDetail(PacsDetail pacsDetail) {
    this.pacsDetail = pacsDetail;
    return this;
  }
}
