package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class UserInteractionContext {
  private UserInteractionType interactionType;

  private Integer videoId;

  public UserInteractionType getInteractionType() {
    return interactionType;
  }

  public Integer getVideoId() {
    return videoId;
  }

  public UserInteractionContext setInteractionType(UserInteractionType interactionType) {
    this.interactionType = interactionType;
    return this;
  }

  public UserInteractionContext setVideoId(Integer videoId) {
    this.videoId = videoId;
    return this;
  }
}
