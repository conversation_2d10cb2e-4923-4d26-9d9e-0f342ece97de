package com.netflix.api.service.ratings;

import static com.netflix.api.service.video.APIThumbRating.UNRATED;

import com.google.common.base.Preconditions;
import com.google.protobuf.util.Timestamps;
import com.netflix.api.grpc.GrpcCallHelpers.Future;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserInternal;
import com.netflix.api.service.video.APIThumbRating;
import com.netflix.api.service.video.APIThumbRatingTimestamped;
import com.netflix.myratings.protogen.ThumbRatingSummary;
import com.netflix.type.protogen.BasicTypes.Video;
import com.netflix.type.protogen.BasicTypes.Visitor;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletionStage;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import ust.rating.v1.GetThumbRatingSummaryRequest;
import ust.rating.v1.Rating;
import ust.rating.v1.RatingAccess;
import ust.rating.v1.SummaryType;

@Component
public class MyRatingsHandler {
  private static final Logger logger = LoggerFactory.getLogger(MyRatingsHandler.class);

  private final RatingAccess ratingAccess;

  @Autowired
  public MyRatingsHandler(RatingAccess ratingAccess) {
    this.ratingAccess = ratingAccess;
  }

  /**
   * Get user's thumb ratings
   *
   * @param apiUser user
   * @return mapping of video ids to rating
   */
  public CompletionStage<Map<Integer, APIThumbRatingTimestamped>> getUserThumbsRatings(
      final APIUser apiUser) {
    return getUserThumbsRatingsUsingUST(apiUser, true);
  }

  /**
   * Sets thumb rating on a given video
   *
   * @param videoId id of video to apply thumb rating to
   * @param apiUser user
   * @param rating the rating to apply
   * @param trackId (optional) track id for analytical purposes
   * @return thumb rating that was applied
   */
  public CompletionStage<APIThumbRating> setThumbRating(
      final Integer videoId,
      final APIUser apiUser,
      final APIThumbRating rating,
      final Integer trackId) {
    return setUserThumbRatingUsingUST(videoId, apiUser, rating, trackId);
  }

  /**
   * Get thumb summary data (such as total thumbs counts) for a set of videos.
   *
   * @param videoIds video ids to get summaries for
   * @param summaryType type of summary see {@link
   *     com.netflix.api.service.ratings.APIThumbSummaryType}
   * @return map of video id to summary
   */
  public Observable<Map<Integer, APIThumbRatingSummaryData>> getThumbSummary(
      final Set<Integer> videoIds, final APIThumbSummaryType summaryType) {
    return getThumbSummaryUsingUST(videoIds, summaryType);
  }

  /**
   * Determines if thumb ratings message should be shown.
   *
   * @return Returns true, if user has not rated any videos. False, otherwise.
   */
  public CompletionStage<Boolean> shouldShowFirstThumbsRatingMessage() {
    Preconditions.checkNotNull(APIRequest.getCurrentRequest().getUser(), "User cannot be null");
    return getUserThumbsRatingsUsingUST(APIRequest.getCurrentRequest().getUser(), false)
        .thenApply(resultMap -> (resultMap == null || resultMap.isEmpty()));
  }

  /** Convert APIThumbRating to Rating (UST) */
  private static Rating convertThumbRatingForUst(final APIThumbRating rating) {
    if (UNRATED.equals(rating)) {
      return Rating.THUMBS_UNRATED;
    }
    try {
      return Rating.valueOf(rating.name());
    } catch (IllegalArgumentException e) {
      return Rating.UNRECOGNIZED;
    }
  }

  private static final Function<APIThumbSummaryType, SummaryType> mapSummaryTypeUst =
      summary -> SummaryType.valueOf(summary.name());

  private static final Function<Integer, Video> mapVideo =
      val -> Video.newBuilder().setId(val).build();

  /** Convert Rating (UST) to APIThumbRating */
  private static Optional<APIThumbRating> convertUstRating(final Rating rating) {
    if (Rating.THUMBS_UNRATED.equals(rating)) {
      return Optional.of(UNRATED);
    }
    try {
      return Optional.of(APIThumbRating.valueOf(rating.name()));
    } catch (IllegalArgumentException e) {
      logger.error("Unknown ratingName={}", rating.name());
      return Optional.empty();
    }
  }

  /** Set thumb rating for a video */
  private CompletionStage<APIThumbRating> setUserThumbRatingUsingUST(
      final Integer videoId,
      final APIUser apiUser,
      final APIThumbRating rating,
      final Integer trackId) {
    Preconditions.checkNotNull(rating, "rating cannot be null");
    Preconditions.checkNotNull(videoId, "videoId cannot be null");
    Preconditions.checkNotNull(apiUser, "user cannot be null");

    final Video video = Video.newBuilder().setId(videoId).build();
    final Visitor visitor =
        Visitor.newBuilder().setId(((APIUserInternal) apiUser).getVisitor().getId()).build();

    if (rating == UNRATED) {
      return Future.call(
              ratingAccess::deleteThumbRating,
              ust.rating.v1.DeleteThumbRatingRequest.newBuilder()
                  .setVideo(video)
                  .setVisitor(visitor)
                  .setTrackId(trackId)
                  .build())
          .thenApply(result -> convertUstRating(result.getRating().getRating()).orElse(UNRATED));
    } else {
      return Future.call(
              ratingAccess::setThumbRating,
              ust.rating.v1.SetThumbRatingRequest.newBuilder()
                  .setVideo(video)
                  .setVisitor(visitor)
                  .setRating(convertThumbRatingForUst(rating))
                  .setTrackId(trackId)
                  .build())
          .thenApply(result -> convertUstRating(result.getRating().getRating()).orElse(UNRATED));
    }
  }

  /**
   * Get thumb ratings for one or more videos. If no videos provided, all videos rated by user are
   * returned.
   *
   * @param sortByTimestampDesc if true, method returns linked hash map sorted by timestamp
   * @param videoIds provide zero or more; if no video ids provided, returns all videos rated by
   *     user
   */
  public CompletionStage<Map<Integer, APIThumbRatingTimestamped>> getUserThumbsRatingsUsingUST(
      final APIUser user, final boolean sortByTimestampDesc, final Integer... videoIds) {
    Preconditions.checkNotNull(user, "user cannot be null");

    final Visitor visitorForUstCall =
        Visitor.newBuilder().setId(((APIUserInternal) user).getVisitor().getId()).build();

    if (videoIds.length == 0) {
      return Future.call(
              ratingAccess::getAllThumbRatings,
              ust.rating.v1.GetAllThumbRatingsRequest.newBuilder()
                  .setVisitor(visitorForUstCall)
                  .build())
          .thenApply(
              ustResp -> {
                final Map<Integer, APIThumbRatingTimestamped> result = new LinkedHashMap<>();

                ustResp
                    .getResultsMap()
                    .forEach(
                        (videoId, rating) -> {
                          final Optional<APIThumbRating> apiRatingOpt =
                              convertUstRating(rating.getRating());
                          if (apiRatingOpt.isEmpty()) return;
                          result.put(
                              videoId,
                              new APIThumbRatingTimestamped(
                                  Timestamps.toMillis(rating.getTimestamp()), apiRatingOpt.get()));
                        });

                return sortByTimestampDesc ? sortResultByTimestampDesc(result) : result;
              });
    }

    return Future.call(
            ratingAccess::getThumbRatings,
            ust.rating.v1.GetThumbRatingsRequest.newBuilder()
                .addAllVideos(Arrays.stream(videoIds).map(mapVideo).toList())
                .setVisitor(visitorForUstCall)
                .build())
        .thenApply(
            ustResp -> {
              final Map<Integer, APIThumbRatingTimestamped> result = new LinkedHashMap<>();

              ustResp
                  .getThumbRatingsMap()
                  .forEach(
                      (videoId, rating) -> {
                        final Optional<APIThumbRating> apiRatingOpt =
                            convertUstRating(rating.getRating().getRating());
                        if (apiRatingOpt.isEmpty()) return;
                        result.put(
                            videoId,
                            new APIThumbRatingTimestamped(
                                Timestamps.toMillis(rating.getRating().getTimestamp()),
                                apiRatingOpt.get()));
                      });

              return sortByTimestampDesc ? sortResultByTimestampDesc(result) : result;
            });
  }

  private static Map<Integer, APIThumbRatingTimestamped> sortResultByTimestampDesc(
      final Map<Integer, APIThumbRatingTimestamped> ratingsMap) {
    return ratingsMap.entrySet().stream()
        .sorted(
            Map.Entry.comparingByValue((v1, v2) -> Long.compare(v2.timestamp(), v1.timestamp())))
        .collect(
            Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                // can't have the same timestamp for two rated videos, in practice
                (u, v) -> u,
                LinkedHashMap::new));
  }

  /**
   * Get thumb summary for specified videos.
   *
   * @param summaryType "Thumbs plus stars" summary type not supported by UST (legacy)
   */
  private Observable<Map<Integer, APIThumbRatingSummaryData>> getThumbSummaryUsingUST(
      final Set<Integer> videoIds, final APIThumbSummaryType summaryType) {
    Preconditions.checkNotNull(summaryType, "summary type cannot be null");
    Preconditions.checkNotNull(videoIds, "video ids cannot be null");

    return RxObservable.defer(
            ratingAccess::getThumbRatingSummary,
            GetThumbRatingSummaryRequest.newBuilder()
                .addAllVideos(videoIds.stream().map(mapVideo).toList())
                .setSummaryType(mapSummaryTypeUst.apply(summaryType))
                .build())
        .map(
            response -> {
              final Map<Integer, APIThumbRatingSummaryData> results = new HashMap<>();

              response
                  .getThumbSummaryMap()
                  .forEach(
                      (videoId, thumbSummary) ->
                          results.put(
                              videoId,
                              new APIThumbRatingSummaryData(
                                  new APIThumbRatingSummary(
                                      ThumbRatingSummary.newBuilder()
                                          .setCountThumbsUp(thumbSummary.getCountThumbsup())
                                          .setCountThumbsDown(thumbSummary.getCountThumbsdown())
                                          .setCountTotal(thumbSummary.getCountTotal())
                                          .setPercentThumbsUp(thumbSummary.getPercentThumbsup())
                                          .setPercentThumbsDown(thumbSummary.getPercentThumbsdown())
                                          .build()),
                                  response.getTtl())));
              return results;
            });
  }
}
