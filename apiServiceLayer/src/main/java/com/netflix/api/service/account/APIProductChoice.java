package com.netflix.api.service.account;

public interface APIProductChoice {
  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.price"
   */
  Double getPrice();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.price_formatted"
   */
  String getPriceFormatted();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.currency"
   */
  String getCurrency();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.country"
   */
  String getCountry();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.price_tier"
   */
  String getPriceTier();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_id"
   */
  Long getPlanId();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.additional_amount_over_current"
   */
  Double getAdditionalAmountOverCurrent();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.additional_amount_over_current_formatted"
   */
  String getAdditionalAmountOverCurrentFormatted();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_change_effective_date"
   */
  Long getPlanChangeEffectiveDate();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.max_streams"
   */
  Integer getMaxStreams();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_name"
   */
  String getPlanName();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_desc"
   */
  String getPlanDesc();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_desc_short"
   */
  String getPlanDescShort();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.new_billing_date"
   */
  Long getNewBillingDate();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.revoke_uou_offer"
   */
  Boolean getRevokeUouOffer();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_status"
   */
  APIPlanStatus getPlanStatus();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_change_type"
   */
  APIPlanChangeType getPlanChangeType();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.gf_state"
   */
  APIGrandfatheredState getGfState();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.quality"
   */
  APIStreamQuality getQuality();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.price_duration"
   */
  APIPriceDuration getPriceDuration();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.is_new_billing_date_approximate"
   */
  Boolean getIsNewBillingDateApproximate();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.price_consent"
   */
  APIPriceConsentInfo getPriceConsent();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_max_download_devices"
   */
  Integer getPlanMaxDownloadDevices();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#ProductChoice.plan_has_ads"
   */
  Boolean getPlanHasAds();
}
