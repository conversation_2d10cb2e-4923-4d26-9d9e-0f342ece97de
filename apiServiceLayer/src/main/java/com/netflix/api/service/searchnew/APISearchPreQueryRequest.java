package com.netflix.api.service.searchnew;

import java.util.Set;

/** Request for a search list (pass in the search term and listType, e.g., titles) */
public class APISearchPreQueryRequest extends APISearchRequest {

  private final APISearchConstants.SearchListType listType;

  public APISearchPreQueryRequest(
      APISearchConstants.SearchListType listType, Set<String> itemTypes) {
    this.listType = listType;
    if (itemTypes != null && !itemTypes.isEmpty()) {
      String itemTypesStr = String.join(",", itemTypes);
      super.withOtherParam("supportedItemTypes", itemTypesStr);
    }
  }

  public APISearchPreQueryRequest() {
    this.listType = APISearchConstants.SearchListType.empty;
  }

  public APISearchConstants.SearchListType getListType() {
    return listType;
  }
}
