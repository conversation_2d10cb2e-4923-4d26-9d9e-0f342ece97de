package com.netflix.api.service.interactive;

public class APIPlaybackImpressionDataImpl implements APIPlaybackImpressionData {
  protected String impressionId;

  @Override
  public String getImpressionId() {
    return impressionId;
  }

  @Override
  public APIPlaybackImpressionData setImpressionId(String impressionId) {
    this.impressionId = impressionId;
    return this;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((impressionId == null) ? 0 : impressionId.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    APIPlaybackImpressionDataImpl other = (APIPlaybackImpressionDataImpl) obj;
    if (impressionId == null) {
      if (other.impressionId != null) return false;
    } else if (!impressionId.equals(other.impressionId)) return false;
    return true;
  }

  @Override
  public String toString() {
    return "APIPlaybackImpressionDataImpl [impressionId=" + impressionId + "]";
  }
}
