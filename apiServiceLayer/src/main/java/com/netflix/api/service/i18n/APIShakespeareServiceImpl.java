package com.netflix.api.service.i18n;

import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.grpc.GrpcCallHelpers.RxSingle;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.abtest.ABAdapterBase;
import com.netflix.api.service.identity.APIMembershipStatus;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.archaius.api.Property;
import com.netflix.dloc.protogen.Data;
import com.netflix.dloc.protogen.DlocServiceGrpc.DlocServiceStub;
import com.netflix.dloc.protogen.GetAllTestCopiesByABTestIDRequest;
import com.netflix.dloc.protogen.GetAllTestCopiesObelixRequest;
import com.netflix.dloc.protogen.GetBatchedTestCopiesObelixRequest;
import com.netflix.dloc.protogen.GetTestCopyObelixRequest;
import com.netflix.dloc.protogen.GetTestCopyRequest;
import com.netflix.dloc.protogen.MapData;
import com.netflix.dloc.protogen.TestCopyContext;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Registry;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Single;

@Component
@EnableDeprecatedMetrics
public class APIShakespeareServiceImpl implements APIShakespeareService {

  private static final Property<Boolean> DISABLE_TEST =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.shakespeare.testcopyobelix.disable", Boolean.class)
          .orElse(true);

  private final Counter counter;
  private final DlocServiceStub dlocServiceStub;
  private final ABAdapterBase ab;

  @Autowired
  public APIShakespeareServiceImpl(
      @GrpcSpringClient("dloc") DlocServiceStub dlocServiceStub,
      Registry registry,
      ABAdapterBase ab) {
    this.dlocServiceStub = dlocServiceStub.withWaitForReady();
    this.ab = ab;
    this.counter = registry.counter("api.shakespeare.testcopyobelix.call");
  }

  private static Optional<TestCopyContext.Builder> getContext(APIShakespeareRequest request) {
    final TestCopyContext.Builder builder = TestCopyContext.newBuilder();
    // INTL-234: Some non-member users (those have either NEVER_MEMBER, NON_REGISTERED_MEMBER
    // or FORMER_MEMBER status) do have profileID created for them.
    // INTL-235: Support both profile and VDID in same context
    if (isVdid(request)) {
      final String vdid =
          request
              .getVdid()
              .orElse(APIRequest.getCurrentRequest().getRequestContext().getVisitorDeviceId());
      if (vdid == null) {
        return Optional.empty();
      }
      builder.setVdid(vdid);
    }

    if (request.getUser() != null) {
      builder.setProfileId(APIUserUtil.getCustomerId(request.getUser()));
    }

    // use request language or profile language, if neither is present return an empty optional
    if (request.getLanguage() != null) {
      builder.setLanguage(request.getLanguage());
    } else if (request.getUser() != null) {
      builder.setLanguage(request.getUser().getLanguage());
    } else {
      return Optional.empty();
    }

    return Optional.of(builder);
  }

  private static boolean isVdid(APIShakespeareRequest request) {
    return request.getUser() == null
        || request.getUser().getMembershipStatus() == APIMembershipStatus.NEVER_MEMBER
        || request.getUser().getMembershipStatus() == APIMembershipStatus.NON_REGISTERED_MEMBER
        || request.getUser().getMembershipStatus() == APIMembershipStatus.FORMER_MEMBER;
  }

  @Override
  @Deprecated
  public Observable<String> getTestCopy(String messageID, APIShakespeareRequest request) {
    if (request == null || messageID == null) {
      return Observable.error(new IllegalArgumentException("Must provide request"));
    }
    try {
      return getContext(request)
          .map(
              builder ->
                  RxObservable.defer(
                          dlocServiceStub::getTestCopy,
                          GetTestCopyRequest.newBuilder()
                              .setMessageId(messageID)
                              .setContext(builder)
                              .build())
                      .map(Data::getValue))
          .orElse(Observable.error(new IllegalArgumentException("Must provide language and vdid")));
    } catch (Exception e) {
      return Observable.error(e);
    }
  }

  @Override
  @Deprecated
  public Observable<String> getTestCopyObelix(
      String namespace, String bundle, String key, APIShakespeareRequest request) {
    return getTestCopyObelix(namespace, bundle, key, request, false);
  }

  @Deprecated
  public Observable<String> getTestCopyObelix(
      String namespace,
      String bundle,
      String key,
      APIShakespeareRequest request,
      Boolean keysAsValues) {
    counter.increment();
    if (namespace == null || bundle == null || key == null || request == null) {
      return Observable.error(new IllegalArgumentException("Must provide request"));
    }
    if (DISABLE_TEST.get()) {
      return Observable.empty();
    }
    try {
      return getContext(request)
          .map(
              builder ->
                  RxObservable.defer(
                          dlocServiceStub::getTestCopyObelix,
                          GetTestCopyObelixRequest.newBuilder()
                              .setNamespace(namespace)
                              .setBundle(bundle)
                              .setKey(key)
                              .setContext(builder)
                              .setKeysAsValues(keysAsValues != null && keysAsValues)
                              .build())
                      .map(Data::getValue))
          .orElse(Observable.error(new IllegalArgumentException("Must provide language and vdid")));
    } catch (Exception e) {
      return Observable.error(e);
    }
  }

  @Override
  public Single<Map<String, String>> getAllTestCopiesObelix(
      String namespace, String bundle, APIShakespeareRequest request) {
    return getAllTestCopiesObelix(namespace, bundle, request, false);
  }

  public Single<Map<String, String>> getAllTestCopiesObelix(
      String namespace, String bundle, APIShakespeareRequest request, Boolean keysAsValues) {
    if (namespace == null || bundle == null || request == null) {
      return Single.error(new IllegalArgumentException("Must provide request"));
    }
    try {
      return getContext(request)
          .map(
              shakespeareContext ->
                  ab.experimentationContext()
                      .flatMap(
                          allocs -> {
                            // calling ab will populate microcontext, remove once we universally
                            // resolve microcontext in a filter or upstream
                            final GetAllTestCopiesObelixRequest.Builder
                                getAllTestCopiesObelixRequest =
                                    GetAllTestCopiesObelixRequest.newBuilder()
                                        .setNamespace(namespace)
                                        .setBundle(bundle)
                                        .setContext(shakespeareContext)
                                        .setKeysAsValues(keysAsValues != null && keysAsValues);
                            return RxSingle.defer(
                                    dlocServiceStub::getAllTestCopiesObelix,
                                    getAllTestCopiesObelixRequest.build())
                                .map(MapData::getValuesMap);
                          }))
          .orElse(Single.error(new IllegalArgumentException("Must provide language and vdid")));
    } catch (Exception e) {
      return Single.error(e);
    }
  }

  @Override
  public Single<Map<String, Map<String, String>>> getBatchedTestCopiesObelix(
      String namespace, Collection<String> bundles, APIShakespeareRequest request) {
    return getBatchedTestCopiesObelix(namespace, bundles, request, false);
  }

  public Single<Map<String, Map<String, String>>> getBatchedTestCopiesObelix(
      String namespace,
      Collection<String> bundles,
      APIShakespeareRequest shakespeareRequest,
      Boolean keysAsValues) {
    if (namespace == null || bundles == null || shakespeareRequest == null) {
      return Single.error(new IllegalArgumentException("Must provide shakespeareRequest"));
    }
    try {
      return getContext(shakespeareRequest)
          .map(
              builder ->
                  ab.experimentationContext()
                      .flatMap(
                          allocs ->
                              RxSingle.defer(
                                      dlocServiceStub::getBatchedTestCopiesObelix,
                                      GetBatchedTestCopiesObelixRequest.newBuilder()
                                          .setNamespace(namespace)
                                          .addAllBundle(bundles)
                                          .setContext(builder)
                                          .setKeysAsValues(keysAsValues != null && keysAsValues)
                                          .build())
                                  .map(
                                      mapMap -> {
                                        var valuesMap = mapMap.getValuesMap();
                                        Map<String, Map<String, String>> map =
                                            HashMap.newHashMap(valuesMap.size());
                                        valuesMap.forEach(
                                            (key, value) -> map.put(key, value.getValuesMap()));
                                        return map;
                                      })))
          .orElse(Single.error(new IllegalArgumentException("Must provide language and vdid")));
    } catch (Exception e) {
      return Single.error(e);
    }
  }

  @Override
  public Single<Map<String, String>> getAllTestCopiesByABTestIds(
      Collection<Integer> testIds, APIShakespeareRequest request) {
    if (testIds == null || testIds.isEmpty() || request == null) {
      return Single.error(new IllegalArgumentException("Must provide test ids and request"));
    }
    try {
      return getContext(request)
          .map(
              builder ->
                  ab.experimentationContext()
                      .flatMap(
                          allocs ->
                              RxSingle.defer(
                                      dlocServiceStub::getAllTestCopiesByABTestID,
                                      GetAllTestCopiesByABTestIDRequest.newBuilder()
                                          .addAllTestIds(testIds)
                                          .setContext(builder)
                                          .setKeysAsValues(request.isKeysAsValues())
                                          .build())
                                  .map(MapData::getValuesMap)))
          .orElse(Single.error(new IllegalArgumentException("Must provide language and vdid")));
    } catch (Exception e) {
      return Single.error(e);
    }
  }
}
