package com.netflix.api.service.video;

import com.netflix.api.util.Localize;
import com.netflix.content.util.ContentResourceIDLookup;
import com.netflix.videometadata.l10n.VMSL10NProviderDelegate;

public class LocalizedRatingReason {
  private static final String REASON_DESCRIPTION_SEPARATOR = ":";

  private final String description;
  private final String level;

  private LocalizedRatingReason(String description, String level) {
    this.description = description;
    this.level = level;
  }

  private LocalizedRatingReason(String description) {
    this(description, null);
  }

  public String getDescription() {
    return description;
  }

  public String getLevel() {
    return level;
  }

  public static LocalizedRatingReason getInstance(
      VMSL10NProviderDelegate vmsl10NProviderDelegate, Integer reasonId, String defaultReason) {
    String reasonString =
        Localize.localize(
            vmsl10NProviderDelegate,
            ContentResourceIDLookup.getCertificationDescriptionID(reasonId),
            defaultReason);
    return from(reasonString);
  }

  static LocalizedRatingReason from(String reasonString) {
    if (reasonString == null) {
      return null;
    }
    // Violence: High -> [Violence, High]
    // Violence -> [Violence, null]
    String[] tokens = reasonString.split(REASON_DESCRIPTION_SEPARATOR, 2);
    if (tokens.length == 1) {
      return new LocalizedRatingReason(tokens[0].trim());
    }
    return new LocalizedRatingReason(tokens[0].trim(), tokens[1].trim());
  }
}
