package com.netflix.api.adapters;

import com.netflix.api.service.identity.APIParentalControlMaturity;
import com.netflix.api.service.identity.APIParentalControlMaturityImpl;
import com.netflix.api.service.identity.APIProfileMaturity;
import com.netflix.api.service.ratings.MaturityRatingsDataFetcher;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MaturityRatingsAdapter {
  private final MaturityRatingsDataFetcher maturityRatingsDataFetcher;

  @Autowired
  public MaturityRatingsAdapter(MaturityRatingsDataFetcher maturityRatingsDataFetcher) {
    this.maturityRatingsDataFetcher = maturityRatingsDataFetcher;
  }

  public Optional<APIProfileMaturity> getAPIProfileMaturityRating(
      Integer maturity, String country) {
    return getProfileMaturity(maturity, country);
  }

  public List<APIParentalControlMaturity> getParentalControlMaturityRatings(String country) {
    Map<Long, APIInternalMaturity> ratings =
        maturityRatingsDataFetcher.getRatingForCountry(country);
    List<APIParentalControlMaturity> apiParentalControlMaturities = new ArrayList<>();
    ratings.forEach(
        (key, value) -> {
          if (includeRating(Boolean.TRUE, value)) {
            if (maturityRatingsDataFetcher.getMaturityLabel(key).isPresent()
                && maturityRatingsDataFetcher.getMaturityDesc(key).isPresent()) {
              APIParentalControlMaturityImpl apiParentalControlMaturity =
                  new APIParentalControlMaturityImpl(
                      value.maturityLevel(),
                      maturityRatingsDataFetcher.getMaturityLabel(key).get(),
                      maturityRatingsDataFetcher.getMaturityDesc(key).get());
              apiParentalControlMaturities.add(apiParentalControlMaturity);
            }
          }
        });
    apiParentalControlMaturities.sort(
        Comparator.comparingInt(APIParentalControlMaturity::getMaturityValue));
    return apiParentalControlMaturities;
  }

  private Map<Long, Integer> getMaturityRatings(String country) {
    Map<Long, APIInternalMaturity> ratings =
        maturityRatingsDataFetcher.getRatingForCountry(country);
    // filtering ratings without labels / desc.
    return ratings.entrySet().stream()
        .filter(val -> includeRating(true, val.getValue()))
        .filter(
            val ->
                maturityRatingsDataFetcher.getMaturityLabel(val.getKey()).isPresent()
                    && maturityRatingsDataFetcher.getMaturityDesc(val.getKey()).isPresent())
        .collect(Collectors.toMap(Entry::getKey, entry -> entry.getValue().maturityLevel()));
  }

  private boolean includeRating(Boolean filterForParentalControls, APIInternalMaturity val) {
    return !filterForParentalControls
        || val.excludeFromParentalControls() == null
        || !val.excludeFromParentalControls();
  }

  private record Rating(Long id, Integer value) {}

  private Optional<APIProfileMaturity> getProfileMaturity(Integer maturityLevel, String country) {
    if (maturityLevel == null || country == null) {
      return Optional.empty();
    }
    Map<Long, Integer> maturityRatings = getMaturityRatings(country);
    if (maturityRatings.isEmpty()) {
      return Optional.empty();
    }
    Map<Long, Integer> sortedMap =
        maturityRatings.entrySet().stream()
            .filter(entry -> entry.getValue() != null)
            .sorted(Map.Entry.comparingByValue())
            .collect(
                Collectors.toMap(
                    Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
    Rating minimum = null;
    Rating maximum = null;
    List<Long> ratingsResult = new ArrayList<>();
    for (Entry<Long, Integer> entry : sortedMap.entrySet()) {
      Long ratingId = entry.getKey();
      Integer ratingValue = entry.getValue();
      if (minimum == null) {
        minimum = new Rating(ratingId, ratingValue);
      }
      // If the rating is below the maturity level input, then this is a candidate
      // we want to find the highest rating which is below the input maturity
      if (ratingValue <= maturityLevel) {
        // discard prior results if we find a higher rating value below input maturity
        // do not discard for case where current rating ==  prev highest rating  when there are
        // multiple labels (ex: G,TV-G ) with same value
        if (maximum != null && ratingValue > maximum.value) {
          ratingsResult.clear();
        }
        ratingsResult.add(ratingId);
      }
      maximum = new Rating(ratingId, ratingValue);
    }
    // case where the input rating is less than the lowest rating available
    if (ratingsResult.isEmpty() && minimum != null && minimum.value > maturityLevel) {
      ratingsResult.add(minimum.id);
    }
    APIProfileMaturity result = new APIProfileMaturity();
    for (Long rating : ratingsResult) {
      Integer ratingValue = sortedMap.get(rating);
      if (ratingValue.equals(minimum.value)) {
        result.setLowestMaturity(Boolean.TRUE);
      } else {
        result.setLowestMaturity(Boolean.FALSE);
      }
      if (ratingValue.equals(maximum.value)) {
        result.setHighestMaturity(Boolean.TRUE);
      } else {
        result.setHighestMaturity(Boolean.FALSE);
      }
      // should not ever be nul since null rating labels  are filtered in getMaturityRatings()
      maturityRatingsDataFetcher.getMaturityLabel(rating).ifPresent(result::addMaturityLabel);
      result.setMaturityValue(ratingValue);
    }
    return Optional.of(result);
  }
}
