package com.netflix.api.dependencies.device;

import com.google.inject.AbstractModule;
import com.netflix.streaming.dts.client.DtsClientModule;

public class DeviceModule extends AbstractModule {
  @Override
  protected void configure() {
    install(new DtsClientModule());
  }

  public boolean equals(Object obj) {
    return obj != null && getClass().equals(obj.getClass());
  }

  @Override
  public int hashCode() {
    return getClass().hashCode();
  }
}
