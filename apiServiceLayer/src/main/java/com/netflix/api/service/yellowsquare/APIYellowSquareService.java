package com.netflix.api.service.yellowsquare;

import com.netflix.api.service.identity.APIUser;
import java.util.List;
import java.util.Map;
import rx.Observable;

/**
 * A service interface to the Yellow Square service.
 *
 * @see <a href="https://confluence.netflix.com/display/MEM/Yellow+Square">Yellow Square</a>
 */
public interface APIYellowSquareService {

  /**
   * Create multiple tags for a customer with the passed in value, or update a previously existing
   * tag with the values passed.
   *
   * @param namespace Needs to be defined in the Yellow Square service, used to segment the tags
   * @param key Used to further segment the tags within the namespace
   * @param tags A map containing the name value pairs of tags to be created
   * @param ttl in seconds; if no value is supplied as a parameter then defer to the TTL from
   *     tagDefinition. If both places' TTLs are null then the tag will never expire.
   * @return an empty Observable, if there is an error <code>onError</code> will be invoked,
   *     otherwise assume success
   */
  Observable<Void> setTag(String namespace, String key, Map<String, String> tags, Integer ttl);

  /**
   * Create multiple tags for a customer with the passed in value, or update a previously existing
   * tag with the values passed.
   *
   * @param namespace Needs to be defined in the Yellow Square service, used to segment the tags
   * @param user The user for which tags are created. Edge will extract the customer id or the
   *     account owner id from it and send it to the Yellow Square service. Use this method as a
   *     replacement for sending a String key containing the customer id or the account owner id,
   *     which are no longer available in Edge. Continue to use the signature with a String key if
   *     its content is not the customer id or the account owner id.
   * @param useAccountOwnerAsKey if false, Edge will will extract the customer id from user; if
   *     true, Edge will extract the account owner id
   * @param tags A map containing the name value pairs of tags to be created
   * @param ttl in seconds; if no value is supplied as a parameter then defer to the TTL from
   *     tagDefinition. If both places' TTLs are null then the tag will never expire.
   * @return an empty Observable, if there is an error <code>onError</code> will be invoked,
   *     otherwise assume success
   */
  Observable<Void> setTag(
      String namespace,
      APIUser user,
      boolean useAccountOwnerAsKey,
      Map<String, String> tags,
      Integer ttl);

  /**
   * Get unexpired tags
   *
   * @return an Observable of tags; if no tags are present an empty Observable will be returned, all
   *     errors are passed back through onError for security reasons edge replaces all occurrences
   *     of customer id within tags with the customer guid
   */
  Observable<APIYellowSquareTag> getTags(String namespace, String key, List<String> names);

  /**
   * Get unexpired tags
   *
   * @param namespace as in the namespace
   * @param user The user for which tags are retrieved. Edge will extract the customer id or the
   *     account owner id from it and send it to the Yellow Square service. Use this method as a
   *     replacement for sending a String key containing the customer id or the account owner id, as
   *     these are no longer available in Edge. Continue to use the signature with a String key if
   *     its content is not the customer id or the account owner id.
   * @param useAccountOwnerAsKey if false, Edge will will extract the customer id from user; if
   *     true, Edge will extract the account owner id
   * @param names of tags to retrieve
   * @return an Observable of tags; if no tags are present an empty Observable will be returned, all
   *     errors are passed back through onError for security reasons edge replaces all occurrences
   *     of customer id within tags with the customer guid
   */
  Observable<APIYellowSquareTag> getTags(
      String namespace, APIUser user, boolean useAccountOwnerAsKey, List<String> names);
}
