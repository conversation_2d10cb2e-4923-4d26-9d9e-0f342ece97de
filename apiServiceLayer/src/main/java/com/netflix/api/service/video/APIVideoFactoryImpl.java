package com.netflix.api.service.video;

import com.netflix.api.dependencies.device.DeviceTypeFactory;
import com.netflix.api.facade.service.LookupContext;
import com.netflix.api.facade.service.VideoLookup;
import com.netflix.api.facade.service.collection.CollectionLookup;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.api.service.obelix.APIObelixService;
import com.netflix.archaius.api.Property;
import com.netflix.lang.RequestVariable;
import com.netflix.type.Video;
import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.videometadata.type.SupplementalVideo;
import com.netflix.vms.model.ContainerData;
import com.netflix.vms.provider.VideoFormatDescriptorProvider;
import jakarta.inject.Provider;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class APIVideoFactoryImpl implements APIVideoFactory {
  private static final Logger logger = LoggerFactory.getLogger(APIVideoFactoryImpl.class);

  private final Property<Boolean> enableTitlegroupContainer;

  final DeviceTypeFactory deviceTypeFactory;
  final Provider<APIObelixService> obelixServiceProvider;
  final VideoLookup videoLookup;
  final CollectionLookup collectionLookup;
  final USTVideoMetadataAdapter videoMetadataAdapter;
  final VideoFormatDescriptorProvider videoFormatDescriptorProvider;

  // a request scoped cache of APICachedVideoImpl's -- in many cases, clients get APIVideo object,
  // get its reference (id) dump the object on the floor only to ask for it again. Assumption is
  // that Edge layer memo-izes APIVideo instances. Memo-ize it here.
  private static final RequestVariable<ConcurrentMap<Integer, APICachedVideoImpl>>
      requestScopedVideos =
          new RequestVariable<>() {
            @Override
            public ConcurrentMap<Integer, APICachedVideoImpl> initialValue() {
              return new ConcurrentHashMap<>();
            }
          };

  @Autowired
  public APIVideoFactoryImpl(
      com.netflix.archaius.api.PropertyRepository pr,
      DeviceTypeFactory deviceTypeFactory,
      Provider<APIObelixService> apiObelixServiceProvider,
      VideoLookup videoLookup,
      CollectionLookup collectionLookup,
      USTVideoMetadataAdapter videoMetadataAdapter,
      VideoFormatDescriptorProvider videoFormatDescriptorProvider) {
    this.obelixServiceProvider = apiObelixServiceProvider;
    this.deviceTypeFactory = deviceTypeFactory;
    this.videoLookup = videoLookup;
    this.collectionLookup = collectionLookup;
    this.videoMetadataAdapter = videoMetadataAdapter;
    this.videoFormatDescriptorProvider = videoFormatDescriptorProvider;

    enableTitlegroupContainer =
        pr.get("com.netflix.api.videofactory.enable_container", Boolean.class).orElse(false);
  }

  private APICachedVideoImpl getCachedInstance(final CompleteVideo video) {
    return getCachedInstance(video, videoId -> createAPIVideo(video));
  }

  public VideoLookup getVideoLookup() {
    return videoLookup;
  }

  public CollectionLookup getCollectionLookup() {
    return collectionLookup;
  }

  static void checkCountryStatus() {
    GeoStatusHelper.checkCountryStatus();
  }

  @Deprecated
  public APICachedVideoImpl getInstance(int videoId) {
    var map = videoLookup.getVideos(Set.of(videoId));
    return getCachedInstance(map.get(videoId).orElse(null));
  }

  @Deprecated
  public APICachedVideoImpl getInstance(Video video) {
    var id = video.getId();
    var map = videoLookup.getVideos(Set.of(id));
    return getCachedInstance(map.get(id).orElse(null));
  }

  public Map<Integer, Optional<APIVideo>> getInstances(Collection<Integer> videoIds) {
    final Map<Integer, Optional<CompleteVideo>> videos = videoLookup.getVideos(videoIds);
    final Map<Integer, Optional<APIVideo>> results = HashMap.newHashMap(videos.size());
    for (Entry<Integer, Optional<CompleteVideo>> entry : videos.entrySet()) {
      results.put(entry.getKey(), entry.getValue().map(this::getCachedInstance));
    }
    return results;
  }

  private CompletionStage<Map<Integer, Optional<APIVideo>>> getInstancesAsync(
      Collection<Integer> videoIds) {
    LookupContext context =
        LookupContext.builder().setCountry(RequestContextWrapper.get().getCountry()).build();
    CompletableFuture<Map<Integer, Optional<CompleteVideo>>> videosFuture =
        videoLookup.getVideosAsync(videoIds, context);
    return videosFuture.thenApply(
        videos -> {
          // need to preserve order from original collection
          final Map<Integer, Optional<APIVideo>> results = HashMap.newHashMap(videoIds.size());
          for (Integer videoId : videoIds) {
            if (videos.containsKey(videoId)) {
              Optional<CompleteVideo> video = videos.get(videoId);
              if (video.isPresent()) {
                results.put(videoId, videos.get(videoId).map(this::getCachedInstance));
              }
            } else {
              // should never happen
              logger.warn("videoId={} not returned by lookup", videoId);
            }
          }
          return results;
        });
  }

  /**
   * If a video cannot be looked up it will not be present in the response collection. Order of
   * inputs is preserved.
   */
  public Collection<APIVideo> getInstancesCollection(Collection<Integer> videoIds) {
    if (videoIds == null) {
      return List.of();
    }
    return getApiVideos(videoIds, getInstances(videoIds));
  }

  @NonNull
  private Collection<APIVideo> getApiVideos(
      Collection<Integer> videoIds, Map<Integer, Optional<APIVideo>> map) {
    Collection<APIVideo> videos = new ArrayList<>(map.size());
    for (Integer videoId : videoIds) {
      var video = map.getOrDefault(videoId, Optional.empty());
      video.ifPresent(videos::add);
    }
    return videos;
  }

  /**
   * If a video cannot be looked up it will not be present in the response collection. Order of
   * inputs is preserved.
   */
  public CompletionStage<Collection<APIVideo>> getInstancesCollectionAsync(
      Collection<Integer> videoIds) {
    if (videoIds == null || videoIds.isEmpty()) {
      return CompletableFuture.completedFuture(List.of());
    }
    return getInstancesAsync(videoIds).thenApply(map -> getApiVideos(videoIds, map));
  }

  private APICachedVideoImpl createAPIVideo(CompleteVideo completeVideo) {
    APICachedVideoImpl result = null;
    // Do country check first and fail fast if needed;
    checkCountryStatus();

    if (completeVideo.isShow()) {
      result =
          new APICachedShowImpl(
              completeVideo, this, videoMetadataAdapter, videoFormatDescriptorProvider);
    } else if (completeVideo.isSupplementalVideo()) {
      SupplementalVideo sv = videoLookup.getSupplementalVideo(completeVideo.getId());
      if (sv != null) {
        result =
            new APICachedSupplementalVideoImpl(
                sv, completeVideo, this, videoMetadataAdapter, videoFormatDescriptorProvider);
      }
    } else if (completeVideo.isMovie()) {
      result =
          new APICachedViewableImpl(
              completeVideo, this, videoMetadataAdapter, videoFormatDescriptorProvider);
    } else if (completeVideo.isEpisode()) {
      result =
          new APICachedEpisodeImpl(
              completeVideo, this, videoMetadataAdapter, videoFormatDescriptorProvider);
    } else if (completeVideo.isEpisodeBatch()) {
      result =
          new APICachedEpisodeBatchImpl(
              completeVideo, this, videoMetadataAdapter, videoFormatDescriptorProvider);
    } else if (completeVideo.isSeason()) {
      result =
          new APICachedSeasonImpl(
              completeVideo, this, videoMetadataAdapter, videoFormatDescriptorProvider);
    } else if (completeVideo.isTitleGroup()) {
      result = createTitleGroupVideoInstance(completeVideo);
    } else if (completeVideo.getNodeType().isContainer()) {
      result = createCachedContainerVideo(completeVideo);
    }

    if (result == null) {
      throw new APIServiceRuntimeException(
          "Video["
              + completeVideo.getId()
              + "] is not any of APIShow, APISeason, APISupplementalVideo, APIMovie, APIEpisode, APIGenericContainer"
              + (!shouldMasqueradeTitleGroupAsMovie() ? ", APITitleGroup!" : "!"));
    }

    return result;
  }

  private APICachedVideoImpl getCachedInstance(
      final CompleteVideo video, Function<Integer, APICachedVideoImpl> ifAbsent) {
    if (video == null) {
      return null;
    }
    APICachedVideoImpl apiCachedVideo = requestScopedVideos.get().get(video.getId());
    if (apiCachedVideo == null) {
      apiCachedVideo = requestScopedVideos.get().computeIfAbsent(video.getId(), ifAbsent);
    }
    return apiCachedVideo;
  }

  private boolean shouldMasqueradeTitleGroupAsMovie() {
    return !enableTitlegroupContainer.get();
  }

  public APICachedGenericContainerVideoImpl createCachedContainerVideo(CompleteVideo video) {
    ContainerData data = video.getContainerData();
    if (data == null) return null;
    if (data.getContainerType() != ContainerData.ContainerType.GENERIC) {
      return null;
    }
    return new APICachedGenericContainerVideoImpl(
        video, this, videoMetadataAdapter, videoFormatDescriptorProvider);
  }

  private APICachedVideoImpl createTitleGroupVideoInstance(final CompleteVideo completeVideo) {
    if (shouldMasqueradeTitleGroupAsMovie()) {
      return new APICachedTitleGroupViewableImpl(
          completeVideo, this, videoMetadataAdapter, videoFormatDescriptorProvider);
    } else {
      return new APICachedTitleGroupVideoImpl(
          completeVideo, this, videoMetadataAdapter, videoFormatDescriptorProvider);
    }
  }
}
