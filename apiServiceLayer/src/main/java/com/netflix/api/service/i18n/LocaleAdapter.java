package com.netflix.api.service.i18n;

import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIMembershipStatus;
import com.netflix.api.service.identity.APIUser;
import com.netflix.i18n.NFLanguageScriptRegistry;
import com.netflix.i18n.NFLocale;
import com.netflix.type.ISOCountry;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class LocaleAdapter {

  public List<NFLocale> getLocalesForScripts(Collection<String> scriptNames) {
    if (scriptNames != null && !scriptNames.isEmpty()) {
      return NFLanguageScriptRegistry.getLocales(scriptNames);
    } else {
      return Collections.emptyList();
    }
  }

  public List<NFLocale> getMatchingLocales(
      List<String> devicePreferredLanguages,
      List<String> deviceSupportedLanguages,
      boolean useNRMLanguagePreferences) {

    // determine user language preferences
    List<String> userPreferredLanguages = getUserPreferredLanguages(useNRMLanguagePreferences);

    return getMatchingLocales(
        devicePreferredLanguages, deviceSupportedLanguages, userPreferredLanguages);
  }

  public List<NFLocale> getMatchingLocales(
      List<String> devicePreferredLanguages,
      List<String> deviceSupportedLanguages,
      List<String> userPreferredLanguages) {

    // determine country
    ISOCountry country = RequestContextWrapper.get().getCountry();

    return getMatchingLocales(
        userPreferredLanguages, devicePreferredLanguages, deviceSupportedLanguages, country);
  }

  private List<String> getUserPreferredLanguages(boolean useNRMLanguagePreferences) {
    // determine user preferred languages
    List<String> userPreferredLanguages = null;
    APIUser user = APIRequest.getCurrentRequest().getUser();
    if (user != null && user.getLanguage() != null) {
      // for NRM users, use language preferences only if clients pass an explicit flag to do so.
      if (useNRMLanguagePreferences
          || !APIMembershipStatus.NON_REGISTERED_MEMBER.equals(user.getMembershipStatus())) {
        userPreferredLanguages = Collections.singletonList(user.getLanguage());
      }
    }
    return userPreferredLanguages;
  }

  private List<NFLocale> getMatchingLocales(
      List<String> userPreferredLanguages,
      List<String> devicePreferredLanguages,
      List<String> deviceSupportedLanguages,
      ISOCountry country) {

    List<NFLocale> userPreferredLocales =
        userPreferredLanguages == null
            ? Collections.emptyList()
            : userPreferredLanguages.stream().map(NFLocale::findInstance).toList();

    List<NFLocale> devicePreferredLocales =
        devicePreferredLanguages == null
            ? Collections.emptyList()
            : devicePreferredLanguages.stream().map(NFLocale::findInstance).toList();

    List<NFLocale> deviceSupportedLocales =
        deviceSupportedLanguages == null
            ? Collections.emptyList()
            : deviceSupportedLanguages.stream().map(NFLocale::findInstance).toList();
    return NFLocale.getMatchingLanguages(
        userPreferredLocales, devicePreferredLocales, deviceSupportedLocales, country);
  }
}
