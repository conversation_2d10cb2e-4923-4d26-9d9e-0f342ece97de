package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class TextMessage {
  private String tagline;

  private TextMessageClassification classification;

  /** Date to display on the badge. */
  private String badgeDate;

  /** Prefix for badge text. */
  private String badgePrefix;

  /** Call to action message. */
  private String ctaMessage;

  public String getTagline() {
    return tagline;
  }

  public TextMessageClassification getClassification() {
    return classification;
  }

  public String getBadgeDate() {
    return badgeDate;
  }

  public String getBadgePrefix() {
    return badgePrefix;
  }

  public String getCtaMessage() {
    return ctaMessage;
  }

  public TextMessage setTagline(String tagline) {
    this.tagline = tagline;
    return this;
  }

  public TextMessage setClassification(TextMessageClassification classification) {
    this.classification = classification;
    return this;
  }

  public TextMessage setBadgeDate(String badgeDate) {
    this.badgeDate = badgeDate;
    return this;
  }

  public TextMessage setBadgePrefix(String badgePrefix) {
    this.badgePrefix = badgePrefix;
    return this;
  }

  public TextMessage setCtaMessage(String ctaMessage) {
    this.ctaMessage = ctaMessage;
    return this;
  }
}
