package com.netflix.api.platform.identity;

import com.google.common.annotations.VisibleForTesting;
import com.netflix.api.platform.NetflixESN;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.server.context.RequestContext;
import com.netflix.type.DeviceType;
import com.netflix.type.TypeManager;
import com.netflix.type.Visitor;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;

/** Centralized place to set Identity */
public class RequestScopedIdentityManager {

  private RequestScopedIdentityManager() {}

  private static final Property<Boolean> SET_PASSPORT_IN_REQUEST_CONTEXT =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("eas.api.RequestScopedIdentityManager.setPassportInRequestContext", Boolean.class)
          .orElse(true);

  @VisibleForTesting static final String PASSPORT_REQUEST_CONTEXT_KEY = "Passport";

  public static void setIdentity(
      HttpServletRequest request,
      IdentityResult<?> idResult,
      PassportIdentity identity,
      String visitorDeviceId) {
    setUserIdentity(idResult, identity);
    setDeviceIdentity(request, idResult, identity, visitorDeviceId);
  }

  private static void setUserIdentity(IdentityResult<?> idResult, PassportIdentity passport) {
    CurrentIdentityResult.set(idResult);
    CurrentIdentityResult.setPassport(passport);

    RequestContext requestContext = CurrentRequestContext.get();
    updatePassportInRequestContext(requestContext, passport);
  }

  /**
   * @param request Used to get params and headers that can provide ESN
   * @param idResult
   * @param passport
   * @param visitorDeviceId
   */
  private static void setDeviceIdentity(
      HttpServletRequest request,
      IdentityResult idResult,
      PassportIdentity passport,
      String visitorDeviceId) {
    CurrentIdentityResult.setVisitorDeviceId(visitorDeviceId);

    RequestContext requestContext = CurrentRequestContext.get();
    NetflixESN netflixESN = NetflixESN.create(request, idResult, passport);
    String esn = netflixESN == null ? null : netflixESN.getESN();
    if (null != esn) {
      requestContext.setDeviceId(esn);
    }
    // TODO also set devicetype if idResult is null
    if (idResult != null) {
      Integer deviceTypeInt = idResult.getDeviceType();
      if (null != deviceTypeInt) {
        DeviceType deviceType = TypeManager.findObject(DeviceType.class, deviceTypeInt);
        if (null != deviceType) {
          requestContext.setDeviceType(deviceType);
        }
      }
      Long customerId = idResult.getCustomerId();
      if (null != customerId) {
        Visitor visitor = TypeManager.findObject(Visitor.class, customerId);
        CurrentVisitor.set(visitor);
      }
    }
  }

  @VisibleForTesting
  static void updatePassportInRequestContext(
      RequestContext requestContext, PassportIdentity identity) {
    if (!SET_PASSPORT_IN_REQUEST_CONTEXT.get()) {
      return;
    }

    if (identity == null) {
      return;
    }

    if (!requestContextHasPassport(requestContext)) {
      return;
    }

    requestContext.addContext(PASSPORT_REQUEST_CONTEXT_KEY, identity.getPassportAsString());
  }

  private static boolean requestContextHasPassport(RequestContext requestContext) {
    try {
      String value = requestContext.getContext(PASSPORT_REQUEST_CONTEXT_KEY);
      return StringUtils.isNotBlank(value);
    } catch (ContextSerializationException e) {
      return false;
    }
  }
}
