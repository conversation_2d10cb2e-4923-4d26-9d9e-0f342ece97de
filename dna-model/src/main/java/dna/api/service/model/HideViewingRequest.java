package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class HideViewingRequest {
  private Integer videoId;

  private Boolean allEpisodes;

  public Integer getVideoId() {
    return videoId;
  }

  public Boolean getAllEpisodes() {
    return allEpisodes;
  }

  public HideViewingRequest setVideoId(Integer videoId) {
    this.videoId = videoId;
    return this;
  }

  public HideViewingRequest setAllEpisodes(Boolean allEpisodes) {
    this.allEpisodes = allEpisodes;
    return this;
  }
}
