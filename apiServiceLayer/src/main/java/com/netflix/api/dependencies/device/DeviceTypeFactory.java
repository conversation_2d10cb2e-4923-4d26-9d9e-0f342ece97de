package com.netflix.api.dependencies.device;

import com.netflix.lang.RequestVariable;
import com.netflix.streaming.dts.client.DtsClient;
import com.netflix.streaming.dts.common.UnknownDeviceTypeException;
import com.netflix.streaming.dts.common.model.DeviceType;
import com.netflix.type.TypeManager;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** Factory to look up DeviceType via {@link DeviceTypeRequest} includes request level caching */
@Component
public class DeviceTypeFactory {
  private static final RequestVariable<ConcurrentHashMap<String, DeviceType>> cache =
      new RequestVariable<ConcurrentHashMap<String, DeviceType>>() {
        @Override
        protected ConcurrentHashMap<String, DeviceType> initialValue() {
          return new ConcurrentHashMap<>();
        }
      };

  /*
   * CUPS now needs a device type to retrieve video metadata (like isHd). If deviceType &amp;&amp; ESN do not exist in request scope,
   * use the following device type ID.  I believe that this should likely only get called by the public API.
   */
  public static final Integer SYNTHETIC_DEVICE_TYPE_ID = 921;
  private static final Logger logger = LoggerFactory.getLogger(DeviceTypeFactory.class);
  private final GetDeviceType getDeviceType;

  @Autowired
  public DeviceTypeFactory(DtsClient dtsClient) {
    this.getDeviceType = new ProductionGetDeviceType(dtsClient);
  }

  DeviceTypeFactory(GetDeviceType getDeviceType) {
    this.getDeviceType = getDeviceType;
  }

  public DeviceType get(DeviceTypeRequest request) {
    if (request == null) return null;
    String cacheKey = request.getCacheKey();
    DeviceType deviceType = cache.get().get(cacheKey);
    if (deviceType == null) {
      try {
        deviceType = getDeviceType(request);
        if (deviceType != null) {
          cache.get().put(cacheKey, deviceType);
        }
      } catch (Exception e) {

      }
    }
    return deviceType;
  }

  DeviceType getDeviceType(DeviceTypeRequest request) {
    if (request.isEmpty()) {
      return getDeviceType.getSyntheticDeviceType();
    }
    if (request.getDeviceTypeId() != null) {
      try {
        DeviceType deviceTypeById = getDeviceType.getDeviceTypeFromId(request.getDeviceTypeId());
        if (deviceTypeById != null) {
          return deviceTypeById;
        }
      } catch (UnknownDeviceTypeException ex) {
        // this is recoverable, try other methods of lookup
      }
    }
    if (request.getEsn() != null) {
      try {
        DeviceType deviceTypeByEsn = getDeviceType.getDeviceTypeFromESN(request.getEsn());
        if (deviceTypeByEsn != null) {
          return deviceTypeByEsn;
        }
      } catch (UnknownDeviceTypeException ex) {
        // this is recoverable, try other methods of lookup
      }
    }
    if (request.getEsnPrefix() != null) {
      try {
        DeviceType deviceTypeByEsnPrefix =
            getDeviceType.getDeviceTypeFromESNPrefix(request.getEsnPrefix());
        if (deviceTypeByEsnPrefix != null) {
          return deviceTypeByEsnPrefix;
        }
      } catch (UnknownDeviceTypeException ex) {
        // this is recoverable, try other methods of lookup
      }
    }
    if (request.getConsumerKey() != null) {
      try {
        DeviceType deviceTypeByConsumerKey =
            getDeviceType.getDeviceTypeFromConsumerKey(request.getConsumerKey());
        if (deviceTypeByConsumerKey != null) {
          return deviceTypeByConsumerKey;
        }
      } catch (UnknownDeviceTypeException ex) {
        // this is recoverable, try default lookup
      }
    }
    return null;
  }

  interface GetDeviceType {

    DeviceType getDeviceTypeFromId(Integer deviceTypeId) throws UnknownDeviceTypeException;

    DeviceType getDeviceTypeFromESNPrefix(String esnPrefix) throws UnknownDeviceTypeException;

    DeviceType getDeviceTypeFromESN(String esn) throws UnknownDeviceTypeException;

    DeviceType getDeviceTypeFromConsumerKey(String consumerKey) throws UnknownDeviceTypeException;

    DeviceType getSyntheticDeviceType();
  }

  private static final class ProductionGetDeviceType implements GetDeviceType {
    private final DtsClient dtsClient;

    private ProductionGetDeviceType(DtsClient dtsClient) {
      this.dtsClient = dtsClient;
    }

    @Override
    public DeviceType getDeviceTypeFromId(Integer deviceTypeId) throws UnknownDeviceTypeException {
      return dtsClient.getDeviceTypeFromId(deviceTypeId);
    }

    @Override
    public DeviceType getDeviceTypeFromESNPrefix(String esnPrefix)
        throws UnknownDeviceTypeException {
      return dtsClient.getDeviceTypeFromESNPrefix(esnPrefix);
    }

    @Override
    public DeviceType getDeviceTypeFromESN(String esn) throws UnknownDeviceTypeException {
      return dtsClient.getDeviceTypeFromESN(esn);
    }

    @Override
    public DeviceType getDeviceTypeFromConsumerKey(String consumerKey)
        throws UnknownDeviceTypeException {
      return dtsClient.getDeviceTypeFromESNPrefix(consumerKey);
    }

    @Override
    public DeviceType getSyntheticDeviceType() {
      try {
        com.netflix.type.DeviceType syntheticDeviceType =
            TypeManager.findObject(com.netflix.type.DeviceType.class, SYNTHETIC_DEVICE_TYPE_ID);
        return dtsClient.getDeviceTypeFromId(syntheticDeviceType.getId());
      } catch (UnknownDeviceTypeException ex) {
        logger.error("Default device : " + SYNTHETIC_DEVICE_TYPE_ID + " not found in DTS" + ")");
        return null;
      }
    }
  }
}
