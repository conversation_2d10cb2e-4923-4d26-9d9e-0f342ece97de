package com.netflix.api.service.searchnew;

/** Request for a search list (pass in the search term and listType, e.g., titles) */
public class APISearchRequestByQuery extends APISearchRequest {
  private final String term;
  private final APISearchConstants.SearchListType listType;

  public APISearchRequestByQuery(String term, APISearchConstants.SearchListType listType) {
    this.term = term;
    this.listType = listType;
  }

  public String getTerm() {
    return term;
  }

  public APISearchConstants.SearchListType getListType() {
    return listType;
  }
}
