package dna.api.converters;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.netflix.nes.page.descriptor.protogen.AssistiveAudioPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.GalleryPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.PageDescriptor;
import com.netflix.nes.section.descriptor.protogen.SectionDescriptor;
import com.netflix.nes.section.descriptor.protogen.SimsSectionDescriptor;
import dna.api.converters.nes.NesPageDescriptorInputConverter;
import dna.api.service.model.NesGenreInput;
import dna.api.service.model.NesPageDescriptorInput;
import dna.api.service.model.NesPageDescriptorQualifierUnion;
import dna.api.service.model.NesPageType;
import dna.api.service.model.NesSectionDescriptorInput;
import dna.api.service.model.NesSectionDescriptorQualifierUnion;
import dna.api.service.model.NesSectionType;
import dna.api.service.model.NesVideoInput;
import java.util.EnumSet;
import org.junit.jupiter.api.Test;

class NesPageDescriptorInputConverterTest {

  private static final int EXPECTED_VIDEO_ID = 80766711;

  @Test
  void convertPageDescriptorInputNull() {
    final PageDescriptor pageDescriptor =
        NesPageDescriptorInputConverter.convertPageDescriptorInput(null);
    assertNotNull(pageDescriptor);
  }

  @Test
  void convertPageDescriptorInputEmpty() {
    NesPageDescriptorInput nes = new NesPageDescriptorInput();
    final PageDescriptor pageDescriptor =
        NesPageDescriptorInputConverter.convertPageDescriptorInput(nes);
    assertNotNull(pageDescriptor);
  }

  @Test
  void convertPageDescriptorInput() {
    NesPageDescriptorInput nes =
        new NesPageDescriptorInput()
            .setQualifier(
                new NesPageDescriptorQualifierUnion().setGenre(new NesGenreInput().setId(123L)))
            .setPageType(NesPageType.AssistiveAudio);
    final PageDescriptor pageDescriptor =
        NesPageDescriptorInputConverter.convertPageDescriptorInput(nes);
    assertNotNull(pageDescriptor);
    assertTrue(pageDescriptor.hasAssistiveAudioPageDescriptor());
    final AssistiveAudioPageDescriptor assistiveAudioPageDescriptor =
        pageDescriptor.getAssistiveAudioPageDescriptor();
    assertEquals(123L, assistiveAudioPageDescriptor.getGenre().getId());
  }

  @Test
  void convertGalleryPageDescriptorInput() {
    NesPageDescriptorInput nes =
        new NesPageDescriptorInput()
            .setQualifier(
                new NesPageDescriptorQualifierUnion()
                    .setSectionDescriptor(
                        new NesSectionDescriptorInput()
                            .setQualifier(
                                new NesSectionDescriptorQualifierUnion()
                                    .setCatalogVideo(new NesVideoInput().setId(EXPECTED_VIDEO_ID)))
                            .setSectionType(NesSectionType.Sims)))
            .setPageType(NesPageType.Gallery);
    final PageDescriptor pageDescriptor =
        NesPageDescriptorInputConverter.convertPageDescriptorInput(nes);
    assertNotNull(pageDescriptor);
    assertTrue(pageDescriptor.hasGalleryPageDescriptor());
    final GalleryPageDescriptor galleryPageDescriptor = pageDescriptor.getGalleryPageDescriptor();
    final SectionDescriptor sectionDescriptor =
        galleryPageDescriptor.getSectionDescriptorQualifier();
    assertNotNull(sectionDescriptor);
    assertTrue(sectionDescriptor.hasSimsSectionDescriptor());
    final SimsSectionDescriptor simsSectionDescriptor =
        sectionDescriptor.getSimsSectionDescriptor();
    assertEquals(EXPECTED_VIDEO_ID, simsSectionDescriptor.getCatalogVideo().getId());
  }

  @Test
  void implementsAllPageTypes() {
    for (final NesPageType pageType : EnumSet.allOf(NesPageType.class)) {
      assertTrue(NesPageDescriptorInputConverter.PAGE_DESCRIPTOR_BUILDERS.containsKey(pageType));
    }
  }
}
