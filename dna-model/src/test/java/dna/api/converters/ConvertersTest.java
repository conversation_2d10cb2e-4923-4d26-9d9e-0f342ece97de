package dna.api.converters;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.util.function.BiConsumer;
import org.junit.jupiter.api.Test;

class ConvertersTest {

  @Test
  void safeConvert_singleField_withNonNullField_callsConsumer() {
    // Arrange
    StringBuilder builder = new StringBuilder();
    String field = "test";
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, String> consumer = mock(BiConsumer.class);

    // Act
    StringBuilder result = Converters.safeConvert(builder, field, consumer);

    // Assert
    assertSame(builder, result);
    verify(consumer).accept(builder, field);
  }

  @Test
  void safeConvert_singleField_withNullField_doesNotCallConsumer() {
    // Arrange
    StringBuilder builder = new StringBuilder();
    String field = null;
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, String> consumer = mock(BiConsumer.class);

    // Act
    StringBuilder result = Converters.safeConvert(builder, field, consumer);

    // Assert
    assertSame(builder, result);
    verifyNoInteractions(consumer);
  }

  @Test
  void safeConvert_singleField_withRealConsumer_modifiesBuilder() {
    // Arrange
    StringBuilder builder = new StringBuilder("initial");
    String field = "_appended";
    BiConsumer<StringBuilder, String> consumer = StringBuilder::append;

    // Act
    StringBuilder result = Converters.safeConvert(builder, field, consumer);

    // Assert
    assertSame(builder, result);
    assertEquals("initial_appended", result.toString());
  }

  @Test
  void safeConvert_twoFields_withBothNonNull_callsBothConsumers() {
    // Arrange
    StringBuilder builder = new StringBuilder();
    String field1 = "first";
    Integer field2 = 42;
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, String> consumer1 = mock(BiConsumer.class);
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, Integer> consumer2 = mock(BiConsumer.class);

    // Act
    StringBuilder result = Converters.safeConvert(builder, field1, consumer1, field2, consumer2);

    // Assert
    assertSame(builder, result);
    verify(consumer1).accept(builder, field1);
    verify(consumer2).accept(builder, field2);
  }

  @Test
  void safeConvert_twoFields_withFirstNull_callsOnlySecondConsumer() {
    // Arrange
    StringBuilder builder = new StringBuilder();
    String field1 = null;
    Integer field2 = 42;
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, String> consumer1 = mock(BiConsumer.class);
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, Integer> consumer2 = mock(BiConsumer.class);

    // Act
    StringBuilder result = Converters.safeConvert(builder, field1, consumer1, field2, consumer2);

    // Assert
    assertSame(builder, result);
    verify(consumer1, never()).accept(builder, field1);
    verify(consumer2).accept(builder, field2);
  }

  @Test
  void safeConvert_twoFields_withSecondNull_callsOnlyFirstConsumer() {
    // Arrange
    StringBuilder builder = new StringBuilder();
    String field1 = "first";
    Integer field2 = null;
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, String> consumer1 = mock(BiConsumer.class);
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, Integer> consumer2 = mock(BiConsumer.class);

    // Act
    StringBuilder result = Converters.safeConvert(builder, field1, consumer1, field2, consumer2);

    // Assert
    assertSame(builder, result);
    verify(consumer1).accept(builder, field1);
    verify(consumer2, never()).accept(builder, field2);
  }

  @Test
  void safeConvert_twoFields_withBothNull_callsNoConsumers() {
    // Arrange
    StringBuilder builder = new StringBuilder();
    String field1 = null;
    Integer field2 = null;
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, String> consumer1 = mock(BiConsumer.class);
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, Integer> consumer2 = mock(BiConsumer.class);

    // Act
    StringBuilder result = Converters.safeConvert(builder, field1, consumer1, field2, consumer2);

    // Assert
    assertSame(builder, result);
    verifyNoInteractions(consumer1);
    verifyNoInteractions(consumer2);
  }

  @Test
  void safeConvert_twoFields_withRealConsumers_modifiesBuilderCorrectly() {
    // Arrange
    StringBuilder builder = new StringBuilder("start");
    String field1 = "_middle";
    String field2 = "_end";
    BiConsumer<StringBuilder, String> consumer1 = StringBuilder::append;
    BiConsumer<StringBuilder, String> consumer2 = StringBuilder::append;

    // Act
    StringBuilder result = Converters.safeConvert(builder, field1, consumer1, field2, consumer2);

    // Assert
    assertSame(builder, result);
    assertEquals("start_middle_end", result.toString());
  }

  @Test
  void safeConvert_threeFields_withAllNonNull_callsAllConsumers() {
    // Arrange
    StringBuilder builder = new StringBuilder();
    String field1 = "first";
    Integer field2 = 42;
    Double field3 = 3.14;
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, String> consumer1 = mock(BiConsumer.class);
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, Integer> consumer2 = mock(BiConsumer.class);
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, Double> consumer3 = mock(BiConsumer.class);

    // Act
    StringBuilder result =
        Converters.safeConvert(builder, field1, consumer1, field2, consumer2, field3, consumer3);

    // Assert
    assertSame(builder, result);
    verify(consumer1).accept(builder, field1);
    verify(consumer2).accept(builder, field2);
    verify(consumer3).accept(builder, field3);
  }

  @Test
  void safeConvert_threeFields_withMixedNulls_callsOnlyNonNullConsumers() {
    // Arrange
    StringBuilder builder = new StringBuilder();
    String field1 = "first";
    Integer field2 = null;
    Double field3 = 3.14;
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, String> consumer1 = mock(BiConsumer.class);
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, Integer> consumer2 = mock(BiConsumer.class);
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, Double> consumer3 = mock(BiConsumer.class);

    // Act
    StringBuilder result =
        Converters.safeConvert(builder, field1, consumer1, field2, consumer2, field3, consumer3);

    // Assert
    assertSame(builder, result);
    verify(consumer1).accept(builder, field1);
    verify(consumer2, never()).accept(builder, field2);
    verify(consumer3).accept(builder, field3);
  }

  @Test
  void safeConvert_threeFields_withAllNull_callsNoConsumers() {
    // Arrange
    StringBuilder builder = new StringBuilder();
    String field1 = null;
    Integer field2 = null;
    Double field3 = null;
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, String> consumer1 = mock(BiConsumer.class);
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, Integer> consumer2 = mock(BiConsumer.class);
    @SuppressWarnings("unchecked")
    BiConsumer<StringBuilder, Double> consumer3 = mock(BiConsumer.class);

    // Act
    StringBuilder result =
        Converters.safeConvert(builder, field1, consumer1, field2, consumer2, field3, consumer3);

    // Assert
    assertSame(builder, result);
    verifyNoInteractions(consumer1);
    verifyNoInteractions(consumer2);
    verifyNoInteractions(consumer3);
  }

  @Test
  void safeConvert_threeFields_withRealConsumers_modifiesBuilderCorrectly() {
    // Arrange
    StringBuilder builder = new StringBuilder("prefix");
    String field1 = "_str";
    Integer field2 = 123;
    Double field3 = 4.56;

    // Act
    StringBuilder result =
        Converters.safeConvert(
            builder,
            field1,
            StringBuilder::append,
            field2,
            (b, f) -> b.append("_").append(f),
            field3,
            (b, f) -> b.append("_").append(f));

    // Assert
    assertSame(builder, result);
    assertEquals("prefix_str_123_4.56", result.toString());
  }

  @Test
  void constructor_isPrivate() {
    // Test that the utility class constructor is private by verifying
    // the class is designed as a utility class with static methods only

    // Verify the class exists and has the expected static methods
    assertNotNull(Converters.class.getDeclaredMethods());

    // All public methods should be static
    boolean allMethodsStatic =
        java.util.Arrays.stream(Converters.class.getDeclaredMethods())
            .filter(m -> java.lang.reflect.Modifier.isPublic(m.getModifiers()))
            .allMatch(m -> java.lang.reflect.Modifier.isStatic(m.getModifiers()));

    assertEquals(true, allMethodsStatic, "All public methods should be static in utility class");
  }

  @Test
  void safeConvert_chainingBehavior_returnsSameBuilder() {
    // Test that all overloads return the same builder instance for method chaining
    StringBuilder builder = new StringBuilder();

    // Single field
    StringBuilder result1 = Converters.safeConvert(builder, "test", StringBuilder::append);
    assertSame(builder, result1);

    // Two fields
    StringBuilder result2 =
        Converters.safeConvert(builder, "a", StringBuilder::append, "b", StringBuilder::append);
    assertSame(builder, result2);

    // Three fields
    StringBuilder result3 =
        Converters.safeConvert(
            builder,
            "x",
            StringBuilder::append,
            "y",
            StringBuilder::append,
            "z",
            StringBuilder::append);
    assertSame(builder, result3);
  }
}
