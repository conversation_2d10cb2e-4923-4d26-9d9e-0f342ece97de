package com.netflix.api.service.video;

import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Registry;
import com.netflix.subscriberservice.common.MaturityLevel;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class APIVideoMetadataFallbacks {
  private final Registry registry;
  private final Id fallbackMetricId;

  @Autowired
  public APIVideoMetadataFallbacks(Registry registry) {
    this.registry = registry;
    fallbackMetricId = registry.createId("api.video.metadata.fallback");
  }

  public Map<Integer, Integer> getMaturityLevel(Set<Integer> videoIds) {
    registry.counter(fallbackMetricId.withTag("endpoint", "getMaturityLevel")).increment();

    // maturity level ADULT by default
    return videoIds.stream()
        .collect(
            Collectors.toMap(id -> id, id -> MaturityLevel.getMaturityValue(MaturityLevel.ADULTS)));
  }
}
