package com.netflix.api.service.video;

import com.netflix.api.service.list.APIEntity;
import java.util.Map;
import rx.Observable;

/**
 * Represents loosely typed collection of name-value pairs. The main purpose of this class is to
 * enable {@code Service Layer} dependency data to be passed through without introducing or changing
 * the {@code Service Layer} object model.
 *
 * <AUTHOR>
 */
public interface APIRawData extends APIEntity {

  /**
   * Emits the raw data {@code Map} wrapped into this API entity
   *
   * @return an {@link Observable}{@code <Map<String, ?>>}
   */
  Observable<Map<String, ?>> toObservable();
}
