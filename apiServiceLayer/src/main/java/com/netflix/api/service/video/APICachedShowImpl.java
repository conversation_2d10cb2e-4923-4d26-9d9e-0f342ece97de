package com.netflix.api.service.video;

import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.platform.util.CompletionStageAdapter;
import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.vms.provider.VideoFormatDescriptorProvider;
import java.util.Collections;
import rx.Observable;

/**
 * This object wraps around an {@link APIVideo} object and exposes only those operations that make
 * sense for a "Show" type of object in the Netflix catalog
 *
 * <p>A show has one or more seasons, and one or more episodes, one of which is the current episode.
 * A season has one show parent and has one or more episodes, one of which is the current episode. A
 * (show) episode has a show parent and a season parent.
 */
@EnableDeprecatedMetrics
public class APICachedShowImpl extends APICachedVideoImpl implements APIShow {

  APICachedShowImpl(
      CompleteVideo completeVideo,
      APIVideoFactory videoFactory,
      USTVideoMetadataAdapter videoMetadataAdapter,
      VideoFormatDescriptorProvider videoFormatDescriptorProvider) {
    super(completeVideo, videoFactory, videoMetadataAdapter, videoFormatDescriptorProvider);
  }

  @Override
  public Observable<String> getNumSeasonsLabel(LabelType labelType) {
    return CompletionStageAdapter.toObservable(
            videoMetadataAdapter.getNumSeasonsLabel(
                Collections.singleton(this.getId()), buildLabelType(labelType)))
        .map(reply -> reply.get(this.getId()));
  }
}
