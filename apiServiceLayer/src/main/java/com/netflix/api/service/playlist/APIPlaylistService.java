package com.netflix.api.service.playlist;

import com.netflix.api.service.video.APIVideo;
import jakarta.annotation.Nullable;
import java.util.Collection;
import rx.Observable;

/**
 * Intended as a replacement for APIQueueService if watchlist a/b test productized. Nope, it was not
 * productized.
 */
public interface APIPlaylistService {

  /**
   * Remove videos from the current customer's "continue watching" list.
   *
   * @param videos the videos to be deleted from "continue watching" list
   * @return an <code>Observable</code> that emits nothing if the request succeeded or an OnError if
   *     the request failed
   */
  Observable<Void> removeFromContinueWatching(
      Collection<APIVideo> videos, @Nullable String uiVersion, Integer trackId);
}
