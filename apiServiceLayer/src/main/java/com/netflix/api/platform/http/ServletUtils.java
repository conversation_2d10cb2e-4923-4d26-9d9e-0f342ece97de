package com.netflix.api.platform.http;

import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;
import java.util.Optional;

public class ServletUtils {
  private ServletUtils() {}

  public static <T extends HttpServletResponseWrapper> Optional<T> findWrapper(
      ServletResponse response, Class<T> clazz) {
    ServletResponse current = response;
    while (current instanceof HttpServletResponseWrapper wrapper) {
      if (clazz.isAssignableFrom(current.getClass())) {
        return Optional.of(clazz.cast(current));
      } else {
        current = wrapper.getResponse();
      }
    }
    return Optional.empty();
  }
}
