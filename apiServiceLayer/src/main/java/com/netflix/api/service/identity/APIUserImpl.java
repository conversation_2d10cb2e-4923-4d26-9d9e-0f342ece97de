package com.netflix.api.service.identity;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.protobuf.util.Timestamps;
import com.netflix.api.dependencies.subscriber.AccountProfileHandler;
import com.netflix.api.platform.identity.CurrentIdentityResult;
import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.api.platform.util.APIInstantQueueEvo;
import com.netflix.api.service.APICountry;
import com.netflix.api.service.APICountryFactory;
import com.netflix.api.service.APILocale;
import com.netflix.api.service.APILocaleImpl;
import com.netflix.api.service.ListItem;
import com.netflix.api.service.account.APIPlanDetail;
import com.netflix.api.service.account.APIPlanDetailImpl;
import com.netflix.api.service.plan.APIPlanInfoFactory;
import com.netflix.api.service.plan.APIPriceInfoImpl;
import com.netflix.api.service.plan.APISubscriptionData;
import com.netflix.api.service.video.APIResolutions;
import com.netflix.api.service.video.APIResolutionsImpl;
import com.netflix.api.util.BlockingObservableCache;
import com.netflix.api.util.Cryptex2Manager;
import com.netflix.i18n.NFLocale;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.subscriber.types.protogen.Experience.Type;
import com.netflix.subscriber.types.protogen.Membership.Status;
import com.netflix.subscriber.types.protogen.ProfileCreation;
import com.netflix.subscriberservice.common.MaturityLevel;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.type.NFCountry;
import com.netflix.type.TypeManager;
import com.netflix.type.Visitor;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.CompletionStage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.Observable;
import rx.Single;

public class APIUserImpl implements APIUserInternal {

  private static final Logger logger = LoggerFactory.getLogger(APIUserImpl.class);

  private final LocalizedProfileLogic localizedProfileLogic;
  // members
  private final long customerId;
  private final IdentityResult<?> identityResult;
  private final APIUserFactory userFactory;
  private final APICountryFactory countryFactory;
  private final APIPlanInfoFactory planInfoFactory;
  private final SubtitlesPreferencesHandler subtitlesPreferencesHandler;

  private final AccountProfileHandler accountProfileHandler;

  private final boolean invalidateCache;
  private Long accountOwnerId;
  private Visitor visitor;
  private APIUserPersonalData personal;
  private volatile Observable<Boolean> isOnHoldDueToPayment;

  private volatile APIUser accountOwner;
  private final Cryptex2Manager cryptex2;

  private BlockingObservableCache<Optional<AccountProfileRemote>> bocAccountProfile =
      BlockingObservableCache.builder(this::createObservable).build();

  APIUserImpl(
      AccountProfileRemote accountProfile,
      APIUserFactory userFactory,
      APICountryFactory countryFactory,
      APIPlanInfoFactory planInfoFactory,
      Cryptex2Manager cryptex2,
      SubtitlesPreferencesHandler subtitlesPreferencesHandler,
      LocalizedProfileLogic localizedProfileLogic) {
    this(
        Preconditions.checkNotNull(accountProfile, "Must provide non-null AccountProfile")
            .getBoxedProfileId(),
        accountProfile.getBoxedAccountOwnerId(),
        false,
        userFactory,
        countryFactory,
        planInfoFactory,
        cryptex2,
        subtitlesPreferencesHandler,
        localizedProfileLogic);
    bocAccountProfile =
        BlockingObservableCache.builder(() -> Observable.just(Optional.of(accountProfile))).build();
  }

  APIUserImpl(
      long customerId,
      Long accountOwnerId,
      boolean invalidateCache,
      APIUserFactory userFactory,
      APICountryFactory countryFactory,
      APIPlanInfoFactory planInfoFactory,
      Cryptex2Manager cryptex2,
      SubtitlesPreferencesHandler subtitlesPreferencesHandler,
      LocalizedProfileLogic localizedProfileLogic) {
    this.customerId = customerId;
    this.accountOwnerId = accountOwnerId;
    this.invalidateCache = invalidateCache;
    this.userFactory = userFactory;
    this.countryFactory = countryFactory;
    this.planInfoFactory = planInfoFactory;
    this.cryptex2 = cryptex2;
    IdentityResult<?> current = CurrentIdentityResult.get();
    this.identityResult =
        current != null && current.getCustomerId() != null && customerId == current.getCustomerId()
            ? current
            : null;
    this.subtitlesPreferencesHandler = subtitlesPreferencesHandler;
    this.localizedProfileLogic = localizedProfileLogic;
    this.accountProfileHandler = userFactory.getAccountProfileHandler();
    invalidateCacheIfNecessary();
  }

  APIUserImpl(
      IdentityResult<?> identityResult,
      boolean invalidateCache,
      APIUserFactory userFactory,
      APICountryFactory countryFactory,
      APIPlanInfoFactory planInfoFactory,
      Cryptex2Manager cryptex2,
      SubtitlesPreferencesHandler subtitlesPreferencesHandler,
      LocalizedProfileLogic localizedProfileLogic) {
    this.customerId =
        Preconditions.checkNotNull(identityResult, "IdentityResult must be provided")
            .getCustomerId();
    this.accountOwnerId = identityResult.getAccountOwnerId();
    this.invalidateCache = invalidateCache;
    this.userFactory = userFactory;
    this.countryFactory = countryFactory;
    this.planInfoFactory = planInfoFactory;
    this.identityResult = identityResult;
    this.cryptex2 = cryptex2;
    this.subtitlesPreferencesHandler = subtitlesPreferencesHandler;
    this.localizedProfileLogic = localizedProfileLogic;
    this.accountProfileHandler = userFactory.getAccountProfileHandler();
    invalidateCacheIfNecessary();
  }

  private void invalidateCacheIfNecessary() {
    if (invalidateCache) {
      invalidateInternalState();
    }
  }

  @Override
  public AccountProfileRemote getAccountProfile() {
    try {
      return bocAccountProfile.get().orElse(null);
    } catch (RuntimeException e) {
      if (e.getCause() != null && e.getCause() instanceof APIDeletedProfileException deleted) {
        throw deleted;
      }
      throw e;
    }
  }

  @Override
  public Observable<AccountProfileRemote> getAccountProfileAsObservable() {
    return bocAccountProfile
        .getAsync()
        .flatMap(o -> o.map(Observable::just).orElseGet(Observable::empty));
  }

  // factory method called by BlockingObservableCache (bocAccountProfile) to create an Observable
  // that would emit an AccountProfile
  private Observable<Optional<AccountProfileRemote>> createObservable() {
    AccountProfileHandler handler = userFactory.getAccountProfileHandler();
    return handler
        .get(getRealCustomerId(), invalidateCache)
        .toObservable()
        .onErrorResumeNext(e -> Observable.error(new APIDeletedProfileException(e)))
        .flatMap(
            accountProfile -> {
              if (accountProfile.isEmpty()) {
                return Observable.error(new APIDeletedProfileException());
              }
              if (invalidateCache) {
                handler.invalidate(accountProfile.get().getBoxedProfileId());
              }
              return Observable.just(accountProfile);
            });
  }

  @Override
  public void invalidateInternalState() {
    visitor = null;
    isOnHoldDueToPayment = null;
    accountOwner = null;
    accountProfileHandler.invalidate(customerId);
    bocAccountProfile.reset();
  }

  @Override
  public ProfileReader getProfileReader() {
    return new MembershipProfileReader(this, getAccountProfile(), localizedProfileLogic);
  }

  @Override
  public String getAvatarImage() {
    return APIUserUtil.getAvatarImage(getAccountProfile());
  }

  @Override
  public long getRealAccountOwnerId() {
    if (accountOwnerId == null) {
      accountOwnerId = getAccountProfile().getBoxedAccountOwnerId();
    }
    return accountOwnerId;
  }

  @Override
  public String getCustomerGUID() {
    return getAccountProfile().getBoxedProfileGuid();
  }

  @Override
  public Single<List<AccountProfileRemote>> getAllProfilesSubscriber() {
    return userFactory.getAccountProfileHandler().getProfiles(getAccountProfile());
  }

  @Override
  public Observable<AccountProfileRemote> getProfileByGuid(String guid) {
    return getAllProfilesSubscriber()
        .toObservable()
        .map(
            profiles -> {
              Optional<AccountProfileRemote> first =
                  profiles.stream()
                      .filter(p -> Objects.equals(p.getBoxedProfileGuid(), guid))
                      .findFirst();
              if (first.isPresent()) {
                return first.get();
              }
              throw new IllegalArgumentException(String.format("Profile %s not found", guid));
            });
  }

  @Override
  public Observable<APIUser> getUserByGuid(String guid) {
    return getProfileByGuid(guid).map(userFactory::createUser);
  }

  @Override
  public Single<List<Long>> getAllProfileIds() {
    return getAllProfilesSubscriber()
        .map(profiles -> profiles.stream().map(AccountProfileRemote::getBoxedProfileId).toList());
  }

  @Override
  public APIMaturity getMaturity() {
    MaturityLevel level =
        MaturityLevel.getMaturityLevel(getAccountProfile().getBoxedMaturityLevel());
    return level == null ? APIMaturity.ADULTS : APIMaturity.valueOf(level.name());
  }

  @Override
  public APIMembershipStatus getMembershipStatus() {
    return Optional.of(getAccountProfile().getMembershipStatusEnum())
        .map(Enum::name)
        .map(APIMembershipStatus::valueOf)
        .orElse(null);
  }

  @Override
  public boolean isJfk() {
    return Type.just_for_kids == getAccountProfile().getExperienceTypeEnum();
  }

  @Override
  public Observable<Boolean> isPinEnabled() {
    return getAccountProfileAsObservable()
        .map(accountProfile -> accountProfile.getOptionalYouthMaturityPinEnabled().orElse(false));
  }

  @Override
  public IdentityResult<?> getIdentityResult() {
    if (identityResult != null) {
      DynamicCounter.increment("api.user.identityResult", "present", "true");
      return identityResult;
    } else {
      DynamicCounter.increment("api.user.identityResult", "present", "false");
      return new IdentityUserResult(this);
    }
  }

  @Override
  public Observable<Boolean> isAdultVerifiedObservable() {
    return getAccountProfileAsObservable().map(AccountProfileRemote::getBoxedIsAgeVerified);
  }

  @Override
  public Observable<String> getPin() {
    return getAccountProfileAsObservable().map(AccountProfileRemote::getBoxedYouthPin);
  }

  @Override
  public Long getSubscriptionId() {
    return getAccountProfile().getBoxedPlanId();
  }

  @Override
  public Observable<List<APIServiceEndReasons>> getServiceEndReasons() {
    return getAccountProfileAsObservable().flatMap(accountProfileHandler::getServiceEndReasons);
  }

  public Observable<Boolean> onHoldDueToPayment() {
    // This is not exact but pretty close to the old impl which was based on account holds
    // Account hods are not avialable in Subscriber2.0, so we have to work around that.
    // With this flag set to false, any UI will check with UMA to get any alerts set on the account
    // and playback be blocked.
    if (isOnHoldDueToPayment == null) {
      isOnHoldDueToPayment =
          getAccountProfileAsObservable()
              .flatMap(
                  accountProfile ->
                      Status.CURRENT_MEMBER == accountProfile.getMembershipStatusEnum()
                              && !accountProfile.getOptionalCanWatchNow().orElse(false)
                          ? accountProfileHandler.cannotWatchDueToPayment(accountProfile)
                          : Observable.just(false))
              .cache();
    }
    return isOnHoldDueToPayment;
  }

  @Override
  public Visitor getVisitor() {
    if (visitor == null) {
      visitor = TypeManager.findObject(Visitor.class, getRealCustomerId());
    }
    return visitor;
  }

  @Override
  public Single<Integer> getNumProfilesInAccount() {
    return getAllProfileIds().map(List::size);
  }

  @Override
  public Single<SortedMap<Long, String>> getIconNames() {
    return getAllProfilesSubscriber()
        .map(
            profiles -> {
              SortedMap<Long, String> iconNames = new TreeMap<>();
              for (AccountProfileRemote user : profiles) {
                iconNames.put(user.getBoxedProfileId(), user.getBoxedAvatarImg());
              }
              return iconNames;
            });
  }

  @Override
  public long getCustomerId() {
    return getRealCustomerId();
  }

  @Override
  public APIUser getAccountOwner() {
    if (accountOwner != null) {
      return accountOwner;
    }
    if (isPrimaryAccountOwner()) {
      accountOwner = this;
    }

    accountOwner = userFactory.createUser(getRealAccountOwnerId(), getRealAccountOwnerId());
    return accountOwner;
  }

  @Override
  public boolean isPrimaryAccountOwner() {
    return getRealAccountOwnerId() == getRealCustomerId();
  }

  @Override
  public String getCountryOfSignup() {
    return getCountryOfSignupObservable().toBlocking().first();
  }

  @Override
  public Observable<String> getCountryOfSignupObservable() {
    return getAccountProfileAsObservable().map(AccountProfileRemote::getBoxedCountryOfSignup);
  }

  @Override
  public String getCountryOfRegistration() {
    return getAccountProfile().getBoxedCountryOfRegistration();
  }

  @Override
  public String getLanguage() {
    return getAccountProfile().getBoxedPrimaryLang();
  }

  @Override
  public Observable<APICountry> getAPICountry() {
    return getCountry()
        .filter(Objects::nonNull)
        .map(countryString -> countryFactory.get(NFCountry.findInstance(countryString)));
  }

  private Observable<String> getCountry() {
    return getAccountProfileAsObservable()
        .map(
            accountProfile ->
                accountProfile
                    .getOptionalCountryOfSignup()
                    .orElse(accountProfile.getBoxedCountryOfRegistration()));
  }

  @Override
  public long getRealCustomerId() {
    return customerId;
  }

  @Override
  public boolean isFirstUse() {
    return getProfileReader().isFirstUse();
  }

  @Override
  public long getDateCreated() {
    return getProfileReader().getDateCreated();
  }

  @Override
  public Date getProfileCreationDate() {
    return new Date(Timestamps.toMillis(getAccountProfile().getProfileCreationTime()));
  }

  @Override
  public Experience getPreferredExperience() {
    return getProfileReader().getExperience();
  }

  @Override
  public Observable<ListItem<APILocale>> getPreferredLocales() {
    return _getPreferredLocales()
        .map(list -> list.stream().map(locale -> new ListItem<>(locale, 0)).toList())
        .flatMap(Observable::from);
  }

  public Observable<List<APILocale>> _getPreferredLocales() {
    return getAccountProfileAsObservable()
        .map(
            accountProfile ->
                accountProfile
                    .getOptionalPrimaryLang()
                    .map(NFLocale::createFromString)
                    .map(APILocaleImpl::new)
                    .map(c -> (APILocale) c)
                    .map(Collections::singletonList)
                    .orElse(Collections.emptyList()));
  }

  @Override
  public String getIconName() {
    return getAvatarImage();
  }

  @Override
  public CompletionStage<String> getProfileName() {
    return getProfileReader().getProfileName();
  }

  @Override
  public CompletionStage<String> getHumanReadableProfileName() {
    return getProfileReader().getHtmlDecodedProfileName();
  }

  @Override
  public Observable<APISubtitleSettings> getSubtitleSettings() {
    return subtitlesPreferencesHandler
        .getLegacySubtitlePreferences(customerId)
        .toObservable()
        .cache();
  }

  @Override
  public boolean isDefaultKidsProfile() {
    return isAutoCreatedProfile() && isJfk();
  }

  @Override
  public boolean isFamilyExperience() {
    return Type.family == getAccountProfile().getExperienceTypeEnum();
  }

  @Override
  public boolean isJustForKidsExperience() {
    return isJfk();
  }

  @Override
  public boolean isWatchTogetherExperience() {
    return Type.watch_together == getAccountProfile().getExperienceTypeEnum();
  }

  @Override
  public boolean isAutoCreatedProfile() {
    ProfileCreation.Method profileCreationMethod = getProfileCreationMethod();
    return (profileCreationMethod == ProfileCreation.Method.AUTO
        || profileCreationMethod == ProfileCreation.Method.AUTO_VIA_FORKLIFT);
  }

  @Override
  public APIUserPersonalData getPersonal() {
    if (personal == null) {
      personal = new APIUserPersonalDataImpl(getAccountProfile(), cryptex2);
    }
    return personal;
  }

  protected static String maskEmail(String email) {
    if (Strings.isNullOrEmpty(email) || !email.contains("@")) {
      return email;
    }
    return email.charAt(0)
        + "*****@"
        + email.charAt(email.indexOf('@') + 1)
        + "*****"
        + email.substring(email.lastIndexOf('.'));
  }

  @Override
  public Observable<Long> getPlanId() {
    return getAccountProfileAsObservable().map(AccountProfileRemote::getBoxedPlanId);
  }

  @Override
  public Observable<APIPlanInfo> getPlanInfo() {
    return getAccountProfileAsObservable()
        .filter(APIUserUtil::isActiveOrOnHold)
        .flatMap(planInfoFactory::create);
  }

  @Override
  public Observable<APIResolutions> getPlanResolutions() {
    return getAccountProfileAsObservable()
        .filter(APIUserUtil::isActiveOrOnHold)
        .flatMap(planInfoFactory::getPlanData)
        .map(
            planData ->
                new APIResolutionsImpl(
                    planData.isHDEnabled(), planData.isHDEnabled(), planData.isUHDEnabled()));
  }

  @Override
  public Observable<APIPlanDetail> getPlanDetails() {
    return getAccountProfileAsObservable()
        .filter(APIUserUtil::isActiveOrOnHold)
        .flatMap(planInfoFactory::getPlanData)
        .map(APIPlanDetailImpl::new);
  }

  @Override
  public Observable<APISubscriptionData> getSubscriptionDetails() {
    return getAccountProfileAsObservable()
        .filter(APIUserUtil::isActiveOrOnHold)
        .map(AccountProfileRemote::getBoxedAccountOwnerId)
        .flatMap(planInfoFactory::getMemberData);
  }

  @Override
  public Observable<APIPriceInfo> getPriceDetails() {
    return getAccountProfileAsObservable()
        .filter(APIUserUtil::isActiveOrOnHold)
        .flatMap(planInfoFactory::getAccountPlanInfo)
        .map(APIPriceInfoImpl::new);
  }

  @Override
  public boolean hasAlerts() {
    return getAccountProfile().getOptionalHasAlerts().orElse(false);
  }

  @Override
  public Observable<Long> getMemberSince() {
    return getAccountProfileAsObservable()
        .map(AccountProfileRemote::getBoxedMemberSince)
        .filter(Objects::nonNull);
  }

  @Override
  public boolean isTimeForTou() {
    return hasAlerts() && accountProfileHandler.isTimeForTOU(accountOwnerId);
  }

  @Override
  public boolean cookieDisclosureAccepted() {
    return getAccountProfile().getOptionalCookieDisclosureAccepted().orElse(false);
  }

  @Override
  @Deprecated
  public Observable<Integer> getRawMaturityValue() {
    return getAccountProfileAsObservable().map(AccountProfileRemote::getBoxedMaturityLevel);
  }

  @Override
  public Observable<String> getProfileLockPin() {
    return getAccountProfileAsObservable().map(AccountProfileRemote::getBoxedProfileAccessPin);
  }

  public Observable<Boolean> isProfileCreationLocked() {
    return getAccountProfileAsObservable()
        .map(account -> account.getParentalControlState().getBoxedIsProfileCreationLocked());
  }

  @Override
  public Observable<Boolean> isProfileLocked() {
    return getAccountProfileAsObservable().map(AccountProfileRemote::hasProfileAccessPin);
  }

  @Override
  public Observable<Boolean> isAccountMigratedToProfileHub() {
    return getAccountProfileAsObservable()
        .map(account -> account.getParentalControlState().getBoxedIsUsingProfileHub());
  }

  @Override
  public boolean getCanStreamNow() {
    return getAccountProfile().getOptionalCanWatchNow().orElse(false);
  }

  @Override
  public boolean getHasDVD() {
    return false;
  }

  @Override
  public boolean isAutoplayEnabled() {
    return getAccountProfile().getOptionalHasAutoPlayback().orElse(false);
  }

  @Override
  public Date getMemberSinceDate() {
    return getAccountProfile().getOptionalMemberSince().map(Date::new).orElse(null);
  }

  @Override
  public boolean isInstantQueueAllowed() {
    try {
      return APIInstantQueueEvo.getInstance().isInstantQueueAllowed(getAccountProfile());
    } catch (Exception e) {
      logger.error("Failed to determine whether instant queue is allowed.", e);
      return true;
    }
  }

  @Override
  public boolean isTestAccount() {
    return getAccountProfile().getTesterFlagsCount() > 0;
  }

  @Override
  @Deprecated
  public Boolean isPartnerPreviewAccount() {
    return getAccountProfile().getOptionalIsPartnerPreviewAccount().orElse(false);
  }

  @Override
  public ProfileCreation.Method getProfileCreationMethod() {
    return getAccountProfile().getProfileCreationInfo();
  }

  @Override
  public String getEncryptedEmailAddress() {
    return getAccountProfile().getBoxedEncryptedEmail();
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + Long.hashCode(customerId);
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) {
      return true;
    }
    if (obj == null) {
      return false;
    }
    if (obj instanceof APIUserImpl other) {
      return customerId == other.customerId;
    }
    return false;
  }

  @Override
  public String toString() {
    return String.format("user: %s", getCustomerGUID());
  }
}
