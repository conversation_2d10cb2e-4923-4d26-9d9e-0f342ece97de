package com.netflix.api.service.list;

import com.google.common.base.Preconditions;
import com.netflix.api.service.APIAnnotation;
import com.netflix.api.service.ContextualItem;
import com.netflix.api.service.ListItem;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.IntStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.Observable;

/**
 * A list builder delegate of ListItem&amp;lt;item&amp;gt; - this class provides base functionality needed
 * to create such a subscription given a list of references to the items.
 *
 * <p>** Note: This class is an adaptation of the original ApiListBuilderImpl which used to be the
 * super class of all list builders. While migrating to Observable due to Java's restrictions of no
 * multiple inheritance this class has been made a delegate. The idea is that each ListBuilder will
 * have an instance of this delegate and it will forward all calls that the Builder does not need to
 * override to this delegate. (mimicking the super class behavior) This class is stateless and its
 * instantiated with a reference to the builder so that it can callback the builder for method
 * overriding. (if the builder doesn't want to override then it simply delegates the call back to
 * this class)
 */
abstract class APIListImpl<Item, Reference> implements APIList<Item> {
  private static final Logger log = LoggerFactory.getLogger(APIListImpl.class);

  // cache the references locally once fetched since fetching references may be an expensive
  // operation.
  private final AtomicReference<ReferenceSource<Reference>> referenceSource =
      new AtomicReference<>();
  private ConcurrentHashMap<Integer, ReferenceHolder> cachedReferenceMap =
      new ConcurrentHashMap<>();
  private ConcurrentHashMap<Integer, AtomicInteger> locks = new ConcurrentHashMap<>();
  private final AtomicReference<Observable<ListItem<Item>>> cachedListItems =
      new AtomicReference<>();
  private final AtomicReference<Observable<Integer>> size =
      new AtomicReference<>(
          Observable.fromCallable(() -> getCachedReferenceSource().getSize()).cache());

  protected class ReferenceHolder {

    final AtomicReference<ListItem<Item>> itemRef;

    ReferenceHolder(Reference reference, Integer index, Item item) {
      Preconditions.checkArgument(
          reference != null || item != null,
          "Either reference or item it refers to must not be null");

      if (item == null) itemRef = new AtomicReference<>();
      else itemRef = new AtomicReference<>(new ListItem<>(item, index));
    }
  }

  protected ReferenceSource<Reference> getCachedReferenceSource() {
    if (referenceSource.get() == null) {
      synchronized (referenceSource) {
        if (referenceSource.get() == null)
          referenceSource.compareAndSet(null, loadReferenceSource());
      }
    }
    return referenceSource.get();
  }

  private synchronized ConcurrentHashMap<Integer, ReferenceHolder> getCachedReferenceMap() {
    if (cachedReferenceMap == null)
      cachedReferenceMap = new ConcurrentHashMap<>(getSize().toBlocking().firstOrDefault(16));
    return cachedReferenceMap;
  }

  private synchronized ConcurrentHashMap<Integer, AtomicInteger> getLocks() {
    if (locks == null) locks = new ConcurrentHashMap<>(getSize().toBlocking().firstOrDefault(16));
    return locks;
  }

  private Observable<ListItem<Item>> getListItem(final Integer index) {
    Preconditions.checkNotNull(index);

    if (getCachedReferenceMap().get(index) == null) {
      // create a unique monitor for each index and lock on it, so that we don't block access to the
      // whole
      // concurrent map. if we don't lock at all, we end up doing double work.
      getLocks().putIfAbsent(index, new AtomicInteger(index));
      synchronized (getLocks().get(index)) {
        // init and cache reference holder
        if (getCachedReferenceMap().get(index) == null) {
          Reference reference = getCachedReferenceSource().getReference(index);
          if (reference == null) {
            // we get here if MAP mis-calculates the listSize. All we can do at this point is to
            // ignore this index.
            log.warn("Failed to get reference for index={}", index);
            return Observable.empty();
          }
          Item item = loadItem(reference);
          ReferenceHolder referenceHolder = new ReferenceHolder(reference, index, item);
          getCachedReferenceMap().putIfAbsent(index, referenceHolder);
        }
      }
    }

    return Observable.just(cachedReferenceMap.get(index).itemRef.get());
  }

  protected abstract ReferenceSource<Reference> loadReferenceSource();

  @Override
  public Observable<ListItem<Item>> getListItems() {
    Observable<ListItem<Item>> result = cachedListItems.get();
    if (result != null) return result;
    result = _getListItems().cache();
    if (!cachedListItems.compareAndSet(null, result)) {
      result = cachedListItems.get();
    }
    return result;
  }

  private Observable<ListItem<Item>> _getListItems() {
    return Observable.mergeDelayError(
        getSize()
            .filter(listSize -> listSize > 0)
            .flatMap(
                listSize ->
                    Observable.from(
                        IntStream.range(0, listSize).boxed().map(this::getListItem).toList())));
  }

  /**
   * Load an item given a reference to the item. Sub-classes own the business logic of how to load
   * an item from the reference.
   *
   * @param reference to the item.
   * @return the referenced item.
   */
  public abstract Item loadItem(Reference reference);

  @Override
  public Observable<Integer> getSize() {
    return size.get();
  }

  @Override
  public Observable<List<Item>> toOrderedList() {
    return getListItems()
        .toSortedList()
        .map(
            listItems ->
                listItems.stream()
                    .map(itemListItem -> itemListItem == null ? null : itemListItem.getItem())
                    .toList());
  }

  @Override
  public Observable<Item> toItems() {
    return getListItems().map(ContextualItem::getItem);
  }

  @Deprecated
  @Override
  public String getName() {
    return null;
  }

  @Deprecated
  @Override
  public String getId() {
    return null;
  }

  @Override
  public Observable<String> getListName() {
    if (getName() == null) {
      return Observable.empty();
    } else {
      return Observable.just(getName());
    }
  }

  @Override
  public Observable<String> getListId() {
    if (getId() == null) {
      return Observable.empty();
    } else {
      return Observable.just(getId());
    }
  }

  @Override
  public Observable<APIAnnotation<String, Object>> getAnnotations() {
    return Observable.empty();
  }
}
