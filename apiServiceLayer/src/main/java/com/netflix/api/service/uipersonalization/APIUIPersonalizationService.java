package com.netflix.api.service.uipersonalization;

import com.netflix.api.service.identity.APIUser;
import rx.Completable;
import rx.Observable;

/**
 * An API representation for the UI Personalization Service
 *
 * @see <a href="https://go/uipersonalization">UI Personalization</a>
 */
public interface APIUIPersonalizationService {
  Observable<APIUIPersonalization> getPersonalization(
      APIUIPersonalizationSelector selector, boolean consistentRead);

  Observable<APIUIPersonalization> getPersonalization(APIUIPersonalizationRequest request);

  Observable<Completable> setPersonalization(APIUIPersonalization personalization);

  Observable<Void> setPersonalization(APIUIPersonalization personalization, String profileGuid);

  Observable<Completable> deletePersonalization(APIUIPersonalization personalization);

  /**
   * Determine if the current profile user is eligible for country language based on primary and
   * secondary language settings
   *
   * @deprecated test is disabled
   * @param user the profile used to determine eligibility
   * @return true if the profile is eligible and false in all other situations
   */
  @Deprecated
  Observable<Boolean> isCountryCatalogAvailable(APIUser user);
}
