package com.netflix.api.service.uipersonalization;

import com.netflix.api.service.identity.APIUser;

public class APIUIPersonalizationRequest {
  private final APIUser user;
  private final boolean retrieveVideoMerchDisabled;
  private final boolean retrieveSecondaryLanguages;
  private final boolean retrieveDisabledVotingTitles;
  private final boolean retrieveHiddenModules;
  private final boolean consistentRead;

  private APIUIPersonalizationRequest(
      APIUser user,
      boolean retrieveVideoMerchDisabled,
      boolean retrieveSecondaryLanguages,
      boolean retrieveDisabledVotingTitles,
      boolean retrieveHiddenModules,
      boolean consistentRead) {
    this.user = user;
    this.retrieveVideoMerchDisabled = retrieveVideoMerchDisabled;
    this.retrieveSecondaryLanguages = retrieveSecondaryLanguages;
    this.retrieveDisabledVotingTitles = retrieveDisabledVotingTitles;
    this.retrieveHiddenModules = retrieveHiddenModules;
    this.consistentRead = consistentRead;
  }

  public static Builder builder() {
    return new Builder();
  }

  public APIUser getUser() {
    return user;
  }

  public boolean isRetrieveDisabledVotingTitles() {
    return retrieveDisabledVotingTitles;
  }

  public boolean isRetrieveHiddenModules() {
    return retrieveHiddenModules;
  }

  public boolean isRetrieveSecondaryLanguages() {
    return retrieveSecondaryLanguages;
  }

  public boolean isRetrieveVideoMerchDisabled() {
    return retrieveVideoMerchDisabled;
  }

  public boolean isConsistentRead() {
    return consistentRead;
  }

  public static class Builder {

    private APIUser user;
    private boolean retrieveVideoMerchDisabled;
    private boolean retrieveSecondaryLanguages;
    private boolean retrieveDisabledVotingTitles;
    private boolean retrieveHiddenModules;
    private boolean consistentRead;

    Builder() {}

    public Builder user(APIUser user) {
      this.user = user;
      return this;
    }

    public Builder retrieveVideoMerchDisabled(boolean retrieveVideoMerchDisabled) {
      this.retrieveVideoMerchDisabled = retrieveVideoMerchDisabled;
      return this;
    }

    public Builder retrieveSecondaryLanguages(boolean retrieveSecondaryLanguages) {
      this.retrieveSecondaryLanguages = retrieveSecondaryLanguages;
      return this;
    }

    public Builder retrieveDisabledVotingTitles(boolean retrieveDisabledVotingTitles) {
      this.retrieveDisabledVotingTitles = retrieveDisabledVotingTitles;
      return this;
    }

    public Builder retrieveHiddenModules(boolean retrieveHiddenModules) {
      this.retrieveHiddenModules = retrieveHiddenModules;
      return this;
    }

    public Builder consistentRead(boolean consistentRead) {
      this.consistentRead = consistentRead;
      return this;
    }

    public APIUIPersonalizationRequest build() {
      return new APIUIPersonalizationRequest(
          user,
          retrieveVideoMerchDisabled,
          retrieveSecondaryLanguages,
          retrieveDisabledVotingTitles,
          retrieveHiddenModules,
          consistentRead);
    }

    public String toString() {
      return "APIUserPersonalizationRequest.Builder(user="
          + this.user
          + ", retrieveVideoMerchDisabled="
          + this.retrieveVideoMerchDisabled
          + ", retrieveSecondaryLanguages="
          + this.retrieveSecondaryLanguages
          + ", retrieveDisabledVotingTitles="
          + this.retrieveDisabledVotingTitles
          + ", retrieveHiddenModules="
          + this.retrieveHiddenModules
          + ", consistentRead="
          + this.consistentRead
          + ")";
    }
  }
}
