# yaml-language-server: $schema=../../../../../api/structured/generated/v1/MeshRoot.json
# this mesh config will get merge with the mesh config generated by SBN
apiVersion: "v1"
spec:
  meshServers:
    - name: sbn
      config:
        generalOptions:
          operation:
            resiliencyOptions:
              prioritizedCpuLoadShedding:
                simulateMode: true
                cpuToStartSheddingBulk: 80
                cpuToShedAllBulk: 81
                cpuToStartSheddingBestEffort: 80
                cpuToShedAllBestEffort: 81
                cpuToStartSheddingDegraded: 80
                cpuToShedAllDegraded: 81
                cpuToStartSheddingCritical: 81
                cpuToShedAllCritical: 90
