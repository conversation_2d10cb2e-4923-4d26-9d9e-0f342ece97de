package com.netflix.api.platform.http;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.WriteListener;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;
import java.io.ByteArrayOutputStream;
import java.io.CharArrayWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import org.jetbrains.annotations.NotNull;

public class CapturingResponseWrapper extends HttpServletResponseWrapper {
  private int status;
  private BufferingServletOutputStream outStream;
  private BufferingPrintWriter writer;
  private static final int BUFFER_SIZE = 8 * 1024;
  private final Map<String, String> cookies = new HashMap<>();
  private final LinkedList<String> contentTypes = new LinkedList<>();

  public CapturingResponseWrapper(HttpServletResponse response) {
    super(response);
  }

  @Override
  public int getStatus() {
    return status;
  }

  public Map<String, String> getCookies() {
    return cookies;
  }

  public List<String> getContentTypes() {
    return contentTypes;
  }

  @Override
  public String getContentType() {
    return contentTypes.peekLast();
  }

  @Override
  public void setContentType(String type) {
    if (type != null) {
      contentTypes.add(type);
    }
  }

  @Override
  public void addCookie(Cookie cookie) {
    cookies.put(cookie.getName(), cookie.getValue());
    super.addCookie(cookie);
  }

  @Override
  public synchronized ServletOutputStream getOutputStream() {
    if (outStream == null) {
      if (writer != null) {
        throw new IllegalStateException("getWriter() already called");
      }
      outStream = new BufferingServletOutputStream(BUFFER_SIZE);
    }
    return outStream;
  }

  @Override
  public synchronized PrintWriter getWriter() {
    if (writer == null) {
      if (outStream != null) {
        throw new IllegalStateException("getOutputStream() already called");
      }
      writer = new BufferingPrintWriter(BUFFER_SIZE);
    }
    return writer;
  }

  @Override
  public void flushBuffer() {
    // if buffering, ignore explicit flush requests
  }

  @Override
  public synchronized void resetBuffer() {
    if (outStream != null) {
      outStream.resetBuffer();
    }
    if (writer != null) {
      writer.resetBuffer();
    }
  }

  @Override
  public synchronized void reset() {
    super.reset();
    if (outStream != null) {
      outStream.reset();
    }
    if (writer != null) {
      writer.reset();
    }
  }

  private static final class BufferingServletOutputStream extends ServletOutputStream {
    private final ByteArrayOutputStream buffer;

    BufferingServletOutputStream(int initialSize) {
      buffer = new ByteArrayOutputStream(initialSize);
    }

    @Override
    public void close() {}

    @Override
    public void flush() {}

    @Override
    public void write(int b) {
      buffer.write(b);
    }

    @Override
    public void write(@NotNull byte[] b) throws IOException {
      buffer.write(b);
    }

    @Override
    public void write(@NotNull byte[] b, int off, int len) {
      buffer.write(b, off, len);
    }

    void resetBuffer() {
      buffer.reset();
    }

    void reset() {
      buffer.reset();
    }

    @Override
    public boolean isReady() {
      throw new UnsupportedOperationException("Async IO not supported");
    }

    @Override
    public void setWriteListener(WriteListener writeListener) {
      throw new UnsupportedOperationException("Async IO not supported");
    }
  }

  private static final class BufferingPrintWriter extends PrintWriter {
    private final CharArrayWriter buffer;

    BufferingPrintWriter(int initialSize) {
      this(new CharArrayWriter(initialSize));
    }

    private BufferingPrintWriter(CharArrayWriter buffer) {
      super(buffer);
      this.buffer = buffer;
    }

    void resetBuffer() {
      buffer.reset();
    }

    void reset() {
      buffer.reset();
    }
  }

  @Override
  public void sendError(final int sc) {
    noteStatus(sc);
  }

  @Override
  public void sendError(final int sc, final String msg) {
    noteStatus(sc);
  }

  @Override
  public void sendRedirect(final String location) {
    throw new UnsupportedOperationException("Redirects not supported.");
  }

  @Override
  public void setStatus(int sc) {
    noteStatus(sc);
  }

  private void noteStatus(int sc) {
    this.status = sc;
  }
}
