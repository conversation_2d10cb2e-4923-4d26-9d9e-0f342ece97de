package com.netflix.api.platform.context;

import com.netflix.api.platform.context.RequestContextWrapper.Builder;
import com.netflix.i18n.NFLocale;
import com.netflix.type.DeviceType;
import com.netflix.type.ISOCountry;
import java.util.Objects;
import java.util.regex.Pattern;

public class LockFreeImmutableRequestContext implements ImmutableRequestContext {

  private static final Pattern comma = Pattern.compile(",");

  private static String getFirstLocale(String localeList) {
    if (localeList == null || localeList.isEmpty()) return null;

    return comma.split(localeList)[0];
  }

  private final String deviceId;
  private final DeviceType deviceType;
  private final String requestId;
  private final ISOCountry country;
  private final String localeList;
  private final String firstLocale;
  private final NFLocale nfLocale;

  LockFreeImmutableRequestContext(Builder builder) {
    this.requestId = builder.requestId;
    this.country = builder.country;
    this.localeList = builder.localeList;
    this.deviceId = builder.deviceId;
    this.deviceType = builder.deviceType;
    this.firstLocale = getFirstLocale(localeList);
    this.nfLocale = this.firstLocale == null ? null : NFLocale.findInstance(this.firstLocale);
  }

  @Override
  public String getRequestId() {
    return requestId;
  }

  @Override
  public ISOCountry getCountry() {
    return country;
  }

  @Override
  public String getLocale() {
    return firstLocale;
  }

  @Override
  public NFLocale getNfLocale() {
    return nfLocale;
  }

  @Override
  public String getLocaleList() {
    return localeList;
  }

  @Override
  public DeviceType getDeviceType() {
    return deviceType;
  }

  @Override
  public String getDeviceId() {
    return deviceId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof LockFreeImmutableRequestContext that)) {
      return false;
    }
    return Objects.equals(deviceId, that.deviceId)
        && Objects.equals(deviceType, that.deviceType)
        && Objects.equals(requestId, that.requestId)
        && Objects.equals(country, that.country)
        && Objects.equals(localeList, that.localeList);
  }

  @Override
  public int hashCode() {
    return Objects.hash(deviceId, deviceType, requestId, country, localeList);
  }

  @Override
  public String toString() {
    return "LockFreeImmutableRequestContext{"
        + "deviceId='"
        + deviceId
        + '\''
        + ", deviceType="
        + deviceType
        + ", requestId='"
        + requestId
        + '\''
        + ", country="
        + country
        + ", localeList='"
        + localeList
        + '\''
        + ", firstLocale='"
        + firstLocale
        + '\''
        + ", nfLocale="
        + nfLocale
        + '}';
  }
}
