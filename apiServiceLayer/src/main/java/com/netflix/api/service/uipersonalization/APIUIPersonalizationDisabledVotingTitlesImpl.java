package com.netflix.api.service.uipersonalization;

import com.netflix.uipersonalization.protogen.DisabledVotingTitles;
import com.netflix.uipersonalization.protogen.MergeStrategy;
import java.util.List;

public class APIUIPersonalizationDisabledVotingTitlesImpl
    implements APIUIPersonalizationDisabledVotingTitles {
  private final DisabledVotingTitles disabledVotingTitles;

  APIUIPersonalizationDisabledVotingTitlesImpl(DisabledVotingTitles disabledVotingTitles) {
    this.disabledVotingTitles = disabledVotingTitles;
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#DisabledVotingTitles.title"
   */
  @Override
  public List<Integer> getTitles() {
    return this.disabledVotingTitles.getTitleList();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#DisabledVotingTitles.merge_strategy"
   */
  @Override
  public APIUIPersonalizationMergeStrategy getMergeStrategy() {
    MergeStrategy mergeStrategy = this.disabledVotingTitles.getMergeStrategy();
    if (mergeStrategy == null) {
      return null;
    }
    if (mergeStrategy == MergeStrategy.UNRECOGNIZED) {
      return APIUIPersonalizationMergeStrategy.UNRECOGNIZED;
    } else {
      try {
        return Enum.valueOf(APIUIPersonalizationMergeStrategy.class, mergeStrategy.name());
      } catch (IllegalArgumentException e) {
        return APIUIPersonalizationMergeStrategy.UNRECOGNIZED;
      }
    }
  }
}
