package dna.api.datasources.images;

import static com.netflix.api.platform.util.CompletionStageAdapter.from;
import static com.netflix.api.service.video.ImageResolverUtils.setParams;
import static java.util.Optional.ofNullable;

import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import com.google.common.primitives.Ints;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.batch.APIImageServiceImpl;
import com.netflix.api.service.batch.model.APIAvatarKey;
import com.netflix.api.service.batch.model.APIAvatarType;
import com.netflix.api.service.batch.model.APIProfileIconCriteria;
import com.netflix.api.service.video.APIArtWorkImageImpl;
import com.netflix.api.service.video.APIImage;
import com.netflix.api.service.video.APIImage.ImageExtension;
import com.netflix.api.service.video.ImageResolverUtils;
import com.netflix.demograph.datasource.batch.BatchDataSource;
import com.netflix.i18n.NFLocale;
import com.netflix.images.protogen.GetVideoImagesRequest;
import com.netflix.images.protogen.GetVideoImagesRequest.Builder;
import com.netflix.images.protogen.ImageCriteria;
import com.netflix.images.protogen.ImageDimension;
import com.netflix.images.protogen.ImageSortAlgorithm;
import com.netflix.images.protogen.MultiValueAttribute;
import com.netflix.playapi.protogen.PlayApiTrickPlayServiceGrpc;
import com.netflix.playapi.trickplay.ArchiveType;
import com.netflix.playapi.trickplay.TrickPlayFormat;
import com.netflix.playapi.trickplay.TrickPlayRequest;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.type.proto.Locales;
import com.netflix.type.proto.Videos;
import dna.api.datasources.DataSourceUtil;
import dna.api.datasources.utils.DataSources;
import dna.api.service.model.Avatar;
import dna.api.service.model.AvatarRecipe;
import dna.api.service.model.AvatarType;
import dna.api.service.model.Image;
import dna.api.service.model.ImageRecipe;
import dna.api.service.model.TrickPlay;
import dna.api.service.model.TrickPlayRecipe;
import jakarta.annotation.Nonnull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Single;

@Component
public class ImagesDataSource {

  private static final Logger log = LoggerFactory.getLogger(ImagesDataSource.class);

  private static final Image IMAGE_DEFAULT = new Image();

  // datasources (must be singletons for demograph implicit caching to work)
  private final BatchDataSource<AvatarImageRequest, APIImage> avatarImageByRecipe;
  private final BatchDataSource<VideoImageRequest, APIImage> artworkImagesByRecipe;
  private final BatchDataSource<VideoImageRequest, List<APIImage>> multipleArtworkImagesByRecipe;
  private final BatchDataSource<VideoImageRequest, APIImage> interestingMomentsByRecipe;
  private final BatchDataSource<CharacterImageRequest, APIImage> characterArtworkByRecipe;
  private final BatchDataSource<TrickPlayInfoRequest, TrickPlay> trickPlayByRecipePlayApi;
  private final BatchDataSource<PersonImageRequest, APIImage> personImagesByRecipe;

  @Autowired
  ImagesDataSource(
      ImageResolverUtils imageResolverUtils,
      APIImageServiceImpl imageService,
      @GrpcSpringClient("playapi")
          PlayApiTrickPlayServiceGrpc.PlayApiTrickPlayServiceStub trickplay) {

    // init datasources
    avatarImageByRecipe = _avatarImageByRecipe(imageService);
    artworkImagesByRecipe = _artworkImagesByRecipe(imageResolverUtils, imageService);
    multipleArtworkImagesByRecipe =
        _multipleArtworkImagesByRecipe(imageResolverUtils, imageService);
    interestingMomentsByRecipe = _interestingMomentsByRecipe(imageResolverUtils, imageService);
    characterArtworkByRecipe = _characterArtworkByRecipe(imageResolverUtils, imageService);
    trickPlayByRecipePlayApi = _trickPlayByRecipePlayApiGrpc(trickplay);
    personImagesByRecipe = _personImagesByRecipe(imageResolverUtils, imageService);
  }

  // datasources public accessors to support mocking / testing

  public BatchDataSource<AvatarImageRequest, APIImage> avatarImageByRecipe() {
    return avatarImageByRecipe;
  }

  public BatchDataSource<VideoImageRequest, APIImage> artworkImagesByRecipe() {
    return artworkImagesByRecipe;
  }

  public BatchDataSource<PersonImageRequest, APIImage> personImagesByRecipe() {
    return personImagesByRecipe;
  }

  public BatchDataSource<VideoImageRequest, List<APIImage>> multipleArtworkImagesByRecipe() {
    return multipleArtworkImagesByRecipe;
  }

  public BatchDataSource<VideoImageRequest, APIImage> interestingMomentsByRecipe() {
    return interestingMomentsByRecipe;
  }

  public BatchDataSource<CharacterImageRequest, APIImage> characterArtworkByRecipe() {
    return characterArtworkByRecipe;
  }

  public BatchDataSource<TrickPlayInfoRequest, TrickPlay> trickPlayByRecipe() {
    return trickPlayByRecipePlayApi;
  }

  // datasource initializers

  private BatchDataSource<AvatarImageRequest, APIImage> _avatarImageByRecipe(
      APIImageServiceImpl imageService) {
    return DataSources.of(
        "avatarImageByRecipe",
        (ctx, avatarRequests) -> {
          Map<AvatarRecipe, Set<APIAvatarKey>> batches =
              DataSourceUtil.buildMultiValuedMap(
                  avatarRequests,
                  AvatarImageRequest::recipe,
                  req ->
                      APIAvatarKey.builder()
                          .key(req.name())
                          .kids(req.kids())
                          .locale(req.locale())
                          .build(),
                  v -> new HashSet<>());

          return from(
              Observable.from(batches.entrySet())
                  .flatMap(
                      batch -> {
                        AvatarRecipe recipe = batch.getKey();
                        APIProfileIconCriteria criteria =
                            APIProfileIconCriteria.newBuilder()
                                .setWidth(recipe.getWidth())
                                .setHeight(recipe.getHeight())
                                .setSecure(recipe.getSecureImageUrl())
                                .setParams(recipe.getParams())
                                .setRecipePreferences(recipe.getRecipePreferences())
                                .setAvatarType(
                                    APIAvatarType.from(
                                        Optional.ofNullable(recipe.getAvatarType())
                                            .map(AvatarType::name)
                                            .orElse(AvatarType.AVATAR_TYPE_DEFAULT.name())))
                                .build();
                        return imageService
                            .getProfileIconsByKey(batch.getValue(), criteria)
                            .onErrorResumeNext(ignore -> Single.just(Collections.emptyMap()))
                            .map(
                                avatars -> {
                                  Map<AvatarImageRequest, APIImage> map =
                                      HashMap.newHashMap(avatars.size());
                                  for (Entry<APIAvatarKey, APIImage> entry : avatars.entrySet()) {
                                    map.put(
                                        new AvatarImageRequest(
                                            entry.getKey().getKey(),
                                            entry.getKey().isKids(),
                                            entry.getKey().getLocale(),
                                            recipe),
                                        entry.getValue());
                                  }
                                  return map;
                                })
                            .toObservable();
                      })
                  .reduce(
                      (a, c) -> {
                        a.putAll(c);
                        return a;
                      })
                  .toSingle());
        });
  }

  private BatchDataSource<PersonImageRequest, APIImage> _personImagesByRecipe(
      ImageResolverUtils imageResolverUtils, APIImageServiceImpl imageService) {
    return DataSources.of(
        "personImagesByRecipe",
        (ctx, imageRequests) -> {
          Map<ImageRecipe, Set<Long>> batches =
              DataSourceUtil.buildMultiValuedMap(
                  imageRequests,
                  PersonImageRequest::recipe,
                  PersonImageRequest::personId,
                  v -> new HashSet<>());

          return from(
              Observable.from(batches.entrySet())
                  .flatMap(
                      batch -> {
                        Single<ImageCriteria.Builder> criteria =
                            getEntityImageCriteria(imageResolverUtils, batch.getKey());
                        return getPersonImage(imageService, batch, criteria);
                      })
                  .reduce(
                      (a, c) -> {
                        a.putAll(c);
                        return a;
                      }));
        });
  }

  private BatchDataSource<VideoImageRequest, APIImage> _artworkImagesByRecipe(
      ImageResolverUtils imageResolverUtils, APIImageServiceImpl imageService) {
    return DataSources.of(
        "artworkImagesByRecipe",
        (ctx, imageRequests) -> {
          Map<ImageRecipe, Set<Integer>> batches =
              DataSourceUtil.buildMultiValuedMap(
                  imageRequests,
                  VideoImageRequest::recipe,
                  VideoImageRequest::videoId,
                  v -> new HashSet<>());

          return from(
              Observable.from(batches.entrySet())
                  .flatMap(
                      batch -> {
                        Single<com.netflix.images.protogen.GetVideoImagesRequest.Builder> criteria =
                            getVideoArtworkImageResolverCriteria(
                                imageResolverUtils, batch.getKey(), batch.getValue(), false);
                        return imageService
                            .getVideoArtwork(batch.getValue(), criteria)
                            .onErrorResumeNext(
                                e -> {
                                  // should have served a fallback, this should never happen
                                  log.error("error getting video images", e);
                                  return Single.just(Collections.emptyMap());
                                })
                            .map(
                                imagesByVideoId ->
                                    imagesByVideoId.entrySet().stream()
                                        .collect(
                                            Collectors.toMap(
                                                (Map.Entry<Integer, APIImage> entry) ->
                                                    new VideoImageRequest(
                                                        entry.getKey(), batch.getKey()),
                                                Map.Entry::getValue)))
                            .toObservable();
                      })
                  .reduce(
                      (a, c) -> {
                        a.putAll(c);
                        return a;
                      }));
        });
  }

  private BatchDataSource<VideoImageRequest, List<APIImage>> _multipleArtworkImagesByRecipe(
      ImageResolverUtils imageResolverUtils, APIImageServiceImpl imageService) {
    return DataSources.of(
        "multipleArtworkImagesByRecipe",
        (ctx, imageRequests) -> {
          Map<ImageRecipe, Set<Integer>> batches =
              DataSourceUtil.buildMultiValuedMap(
                  imageRequests,
                  VideoImageRequest::recipe,
                  VideoImageRequest::videoId,
                  v -> new HashSet<>());

          return from(
              Observable.from(batches.entrySet())
                  .flatMap(
                      batch -> {
                        Single<com.netflix.images.protogen.GetVideoImagesRequest.Builder> criteria =
                            getVideoArtworkImageResolverCriteria(
                                imageResolverUtils, batch.getKey(), batch.getValue(), true);
                        return imageService
                            .getMultipleVideoArtwork(batch.getValue(), criteria)
                            .onErrorResumeNext(
                                e -> {
                                  // should have served a fallback, this should never happen
                                  log.error("error getting video images", e);
                                  return Single.just(Collections.emptyMap());
                                })
                            .map(
                                imagesByVideoId ->
                                    imagesByVideoId.entrySet().stream()
                                        .collect(
                                            Collectors.toMap(
                                                (Map.Entry<Integer, List<APIImage>> entry) ->
                                                    new VideoImageRequest(
                                                        entry.getKey(), batch.getKey()),
                                                Map.Entry::getValue)))
                            .toObservable();
                      })
                  .reduce(
                      (a, c) -> {
                        a.putAll(c);
                        return a;
                      }));
        });
  }

  private BatchDataSource<VideoImageRequest, APIImage> _interestingMomentsByRecipe(
      ImageResolverUtils imageResolverUtils, APIImageServiceImpl imageService) {
    return DataSources.of(
        "interestingMomentsByRecipe",
        (ctx, imageRequests) -> {
          Map<ImageRecipe, Set<Integer>> batches =
              DataSourceUtil.buildMultiValuedMap(
                  imageRequests,
                  VideoImageRequest::recipe,
                  VideoImageRequest::videoId,
                  v -> new HashSet<>());

          return from(
              Observable.from(batches.entrySet())
                  .flatMap(
                      batch -> {
                        Single<ImageCriteria> criteria =
                            getStillsImageResolverCriteria(
                                imageResolverUtils, batch.getKey(), batch.getValue());
                        return imageService
                            .getStillImages(batch.getValue(), criteria)
                            .onErrorResumeNext(
                                e -> {
                                  log.error("error getting interesting moments", e);
                                  return Single.just(Collections.emptyMap());
                                })
                            .map(
                                momentsByVideoId -> {
                                    int size = momentsByVideoId.size();
                                    return momentsByVideoId.entrySet().stream()
                                        .collect(
                                            Collectors.toMap(
                                                (Map.Entry<Integer, APIImage> entry) ->
                                                    new VideoImageRequest(entry.getKey(), batch.getKey()),
                                                Map.Entry::getValue,
                                                (existing, replacement) -> existing, // merge function
                                                () -> new HashMap<>(size)));
                                })
                            .toObservable();
                      })
                  .reduce(
                      (a, c) -> {
                        a.putAll(c);
                        return a;
                      }));
        });
  }

  private BatchDataSource<CharacterImageRequest, APIImage> _characterArtworkByRecipe(
      ImageResolverUtils imageResolverUtils, APIImageServiceImpl imageService) {
    return DataSources.of(
        "characterArtworkByRecipe",
        (ctx, imageRequests) -> {
          Map<ImageRecipe, Set<Integer>> batches =
              DataSourceUtil.buildMultiValuedMap(
                  imageRequests,
                  CharacterImageRequest::recipe,
                  r -> r.characterId().intValue(),
                  v -> new HashSet<>());

          return from(
              Observable.from(batches.entrySet())
                  .flatMap(
                      batch -> {
                        Single<ImageCriteria.Builder> criteria =
                            getEntityImageCriteria(imageResolverUtils, batch.getKey());
                        return getCharacterImage(imageService, batch, criteria);
                      })
                  .reduce(
                      (a, c) -> {
                        a.putAll(c);
                        return a;
                      }));
        });
  }

  private static Observable<Map<CharacterImageRequest, APIImage>> getCharacterImage(
      APIImageServiceImpl imageService,
      Entry<ImageRecipe, Set<Integer>> batch,
      Single<ImageCriteria.Builder> criteria) {
    return imageService
        .getCharacterArtWorkRemote(batch.getValue(), criteria)
        .onErrorResumeNext(
            e -> {
              log.error("error getting character images", e);
              return Single.just(Collections.emptyMap());
            })
        .map(
            imagesByCharacterId ->
                imagesByCharacterId.entrySet().stream()
                    .collect(
                        Collectors.toMap(
                            (Map.Entry<Integer, APIImage> entry) ->
                                new CharacterImageRequest(
                                    entry.getKey().longValue(), batch.getKey()),
                            Map.Entry::getValue)))
        .toObservable();
  }

  private static Observable<Map<PersonImageRequest, APIImage>> getPersonImage(
      APIImageServiceImpl imageService,
      Entry<ImageRecipe, Set<Long>> batch,
      Single<ImageCriteria.Builder> criteria) {
    return imageService
        .getPersonArtWorkRemote(batch.getValue(), criteria)
        .onErrorResumeNext(
            e -> {
              log.error("error getting person images", e);
              return Single.just(Collections.emptyMap());
            })
        .map(
            imagesByPersonId ->
                imagesByPersonId.entrySet().stream()
                    .collect(
                        Collectors.toMap(
                            (Map.Entry<Long, APIImage> entry) ->
                                new PersonImageRequest(entry.getKey(), batch.getKey()),
                            Entry::getValue)))
        .toObservable();
  }

  private BatchDataSource<TrickPlayInfoRequest, TrickPlay> _trickPlayByRecipePlayApiGrpc(
      PlayApiTrickPlayServiceGrpc.PlayApiTrickPlayServiceStub trickplay) {

    return DataSources.of(
        "trickPlayByRecipePlayApiGrpc",
        (ctx, trickPlayInfoRequests) -> {

          // FIXME use video ids and precheck the same things as in collectTrickplayVideos
          Map<TrickPlayRecipe, Set<Integer>> batches =
              DataSourceUtil.buildMultiValuedMap(
                  trickPlayInfoRequests,
                  TrickPlayInfoRequest::recipe,
                  TrickPlayInfoRequest::videoId,
                  v -> new HashSet<>());

          return from(
              Observable.from(batches.entrySet())
                  .flatMap(
                      batch -> {
                        var request =
                            TrickPlayRequest.newBuilder()
                                .addAllVideoIds(batch.getValue())
                                .setCriteria(getTrickPlayCriteria(batch.getKey()))
                                .build();
                        log.debug("trickplay request:  {}", request);

                        return RxObservable.defer(trickplay::getTrickPlay, request)
                            .map(
                                reply -> {
                                  log.debug("trickplay reply: {}", reply);

                                  var packs = reply != null ? reply.getResultsList() : null;
                                  Map<TrickPlayInfoRequest, TrickPlay> map =
                                      emptyTrickplayMap(batch.getKey(), batch.getValue());

                                  if (packs == null) {
                                    return map;
                                  }

                                  // for each video in batch
                                  for (Integer videoId : batch.getValue()) {
                                    // go through results and extract all the ones matching the
                                    // current video id
                                    List<com.netflix.playapi.trickplay.TrickPlayImagePack>
                                        matchingPacks = new ArrayList<>();
                                    for (var pack : packs) {
                                      if (pack.getVideoId() == videoId) {

                                        matchingPacks.add(pack.getPack());
                                      }
                                    }

                                    // build trickplay for this video, taking "secure" flag from
                                    // recipe into account
                                    var mapKey = new TrickPlayInfoRequest(videoId, batch.getKey());
                                    map.put(
                                        mapKey,
                                        toPlayApiTrickplayInfo(
                                            matchingPacks, batch.getKey().getSecure()));
                                  }
                                  return map;
                                })
                            .onErrorReturn(
                                e -> emptyTrickplayMap(batch.getKey(), batch.getValue()));
                      })
                  .reduce(
                      (a, c) -> {
                        a.putAll(c);
                        return a;
                      })
                  .onErrorResumeNext(
                      e -> {
                        log.info("error getting trickplay info", e);
                        return Observable.just(Collections.emptyMap());
                      }));
        });
  }

  private static TrickPlay toPlayApiTrickplayInfo(
      List<com.netflix.playapi.trickplay.TrickPlayImagePack> packs, Boolean secureImageUrl) {
    com.netflix.playapi.trickplay.TrickPlayImagePack zipPack = null;
    com.netflix.playapi.trickplay.TrickPlayImagePack bifPack = null;
    TrickPlay trickPlayInfo = new TrickPlay();
    for (var pack : packs) {
      if ("ZIP".equalsIgnoreCase(pack.getRecipeName())) {
        zipPack = pack;
      } else if ("BIF".equalsIgnoreCase(pack.getRecipeName())) {
        bifPack = pack;
      }
    }
    if (zipPack != null && bifPack != null) {
      trickPlayInfo.setBaseUrl(secureImageUrl ? zipPack.getSecureUrl() : zipPack.getUrl());
      trickPlayInfo.setBifUrl(secureImageUrl ? bifPack.getSecureUrl() : bifPack.getUrl());
      trickPlayInfo.setFrameInterval(zipPack.getFrameInterval());
      trickPlayInfo.setHeight(zipPack.getHeight());
      trickPlayInfo.setWidth(zipPack.getWidth());
      trickPlayInfo.setImageCount(zipPack.getImageCount());
    } else if (zipPack != null) {
      trickPlayInfo.setBaseUrl(secureImageUrl ? zipPack.getSecureUrl() : zipPack.getUrl());
      // no bif url
      trickPlayInfo.setFrameInterval(zipPack.getFrameInterval());
      trickPlayInfo.setHeight(zipPack.getHeight());
      trickPlayInfo.setWidth(zipPack.getWidth());
      trickPlayInfo.setImageCount(zipPack.getImageCount());
    } else if (bifPack != null) {
      // handle BIF only
      trickPlayInfo.setBifUrl(secureImageUrl ? bifPack.getSecureUrl() : bifPack.getUrl());
      trickPlayInfo.setFrameInterval(bifPack.getFrameInterval());
      trickPlayInfo.setHeight(bifPack.getHeight());
      trickPlayInfo.setWidth(bifPack.getWidth());
      trickPlayInfo.setImageCount(bifPack.getImageCount());
    }
    log.debug(trickPlayInfo.toString());
    return trickPlayInfo;
  }

  private static Map<TrickPlayInfoRequest, TrickPlay> emptyTrickplayMap(
      TrickPlayRecipe r, Set<Integer> batch) {
    Map<TrickPlayInfoRequest, TrickPlay> map = new HashMap<>();

    // must have results for all requested videos
    for (Integer videoId : batch) {
      map.put(new TrickPlayInfoRequest(videoId, r), null);
    }
    return map;
  }

  private static Single<com.netflix.images.protogen.GetVideoImagesRequest.Builder>
      getVideoArtworkImageResolverCriteria(
          ImageResolverUtils imageResolverUtils,
          ImageRecipe imageRecipe,
          Set<Integer> videoIds,
          boolean multipleArtwork) {
    return imageResolverUtils
        .asyncSetRequestLevelCriteria()
        .map(
            requestLevelCriteria -> {

              // Build criteria
              ImageCriteria.Builder criteriaBuilder = ImageCriteria.newBuilder();
              criteriaBuilder.mergeFrom(requestLevelCriteria);

              // explicit opt-in takes precedence over FP
              if (imageRecipe.getPerformNewContentCheck() != null) {
                criteriaBuilder.setPerformNewContentCheck(imageRecipe.getPerformNewContentCheck());
              } else
                criteriaBuilder.setPerformNewContentCheck(
                    ImageResolverUtils.PERFORM_NEW_CONTENT_CHECK.get());
              criteriaBuilder.setType(imageRecipe.getArtworkType());

              if (imageRecipe.getAvailableOnly() != null) {
                criteriaBuilder.setGraybox(!imageRecipe.getAvailableOnly());
              }

              List<String> recipes = imageRecipe.getRecipePreferences();
              if (recipes != null) {
                criteriaBuilder.addAllRecipes(recipes);
              }

              criteriaBuilder = setAttributesAndParams(imageRecipe, criteriaBuilder);

              if (multipleArtwork) {
                criteriaBuilder.putParams("artwork_multiple", "true");
              }

              criteriaBuilder = setSizeConstraints(imageRecipe, criteriaBuilder);

              // locale overrides - DNA-506
              if (imageRecipe.getLocaleOverrides() != null
                  && !imageRecipe.getLocaleOverrides().isEmpty()) {
                criteriaBuilder.clearLocales();
                for (String locale : imageRecipe.getLocaleOverrides()) {
                  criteriaBuilder.addLocales(
                      Locales.toProtobuf(NFLocale.findInstance(locale).toLocale()));
                }
              }

              return buildVideoImagesRequest(videoIds, criteriaBuilder);
            });
  }

  private static ImageCriteria.Builder setAttributesAndParams(
      ImageRecipe imageRecipe, ImageCriteria.Builder criteriaBuilder) {

    final Multimap<String, String> attributes =
        imageRecipe.getAttributes() != null
            ? ImmutableMultimap.copyOf(imageRecipe.getAttributes().entrySet())
            : null;
    if (attributes != null) {
      attributes.asMap().entrySet().stream()
          .filter(
              e ->
                  e.getKey() != null
                      && !e.getKey().isEmpty()
                      && e.getValue() != null
                      && !e.getValue().isEmpty())
          .forEach(
              e -> {
                MultiValueAttribute mva =
                    MultiValueAttribute.newBuilder().addAllValue(e.getValue()).build();
                criteriaBuilder.putMultiAttributes(e.getKey(), mva);
              });
    }
    return setParams(imageRecipe.getParams(), criteriaBuilder);
  }

  private static Builder buildVideoImagesRequest(
      Set<Integer> videoIds, final ImageCriteria.Builder criteriaBuilder) {
    // Build request
    final GetVideoImagesRequest.Builder requestBuilder = GetVideoImagesRequest.newBuilder();
    videoIds.forEach(id -> requestBuilder.addVideos(Videos.toProtobuf(id)));
    requestBuilder.setCriteria(criteriaBuilder.build());
    return requestBuilder;
  }

  private static Single<ImageCriteria> getStillsImageResolverCriteria(
      ImageResolverUtils imageResolverUtils, ImageRecipe imageRecipe, Set<Integer> videoIds) {

    // create backend request
    Collection<APIImage.ImageExtension> extensionPreferences =
        ofNullable(imageRecipe)
            .flatMap(it -> ofNullable(it.getRecipePreferences()))
            .filter(
                it ->
                    !it.isEmpty()) // if empty - don't even perform streaming and mapping fall back
            // to defaults
            .map(it -> it.stream().map(ImageExtension::valueOf).toList())
            .orElse(List.of(ImageExtension.jpg));

    List<String> recipes = new ArrayList<>();
    for (ImageExtension ext : extensionPreferences) {
      recipes.add(ext.name());
    }

    return imageResolverUtils
        .asyncSetRequestLevelCriteria()
        .map(
            requestLevelCriteria -> {

              // Build criteria
              ImageCriteria.Builder criteriaBuilder = ImageCriteria.newBuilder();
              criteriaBuilder.mergeFrom(requestLevelCriteria);

              criteriaBuilder.setType("MERCH_STILL");
              criteriaBuilder.addAllRecipes(recipes);

              // we are within a batch, so all videoIds have exactly the same recipe
              if (imageRecipe != null) {
                criteriaBuilder = setSizeConstraints(imageRecipe, criteriaBuilder);
                criteriaBuilder = setParams(imageRecipe.getParams(), criteriaBuilder);
              }

              return criteriaBuilder.build();
            });
  }

  private static ImageCriteria.Builder setSizeConstraints(
      ImageRecipe imageRecipe, final ImageCriteria.Builder criteriaBuilder) {

    if (imageRecipe.getWidth() != null) {
      criteriaBuilder.setWidth(imageRecipe.getWidth());
    }
    if (imageRecipe.getWidthVariance() != null) {
      criteriaBuilder.setWidthVariance(imageRecipe.getWidthVariance());
    }
    if (imageRecipe.getHeight() != null) {
      criteriaBuilder.setHeight(imageRecipe.getHeight());
    }
    if (imageRecipe.getHeightVariance() != null) {
      criteriaBuilder.setHeightVariance(imageRecipe.getHeightVariance());
    }
    boolean useHeight = imageRecipe.getWidth() == null && imageRecipe.getHeight() != null;
    criteriaBuilder.setPreferredDimension(useHeight ? ImageDimension.HEIGHT : ImageDimension.WIDTH);

    criteriaBuilder.setSortAlgorithm(getBestMatchAlgorithm(imageRecipe.getSizeMatchAlgorithm()));

    // choose algorithm is always true, since a null algo defaults to closestMatch in API
    criteriaBuilder.setIsChooseAlgorithm(true);

    return criteriaBuilder;
  }

  private static Single<ImageCriteria.Builder> getEntityImageCriteria(
      ImageResolverUtils imageResolverUtils, ImageRecipe imageRecipe) {
    return imageResolverUtils
        .asyncSetRequestLevelCriteria()
        .map(
            requestLevelCriteria -> {

              // Build criteria
              ImageCriteria.Builder criteriaBuilder = ImageCriteria.newBuilder();
              criteriaBuilder.mergeFrom(requestLevelCriteria);

              criteriaBuilder.setType(imageRecipe.getArtworkType());

              if (imageRecipe.getRecipePreferences() != null) {
                criteriaBuilder.addAllRecipes(imageRecipe.getRecipePreferences());
              }

              criteriaBuilder = setAttributesAndParams(imageRecipe, criteriaBuilder);

              criteriaBuilder = setSizeConstraints(imageRecipe, criteriaBuilder);

              return criteriaBuilder;
            });
  }

  private static com.netflix.playapi.trickplay.TrickPlayCriteria getTrickPlayCriteria(
      TrickPlayRecipe request) {

    var locales =
        RequestContextWrapper.convertLocales(RequestContextWrapper.get().getLocaleList()).stream()
            .map(NFLocale::getId)
            .collect(Collectors.toList());

    var criteria =
        com.netflix.playapi.trickplay.TrickPlayCriteria.newBuilder()
            .addAllLocales(locales)
            .setFormat(convertTrickplayFormat(request.getTrickPlayFormat()));

    if (request.getArchiveType() != null) {
      criteria.addArchiveTypes(ArchiveType.valueOf(request.getArchiveType()));
    } else {
      criteria.addArchiveTypes(ArchiveType.BIF);
      criteria.addArchiveTypes(ArchiveType.ZIP);
    }

    return criteria.build();
  }

  private static TrickPlayFormat convertTrickplayFormat(String trickPlayFormat) {
    // not a 1:1 match for legacy reasons
    return switch (trickPlayFormat) {
      case "SD", "W240" -> TrickPlayFormat.W240;
      case "HD", "W320" -> TrickPlayFormat.W320;
      case "W640" -> TrickPlayFormat.W640;
      default -> TrickPlayFormat.UNRECOGNIZED;
    };
  }

  private static ImageSortAlgorithm getBestMatchAlgorithm(String sizeMatchAlgorithm) {
    if (sizeMatchAlgorithm == null) {
      return ImageSortAlgorithm.CLOSEST_MATCH;
    }
    return switch (sizeMatchAlgorithm) {
      case "CLOSEST_MATCH" -> ImageSortAlgorithm.CLOSEST_MATCH;
      case "SMALLER_MATCH" -> ImageSortAlgorithm.SMALLER_MATCH;
      case "LARGER_MATCH" -> ImageSortAlgorithm.LARGER_MATCH;
      default -> throw new RuntimeException("Unknown size match algorithm " + sizeMatchAlgorithm);
    };
  }

  public static Image convertApiImageToImageResponse(APIImage image, ImageRecipe imageRecipe) {
    if (image == null) {
      return IMAGE_DEFAULT;
    }
    return convertApiImageToImageResponse(
        image.getImageTypeIdentifier(),
        image,
        Optional.ofNullable(imageRecipe.getSecureImageUrl()).orElse(true),
        imageRecipe.getArtworkType());
  }

  public static Image convertApiImageToImageResponse(APIImage image, boolean isSecureUrl) {
    if (image == null) {
      return IMAGE_DEFAULT;
    }
    return convertApiImageToImageResponse(image.getImageTypeIdentifier(), image, isSecureUrl, "");
  }

  public static Avatar convertApiImageToAvatarResponse(
      APIImage image, boolean isSecureUrl, String iconName) {
    if (image == null) {
      return new Avatar().setIsInDefaultSet(isIconInDefaultSet(iconName));
    }
    return new Avatar()
        .setImage(
            convertApiImageToImageResponse(image.getImageTypeIdentifier(), image, isSecureUrl, "")
                .setKey(image.getImageTypeIdentifier()))
        .setIsInDefaultSet(isIconInDefaultSet(iconName));
  }

  private static boolean isIconInDefaultSet(String iconName) {
    return getIconNumberFromName(iconName).map(i -> i >= 25 && i <= 29).orElse(false);
  }

  private static Optional<Integer> getIconNumberFromName(@Nonnull String iconName) {
    return Optional.ofNullable(Ints.tryParse(iconName.substring(4)));
  }

  public static List<Image> convertApiImagesToImageResponse(
      List<APIImage> images, ImageRecipe imageRecipe) {
    List<Image> results = new ArrayList<>();
    for (APIImage image : images) {
      results.add(
          convertApiImageToImageResponse(
              image.getImageTypeIdentifier(),
              image,
              Optional.ofNullable(imageRecipe.getSecureImageUrl()).orElse(true),
              imageRecipe.getArtworkType()));
    }
    return results;
  }

  @SuppressWarnings("unchecked")
  public static Image convertApiImageToImageResponse(
      String key, APIImage apiImage, boolean isSecureUrl, String requestedArtworkType) {
    if (apiImage == null) {
      return IMAGE_DEFAULT;
    }
    Image result =
        new Image()
            .setKey(key)
            .setUrl(apiImage.getUrl())
            .setWidth(apiImage.getWidth())
            .setUrl(apiImage.getUrl(isSecureUrl))
            .setHeight(apiImage.getHeight())
            .setExtension(apiImage.getExtension().name())
            .setAttributes(apiImage.getAttributes())
            .setMultiValueAttributes((Map) apiImage.getMultiValueAttributes())
            .setIsSmoky(apiImage.isSmoky())
            .setIsRightToLeft(apiImage.isRightToLeft())
            .setSourceFileId(apiImage.getSourceFileId())
            .setImageTypeIdentifier(apiImage.getImageTypeIdentifier());

    if (apiImage instanceof APIArtWorkImageImpl) {
      com.netflix.images.protogen.Image source = ((APIArtWorkImageImpl) apiImage).getSource();
      if (source != null) {
        result.setAvailable(source.getIsAvailable()).setKey(source.getKey());
      }
      if (source.getIsAvailable()) {
        result.setType(source.getType().getValue());
      } else {
        result.setType(requestedArtworkType);
      }
    } else {
      result.setType(requestedArtworkType);
    }
    return result;
  }
}
