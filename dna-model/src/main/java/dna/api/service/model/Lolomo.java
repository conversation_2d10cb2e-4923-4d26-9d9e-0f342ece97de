package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class <PERSON><PERSON><PERSON> implements GraphData {
  public Map<String, AnnotatedList> billboardVideos;

  public Map<String, LolomoCharactersList> charactersList;

  public Map<String, AnnotatedList> refreshVolatileContent;

  public Map<String, AnnotatedList> newLolomo;

  public Map<String, AnnotatedList> preAppLolomo;

  public Map<String, AnnotatedList> newReleases;

  public Map<String, LolomoVideosList> videosList;

  public Map<String, LolomoGamesList> gamesList;

  public Map<String, AnnotatedList> refreshAdaptiveContent;
}
