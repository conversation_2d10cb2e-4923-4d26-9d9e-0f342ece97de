package com.netflix.api.service.vhs;

import com.google.common.collect.Lists;
import com.google.protobuf.util.Timestamps;
import com.netflix.api.dependencies.device.DeviceTypeFactory;
import com.netflix.api.dependencies.device.DeviceTypeRequest;
import com.netflix.api.dependencies.vhs.ViewingHistoryRequestBuilder;
import com.netflix.api.grpc.GrpcCallHelpers.Future;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.service.account.APIAccountManagementServiceImpl;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserInternal;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.api.service.vhs.async.EnqueuePlaybackEventProvider;
import com.netflix.api.service.video.APIVideo;
import com.netflix.api.service.video.APIVideoFactory;
import com.netflix.api.util.ObservableFactory;
import com.netflix.archaius.api.Property;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.onramp.protogen.AddOnrampSelectionRequest;
import com.netflix.onramp.protogen.OnrampSelection;
import com.netflix.onramp.protogen.OnrampSelection.Builder;
import com.netflix.onramp.protogen.OnrampServiceGrpc.OnrampServiceStub;
import com.netflix.playapi.events.streaming.IdentityInfo;
import com.netflix.playapi.events.user.ViewingHistoryHideRequest;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Registry;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.streaming.dts.common.model.DeviceType;
import com.netflix.streamsaccountingservice.protogen.ActiveSessionInfo;
import com.netflix.streamsaccountingservice.protogen.ActiveSessionsReply;
import com.netflix.streamsaccountingservice.protogen.GetActiveSessionsRequest;
import com.netflix.streamsaccountingservice.protogen.StreamsAccountingServiceServiceGrpc.StreamsAccountingServiceServiceStub;
import com.netflix.type.proto.Videos;
import com.netflix.type.proto.Visitors;
import com.netflix.vhs.protogen.GetMostRecentPlaybackStartTimePerProfileRequest;
import com.netflix.vhs.protogen.ProfileWatchTime;
import com.netflix.vhs.protogen.VhsServiceGrpc.VhsServiceStub;
import com.netflix.vhs.protogen.ViewingHistory;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.concurrent.CompletionStage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Completable;
import rx.Observable;
import rx.Single;

@Component
public class APIVideoHistoryServiceImpl implements APIVideoHistoryService {

  private static final Logger logger = LoggerFactory.getLogger(APIVideoHistoryServiceImpl.class);
  private static final Property<Boolean> LOG_TO_MANTIS =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.videohistory.mantis", Boolean.class)
          .orElse(false);

  private static final ActiveSessionsReply ACTIVE_SESIONS_FALLBACK_GRPC =
      ActiveSessionsReply.newBuilder().setSuccess(false).setAllowSessions(true).build();

  private final APIVideoFactory videoFactory;
  private final APIViewingsFactory viewingsFactory;
  private final StreamsAccountingServiceServiceStub sasClient;
  private final DeviceTypeFactory deviceTypeFactory;
  private final VhsServiceStub vhsServiceClient;
  private final OnrampServiceStub onrampClient;
  private static final String GET_CURRENT_VIEWINGS_CALL = "getCurrentViewings";
  private static final String GET_CURRENT_VIEWINGS_PDS_CALL = "getCurrentViewingsPds";
  private final Registry registry;
  private final Id currentViewingsCounter;
  private final Id currentViewingsPdsCounter;
  private final Counter addedFakePlaysSuccess;
  private final Counter addedFakePlaysFailure;
  private final EnqueuePlaybackEventProvider enqueuePlaybackEventProvider;
  private final APIAccountManagementServiceImpl accountManagementService;
  private final ObservableFactory observableFactory;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public APIVideoHistoryServiceImpl(
      final APIVideoFactory videoFactory,
      final APIViewingsFactory viewingsFactory,
      @GrpcSpringClient("streamsaccountingservice")
          final StreamsAccountingServiceServiceStub sasClient,
      final DeviceTypeFactory deviceTypeFactory,
      @GrpcSpringClient("onramp") final OnrampServiceStub onrampClient,
      @GrpcSpringClient("vhs") final VhsServiceStub vhsServiceClient,
      final Registry registry,
      final EnqueuePlaybackEventProvider enqueuePlaybackEventProvider,
      final APIAccountManagementServiceImpl accountManagementService,
      final ObservableFactory observableFactory) {
    this.videoFactory = videoFactory;
    this.viewingsFactory = viewingsFactory;
    this.sasClient = sasClient;
    this.deviceTypeFactory = deviceTypeFactory;
    this.onrampClient = onrampClient;
    this.vhsServiceClient = vhsServiceClient;
    this.registry = registry;
    this.currentViewingsCounter = registry.createId(GET_CURRENT_VIEWINGS_CALL);
    this.currentViewingsPdsCounter = registry.createId(GET_CURRENT_VIEWINGS_PDS_CALL);
    this.enqueuePlaybackEventProvider = enqueuePlaybackEventProvider;
    this.accountManagementService = accountManagementService;
    this.addedFakePlaysSuccess = registry.counter("api.onramp.addFakePlays", "result", "success");
    this.addedFakePlaysFailure = registry.counter("api.onramp.addFakePlays", "result", "failure");
    this.observableFactory = observableFactory;
  }

  public CompletionStage<List<APIVideoViewingRecord>> getViewingHistory(
      ViewingHistoryRequestBuilder builder) {
    return Future.call(vhsServiceClient::getViewingHistory, builder.getRequest())
        .thenApply(
            viewingHistory -> {
              final Map<Integer, ViewingHistory.ViewingRecord> recordsMap =
                  viewingHistory.getViewingHistory().getRecordsMap();
              final List<APIVideoViewingRecord> results =
                  Lists.newArrayListWithCapacity(recordsMap.size());
              // remove all records that have videos that are not in catalog
              Map<Integer, Optional<APIVideo>> videoMap =
                  videoFactory.getInstances(recordsMap.keySet());
              for (Entry<Integer, ViewingHistory.ViewingRecord> entry : recordsMap.entrySet()) {
                videoMap
                    .get(entry.getKey())
                    .ifPresent(
                        apiVideo -> {
                          if (apiVideo.isViewable()) {
                            results.add(new APIVideoViewingRecordImpl(entry.getValue()));
                          }
                        });
              }
              sortRecords(results);
              return results;
            })
        .exceptionally(t -> Collections.emptyList());
  }

  static void sortRecords(List<APIVideoViewingRecord> real) {
    // make a copy and sort in reverse chronological order
    real.sort((a, b) -> Long.compare(b.getMostRecentDate(), a.getMostRecentDate()));
  }

  @Override
  public Observable<APIViewings> getCurrentViewings(final APIUser user) {
    // DNA-1325, DNA-3226
    registry.counter(currentViewingsCounter).increment();
    long accountId = APIUserUtil.getAccountOwnerId(user);
    return getCurrentViewingsFromPds(accountId).map(viewingsFactory::getInstance);
  }

  private Observable<List<ViewingHolder>> getCurrentViewingsFromPds(final long accountId) {
    return accountManagementService
        .getPlan()
        .flatMap(
            plan -> {
              GetActiveSessionsRequest.Builder checkReservationRequestBuilder =
                  GetActiveSessionsRequest.newBuilder().setAccountId(accountId);

              APIUser user = APIRequest.getCurrentRequest().getUser();
              boolean isTestAccount = user != null && user.isTestAccount();

              int maxConcurrentStreams = 0;
              if (plan != null) {
                maxConcurrentStreams = plan.getMaxConcurrentStreams();
              }

              checkReservationRequestBuilder.setIsTester(isTestAccount);
              checkReservationRequestBuilder.setMaxStreams(maxConcurrentStreams);

              return RxObservable.defer(
                      sasClient::getActiveSessions, checkReservationRequestBuilder.build())
                  .onErrorReturn(e -> ACTIVE_SESIONS_FALLBACK_GRPC)
                  .map(
                      activeSessionsReply -> {
                        if (LOG_TO_MANTIS.get()) {
                          MantisPublishContext.getCurrent()
                              .add("videohistory.currentviewing", activeSessionsReply.toString());
                        }
                        var activeSessionsList = activeSessionsReply.getActiveSessionsList();
                        List<ViewingHolder> viewings = new ArrayList<>(activeSessionsList.size());
                        for (ActiveSessionInfo sessionInfo : activeSessionsList) {
                          var builder = ViewingHolder.builder().activeSessionInfo(sessionInfo);
                          builder.deviceTypeId(
                              Optional.ofNullable(
                                      deviceTypeFactory.get(
                                          new DeviceTypeRequest().withEsn(sessionInfo.getEsn())))
                                  .map(DeviceType::getDeviceTypeId)
                                  .orElse(0));
                          viewings.add(builder.build());
                        }
                        registry
                            .counter(
                                currentViewingsPdsCounter.withTag(
                                    "viewingsCount", String.valueOf(activeSessionsList.size())))
                            .increment();
                        return viewings;
                      });
            })
        .onErrorResumeNext(
            e -> {
              logger.debug("error getting current viewings", e);
              return Observable.just(List.of());
            });
  }

  @Override
  public Completable addFakePlays(final APIUser user, final Collection<Integer> videoIds) {
    if (user == null) {
      return Completable.error(new IllegalArgumentException("Must provide user"));
    }

    AddOnrampSelectionRequest.Builder builder =
        AddOnrampSelectionRequest.newBuilder()
            .setVisitor(Visitors.toProtobuf(CurrentVisitor.get()));
    if (videoIds != null) {
      for (Integer videoId : videoIds) {
        Builder videoBuilder = OnrampSelection.newBuilder().setVideo(Videos.toProtobuf(videoId));
        builder.addOnrampSelectionData(videoBuilder.build());
      }
    }
    Future.call(onrampClient::addOnrampSelections, builder.build())
        .whenComplete(
            (reply, error) -> {
              if (error != null) {
                addedFakePlaysFailure.increment();
                logger.debug("error adding fake plays", error);
              } else {
                addedFakePlaysSuccess.increment();
              }
            });

    // there's no handling of added for on-ramp videos on the device and addOnrampSelection is a
    // fire-and-forget; we just have to return - something - as some endpoints map on it
    return Completable.complete();
  }

  @Override
  public Observable<Boolean> hideViewing(
      APIUser user, final int videoId, final boolean allEpisodes) {
    if (user == null) {
      return Observable.error(new APIServiceRuntimeException("No user credentials present"));
    }
    return observableFactory.from(
        () ->
            enqueuePlaybackEventProvider.enqueue(
                ViewingHistoryHideRequest.newBuilder()
                    .setIdentityInfo(
                        IdentityInfo.newBuilder()
                            .setAccountId(APIUserUtil.getAccountOwnerId(user))
                            .setProfileId(APIUserUtil.getCustomerId(user))
                            .build())
                    .setViewableId(videoId)
                    .setCountry(RequestContextWrapper.get().getCountry().getId())
                    .setAllEpisodes(allEpisodes)
                    .setRequestedTime(Timestamps.fromMillis(System.currentTimeMillis()))
                    .build()));
  }

  public Single<List<String>> getActiveProfilesWithinLastNDays(Integer numDays) {

    String esn = APIRequest.getCurrentRequest().getRequestContext().getESN();
    long realAccountOwnerId =
        ((APIUserInternal) APIRequest.getCurrentRequest().getUser().getAccountOwner())
            .getRealAccountOwnerId();

    GetMostRecentPlaybackStartTimePerProfileRequest request =
        GetMostRecentPlaybackStartTimePerProfileRequest.newBuilder()
            .setEsn(esn)
            .setAccountOwnerId(realAccountOwnerId)
            .setMaxDaysToFetch(numDays)
            .build();
    return RxObservable.call(vhsServiceClient::getMostRecentPlaybackStartTimePerProfile, request)
        .toSingle()
        .map(
            response ->
                response.getProfileWatchTimesList().stream()
                    .map(ProfileWatchTime::getProfileGuid)
                    .toList());
  }
}
