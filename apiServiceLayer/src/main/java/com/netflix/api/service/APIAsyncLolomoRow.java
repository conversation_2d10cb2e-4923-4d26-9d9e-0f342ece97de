package com.netflix.api.service;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import com.netflix.api.service.list.APIAsyncListImpl;
import com.netflix.api.service.list.APIEntity;
import com.netflix.api.service.list.APILolomoList;
import com.netflix.api.service.list.MapResponseUtils;
import com.netflix.api.service.list.ProcessedMapAnnotations;
import com.netflix.api.service.video.APIRawDataImpl;
import com.netflix.api.service.video.APIVideoFactory;
import com.netflix.map.annotation.MapAnnotationConstants;
import com.netflix.map.datatypes.MapItem;
import com.netflix.map.datatypes.MapList;
import com.netflix.map.datatypes.MapResponse;
import com.netflix.servo.tag.BasicTag;
import com.netflix.servo.tag.BasicTagList;
import com.netflix.servo.tag.Tag;
import com.netflix.type.Video;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.Observable;
import rx.Single;

/**
 * This class is instantiated in the following cases: 1) A MAP request is passed in for a single
 * row. This class is responsible for doing the call to MAP and presenting results that obey the
 * APIList interface 2) A MAP response containing row metadata is passed in. This occurs when a root
 * lolomo has been retrieved and there is *some* row-level metadata available.
 */
public class APIAsyncLolomoRow<T extends APIEntity> extends APIAsyncListImpl<T, MapItem<?>>
    implements APILolomoList<T> {
  private final Observable<MapList<MapItem<?>>> cachedMapResponse;
  private final Observable<ProcessedMapAnnotations> cachedMapAnnotations;
  private final APIVideoFactory videoFactory;
  private final MapResponseUtils mapResponseUtils;

  private static final Logger logger = LoggerFactory.getLogger(APIAsyncLolomoRow.class);

  @AssistedInject
  APIAsyncLolomoRow(
      @Assisted Observable<MapResponse> mapResponse,
      APIVideoFactory videoFactory,
      MapResponseUtils mapResponseUtils) {
    this.videoFactory = videoFactory;
    this.mapResponseUtils = mapResponseUtils;
    this.cachedMapResponse =
        Observable.defer(() -> mapResponse.map(r -> (MapList<MapItem<?>>) r.getList(0))).cache();
    this.cachedMapAnnotations =
        Observable.defer(() -> cachedMapResponse.map(ProcessedMapAnnotations::from)).cache();
  }

  @Override
  protected Observable<MapItem<?>> loadReferences() {
    return cachedMapResponse.flatMap(Observable::from);
  }

  @Override
  protected Single<T> loadItem(MapItem<?> mapItem) {
    Observable<String> itemTypeObservable =
        cachedMapAnnotations.map(
            anns -> anns.getUnsanitizedAnnotation(MapAnnotationConstants.ITEMTYPE, String.class));
    return itemTypeObservable
        .map(
            itemType -> {
              // video
              if (MapAnnotationConstants.ItemTypes.video.name().equals(itemType)) {
                if (mapItem.getItem() instanceof Video) {
                  return (T) videoFactory.getInstance((Video) mapItem.getItem());
                } else {
                  try {
                    Class<?> clazz = mapItem.getItem().getClass();
                    if (Map.class.isAssignableFrom(clazz)) {
                      Map<String, ?> rawDataItem = (Map<String, ?>) mapItem.getItem();
                      return (T) APIRawDataImpl.newInstance(rawDataItem);
                    }
                  } catch (Exception e) {
                    logger.error("Unknown itemType={}", mapItem.getItem().getClass(), e);
                  }
                }
              }
              logger.error(
                  "AsyncLolomoRow returning null on itemType={}, mapItem={}",
                  itemType,
                  mapItem.getItem().getClass().getCanonicalName());

              List<Tag> tags = new ArrayList<>();
              tags.add(new BasicTag("itemType", itemType));
              tags.add(new BasicTag("mapClass", mapItem.getClass().getSimpleName()));
              tags.add(new BasicTag("itemClass", mapItem.getItem().getClass().getSimpleName()));
              tags.add(new BasicTag("class", getClass().getSimpleName()));
              BasicTagList tagList = new BasicTagList(tags);
              incrementNullItem(tagList);
              return null;
            })
        .toSingle();
  }

  @Override
  public Observable<String> getListId() {
    return cachedMapAnnotations.map(
        anns -> anns.getUnsanitizedAnnotation(MapAnnotationConstants.UUID, String.class));
  }

  @Override
  public Observable<APIAnnotation<String, Object>> getAnnotations() {
    return cachedMapAnnotations.flatMap(anns -> Observable.from(anns.getApiAnnotations()));
  }

  @Override
  public Observable<String> getListName() {
    return cachedMapAnnotations.map(
        anns -> anns.getUnsanitizedAnnotation(MapAnnotationConstants.TITLE, String.class));
  }

  @Override
  public Single<ListContext> getContext() {
    return cachedMapAnnotations
        .map(
            anns -> {
              ListContext result = ListContext.unknown;
              String value =
                  anns.getUnsanitizedAnnotation(MapAnnotationConstants.LISTCONTEXT, String.class);

              try {
                if (value == null) {
                  String uuid =
                      anns.getUnsanitizedAnnotation(MapAnnotationConstants.UUID, String.class);
                  logger.error("list context is null for uuid={}", uuid);
                } else {
                  result = ListContext.valueOf(value);
                }
              } catch (Exception e) {
                logger.error("list context annotation '{}' not mapped correctly", value, e);
              }
              return result;
            })
        .toSingle();
  }
}
