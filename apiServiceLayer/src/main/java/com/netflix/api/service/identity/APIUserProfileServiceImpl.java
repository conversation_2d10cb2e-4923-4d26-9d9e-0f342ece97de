package com.netflix.api.service.identity;

import com.netflix.api.adapters.StreamingClientServiceAdapter;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.identity.CurrentIdentityResult;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIClientCapabilitiesInternal;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import com.netflix.api.service.batch.APILoloUtils;
import com.netflix.api.service.batch.USTListAdapter;
import com.netflix.api.service.identity.exceptions.DeleteProfileException;
import com.netflix.api.service.profiles.APILolopi;
import com.netflix.api.service.profiles.APILolopiImpl;
import com.netflix.archaius.api.Property;
import com.netflix.lang.RequestVariable;
import com.netflix.map.datatypes.MapAnnotations;
import com.netflix.pacs.protogen.Feature;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.servo.DefaultMonitorRegistry;
import com.netflix.servo.monitor.Counter;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.servo.monitor.Monitors;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.subscriberservice.protogen.DeleteProfileRequest;
import com.netflix.subscriberservice.protogen.SubscriberManagementServiceGrpc.SubscriberManagementServiceStub;
import com.netflix.ust.adapters.USTStatus;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Completable;
import rx.Observable;
import rx.Single;
import ust.common.v1.Customer;
import ust.product.features.v1.FeatureResult;
import ust.product.features.v1.GetFeaturesRequest;
import ust.product.features.v1.ProductFeaturesAccess;

@Component
@EnableDeprecatedMetrics
public class APIUserProfileServiceImpl implements APIUserProfileService {
  private static final Logger logger = LoggerFactory.getLogger(APIUserProfileServiceImpl.class);

  public static final Property<Boolean> parentalControlOnLegacyDevice =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.legacy.pin.parentalcontrol.feature.on", Boolean.class)
          .orElse(false);

  private static final Counter COUNTER_DELETE_PROFILE_ERROR =
      Monitors.newCounter("COUNTER-USERPROFILES.COUNTER_DELETE_PROFILE_ERROR");
  private static final Counter COUNTER_DELETE_PROFILE_VALIDATION_ERROR =
      Monitors.newCounter("COUNTER-USERPROFILES.COUNTER_DELETE_PROFILE_VALIDATION_ERROR");
  public static final Property<Boolean> ENABLE_CAN_ADD_PROFILE_PACS =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.userprofile.canAddProfilePacs.enable", Boolean.class)
          .orElse(true);

  private static final RequestVariable<ConcurrentHashMap<Long, Single<APILolopi>>> LOLOPI_CACHE =
      new RequestVariable<>() {
        @Override
        protected ConcurrentHashMap<Long, Single<APILolopi>> initialValue() {
          return new ConcurrentHashMap<>();
        }
      };

  static {
    DefaultMonitorRegistry.getInstance().register(COUNTER_DELETE_PROFILE_ERROR);
    DefaultMonitorRegistry.getInstance().register(COUNTER_DELETE_PROFILE_VALIDATION_ERROR);
  }

  static final int MAX_PROFILES_ALLOWED_PER_ACCOUNT = 5;

  private final APIUserFactory userFactory;
  private final APIProfileErrors.Localizer localizer;
  private final StreamingClientServiceAdapter streamingClientServiceAdapter;
  private final SubscriberManagementServiceStub subscriberManagementServiceStub;
  private final ProductFeaturesAccess productFeaturesAccess;
  private final USTListAdapter listAdapter;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public APIUserProfileServiceImpl(
      APIUserFactory userFactory,
      APIProfileErrors.Localizer localizer,
      StreamingClientServiceAdapter streamingClientServiceAdapter,
      @GrpcSpringClient("subscriberservice")
          SubscriberManagementServiceStub subscriberManagementServiceStub,
      ProductFeaturesAccess productFeaturesAccess,
      USTListAdapter listAdapter) {
    this.userFactory = userFactory;
    this.localizer = localizer;
    this.streamingClientServiceAdapter = streamingClientServiceAdapter;
    this.subscriberManagementServiceStub = subscriberManagementServiceStub;
    this.productFeaturesAccess = productFeaturesAccess;
    this.listAdapter = listAdapter;
  }

  @Override
  @Deprecated
  public APIUserProfileBuilder createUserWithOwner(final APIUser accountOwner) {
    return new APIUserCreateListBuilderImpl(accountOwner, userFactory, localizer);
  }

  @Override
  public APIUserProfileBuilder createUserProfileWithOwner(final APIUser accountOwner) {
    return new APIUserCreateListBuilderImpl(accountOwner, userFactory, localizer);
  }

  @Override
  public APIUserProfileBuilder editUser(final APIUser userToEdit) {
    return new APIUserEditListBuilderImpl(userToEdit, userFactory, localizer);
  }

  @Override
  public APIUserProfileBuilder editUserProfile(final APIUser userToEdit) {
    return new APIUserEditListBuilderImpl(userToEdit, userFactory, localizer);
  }

  @Override
  public Completable deleteProfileCompletable(String guid) {
    final APIUserInternal currentUser = (APIUserInternal) APIRequest.getCurrentRequest().getUser();
    if (guid == null || guid.isEmpty() || currentUser == null) {
      COUNTER_DELETE_PROFILE_VALIDATION_ERROR.increment();
      return Completable.error(new DeleteProfileException(getDeleteErrorMessage()));
    }
    return RxObservable.call(
            subscriberManagementServiceStub::deleteProfile,
            DeleteProfileRequest.newBuilder()
                .setAccountId(currentUser.getRealAccountOwnerId())
                .setProfileGuid(guid)
                .build())
        .toCompletable()
        .onErrorResumeNext(
            e -> {
              COUNTER_DELETE_PROFILE_ERROR.increment();
              logger.error("Could not delete profile", e);
              return Completable.error(new DeleteProfileException(getDeleteErrorMessage(), e));
            });
  }

  private String getDeleteErrorMessage() {
    return localizer.localize(
        APIProfileErrors.DELETE_PROFILE_ERROR_MESSAGE_KEY,
        APIProfileErrors.DEFAULT_DELETE_PROFILE_ERROR_MESSAGE);
  }

  @Override
  @Deprecated
  public Observable<Boolean> canCreateProfile(final APIUser user) {
    return canCreateUserProfile(user)
        .flatMap(
            result -> {
              try {
                if (parentalControlOnLegacyDevice.get() && Boolean.TRUE.equals(result)) {
                  return user.isProfileCreationLocked()
                      .map(profileCreationLocked -> !profileCreationLocked);
                } else {
                  return Observable.just(result);
                }
              } catch (Throwable t) {
                return Observable.error(t);
              }
            });
  }

  @Override
  public Observable<Boolean> canCreateUserProfile(final APIUser user) {
    if (ENABLE_CAN_ADD_PROFILE_PACS.get()) {
      Optional<Customer> customer = currentPassportCustomer();
      if (customer.isPresent()) {
        return RxObservable.defer(
                productFeaturesAccess::getFeatures,
                GetFeaturesRequest.newBuilder()
                    .setCustomer(customer.get())
                    .addFeatures(Feature.CAN_ADD_PROFILE)
                    .build())
            .map(
                r -> {
                  boolean shadow = false;
                  for (FeatureResult result : r.getResultsList()) {
                    if (result.getKey() == Feature.CAN_ADD_PROFILE
                        && USTStatus.isOk(result.getStatus())) {
                      shadow = result.getValue().getValue();
                    }
                  }
                  return shadow;
                })
            .onErrorResumeNext(
                t -> {
                  logger.error("Shadow error canCreateUserProfile", t);
                  DynamicCounter.increment("api.canCreateProfile.error");
                  return user.getNumProfilesInAccount()
                      .toObservable()
                      .map(
                          numProfilesInAccount ->
                              numProfilesInAccount < MAX_PROFILES_ALLOWED_PER_ACCOUNT);
                });
      } else {
        logger.error("Shadow invalid no passport {}", APIUserUtil.getCustomerId(user));
        DynamicCounter.increment("api.canCreateProfile.shadow", "result", "invalid");
      }
    }

    return user.getNumProfilesInAccount()
        .toObservable()
        .map(numProfilesInAccount -> numProfilesInAccount < MAX_PROFILES_ALLOWED_PER_ACCOUNT);
  }

  private static Optional<Customer> currentPassportCustomer() {

    PassportIdentity identity = CurrentIdentityResult.getPassportIdentity();
    if (identity != null) {
      String passport = identity.getPassportAsString();
      if (passport != null) {
        return Optional.of(Customer.newBuilder().setPassport(passport).build());
      }
    }
    return Optional.empty();
  }

  @Override
  public Single<APILolopi> getLolopi(APIUser user, Map<String, Object> requestAnnotations) {
    APIRequest request = APIRequest.getCurrentRequest();
    // for unpersonalized lolopi's the user can be null
    Observable<AccountProfileRemote> accountProfileObservable = null;
    if (user != null) {
      accountProfileObservable = ((APIUserInternal) user).getAccountProfileAsObservable();
    }
    Map<String, Object> annotationsCopy = new HashMap<>();
    if (requestAnnotations != null) {
      annotationsCopy.putAll(requestAnnotations);
    }

    Observable<APIUser> lolopiUser;
    if (user != null) {
      lolopiUser = Observable.just(user);
    } else {
      lolopiUser = APIRequest.getCurrentRequest().getObservableUser();
    }
    Single<HashSet<String>> currentlyUsedIcons =
        lolopiUser
            .toSingle()
            .flatMap(APIUser::getIconNames)
            .map(iconName -> new HashSet<>(iconName.values()));

    Map<String, Object> allAnnotations =
        APILoloUtils.getRequestContextEntities(user, request, false);
    allAnnotations.putAll(annotationsCopy);

    // DNA-3051 pass image capabilities same as for other image calls
    Optional<APIClientCapabilitiesInternal> apiClientCapabilitiesInternal =
        APIClientCapabilitiesInternal.get(APIRequest.getCurrentRequest());
    if (apiClientCapabilitiesInternal.isPresent()) {
      Map<String, Collection<String>> map = apiClientCapabilitiesInternal.get().asMap();
      allAnnotations.put(com.netflix.map.annotation.MapAnnotationConstants.IMAGE_CAPABILITIES, map);
    }

    // DNA-3051 hack propagated from avatar calls. TODO bug Adam Rofer to retire it from images.
    boolean isLocalizedKidsProfileSupported =
        Optional.ofNullable(APIRequest.getCurrentRequest())
            .map(APIRequest::getRequestContext)
            .map(APIRequestContext::deviceSupportsLocalizedKidsProfile)
            .orElse(false);
    Map<String, String> imageParams = new HashMap<>();
    if (allAnnotations.containsKey("imageParams")) {
      @SuppressWarnings("unchecked")
      Map<String, String> tempImageParams = (Map<String, String>) allAnnotations.get("imageParams");
      imageParams = tempImageParams;
    }
    imageParams.put(
        "supports_localized_kids_profile", Boolean.toString(isLocalizedKidsProfileSupported));
    allAnnotations.put("imageParams", imageParams);

    final MapAnnotations annotations =
        new MapAnnotations().addAllAnnotationsFromMap(allAnnotations);
    if (accountProfileObservable != null) {
      return accountProfileObservable
          .toSingle()
          .flatMap(
              accountProfile -> getLolopiInternal(accountProfile, annotations, currentlyUsedIcons));
    } else {
      return getLolopiInternal(null, annotations, currentlyUsedIcons);
    }
  }

  private Single<APILolopi> getLolopiInternal(
      @Nullable AccountProfileRemote accountProfile,
      MapAnnotations annotations,
      Single<HashSet<String>> currentlyUsedIcons) {

    Long cacheKey = accountProfile == null ? 0L : accountProfile.getOptionalProfileId().orElse(0L);
    logger.debug("looking for cache key {}", cacheKey);
    final Single<APILolopi> cachedSingle = LOLOPI_CACHE.get().get(cacheKey);
    if (cachedSingle != null) {
      logger.debug("found lolopi in cache for key {}", cacheKey);
      return cachedSingle;
    }

    final Single<APILolopi> singleDefer =
        Single.defer(
                () ->
                    streamingClientServiceAdapter
                        .getHostName()
                        .flatMap(
                            host ->
                                currentlyUsedIcons.flatMap(
                                    icons -> {
                                      annotations.putStringSet("blacklistIcons", icons);
                                      annotations.put("ocaHostname", host);
                                      annotations.put("gpsModel", "SAGET");
                                      return listAdapter.getNewLolopi(
                                          annotations.getAnnotationsAsMap());
                                    }))
                        .map(response -> (APILolopi) new APILolopiImpl(response)))
            .cache();
    final Single<APILolopi> previous = LOLOPI_CACHE.get().putIfAbsent(cacheKey, singleDefer);

    logger.debug("adding to cache for key {}", cacheKey);
    return previous == null ? singleDefer : previous;
  }
}
