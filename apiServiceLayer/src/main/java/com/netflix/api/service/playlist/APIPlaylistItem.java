package com.netflix.api.service.playlist;

import com.netflix.api.service.video.APIVideo;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.Map;
import java.util.Set;

public interface APIPlaylistItem {
  /**
   * @return underlying video item
   */
  @Nonnull
  APIVideo getVideo();

  /**
   * Retrieves stored metadata value
   *
   * @param key arbitrary key
   * @return any string value returned by Merch platform
   */
  @Nullable
  String getMetadata(String key);

  /** Returns all annotation keys for this item */
  @Nonnull
  Set<String> getMetadataKeys();

  /** Returns true if the item was marked as remind me */
  boolean isInRemindMe();

  /** Returns all annotations */
  Map<String, String> getMetadata();
}
