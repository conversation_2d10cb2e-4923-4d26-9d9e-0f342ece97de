package com.netflix.api.dependencies;

import com.netflix.api.platform.http.APIAsyncFilterResponseHelper;
import com.netflix.api.util.ServiceRequestUtils;
import com.netflix.hystrix.HystrixInvokableInfo;
import com.netflix.hystrix.HystrixRequestLog;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.server.context.CurrentRequestInfo;
import com.netflix.server.context.RequestInfo;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/** Publish NFDependencyCommand/HystrixCommand executions */
@Component
@Order(Ordered.LOWEST_PRECEDENCE - 8) // #6
public class APIDependenciesFilter implements Filter {
  private static final Logger logger = LoggerFactory.getLogger(APIDependenciesFilter.class);

  private static final String DEPENDENCY_HEADER_NAME = "X-Netflix.dependency-command.executions";
  private static final String EXECUTION_HEADER_NAME =
      "X-Netflix.dependency-command.totalExecutionTime";
  private static final String DEPENDENCY_MANTIS_KEY_NAME = "api.hystrix.executions";
  private static final String REQUEST_TRACE_KEY_NAME = "DEPENDENCY_COMMAND_EXECUTIONS";

  @Override
  public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
      throws IOException, ServletException {
    try {
      chain.doFilter(request, response);
    } finally {
      APIAsyncFilterResponseHelper.enqueueAfterRequestFinally(
          request,
          error -> {
            try {
              HttpServletRequest httpRequest = (HttpServletRequest) request;
              HttpServletResponse httpResponse = (HttpServletResponse) response;
              HystrixRequestLog hystrixRequestLog = HystrixRequestLog.getCurrentRequest();
              String log = hystrixRequestLog.getExecutedCommandsAsString();
              Collection<HystrixInvokableInfo<?>> executedCommands =
                  hystrixRequestLog.getAllExecutedCommands();
              try {
                // output via Mantis
                MantisPublishContext mantis = MantisPublishContext.getCurrent();
                mantis.add(DEPENDENCY_MANTIS_KEY_NAME, log);

                // output via request trace
                RequestInfo requestInfo = CurrentRequestInfo.get();
                if (requestInfo == null) {
                  requestInfo = new RequestInfo();
                  CurrentRequestInfo.set(requestInfo);
                }
                requestInfo.setExtraInfo(REQUEST_TRACE_KEY_NAME, log);

                // output via HTTPResponse header if the request is internal
                if (ServiceRequestUtils.isInternalRequest(httpRequest)) {
                  int totalDependencyExecutionTime = 0;
                  for (HystrixInvokableInfo<?> i : executedCommands) {
                    totalDependencyExecutionTime += Math.max(i.getExecutionTimeInMilliseconds(), 0);
                  }
                  httpResponse.setHeader(DEPENDENCY_HEADER_NAME, log);
                  httpResponse.setHeader(
                      EXECUTION_HEADER_NAME, String.valueOf(totalDependencyExecutionTime));
                }
              } catch (Throwable e) {
                logger.error("Error reporting hystrix executions", e);
              }
            } catch (Throwable e) {
              logger.error("Error getting or reporting hystrix executions", e);
            }
          });
    }
  }
}
