package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SubtitleSettingsRequest {
  private Color characterColor;

  private Opacity characterOpacity;

  private CharacterSize characterSize;

  private CharacterStyle characterStyle;

  private EdgeAttribute characterEdgeAttributes;

  private Color characterEdgeColor;

  private Color backgroundColor;

  private Opacity backgroundOpacity;

  private Color windowColor;

  private Opacity windowOpacity;

  public Color getCharacterColor() {
    return characterColor;
  }

  public Opacity getCharacterOpacity() {
    return characterOpacity;
  }

  public CharacterSize getCharacterSize() {
    return characterSize;
  }

  public CharacterStyle getCharacterStyle() {
    return characterStyle;
  }

  public EdgeAttribute getCharacterEdgeAttributes() {
    return characterEdgeAttributes;
  }

  public Color getCharacterEdgeColor() {
    return characterEdgeColor;
  }

  public Color getBackgroundColor() {
    return backgroundColor;
  }

  public Opacity getBackgroundOpacity() {
    return backgroundOpacity;
  }

  public Color getWindowColor() {
    return windowColor;
  }

  public Opacity getWindowOpacity() {
    return windowOpacity;
  }

  public SubtitleSettingsRequest setCharacterColor(Color characterColor) {
    this.characterColor = characterColor;
    return this;
  }

  public SubtitleSettingsRequest setCharacterOpacity(Opacity characterOpacity) {
    this.characterOpacity = characterOpacity;
    return this;
  }

  public SubtitleSettingsRequest setCharacterSize(CharacterSize characterSize) {
    this.characterSize = characterSize;
    return this;
  }

  public SubtitleSettingsRequest setCharacterStyle(CharacterStyle characterStyle) {
    this.characterStyle = characterStyle;
    return this;
  }

  public SubtitleSettingsRequest setCharacterEdgeAttributes(EdgeAttribute characterEdgeAttributes) {
    this.characterEdgeAttributes = characterEdgeAttributes;
    return this;
  }

  public SubtitleSettingsRequest setCharacterEdgeColor(Color characterEdgeColor) {
    this.characterEdgeColor = characterEdgeColor;
    return this;
  }

  public SubtitleSettingsRequest setBackgroundColor(Color backgroundColor) {
    this.backgroundColor = backgroundColor;
    return this;
  }

  public SubtitleSettingsRequest setBackgroundOpacity(Opacity backgroundOpacity) {
    this.backgroundOpacity = backgroundOpacity;
    return this;
  }

  public SubtitleSettingsRequest setWindowColor(Color windowColor) {
    this.windowColor = windowColor;
    return this;
  }

  public SubtitleSettingsRequest setWindowOpacity(Opacity windowOpacity) {
    this.windowOpacity = windowOpacity;
    return this;
  }
}
