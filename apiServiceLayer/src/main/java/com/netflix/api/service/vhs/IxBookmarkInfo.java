package com.netflix.api.service.vhs;

import java.util.Objects;

public class IxBookmarkInfo {
  private final double bookmarkPositionDouble;
  private final int bookmarkPosition;
  private final String nextPlaygraphSegmentId;
  private final long lastModified;
  private final int movieId;

  public IxBookmarkInfo(int movieId, int bookmarkPosition, long lastModified) {
    this.bookmarkPosition = bookmarkPosition;
    this.movieId = movieId;
    this.lastModified = lastModified;
    this.nextPlaygraphSegmentId = null;
    this.bookmarkPositionDouble = bookmarkPosition;
  }

  public IxBookmarkInfo(
      int movieId, double bookmarkPosition, String nextPlaygraphSegmentId, long lastModified) {
    this.bookmarkPositionDouble = bookmarkPosition;
    this.movieId = movieId;
    this.lastModified = lastModified;
    this.nextPlaygraphSegmentId = nextPlaygraphSegmentId;
    this.bookmarkPosition = (int) bookmarkPosition;
  }

  public double getBookmarkPositionAsDouble() {
    return bookmarkPositionDouble;
  }

  public String getNextPlaygraphSegmentId() {
    return nextPlaygraphSegmentId;
  }

  public static IxBookmarkInfo empty(int id) {
    return new IxBookmarkInfo(id, -1, 0);
  }

  @Override
  public String toString() {
    return "IxBookmarkInfo [bookmarkPositionDouble="
        + bookmarkPosition
        + ", movieId="
        + getMovieId()
        + ", bookmarkPosition="
        + getBookmarkPosition()
        + ", nextPlaygraphSegmentId="
        + nextPlaygraphSegmentId
        + ", lastModified="
        + getLastModified()
        + "]";
  }

  public int getBookmarkPosition() {
    return bookmarkPosition;
  }

  public long getLastModified() {
    return lastModified;
  }

  public int getMovieId() {
    return movieId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof IxBookmarkInfo)) {
      return false;
    }
    if (!super.equals(o)) {
      return false;
    }
    IxBookmarkInfo that = (IxBookmarkInfo) o;
    return Double.compare(that.bookmarkPosition, bookmarkPosition) == 0
        && Objects.equals(that.nextPlaygraphSegmentId, nextPlaygraphSegmentId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), bookmarkPosition, nextPlaygraphSegmentId);
  }
}
