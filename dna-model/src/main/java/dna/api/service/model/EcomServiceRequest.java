package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class EcomServiceRequest {
  /** ignored */
  @Deprecated private String language;

  /** ignored */
  @Deprecated private String country;

  /** ignored */
  @Deprecated private String authURL;

  /** ignored */
  @Deprecated private String landingURL;

  /** ignored */
  @Deprecated private String promotionCode;

  @Deprecated
  public String getLanguage() {
    return language;
  }

  @Deprecated
  public String getCountry() {
    return country;
  }

  @Deprecated
  public String getAuthURL() {
    return authURL;
  }

  @Deprecated
  public String getLandingURL() {
    return landingURL;
  }

  @Deprecated
  public String getPromotionCode() {
    return promotionCode;
  }

  @Deprecated
  public EcomServiceRequest setLanguage(String language) {
    this.language = language;
    return this;
  }

  @Deprecated
  public EcomServiceRequest setCountry(String country) {
    this.country = country;
    return this;
  }

  @Deprecated
  public EcomServiceRequest setAuthURL(String authURL) {
    this.authURL = authURL;
    return this;
  }

  @Deprecated
  public EcomServiceRequest setLandingURL(String landingURL) {
    this.landingURL = landingURL;
    return this;
  }

  @Deprecated
  public EcomServiceRequest setPromotionCode(String promotionCode) {
    this.promotionCode = promotionCode;
    return this;
  }
}
