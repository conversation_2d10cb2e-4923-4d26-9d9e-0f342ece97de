package com.netflix.api.service.account;

import com.netflix.api.platform.deprecated.DeprecatedMethodTracker;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.plan.PlanData;
import com.netflix.archaius.api.Property;
import java.util.Collections;
import java.util.Set;

public class APIPlanDetailImpl implements APIPlanDetail {

  private static final Property<Boolean> ENABLE_FREEMIUM =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.plandetail.freemium.enable", Boolean.class)
          .orElse(false);
  private static final Property<Boolean> ENABLE_MOBILEONLY =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.plandetail.mobileonly.enable", Boolean.class)
          .orElse(false);

  private final PlanData planData;

  public APIPlanDetailImpl(PlanData planData) {
    this.planData = planData;
  }

  @Override
  public long getPlanId() {
    return planData.getPlanId();
  }

  @Override
  public boolean isSDOnly() {
    return planData.isSDOnly();
  }

  @Override
  public boolean isHDEnabled() {
    return planData.isHDEnabled();
  }

  @Override
  public boolean isUHDEnabled() {
    return planData.isUHDEnabled();
  }

  @Override
  public int getMaxStreams() {
    return planData.getMaxStreams();
  }

  @Override
  public int getMaxDownloadDevices() {
    return planData.getMaxDownloadDevices();
  }

  @Override
  @Deprecated
  public boolean isFreemium() {
    DeprecatedMethodTracker.incrementDeprecatedMetrics(
        "APIPlanDetailImpl",
        "isFreemium",
        Collections.emptyList(),
        APIRequest.getCurrentRequest().getRequestContext().getEndpointPath());
    return ENABLE_FREEMIUM.get() && planData.isFreemium();
  }

  @Override
  @Deprecated
  public boolean isMobileOnly() {
    DeprecatedMethodTracker.incrementDeprecatedMetrics(
        "APIPlanDetailImpl",
        "isMobileOnly",
        Collections.emptyList(),
        APIRequest.getCurrentRequest().getRequestContext().getEndpointPath());
    return ENABLE_MOBILEONLY.get() && planData.isMobileOnly();
  }

  @Override
  public Set<APIPlanDeviceType> getAllowedDeviceTypes() {
    return planData.getAllowedDeviceTypes();
  }
}
