package dna.api.datasources;

import com.netflix.servo.monitor.DynamicCounter;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class DataSourceUtil {

  private DataSourceUtil() {}

  public static <K, V, T> Map<K, Set<V>> buildMultiValuedMap(
      Iterable<T> items,
      Function<T, K> keyExtractor,
      Function<T, V> valueExtractor,
      Function<K, Set<V>> valueSetFactory) {
    Map<K, Set<V>> map = new HashMap<>();
    for (T item : items) {
      Set<V> values = map.computeIfAbsent(keyExtractor.apply(item), valueSetFactory);
      values.add(valueExtractor.apply(item));
    }
    return map;
  }

  public static <K, V> Map<K, V> mapOrEmpty(Map<K, V> map) {
    return map == null ? Map.of() : map;
  }

  public static <T1, T2> List<T2> convertList(
      Collection<T1> input, Function<? super T1, ? extends T2> transformer) {
    if (input == null) {
      return null;
    }
    return (List<T2>) input.stream().map(transformer).toList();
  }

  public static <T1, T2> List<T2> convertList(
      Collection<T1> input,
      Function<? super T1, ? extends T2> transformer,
      Supplier<List<T2>> factory) {
    if (input == null) {
      return null;
    }
    return input.stream().map(transformer).collect(Collectors.toCollection(factory));
  }

  public static <E1 extends Enum<E1>, E2 extends Enum<E2>> Function<E1, E2> enumConverter(
      Class<E2> e2Class) {
    return e1 -> {
      if (e1 == null) {
        return null;
      }
      return E2.valueOf(e2Class, e1.name());
    };
  }

  public static <E1 extends com.google.protobuf.ProtocolMessageEnum, E2 extends Enum<E2>>
      Function<E1, E2> protoEnumConverter(Class<E2> e2Class) {
    return e1 -> {
      if (e1 == null) {
        return null;
      }
      try {
        return E2.valueOf(e2Class, e1.getValueDescriptor().getName());
      } catch (IllegalArgumentException e) {
        DynamicCounter.increment(
            "api.unknown.enum",
            "enum",
            e2Class.getName(),
            "value",
            e1.getValueDescriptor().getName());
        return null;
      }
    };
  }
}
