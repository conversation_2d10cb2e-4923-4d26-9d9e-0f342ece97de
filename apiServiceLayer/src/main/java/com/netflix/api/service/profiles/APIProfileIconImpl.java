package com.netflix.api.service.profiles;

import com.netflix.api.service.video.APIImage;
import com.netflix.api.service.video.APIImageImpl;
import com.netflix.map.jackson.serializable.MapSerializableImage;
import java.util.Collections;
import java.util.Map;

public class APIProfileIconImpl implements APIProfileIcon {

  private final String id;
  private final String url;
  private final boolean isInDefaultSet;
  private final APIImage image;
  private final Map<String, Object> annotations;

  APIProfileIconImpl(Map<String, Object> annotations, MapSerializableImage image) {
    this.id = image.getId();
    this.url = image.getUrl();
    this.isInDefaultSet = false;
    this.image = new APIImageImpl(url, null, image.getW(), image.getH(), null, null, null);
    this.annotations = annotations == null ? Collections.emptyMap() : annotations;
  }

  @Override
  public String getId() {
    return id;
  }

  @Override
  public String getUrl() {
    return url;
  }

  @Override
  public boolean isInDefaultSet() {
    return isInDefaultSet;
  }

  @Override
  public APIImage getImage() {
    return image;
  }

  @Override
  public Map<String, Object> getAnnotations() {
    return annotations;
  }

  @Override
  public Object getAnnotationValue(String key) {
    return annotations.get(key);
  }

  @Override
  public String toString() {
    return "APIProfileIcon{"
        + "id='"
        + id
        + '\''
        + ", url='"
        + url
        + '\''
        + ", isInDefaultSet="
        + isInDefaultSet
        + ", image="
        + image
        + ", annotations="
        + annotations
        + '}';
  }
}
