package com.netflix.api.service.video;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.archaius.api.Property;
import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.vms.provider.VideoFormatDescriptorProvider;

public class APICachedTitleGroupViewableImpl extends APICachedViewableImpl {

  // Titlegroup are masqueraded as movie for backward compatibility purpose, but shouldn't be
  // playable
  // So this is an experiment to disable to viewing capability on the titlegroup.
  // Ideally we change this class to not extends APIViewableImpl once this is tested
  private static final Property<Boolean> isTitleGroupViewable =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.titlegroups.isviewable", Boolean.class)
          .orElse(false);

  public APICachedTitleGroupViewableImpl(
      CompleteVideo video,
      APIVideoFactory videoFactory,
      USTVideoMetadataAdapter videoMetadataAdapter,
      VideoFormatDescriptorProvider videoFormatDescriptorProvider) {
    super(video, videoFactory, videoMetadataAdapter, videoFormatDescriptorProvider);
  }

  @Override
  public boolean isViewable() {
    boolean shouldBeViewable = isTitleGroupViewable.get();
    return shouldBeViewable && super.isViewable();
  }
}
