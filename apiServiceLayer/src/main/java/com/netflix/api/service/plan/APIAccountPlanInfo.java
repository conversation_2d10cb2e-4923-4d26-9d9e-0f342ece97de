package com.netflix.api.service.plan;

public class APIAccountPlanInfo {

  private final String formattedCurrentPrice;
  private final String formattedNextPrice;

  public String getFormattedCurrentPrice() {
    return formattedCurrentPrice;
  }

  public String getFormattedNextPrice() {
    return formattedNextPrice;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIAccountPlanInfo)) {
      return false;
    }

    APIAccountPlanInfo that = (APIAccountPlanInfo) o;

    if (getFormattedCurrentPrice() != null
        ? !getFormattedCurrentPrice().equals(that.getFormattedCurrentPrice())
        : that.getFormattedCurrentPrice() != null) {
      return false;
    }
    return getFormattedNextPrice() != null
        ? getFormattedNextPrice().equals(that.getFormattedNextPrice())
        : that.getFormattedNextPrice() == null;
  }

  @Override
  public int hashCode() {
    int result = getFormattedCurrentPrice() != null ? getFormattedCurrentPrice().hashCode() : 0;
    result =
        31 * result + (getFormattedNextPrice() != null ? getFormattedNextPrice().hashCode() : 0);
    return result;
  }

  APIAccountPlanInfo(String formattedCurrentPrice, String formattedNextPrice) {
    this.formattedCurrentPrice = formattedCurrentPrice;
    this.formattedNextPrice = formattedNextPrice;
  }

  @Override
  public String toString() {
    return "APIAccountPlanInfo{"
        + "formattedCurrentPrice='"
        + formattedCurrentPrice
        + '\''
        + ", formattedNextPrice='"
        + formattedNextPrice
        + '\''
        + '}';
  }
}
