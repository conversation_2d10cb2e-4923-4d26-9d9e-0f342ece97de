package com.netflix.api.service.list;

import com.netflix.api.service.APIRequest;
import com.netflix.api.service.ContextualItem;
import com.netflix.api.service.ListItem;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.servo.tag.TagList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import rx.Observable;
import rx.Single;

/**
 * Provides a public interface of list-like functionality that supports arbitary range queries
 * Concrete subclasses provide an Observable implementation of batch-loading References and an
 * Observable implementation of generating an Item, given an Reference.
 *
 * <p>Modeling both of these as Observable allows either to do IO without blocking. Modeling
 * loadItem as a single ensures that the proper pattern is followed of getting a list of references
 * up front, and then doing some amount of work to process each reference
 */
public abstract class APIAsyncListImpl<Item, Reference> implements APIList<Item> {

  private static final String NULL_ITEM_COUNTER_STRING = "APIList.nullLoadItem";

  /**
   * Batch load all References
   *
   * @return
   */
  protected abstract Observable<Reference> loadReferences();

  /**
   * Given a Reference, return an item
   *
   * @param reference
   * @return
   */
  protected abstract Single<Item> loadItem(Reference reference);

  @Deprecated
  @Override
  public final String getName() {
    DynamicCounter.increment(
        "deprecated.APIAsyncListImpl.getName",
        "path",
        APIRequest.getCurrentRequest().getRequestContext().getEndpointPath());
    return getListName().toBlocking().first();
  }

  @Deprecated
  @Override
  public final String getId() {
    DynamicCounter.increment(
        "deprecated.APIAsyncListImpl.getId",
        "path",
        APIRequest.getCurrentRequest().getRequestContext().getEndpointPath());
    return getListId().toBlocking().first();
  }

  // stable set of references which cannot be modified, just loaded
  private final Observable<Reference> cachedReferences =
      Observable.defer(this::loadReferences).cache();

  private final ConcurrentHashMap<Reference, Observable<Item>> cachedItems =
      new ConcurrentHashMap<>();

  private Observable<Reference> getReferences() {
    return cachedReferences;
  }

  private Observable<Item> getItem(final Reference reference) {
    Observable<Item> itemFromCache = cachedItems.get(reference);
    if (itemFromCache == null) {
      cachedItems.putIfAbsent(
          reference, Single.defer(() -> loadItem(reference)).toObservable().cache());
      return cachedItems.get(reference);
    } else {
      return itemFromCache;
    }
  }

  @Override
  public Observable<ListItem<Item>> getListItems() {
    return Observable.concatEager(
        getReferences()
            .zipWith(
                Observable.range(0, Integer.MAX_VALUE),
                (reference, index) ->
                    getItem(reference)
                        .filter(Objects::nonNull)
                        .map(item -> new ListItem<>(item, index))));
  }

  @Override
  public Observable<Integer> getSize() {
    return getReferences().count();
  }

  @Override
  public Observable<List<Item>> toOrderedList() {
    return toItems().toList();
  }

  @Override
  public Observable<Item> toItems() {
    return getListItems().map(ContextualItem::getItem);
  }

  protected static void incrementNullItem(TagList tags) {
    DynamicCounter.increment(NULL_ITEM_COUNTER_STRING, tags);
  }
}
