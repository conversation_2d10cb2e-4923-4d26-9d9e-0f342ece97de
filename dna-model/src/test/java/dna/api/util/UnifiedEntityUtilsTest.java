package dna.api.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import dna.api.service.model.UnifiedEntityType;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

class UnifiedEntityUtilsTest {

  @Test
  void makeUnifiedId_gameType_returnsValidEntityId() {
    // Arrange
    String gameId = "123456";

    // Act
    String result = UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.GAME, gameId);

    // Assert
    assertNotNull(result);
  }

  @Test
  void makeUnifiedId_characterType_returnsValidEntityId() {
    // Arrange
    String characterId = "789012345";

    // Act
    String result = UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.CHARACTER, characterId);

    // Assert
    assertNotNull(result);
  }

  @Test
  void makeUnifiedId_collectionType_returnsValidEntityId() {
    // Arrange
    String collectionId = "987654";

    // Act
    String result = UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.COLLECTION, collectionId);

    // Assert
    assertNotNull(result);
  }

  @Test
  void makeUnifiedId_personType_returnsValidEntityId() {
    // Arrange
    String personId = "555666";

    // Act
    String result = UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.PERSON, personId);

    // Assert
    assertNotNull(result);
  }

  @Test
  void makeUnifiedId_videoType_returnsValidEntityId() {
    // Arrange
    String videoId = "111222";

    // Act
    String result = UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.VIDEO, videoId);

    // Assert
    assertNotNull(result);
  }

  @Test
  void makeUnifiedId_unknownType_returnsNull() {
    // Arrange - using null type which should hit default case
    String entityId = "123456";

    // Act & Assert
    assertThrows(
        NullPointerException.class,
        () -> {
          UnifiedEntityUtils.makeUnifiedId(null, entityId);
        });
  }

  @Test
  void makeUnifiedId_gameType_invalidId_throwsRuntimeException() {
    // Arrange
    String invalidId = "not-a-number";

    // Act & Assert
    RuntimeException exception =
        assertThrows(
            RuntimeException.class,
            () -> UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.GAME, invalidId));
    assertEquals(
        "Cannot parse: not-a-number into an Integer to form an EntityID", exception.getMessage());
  }

  @Test
  void makeUnifiedId_characterType_invalidId_throwsRuntimeException() {
    // Arrange
    String invalidId = "invalid-long";

    // Act & Assert
    RuntimeException exception =
        assertThrows(
            RuntimeException.class,
            () -> UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.CHARACTER, invalidId));
    assertEquals(
        "Cannot parse: invalid-long into a Long to form an EntityID", exception.getMessage());
  }

  @Test
  void makeUnifiedId_videoType_invalidId_throwsRuntimeException() {
    // Arrange
    String invalidId = "abc123";

    // Act & Assert
    RuntimeException exception =
        assertThrows(
            RuntimeException.class,
            () -> UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.VIDEO, invalidId));
    assertEquals(
        "Cannot parse: abc123 into an Integer to form an EntityID", exception.getMessage());
  }

  @Test
  void makeUnifiedId_personType_invalidId_throwsRuntimeException() {
    // Arrange
    String invalidId = "xyz789";

    // Act & Assert
    RuntimeException exception =
        assertThrows(
            RuntimeException.class,
            () -> UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.PERSON, invalidId));
    assertEquals(
        "Cannot parse: xyz789 into an Integer to form an EntityID", exception.getMessage());
  }

  @Test
  void makeUnifiedId_collectionType_invalidId_throwsRuntimeException() {
    // Arrange
    String invalidId = "collection-id";

    // Act & Assert
    RuntimeException exception =
        assertThrows(
            RuntimeException.class,
            () -> UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.COLLECTION, invalidId));
    assertEquals(
        "Cannot parse: collection-id into an Integer to form an EntityID", exception.getMessage());
  }

  @ParameterizedTest
  @ValueSource(strings = {"0", "1", "999999", "2147483647"})
  void makeUnifiedId_validIntegerIds_success(String validId) {
    // Act & Assert - should not throw exceptions
    assertNotNull(UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.GAME, validId));
    assertNotNull(UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.COLLECTION, validId));
    assertNotNull(UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.PERSON, validId));
    assertNotNull(UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.VIDEO, validId));
  }

  @ParameterizedTest
  @ValueSource(strings = {"0", "1", "999999999999", "9223372036854775807"})
  void makeUnifiedId_validLongIds_success(String validId) {
    // Act & Assert - should not throw exceptions for CHARACTER type which uses Long
    assertNotNull(UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.CHARACTER, validId));
  }

  @ParameterizedTest
  @ValueSource(strings = {"", " ", "null", "undefined", "2147483648"})
  void makeUnifiedId_edgeCaseIds_handlesGracefully(String edgeCaseId) {
    // Act & Assert - these should throw RuntimeException for integer parsing
    for (UnifiedEntityType type :
        new UnifiedEntityType[] {
          UnifiedEntityType.GAME,
          UnifiedEntityType.COLLECTION,
          UnifiedEntityType.PERSON,
          UnifiedEntityType.VIDEO
        }) {
      assertThrows(
          RuntimeException.class, () -> UnifiedEntityUtils.makeUnifiedId(type, edgeCaseId));
    }
  }

  @Test
  void makeUnifiedId_characterType_tooLargeLong_throwsRuntimeException() {
    // Arrange - Long overflow case
    String overflowId = "92233720368547758080"; // Larger than Long.MAX_VALUE

    // Act & Assert
    assertThrows(
        RuntimeException.class,
        () -> UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.CHARACTER, overflowId));
  }

  @Test
  void makeUnifiedId_nullId_throwsRuntimeException() {
    // Act & Assert
    assertThrows(
        RuntimeException.class,
        () -> UnifiedEntityUtils.makeUnifiedId(UnifiedEntityType.GAME, null));
  }
}
