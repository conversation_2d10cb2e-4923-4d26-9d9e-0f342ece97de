package dna.api.converters;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.netflix.api.platform.MockPropertyRepositoryConfig;
import com.netflix.api.service.APIRequestContext;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserInternal;
import com.netflix.ksclient.SendResult;
import com.netflix.zuul.push.ZuulPushMessage;
import dna.api.service.model.PushMessage;
import dna.api.service.model.PushResult;
import java.util.Optional;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpServletRequest;

class ZuulPushMessageConvertersTest {

  @BeforeAll
  static void beforeClass() {
    MockPropertyRepositoryConfig config = new MockPropertyRepositoryConfig();
    config.init();
  }

  @Test
  void pushResult() {
    final PushResult actual =
        ZuulPushMessageConverters.pushResult(
            new SendResult() {
              @Override
              public String details() {
                return null;
              }

              @Override
              public String id() {
                return "some-id";
              }

              @Override
              public Optional<Object> rawResult() {
                return Optional.empty();
              }

              @Override
              public String toString() {
                return "to-string";
              }
            });

    assertNotNull(actual);
    assertEquals("some-id", actual.getId());
    assertEquals("to-string", actual.getResponse());
  }

  @Test
  void pushResult_nullResult() {
    assertThrows(IllegalArgumentException.class, () -> ZuulPushMessageConverters.pushResult(null));
  }

  @Test
  void zuulPushMessage() {
    final APIUserInternal user = mock(APIUserInternal.class);
    when(user.getRealCustomerId()).thenReturn(123456L);
    when(user.getCustomerGUID()).thenReturn("some-customer-guid");

    final APIRequestContext requestContext = mock(APIRequestContext.class);
    when(requestContext.getESN()).thenReturn("some-esn");

    final MockHttpServletRequest httpReq = new MockHttpServletRequest();
    httpReq.addHeader("x-zuul.push-token.sec", "some-token");
    httpReq.addHeader("x-zuul.push-server.ip", "push-server-ip");

    final ZuulPushMessage<String> actual =
        ZuulPushMessageConverters.zuulPushMessage(
            new PushMessage()
                .setAsync(true)
                .setIsSecure(true)
                .setPayload("some-payload")
                .setNotificationGuid("some-notification-guid")
                .setSenderApp("some-sender-app"),
            user,
            requestContext,
            httpReq,
            false);

    assertEquals(123456L, actual.getCustomerId());
    assertEquals("some-esn", actual.getEsn());
    assertEquals("some-customer-guid", actual.getCustomerGUID());
    assertEquals("some-notification-guid", actual.getNotificationGuid());
    assertEquals("some-sender-app", actual.getSenderApp());
    assertEquals("some-payload", actual.getPayload());
    assertEquals("some-token", actual.getSecurityToken());
    assertEquals("push-server-ip", actual.getLocalIP());
  }

  @Test
  void zuulPushMessage_acceptableValuesWhenNotSpecified() {
    final APIUserInternal user = mock(APIUserInternal.class);
    when(user.getRealCustomerId()).thenReturn(123456L);

    final APIRequestContext requestContext = mock(APIRequestContext.class);
    when(requestContext.getAppName()).thenReturn("some-source-app");

    final ZuulPushMessage<String> actual =
        ZuulPushMessageConverters.zuulPushMessage(
            new PushMessage().setPayload("doesnt-matter"),
            user,
            requestContext,
            new MockHttpServletRequest(),
            false);

    assertEquals("some-source-app", actual.getSenderApp());
    assertNull(actual.getEsn());
    assertNull(actual.getCustomerGUID());
    assertTrue(StringUtils.isNotBlank(actual.getNotificationGuid()));
    assertNull(actual.getSecurityToken());
  }

  @Test
  void zuulPushMessage_directPush() {
    final APIUserInternal user = mock(APIUserInternal.class);
    when(user.getRealCustomerId()).thenReturn(123456L);

    final APIRequestContext requestContext = mock(APIRequestContext.class);
    when(requestContext.getAppName()).thenReturn("some-source-app");

    final MockHttpServletRequest httpReq = new MockHttpServletRequest();
    httpReq.addHeader("x-zuul.push-server.ip", "push-server-ip");

    final ZuulPushMessage<String> actual =
        ZuulPushMessageConverters.zuulPushMessage(
            new PushMessage().setPayload("doesnt-matter"), user, requestContext, httpReq, true);

    assertEquals("some-source-app", actual.getSenderApp());
    assertNull(actual.getEsn());
    assertNull(actual.getCustomerGUID());
    assertTrue(StringUtils.isNotBlank(actual.getNotificationGuid()));
    assertNull(actual.getSecurityToken());
    assertNull(actual.getLocalIP());
  }

  @Test
  void zuulPushMessage_isSecureFalse() {
    final APIUserInternal user = mock(APIUserInternal.class);
    when(user.getRealCustomerId()).thenReturn(123456L);

    final APIRequestContext requestContext = mock(APIRequestContext.class);
    when(requestContext.getAppName()).thenReturn("some-source-name");

    final ZuulPushMessage<String> actual =
        ZuulPushMessageConverters.zuulPushMessage(
            new PushMessage().setPayload("doesnt-matter").setIsSecure(false),
            user,
            requestContext,
            new MockHttpServletRequest(),
            false);

    assertNull(actual.getSecurityToken());
  }

  @Test
  void zuulPushMessage_noSenderAppSpecifiedAndNoAppNameInRequestContext() {
    final APIUserInternal user = mock(APIUserInternal.class);
    when(user.getRealCustomerId()).thenReturn(123456L);

    assertThrows(
        IllegalArgumentException.class,
        () ->
            ZuulPushMessageConverters.zuulPushMessage(
                new PushMessage().setPayload("doesnt-matter"),
                user,
                mock(APIRequestContext.class),
                new MockHttpServletRequest(),
                false));
  }

  @Test
  void zuulPushMessage_nullRequest() {
    final APIRequestContext requestContext = mock(APIRequestContext.class);
    when(requestContext.getAppName()).thenReturn("some-source-app");

    final APIUserInternal user = mock(APIUserInternal.class);
    when(user.getRealCustomerId()).thenReturn(123456L);

    assertThrows(
        IllegalArgumentException.class,
        () ->
            ZuulPushMessageConverters.zuulPushMessage(
                null, user, requestContext, new MockHttpServletRequest(), false));
  }

  @Test
  void zuulPushMessage_nullUser() {
    final APIRequestContext requestContext = mock(APIRequestContext.class);
    when(requestContext.getAppName()).thenReturn("some-source-app");

    assertThrows(
        IllegalArgumentException.class,
        () ->
            ZuulPushMessageConverters.zuulPushMessage(
                new PushMessage(), null, requestContext, new MockHttpServletRequest(), false));
  }

  @Test
  void zuulPushMessage_negativeCustomerID() {
    // see APIUserUtils.java for semantics - returns -1 when user is not APIUserInternal
    final APIRequestContext requestContext = mock(APIRequestContext.class);
    when(requestContext.getAppName()).thenReturn("some-source-app");

    assertThrows(
        IllegalArgumentException.class,
        () ->
            ZuulPushMessageConverters.zuulPushMessage(
                new PushMessage(),
                mock(APIUser.class),
                requestContext,
                new MockHttpServletRequest(),
                false));
  }

  @Test
  void zuulPushMessage_nullRequestContext() {
    final APIUserInternal user = mock(APIUserInternal.class);
    when(user.getRealCustomerId()).thenReturn(123456L);
    assertThrows(
        IllegalArgumentException.class,
        () ->
            ZuulPushMessageConverters.zuulPushMessage(
                new PushMessage(), user, null, new MockHttpServletRequest(), false));
  }

  @Test
  void zuulPushMessage_nullServletRequest() {
    final APIRequestContext requestContext = mock(APIRequestContext.class);
    when(requestContext.getAppName()).thenReturn("some-source-app");

    final APIUserInternal user = mock(APIUserInternal.class);
    when(user.getRealCustomerId()).thenReturn(123456L);
    assertThrows(
        IllegalArgumentException.class,
        () ->
            ZuulPushMessageConverters.zuulPushMessage(
                new PushMessage(), user, requestContext, null, false));
  }
}
