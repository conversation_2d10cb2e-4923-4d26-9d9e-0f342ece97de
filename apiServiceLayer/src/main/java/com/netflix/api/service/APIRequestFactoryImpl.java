package com.netflix.api.service;

import com.netflix.api.service.identity.APIUser;
import com.netflix.spectator.api.Registry;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Objects and state for a single HTTP request/response loop to an API EndpointScript.
 *
 * <p>Each EndpointScript execution will receive an instance of this class containing request
 * specific information such as request URI variables and user identities.
 */
@Component
public class APIRequestFactoryImpl implements APIRequestFactory {
  private final APIRequestContextFactory requestContextFactory;
  private final Registry registry;

  @Autowired
  private APIRequestFactoryImpl(APIRequestContextFactory requestContextFactory, Registry registry) {
    this.requestContextFactory = requestContextFactory;
    this.registry = registry;
  }

  public APIRequest newRequestFromRequest(
      HttpServletRequest request,
      HttpServletResponse response,
      String endpointURI,
      String endpointGroup) {

    // build an APIRequest instance containing data specific to this request
    APIRequestContext context =
        requestContextFactory.getInstance(request, endpointURI, endpointGroup);

    // dole out serviceGateway or restrictedServiceGateway based on a Zuul header
    return newRequestFromDependencies(null, context, request, response);
  }

  @Override
  public APIRequest newRequestWithUser(APIRequest apiRequest, APIUser user) {
    return newRequestFromDependencies(
        user,
        apiRequest.getRequestContext(),
        apiRequest.getServletRequest(),
        apiRequest.getServletResponse());
  }

  private APIRequest newRequestFromDependencies(
      APIUser user,
      APIRequestContext apiRequestContext,
      HttpServletRequest request,
      HttpServletResponse response) {
    APIRequest apiRequest = new APIRequest(user, apiRequestContext, request, response, registry);
    APIRequest.setCurrentRequest(apiRequest);
    return apiRequest;
  }

  @Override
  public APIRequest get() {
    return APIRequest.getCurrentRequest();
  }
}
