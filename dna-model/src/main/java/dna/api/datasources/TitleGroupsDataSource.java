package dna.api.datasources;

import static com.netflix.api.platform.util.CompletionStageAdapter.from;
import static java.util.function.Predicate.not;

import com.netflix.api.service.titlegroup.APITitleGroup;
import com.netflix.api.service.titlegroup.APITitleGroupServiceImpl;
import com.netflix.api.service.titlegroup.APITitleGroupSet;
import com.netflix.api.service.titlegroup.APITitleGroupSetMember;
import com.netflix.demograph.datasource.batch.BatchDataSource;
import dna.api.datasources.utils.DataSources;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
public class TitleGroupsDataSource {

  // datasources (must be singletons for demograph implicit caching to work)
  private final BatchDataSource<Integer, Optional<APITitleGroup>> findTitleGroupById;
  private final BatchDataSource<APITitleGroup, Optional<APITitleGroupSet>>
      titleGroupMembersByGroupId;
  private final BatchDataSource<APITitleGroupSetMember, Optional<APITitleGroupSet>>
      titleGroupSiblingsForMember;
  private final BatchDataSource<Integer, Optional<List<APITitleGroupSetMember>>>
      titleGroupMembershipsVideo;

  @Autowired
  TitleGroupsDataSource(APITitleGroupServiceImpl titleGroupService) {
    // init datasources
    findTitleGroupById = _findTitleGroupById(titleGroupService);
    titleGroupMembersByGroupId = _titleGroupMembersByGroupId(titleGroupService);
    titleGroupSiblingsForMember = _titleGroupSiblingsForMember(titleGroupService);
    titleGroupMembershipsVideo = _titleGroupMembershipsVideo(titleGroupService);
  }

  // datasources public accessors to support mocking / testing

  public BatchDataSource<Integer, Optional<APITitleGroup>> findTitleGroupById() {
    return findTitleGroupById;
  }

  public BatchDataSource<APITitleGroup, Optional<APITitleGroupSet>> titleGroupMembersByGroupId() {
    return titleGroupMembersByGroupId;
  }

  public BatchDataSource<APITitleGroupSetMember, Optional<APITitleGroupSet>>
      titleGroupSiblingsForMember() {
    return titleGroupSiblingsForMember;
  }

  public BatchDataSource<Integer, Optional<List<APITitleGroupSetMember>>>
      titleGroupMembershipsVideo() {
    return titleGroupMembershipsVideo;
  }

  // datasources initializers

  private BatchDataSource<Integer, Optional<APITitleGroup>> _findTitleGroupById(
      APITitleGroupServiceImpl titleGroupService) {
    return DataSources.of(
        "findTitleGroupById",
        (ctx, requests) ->
            from(
                titleGroupService
                    .findGroups(requests)
                    .map(
                        list ->
                            list.stream()
                                .collect(
                                    Collectors.toMap(APITitleGroup::getId, Function.identity())))
                    .map(
                        lookupRes ->
                            requests.stream()
                                .collect(
                                    Collectors.toMap(
                                        Function.identity(),
                                        videoId -> Optional.ofNullable(lookupRes.get(videoId)))))));
  }

  private BatchDataSource<APITitleGroup, Optional<APITitleGroupSet>> _titleGroupMembersByGroupId(
      APITitleGroupServiceImpl titleGroupService) {
    return DataSources.of(
        "getTitleGroupMembershipById",
        (ctx, requests) -> {
          Observable<APITitleGroupSet> members =
              titleGroupService.getGroupSets(requests).flatMap(Observable::from);
          Observable<Map<Integer, APITitleGroupSet>> resultMap =
              members.toMap(s -> s.getGroup().getId());

          return from(
              resultMap.map(
                  r -> {
                    Map<APITitleGroup, Optional<APITitleGroupSet>> optionalMap = new HashMap<>();
                    for (APITitleGroup group : requests) {
                      optionalMap.put(group, Optional.ofNullable(r.get(group.getId())));
                    }
                    return optionalMap;
                  }));
        });
  }

  private BatchDataSource<APITitleGroupSetMember, Optional<APITitleGroupSet>>
      _titleGroupSiblingsForMember(APITitleGroupServiceImpl titleGroupService) {
    return DataSources.of(
        "getTitleGroupSiblingsByGroupId",
        (ctx, requests) ->
            from(
                titleGroupService
                    .findGroupSets(requests)
                    .map(
                        groups ->
                            groups.stream()
                                .filter(group -> Objects.nonNull(group.getRelativeToMember()))
                                .collect(
                                    Collectors.groupingBy(
                                        APITitleGroupSet::getRelativeToMember,
                                        HashMap::new,  // Use HashMap for better performance
                                        Collectors.toList()));
                        })
                    .map(
                        result -> {
                          Map<APITitleGroupSetMember, Optional<APITitleGroupSet>> optionalResult =
                              new HashMap<>();
                          for (APITitleGroupSetMember key : requests) {
                            Optional<APITitleGroupSet> groupSetsOpt =
                                Optional.ofNullable(result.get(key))
                                    .filter(not(List::isEmpty))
                                    .map(List::getFirst);
                            optionalResult.put(key, groupSetsOpt);
                          }
                          return optionalResult;
                        })));
  }

  private BatchDataSource<Integer, Optional<List<APITitleGroupSetMember>>>
      _titleGroupMembershipsVideo(APITitleGroupServiceImpl titleGroupService) {
    return DataSources.of(
        "getTitleGroupMembershipsVideoId",
        (ctx, requests) ->
            from(
                titleGroupService
                    .findMembersFromIds(requests)
                    .map(
                        members ->
                            members.stream()
                                .map(APITitleGroupSetMember.class::cast)
                                .collect(
                                    Collectors.groupingBy(APITitleGroupSetMember::getMemberId)))
                    .map(
                        members -> {
                          Map<Integer, Optional<List<APITitleGroupSetMember>>> optionalMap =
                              new HashMap<>();
                          for (Integer videoId : requests) {
                            optionalMap.put(videoId, Optional.ofNullable(members.get(videoId)));
                          }
                          return optionalMap;
                        })));
  }
}
