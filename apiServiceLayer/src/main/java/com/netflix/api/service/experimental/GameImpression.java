package com.netflix.api.service.experimental;

import com.google.protobuf.ByteString;
import java.nio.BufferUnderflowException;
import java.nio.ByteBuffer;
import java.util.Objects;

public class GameImpression {
  final Integer count;
  final Long resetAt;
  final Long updatedAt;

  public GameImpression(Integer count, Long resetAt, Long updatedAt) {
    this.count = count;
    this.resetAt = resetAt;
    this.updatedAt = updatedAt;
  }

  GameImpression(ByteString bytes) throws BufferUnderflowException {
    ByteBuffer buffer = bytes.asReadOnlyByteBuffer();
    this.count = buffer.getInt();
    this.resetAt = buffer.getLong();
    this.updatedAt = buffer.getLong();
  }

  ByteString encode() {
    ByteBuffer buffer =
        ByteBuffer.allocate(4 + 8 + 8).putInt(count).putLong(resetAt).putLong(updatedAt);
    buffer.rewind();
    return ByteString.copyFrom(buffer);
  }

  public Integer getCount() {
    return this.count;
  }

  public Long getResetAt() {
    return this.resetAt;
  }

  public Long getUpdatedAt() {
    return this.updatedAt;
  }

  public boolean equals(final Object o) {
    if (o == this) return true;
    if (!(o instanceof GameImpression other)) return false;
    if (!other.canEqual(this)) return false;
    final Object this$count = this.getCount();
    final Object other$count = other.getCount();
    if (!Objects.equals(this$count, other$count)) return false;
    final Object this$resetAt = this.getResetAt();
    final Object other$resetAt = other.getResetAt();
    if (!Objects.equals(this$resetAt, other$resetAt)) return false;
    final Object this$updatedAt = this.getUpdatedAt();
    final Object other$updatedAt = other.getUpdatedAt();
    return Objects.equals(this$updatedAt, other$updatedAt);
  }

  protected boolean canEqual(final Object other) {
    return other instanceof GameImpression;
  }

  public int hashCode() {
    final int PRIME = 59;
    int result = 1;
    final Object $count = this.getCount();
    result = result * PRIME + ($count == null ? 43 : $count.hashCode());
    final Object $resetAt = this.getResetAt();
    result = result * PRIME + ($resetAt == null ? 43 : $resetAt.hashCode());
    final Object $updatedAt = this.getUpdatedAt();
    result = result * PRIME + ($updatedAt == null ? 43 : $updatedAt.hashCode());
    return result;
  }

  public String toString() {
    return "GameImpression(count="
        + this.getCount()
        + ", resetAt="
        + this.getResetAt()
        + ", updatedAt="
        + this.getUpdatedAt()
        + ")";
  }
}
