package com.netflix.api.service.batch;

import com.netflix.aballocator.protogen.AllocResponse;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.abtest.ABAdapterBase;
import com.netflix.api.service.device.APIDeviceManagementService;
import com.netflix.api.service.identity.APIUser;
import com.netflix.archaius.api.Property;
import com.netflix.originals.row.filters.OriginalsRowFilter;
import com.netflix.originals.row.filters.OriginalsRowFilterBuilder;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
public class OriginalsServiceImpl implements OriginalsService {
  private static final String EXCLUDE_FROM_AB = "Exclude_from_AB";
  private final OriginalsRowFilterBuilder originalsRowFilterBuilderFactory;
  private final ABAdapterBase abAdapterBase;
  private final APIDeviceManagementService deviceManagementService;

  public static final Property<Boolean> ORIGINALS_FILTER_TEST_MODE =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.originals.filter.test.mode", Boolean.class)
          .orElse(false);

  @Autowired
  public OriginalsServiceImpl(
      OriginalsRowFilterBuilder originalsRowFilterBuilderFactory,
      ABAdapterBase abAdapterBase,
      APIDeviceManagementService deviceManagementService) {
    this.originalsRowFilterBuilderFactory = originalsRowFilterBuilderFactory;
    this.abAdapterBase = abAdapterBase;
    this.deviceManagementService = deviceManagementService;
  }

  private Observable<OriginalsRowFilter> getFilter(Map<String, String> ouiAnnotations) {
    Observable<AllocResponse> allocations =
        abAdapterBase.getAllocationsAsProto(ORIGINALS_FILTER_TEST_MODE.get(), false);

    Observable<Map<String, String>> properties =
        deviceManagementService.getCurrentDeviceProperties(
            Collections.singleton(EXCLUDE_FROM_AB), null);
    String esn = APIRequest.getCurrentRequest().getRequestContext().getESN();
    APIUser user = APIRequest.getCurrentRequest().getUser();

    boolean kids = user != null && user.isJustForKidsExperience();
    return properties.flatMap(
        props -> {
          boolean excludeFromAB =
              props.containsKey(EXCLUDE_FROM_AB)
                  && Boolean.parseBoolean(props.get(EXCLUDE_FROM_AB));
          return allocations.map(
              allocs ->
                  originalsRowFilterBuilderFactory
                      .withEsnBucket(
                          APIRequest.getCurrentRequest().getRequestContext().getESNBucket())
                      .withEsn(esn)
                      .excludeFromAB(excludeFromAB)
                      .withUIVersion(ouiAnnotations.get(APILoloUtils.OUI_ANNOTATIONS_UIVERSION_KEY))
                      .withIdiom(ouiAnnotations.get(APILoloUtils.OUI_ANNOTATIONS_IDIOM_KEY))
                      .withPlatform(ouiAnnotations.get(APILoloUtils.OUI_ANNOTATIONS_PLATFORM_KEY))
                      .withOrcVersion(
                          ouiAnnotations.get(APILoloUtils.OUI_ANNOTATIONS_ORCVERSION_KEY))
                      .withKids(kids)
                      .withCountry(
                          APIRequest.getCurrentRequest().getRequestContext().getCountry().getId())
                      .withLanguage(
                          APIRequest.getCurrentRequest().getRequestContext().getLocale().getId())
                      .withIsRtl(
                          APIRequest.getCurrentRequest()
                              .getRequestContext()
                              .getLocale()
                              .isRightToLeft())
                      .getFilterForAllocations(allocs));
        });
  }

  @Override
  public Observable<List<String>> filterSupportedBillboards(
      List<String> unfilteredSupportedBillboards, Map<String, String> ouiAnnotations) {
    return getFilter(ouiAnnotations)
        .flatMap(f -> f.filterSupportedBillboards(unfilteredSupportedBillboards));
  }

  @Override
  public Observable<List<String>> filterSupportedLolomoFeatures(
      List<String> unfilteredSupportedLolomoFeatures, Map<String, String> ouiAnnotations) {
    return getFilter(ouiAnnotations)
        .flatMap(f -> f.filterSupportedLolomoFeatures(unfilteredSupportedLolomoFeatures));
  }

  @Override
  public Observable<List<String>> filterSupportedClientCapabilities(
      List<String> unfilteredSupportedClientCapabilities, Map<String, String> ouiAnnotations) {
    return getFilter(ouiAnnotations)
        .flatMap(f -> f.filterSupportedLolomoFeatures(unfilteredSupportedClientCapabilities));
  }
}
