package com.netflix.api.service.account;

import com.netflix.api.service.identity.APIUser;

public class APIProductChoicesRequest {
  private final APIUser user;
  private final String context;
  private final String locale;
  private final String nglVersion;
  private final String promoData;
  private final String filterType;

  private APIProductChoicesRequest(
      APIUser user,
      String context,
      String locale,
      String nglVersion,
      String promoData,
      String filterType) {
    this.user = user;
    this.context = context;
    this.locale = locale;
    this.nglVersion = nglVersion;
    this.promoData = promoData;
    this.filterType = filterType;
  }

  public APIUser getUser() {
    return user;
  }

  public String getContext() {
    return context;
  }

  public String getLocale() {
    return locale;
  }

  public String getNglVersion() {
    return nglVersion;
  }

  public String getPromoData() {
    return promoData;
  }

  public String getFilterType() {
    return filterType;
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {

    private APIUser user;
    private String context;
    private String locale;
    private String nglVersion;
    private String promoData;
    private String filterType;

    private Builder() {}

    public Builder user(APIUser user) {
      this.user = user;
      return this;
    }

    public Builder context(String context) {
      this.context = context;
      return this;
    }

    public Builder locale(String locale) {
      this.locale = locale;
      return this;
    }

    public Builder nglVersion(String nglVersion) {
      this.nglVersion = nglVersion;
      return this;
    }

    public Builder promoData(String getPromoData) {
      this.promoData = getPromoData;
      return this;
    }

    public Builder filterType(String filterType) {
      this.filterType = filterType;
      return this;
    }

    public APIProductChoicesRequest build() {
      return new APIProductChoicesRequest(user, context, locale, nglVersion, promoData, filterType);
    }

    public String toString() {
      return "APIProductChoicesRequest.Builder(user="
          + this.user
          + ", context="
          + this.context
          + ", locale="
          + this.locale
          + ", nglVersion="
          + this.nglVersion
          + ", getPromoData="
          + this.promoData
          + ", filterType="
          + this.filterType
          + ")";
    }
  }
}
