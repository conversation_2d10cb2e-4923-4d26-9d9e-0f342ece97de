package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** A display ad. */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class DisplayAd {
  /** Identifies the specific ad to be displayed. */
  private String creativeId;

  /** Image URL for the ad */
  private String url;

  /** Info for recording events of the ad impression */
  private DisplayAdEvents events;

  /**
   * Gradient Color Target for the ad image seen by the user, expressed in hexadecimal format
   * #rrggbb.
   */
  private String gradientColorTarget;

  public String getCreativeId() {
    return creativeId;
  }

  public String getUrl() {
    return url;
  }

  public DisplayAdEvents getEvents() {
    return events;
  }

  public String getGradientColorTarget() {
    return gradientColorTarget;
  }

  public DisplayAd setCreativeId(String creativeId) {
    this.creativeId = creativeId;
    return this;
  }

  public DisplayAd setUrl(String url) {
    this.url = url;
    return this;
  }

  public DisplayAd setEvents(DisplayAdEvents events) {
    this.events = events;
    return this;
  }

  public DisplayAd setGradientColorTarget(String gradientColorTarget) {
    this.gradientColorTarget = gradientColorTarget;
    return this;
  }
}
