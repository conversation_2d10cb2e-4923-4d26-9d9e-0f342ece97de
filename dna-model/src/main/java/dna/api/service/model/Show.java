package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class Show implements GraphData {
  /**
   * @dotnext com.netflix.api.service.video.APIShow#hasNeverBeenWatchedBy
   * @deprecated use the video-level route instead
   */
  public Boolean hasBeenWatched;

  /** total display runtime in seconds, summing up all launched episodes (no DAB episodes) */
  public Integer totalDisplayRuntime;

  public Video currentEpisode;

  public Map<String, Video> episodes;

  public Map<String, Video> availableAndUpcomingEpisodes;

  public Map<String, String> numSeasonsLabel;

  public Map<String, Atom<NumSeasonsLabelWithOptionsResponse>> numSeasonsLabelWithOptions;

  public Map<String, Video> seasons;

  public Video upcomingSeason;

  public Map<String, Map<String, LiveEvent>> liveEvents;

  public LiveEvent nextLiveEvent;

  public Boolean getHasBeenWatched() {
    return hasBeenWatched;
  }

  public Integer getTotalDisplayRuntime() {
    return totalDisplayRuntime;
  }

  public Show setHasBeenWatched(Boolean hasBeenWatched) {
    this.hasBeenWatched = hasBeenWatched;
    return this;
  }

  public Show setTotalDisplayRuntime(Integer totalDisplayRuntime) {
    this.totalDisplayRuntime = totalDisplayRuntime;
    return this;
  }
}
