package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class TimedTextType {
  private String value;

  private String description;

  public String getValue() {
    return value;
  }

  public String getDescription() {
    return description;
  }

  public TimedTextType setValue(String value) {
    this.value = value;
    return this;
  }

  public TimedTextType setDescription(String description) {
    this.description = description;
    return this;
  }
}
