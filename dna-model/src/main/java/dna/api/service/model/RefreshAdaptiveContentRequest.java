package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** Refresh volatile content as well as custom annotations, and other request metadata */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class RefreshAdaptiveContentRequest {
  private String lolomoId;

  private Map<String, Object> annotations;

  public String getLolomoId() {
    return lolomoId;
  }

  public Map<String, Object> getAnnotations() {
    return annotations;
  }

  public RefreshAdaptiveContentRequest setLolomoId(String lolomoId) {
    this.lolomoId = lolomoId;
    return this;
  }

  public RefreshAdaptiveContentRequest setAnnotations(Map<String, Object> annotations) {
    this.annotations = annotations;
    return this;
  }
}
