package com.netflix.api.startup.priming;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.inject.Key;
import com.google.inject.Singleton;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class PrimingDependencyInitializationLogger {

  private static final Logger LOGGER =
      LoggerFactory.getLogger(PrimingDependencyInitializationLogger.class);

  private static final Property<Integer> loggingInterval =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.startup.concurrent.logging.intervalsec", Integer.class)
          .orElse(2);
  private static final Property<Integer> stackdumpInterval =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.startup.concurrent.logging.stackdumpintsec", Integer.class)
          .orElse(120);

  private static final Property<Integer> maxLoggingTime =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.startup.concurrent.logging.stopafter", Integer.class)
          .orElse(900);

  private final ConcurrentHashMap<Key<?>, DependencyTask> inFlight = new ConcurrentHashMap<>();
  private final ScheduledExecutorService executor;

  private volatile ScheduledFuture<?> future;

  private long startedLoggingTimestamp = 0;

  public PrimingDependencyInitializationLogger() {
    if (LOGGER.isInfoEnabled()) {
      executor =
          Executors.newSingleThreadScheduledExecutor(
              new ThreadFactoryBuilder().setNameFormat("initlog-%d").build());
      start();
    } else {
      executor = null;
    }
  }

  public void start() {
    if (executor != null && future == null && loggingInterval.get() > 0) {
      startedLoggingTimestamp = System.nanoTime();
      future =
          executor.scheduleWithFixedDelay(
              this::logSummary, loggingInterval.get(), loggingInterval.get(), TimeUnit.SECONDS);
    }
  }

  public void stop() {
    if (future != null) {
      future.cancel(false);
      future = null;
    }
  }

  public void logInitialization(final Key<?> key, final Runnable runnable) {
    markStartDependency(key);
    try {
      runnable.run();
    } finally {
      markDependencyFinished(key);
    }
  }

  private void markStartDependency(Key<?> key) {
    if (!LOGGER.isInfoEnabled()) {
      return;
    }
    inFlight.put(
        key,
        new DependencyTask(key, new WeakReference<>(Thread.currentThread()), System.nanoTime()));
  }

  private void markDependencyFinished(Key<?> key) {
    if (!LOGGER.isInfoEnabled()) {
      return;
    }
    inFlight.remove(key);
  }

  private void logSummary() {
    long deltaNs = System.nanoTime() - startedLoggingTimestamp;
    if (TimeUnit.NANOSECONDS.toSeconds(deltaNs) >= maxLoggingTime.get() && future != null) {
      stop();
      try {
        PrimingMetricsLogger.dump();
      } catch (IOException e) {
        LOGGER.error("Can't dump the priming metrics", e);
      }
    }

    if (LOGGER.isInfoEnabled()) {
      LOGGER.info("Initializing: {}", inFlight.keySet().stream().map(this::keyToString).toList());
    }

    inFlight.values().forEach(DependencyTask::dumpStackIfNeeded);
  }

  private String keyToString(Key<?> key) {
    String str = key.getTypeLiteral().toString();
    if (key.getAnnotation() != null) {
      str += "{" + key.getAnnotation() + "}";
    }
    return str;
  }

  private static class DependencyTask {
    final WeakReference<Thread> thread;
    long lastDump;
    final Key<?> key;

    public DependencyTask(Key<?> key, WeakReference<Thread> thread, long startTime) {
      super();
      this.thread = thread;
      this.lastDump = startTime;
      this.key = key;
    }

    public void dumpStackIfNeeded() {
      long delta = System.nanoTime() - this.lastDump;
      if (TimeUnit.NANOSECONDS.toSeconds(delta) > stackdumpInterval.get()) {
        this.lastDump += delta;
        Thread strongThread = thread.get();
        if (strongThread != null) {
          StringBuffer buffer = new StringBuffer();
          Arrays.stream(strongThread.getStackTrace())
              .forEach(
                  s -> {
                    buffer.append("\n\t");
                    buffer.append(s.toString());
                  });
          LOGGER.info("Slow dependency {} on {}: {} ", key, strongThread.getName(), buffer);
        }
      }
    }
  }
}
