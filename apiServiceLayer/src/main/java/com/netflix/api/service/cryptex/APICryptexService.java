package com.netflix.api.service.cryptex;

import rx.Observable;

/** Service for encryption/decryption of data */
public interface APICryptexService {
  /**
   * Generate AES128-CBC encryption key
   *
   * @return Observable that emits the key pair generated
   */
  Observable<KeyPair> generateAes128Cbc(byte[] data);

  /** The raw key bytes broken into the pair of keys */
  interface KeyPair {
    byte[] aesKey();

    byte[] hmacKey();
  }
}
