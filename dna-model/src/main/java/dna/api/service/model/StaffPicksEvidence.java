package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class StaffPicksEvidence implements GraphData {
  public String curatorByline;

  public String valueProp;

  public String staffPicksType;

  public String getCuratorByline() {
    return curatorByline;
  }

  public String getValueProp() {
    return valueProp;
  }

  public String getStaffPicksType() {
    return staffPicksType;
  }

  public StaffPicksEvidence setCuratorByline(String curatorByline) {
    this.curatorByline = curatorByline;
    return this;
  }

  public StaffPicksEvidence setValueProp(String valueProp) {
    this.valueProp = valueProp;
    return this;
  }

  public StaffPicksEvidence setStaffPicksType(String staffPicksType) {
    this.staffPicksType = staffPicksType;
    return this;
  }
}
