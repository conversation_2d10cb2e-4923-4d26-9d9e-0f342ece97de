package com.netflix.api.service.list;

import com.netflix.api.service.APICountry;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIUser;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @see
 */
public class ListRefreshRequestImpl implements ListRefreshRequest {
  private final APIUser user = APIRequest.getCurrentRequest().getUser();
  private final String listId;
  private final String rootLolomoId;
  private final APILolomoList.ListContext listContext;
  private final String esn =
      Optional.ofNullable(APIRequest.getCurrentRequest().getRequestContext().getESN()).orElse("");
  private final APICountry country =
      APIRequest.getCurrentRequest().getRequestContext().getCountry();
  private final Map<String, Object> annotations = new HashMap<>();

  public ListRefreshRequestImpl(
      String listId, String rootLolomoId, APILolomoList.ListContext listContext) {
    this.listId = listId;
    this.rootLolomoId = rootLolomoId;
    this.listContext = listContext;
  }

  @Override
  public APIUser getUser() {
    return user;
  }

  @Override
  public String getListId() {
    return listId;
  }

  @Override
  public String getRootLolomoId() {
    return rootLolomoId;
  }

  @Override
  public boolean isForKids() {
    return false;
  }

  @Override
  public String getEsn() {
    return esn;
  }

  @Override
  public APICountry getCountry() {
    return country;
  }

  @Override
  public APILolomoList.ListContext getListContext() {
    return listContext;
  }

  /**
   * Get the request annotations added to this request.
   *
   * @return the request annotations set by you.
   */
  @Override
  public Map<String, Object> getAnnotations() {
    return annotations;
  }
}
