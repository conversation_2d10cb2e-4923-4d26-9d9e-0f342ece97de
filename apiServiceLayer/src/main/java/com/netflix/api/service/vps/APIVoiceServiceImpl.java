package com.netflix.api.service.vps;

import com.google.common.base.Preconditions;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.grpc.GrpcCallHelpers.RxSingle;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import com.netflix.api.service.identity.APIMaturity;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.vps.protogen.AppContext;
import com.netflix.vps.protogen.CommandType;
import com.netflix.vps.protogen.CustomerInfo;
import com.netflix.vps.protogen.DeviceInfo;
import com.netflix.vps.protogen.Intent;
import com.netflix.vps.protogen.MaturityLevel;
import com.netflix.vps.protogen.RequestContext;
import com.netflix.vps.protogen.Resource;
import com.netflix.vps.protogen.VpsRequest;
import com.netflix.vps.protogen.VpsRequest.Builder;
import com.netflix.vps.protogen.VpsResponse;
import com.netflix.vps.protogen.VpsServiceGrpc.VpsServiceStub;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Single;

@Component
@EnableDeprecatedMetrics
public class APIVoiceServiceImpl implements APIVoiceService {

  private final VpsServiceStub vpsService;

  @Autowired
  public APIVoiceServiceImpl(@GrpcSpringClient("vps") VpsServiceStub vpsService) {
    this.vpsService = vpsService;
  }

  public Observable<APIVoiceProcessingServiceResponse> getVoiceCommands(
      final APIVoiceRequestInput input,
      final APIVoiceRequestHeader header,
      final APIVoiceRequestPayload voicePayload,
      final String messageId) {
    Preconditions.checkNotNull(input);
    Preconditions.checkNotNull(header);
    Preconditions.checkNotNull(messageId);
    if (!isNonMemberRequest()) {
      Preconditions.checkNotNull(CurrentVisitor.get());
    }
    Preconditions.checkNotNull(APIRequest.getCurrentRequest().getRequestContext());
    return RxObservable.defer(
            vpsService::getListCommands, transformRequest(input, header, voicePayload, messageId))
        .map(this::transformResponse);
  }

  @Override
  public Single<APIVoiceProcessingServiceResponse> logVoiceCommand(
      APIVoiceRequestInput input,
      APIVoiceRequestHeader header,
      APIVoiceRequestPayload voicePayload,
      String messageId) {
    return RxSingle.defer(
            vpsService::logAndAuditListCommands,
            transformRequest(input, header, voicePayload, messageId))
        .map(this::transformResponse);
  }

  @Override
  public APIVoiceRequestHeader createRequestHeader(
      final APIVoiceCommandType commandDirective, final String payLoadVersion) {
    return new APIVoiceRequestHeaderImpl(commandDirective, payLoadVersion);
  }

  @Override
  public APIVoiceRequestInput createRequestInput(
      final String rawTextUtterance,
      final String languageCode,
      final APIMaturity profileMaturity,
      final String appName,
      final boolean odpAware) {
    Preconditions.checkNotNull(rawTextUtterance);
    Preconditions.checkNotNull(languageCode);
    Preconditions.checkNotNull(appName);
    return new APIVoiceRequestInputImpl(
        rawTextUtterance, languageCode, profileMaturity, appName, odpAware, null);
  }

  @Override
  public APIVoiceRequestInput createRequestInput(
      final String rawTextUtterance,
      final String languageCode,
      final APIMaturity profileMaturity,
      final String appName,
      final boolean odpAware,
      final Map<String, String> additionalContext) {
    Preconditions.checkNotNull(rawTextUtterance);
    Preconditions.checkNotNull(languageCode);
    Preconditions.checkNotNull(appName);
    return new APIVoiceRequestInputImpl(
        rawTextUtterance, languageCode, profileMaturity, appName, odpAware, additionalContext);
  }

  @Override
  public APIVoiceResource createVoiceResource(
      final APIVoiceResourceType voiceResourceType, final String id, final String value) {
    return new APIVoiceResourceImpl(voiceResourceType, id, value);
  }

  @Override
  public APIVoiceIntent createVoiceIntent(
      final APIVoiceIntentType voiceIntentType, final String id, final String value) {
    return new APIVoiceIntentImpl(voiceIntentType, id, value);
  }

  @Override
  public APIVoiceRequestPayload createRequestPayload(
      final List<APIVoiceResource> resources, final List<APIVoiceIntent> intents) {
    return new APIVoiceRequestPayloadImpl(resources, intents);
  }

  private VpsRequest transformRequest(
      final APIVoiceRequestInput input,
      final APIVoiceRequestHeader header,
      final APIVoiceRequestPayload voicePayload,
      final String messageId) {

    Builder vpsRequestBuilder = VpsRequest.newBuilder();
    vpsRequestBuilder.setRequestId(messageId);
    vpsRequestBuilder.setRawTextUtterance(input.getRawTextUtterance());

    Optional.ofNullable(header.getPayloadVersion()).ifPresent(vpsRequestBuilder::setPayloadVersion);
    Optional.ofNullable(header.getCommandDirective())
        .ifPresent(
            commandDirective ->
                vpsRequestBuilder.setDirective(CommandType.valueOf(commandDirective.toString())));
    APIRequestContext context = APIRequest.getCurrentRequest().getRequestContext();
    RequestContext.Builder vpsRequestContextBuilder = RequestContext.newBuilder();

    vpsRequestContextBuilder.setDeviceInfo(
        getDeviceBuilder(
            input.isOdpAware(), context.getDeviceTypeId(), context.getDeviceType().getId()));

    CustomerInfo.Builder customerInfoBuilder = CustomerInfo.newBuilder();
    if (isNonMemberRequest()) {
      customerInfoBuilder.setIsNonMember(true);
    } else {
      customerInfoBuilder.setCustomerId(CurrentVisitor.get().getId());
    }

    Optional.ofNullable(input.getProfileMaturity())
        .ifPresent(
            maturity ->
                customerInfoBuilder.setMaturityLevel(MaturityLevel.valueOf(maturity.toString())));

    vpsRequestContextBuilder.setCustomerInfo(customerInfoBuilder);
    vpsRequestContextBuilder.setCountry(context.getCountry().getId());
    vpsRequestContextBuilder.setLanguage(input.getLanguageCode());
    vpsRequestContextBuilder.setAppContext(
        getAppContextBuilder(input.getAppName(), input.getAdditionalContext()));

    vpsRequestBuilder.setContext(vpsRequestContextBuilder);

    Optional.ofNullable(voicePayload)
        .ifPresent(
            payload -> {
              final List<APIVoiceResource> payloadResources = payload.getResources();
              if (payloadResources != null) {
                for (APIVoiceResource resource : payloadResources) {
                  vpsRequestBuilder.addResources(getVoiceResource(resource));
                }
              }

              final List<APIVoiceIntent> payloadIntents = payload.getIntents();
              if (payloadIntents != null) {
                for (APIVoiceIntent intent : payloadIntents) {
                  vpsRequestBuilder.addIntents(getVoiceIntent(intent));
                }
              }
            });

    return vpsRequestBuilder.build();
  }

  private DeviceInfo.Builder getDeviceBuilder(
      boolean isOdpAware, Integer deviceId, int deviceType) {
    DeviceInfo.Builder deviceBuilder = com.netflix.vps.protogen.DeviceInfo.newBuilder();
    deviceBuilder.setDeviceId(String.valueOf(deviceId));
    deviceBuilder.setDeviceType(String.valueOf(deviceType));
    deviceBuilder.setOdpAware(isOdpAware);
    return deviceBuilder;
  }

  private AppContext.Builder getAppContextBuilder(
      String appName, Map<String, String> additionalContext) {
    AppContext.Builder builder = AppContext.newBuilder();
    builder.setName(appName);
    if (additionalContext != null) builder.putAllAdditionalContext(additionalContext);
    return builder;
  }

  private Resource getVoiceResource(APIVoiceResource resource) {
    Resource.Builder builder = Resource.newBuilder();
    builder.setType(Resource.Type.valueOf(resource.getVoiceResourceType().toString()));
    builder.setId(resource.getId());
    builder.setValue(resource.getValue());
    return builder.build();
  }

  private Intent getVoiceIntent(APIVoiceIntent intent) {
    Intent.Builder builder = Intent.newBuilder();
    builder.setId(intent.getId());
    builder.setValue(intent.getValue());
    builder.setType(Intent.Type.valueOf(intent.getVoiceIntentType().toString()));
    return builder.build();
  }

  private APIVoiceProcessingServiceResponseImpl transformResponse(VpsResponse response) {
    return Optional.of(response)
        .map(
            res ->
                new APIVoiceProcessingServiceResponseImpl(
                    res.getCommandsList(), res.getAdditionalPropertiesMap()))
        .get();
  }

  private static boolean isNonMemberRequest() {
    return CurrentVisitor.get() == null;
  }
}
