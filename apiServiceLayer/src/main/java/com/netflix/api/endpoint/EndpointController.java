package com.netflix.api.endpoint;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestFactory;
import com.netflix.api.service.identity.APIAuthorizationException;
import com.netflix.api.service.identity.APIIdentityService;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.archaius.api.Property;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.type.TypeManager;
import com.netflix.type.Visitor;
import jakarta.servlet.AsyncContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import net.jcip.annotations.ThreadSafe;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/*
 * Front-controller for handling all requests (to RESTful 'endpoints') that may be served by the
 * API.Next endpoint scripting tier.
 *
 * <p>If a script is found for a given endpoint, it will be executed. If not, a 404 will be
 * returned.
 *
 * <p><b>Authentication</b>
 *
 * <p>By default, all requests will be authenticated and expect Netflix cookies (NetflixID and
 * SecureNetflixID) to be present and valid, otherwise a 401 will be returned.
 *
 * <p>A script can define an attribute (userAuthenticationRequired == false) to make authentication
 * optional (allow unauthorized requests).
 *
 * <p>If this is set to false then it is up to the script to perform conditional checks on the user
 * being correctly validated.
 *
 * <p><b>Error Responses</b>
 *
 * <p>By default, error codes are returned with empty bodies, such as 401 and 500.
 *
 * <p>A debug parameter (ie. ?debug=true) can be passed in on a request to have the error message
 * printed to the outputstream on a 500.
 *
 * <p>Default responses can be over-written by uploading scripts with the following naming
 * convention:
 *
 * <p>
 * <li>401.groovy: Executed when a 401 occurs (Not applicable if authentication is made optional).
 * <li>500.groovy: Executed when a 500 occurs. This occurs when the main script fails.
 *
 *     <p><b>Development</b>
 *
 *     <p>In development, instead of needing to pass in valid cookies, a userId argument can be
 *     passed in and the APIUser object will be loaded using it.
 *
 *     <p>This will not be permitted in PROD.
 *
 *     <p>
 * <li>Example: /endpoint?userId=123456 will load an APIUser with customerId 123456
 */
@Component
@ThreadSafe
public class EndpointController {
  private static final Logger logger = LoggerFactory.getLogger(EndpointController.class);

  private static class HaltProcessing extends Exception {}

  /**
   * Timeout for the whole request execution
   *
   * <ul>
   *   <li>A value less than 0 to use the EndpointThreadCommandBase default timeout
   *   <li>A value of 0 for no timeout
   *   <li>A value greater than 0 to use as timeout value in milliseconds
   * </ul>
   */
  private static final Property<Long> ASYNC_REQUEST_TIMEOUT =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.servlet.enableasync.timeoutms", Long.class)
          .orElse(60_000L);

  private final APIRequestFactory requestFactory;
  private final APIIdentityService identityService;
  private final EndpointContextInitializer endpointContextInitializer;
  private final RequestRetryLogic requestRetryLogic;
  private final Executor executor;

  @Autowired
  public EndpointController(
      APIRequestFactory requestFactory,
      APIIdentityService identityService,
      EndpointContextInitializer endpointContextInitializer,
      RequestRetryLogic requestRetryLogic,
      EndpointThreadCommandExecutor executor) {
    this.requestFactory = requestFactory;
    this.identityService = identityService;
    this.endpointContextInitializer = endpointContextInitializer;
    this.requestRetryLogic = requestRetryLogic;
    this.executor = executor;
  }

  public void handleRequest(
      HttpServletRequest request, HttpServletResponse response, EndpointScript script) {

    AsyncContext context = request.startAsync();
    if (ASYNC_REQUEST_TIMEOUT.get() < 0) {
      context.setTimeout(EndpointThreadCommandBase.getDefaultExecutionTimeoutInMilliseconds());
    } else {
      context.setTimeout(ASYNC_REQUEST_TIMEOUT.get());
    }

    CompletableFuture.completedFuture(null)
        .thenComposeAsync(_Void -> executeEndpointScriptAsync(request, response, script), executor)
        .whenComplete((_Void, th) -> context.complete());
  }

  private CompletionStage<Void> executeEndpointScriptAsync(
      HttpServletRequest request, HttpServletResponse response, EndpointScript script) {

    APIRequest apiRequest =
        requestFactory.newRequestFromRequest(
            request, response, script.getEndpointURI(), script.getEndpointGroup());

    APIUser user;
    try {
      user = authenticateUser(apiRequest, script);
    } catch (HaltProcessing e) {
      return CompletableFuture.completedFuture(null);
    }

    // we are authenticated (or authentication is optional) so execute the script
    apiRequest = requestFactory.newRequestWithUser(apiRequest, user);

    endpointContextInitializer.initializeContext(request, null);
    if (user != null) {
      endpointContextInitializer.initializeContextLocales(apiRequest.getServletRequest(), user);
    }

    final APIRequest apiRequestFinal = apiRequest;

    return script
        .execute(apiRequest)
        .handle(
            (_Void, throwable) -> {
              if (throwable instanceof CompletionException) {
                Throwable cause = throwable.getCause();
                if (cause != null) {
                  throwable = cause;
                }
              }
              return throwable;
            })
        .thenApply(
            throwable -> {
              if (throwable != null) {
                handleThrowable(response, apiRequestFinal, throwable);
              }
              return null;
            });
  }

  private void handleThrowable(HttpServletResponse response, APIRequest apiRequest, Throwable e) {
    int statusCode = 500;
    if (requestRetryLogic.isRequestRetriableOnException(e, apiRequest)) {
      statusCode = 503;
    }
    logger.error("Endpoint Response {} => {}", statusCode, ScriptDebugContext.getRequestInfo(), e);
    response.setStatus(statusCode);
  }

  private APIUser authenticateUser(APIRequest apiRequest, EndpointScript script)
      throws HaltProcessing {
    // authenticate user
    boolean force401 = false;
    Throwable exception = null;
    APIUser user = null;
    HttpServletResponse response = apiRequest.getServletResponse();
    HttpServletRequest request = apiRequest.getServletRequest();
    try {
      // DNA specific case: insecure cookies are ok and no user auth is required
      user = identityService.authenticateRequest(request, response, false, false);
    } catch (APIAuthorizationException e) {
      exception = e;
      // this is an expected flow so I'm setting logging to debug so we don't generally see it
      if (logger.isDebugEnabled()) {
        logger.debug("Endpoint [{}] Authorization Failure", script.getEndpointURI(), e);
      }
      if (e.getAuthorizationExceptionCause()
          == APIAuthorizationException.AuthorizationExceptionCause.AUTHENTICATION_FAILURE) {
        // if we have cookies that fail authentication, we want to force a 401 regardless of
        // whether the script requires authentication or not
        force401 = true;
      }
    }

    /* if we are not authorized and authorized is required, then return a 401 */
    if (force401) {
      if (logger.isDebugEnabled()) {
        logger.debug("IdentityService returned NULL. Treating as Unauthorized.", exception);
      }
      response.setStatus(401);
      logger.info(
          "Endpoint [{}] Response 401 => Query String: {}",
          script.getEndpointURI(),
          request.getQueryString());
      throw new HaltProcessing();
    }

    // once the user is determined, set the Netflix request variables (ugh) so that underlying
    // dependencies dont
    // crap out
    if (CurrentVisitor.get() == null && user != null) {
      Visitor visitor = TypeManager.findObject(Visitor.class, APIUserUtil.getCustomerId(user));
      CurrentVisitor.set(visitor);
    }

    return user;
  }
}
