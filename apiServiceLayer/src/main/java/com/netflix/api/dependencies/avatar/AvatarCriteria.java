package com.netflix.api.dependencies.avatar;

import java.util.List;
import ust.profile.avatar.v1.AvatarDimensions;

public class AvatarCriteria {
  private AvatarDimensions dimensions;
  private boolean secure;
  private boolean topAligned;
  private boolean supportsLocalizedKidsProfile;
  private List<String> recipePreferences;

  public AvatarDimensions getDimensions() {
    return dimensions;
  }

  public AvatarCriteria setDimensions(AvatarDimensions dimensions) {
    this.dimensions = dimensions;
    return this;
  }

  public boolean isSecure() {
    return secure;
  }

  public AvatarCriteria setSecure(boolean secure) {
    this.secure = secure;
    return this;
  }

  public boolean isTopAligned() {
    return topAligned;
  }

  public AvatarCriteria setTopAligned(boolean topAligned) {
    this.topAligned = topAligned;
    return this;
  }

  public boolean isSupportsLocalizedKidsProfile() {
    return supportsLocalizedKidsProfile;
  }

  public AvatarCriteria setSupportsLocalizedKidsProfile(boolean supportsLocalizedKidsProfile) {
    this.supportsLocalizedKidsProfile = supportsLocalizedKidsProfile;
    return this;
  }

  public List<String> getRecipePreferences() {
    return recipePreferences;
  }

  public AvatarCriteria setRecipePreferences(List<String> recipePreferences) {
    this.recipePreferences = recipePreferences;
    return this;
  }
}
