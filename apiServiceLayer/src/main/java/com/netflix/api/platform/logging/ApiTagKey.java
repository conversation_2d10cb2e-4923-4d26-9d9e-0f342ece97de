package com.netflix.api.platform.logging;

import com.netflix.spectator.api.BasicTag;
import com.netflix.spectator.api.Tag;

/** Standard dimensions used for API metrics. */
public enum ApiTagKey {
  SAMPLING("dna.api.sampling");

  private final String key;

  /** Create a new instance. */
  ApiTagKey(String key) {
    this.key = key;
  }

  /** String to use as the tag key. */
  public String key() {
    return key;
  }

  public Tag tag(String value) {
    return new BasicTag(key, value);
  }

  public Tag tag(int value) {
    return new BasicTag(key, String.valueOf(value));
  }
}
