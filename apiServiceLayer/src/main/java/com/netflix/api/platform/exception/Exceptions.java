package com.netflix.api.platform.exception;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletionException;

@SuppressWarnings("ALL")
public class Exceptions {
  private Exceptions() {}

  public static <T> T sneakyThrow(Throwable th) {
    if (th instanceof RuntimeException e) {
      throw e;
    }

    throw new RuntimeException(th);
  }

  public static <T> T chainedSneakyThrow(Throwable th) {
    if (th instanceof RuntimeException e) {
      throw e;
    }

    throw new CompletionException(th);
  }

  public static List<Throwable> getThrowableList(Throwable throwable) {
    final List<Throwable> list = new ArrayList<>();
    while (throwable != null && !list.contains(throwable)) {
      list.add(throwable);
      throwable = throwable.getCause();
    }
    return list;
  }

  public static Throwable getRootCause(final Throwable throwable) {
    final List<Throwable> list = getThrowableList(throwable);
    return list.isEmpty() ? null : list.get(list.size() - 1);
  }

  public static String getStackTrace(final Throwable throwable) {
    StringWriter sw = new StringWriter();
    PrintWriter pw = new PrintWriter(sw, true);
    throwable.printStackTrace(pw);
    return sw.getBuffer().toString();
  }
}
