package com.netflix.api.dependencies.onramp;

import static com.netflix.api.grpc.GrpcCallHelpers.Future.call;
import static com.netflix.map.annotation.MapAnnotationConstants.DISABLE_ONRAMP_FOR_SECONDARY_PROFILES;
import static com.netflix.map.annotation.MapAnnotationConstants.ONRAMP_ELIGIBILITY;
import static com.netflix.map.annotation.MapAnnotationConstants.STATUSCODE;
import static com.netflix.map.annotation.MapAnnotationConstants.StatusCodes.failure;

import com.netflix.api.platform.context.Contexts;
import com.netflix.onramp.protogen.GetOnRampListReply;
import com.netflix.onramp.protogen.GetOnrampListRequest;
import com.netflix.onramp.protogen.MixedTypeMap;
import com.netflix.onramp.protogen.OnrampEligibilityRequest;
import com.netflix.onramp.protogen.OnrampServiceGrpc.OnrampServiceStub;
import com.netflix.onramp.protogen.RecordOnrampImpressionRequest;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.type.proto.ISOCountries;
import com.netflix.type.protogen.BasicTypes.Locale;
import com.netflix.type.protogen.BasicTypes.Visitor;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletionStage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OnrampAdapter {
  private static final Logger logger = LoggerFactory.getLogger(OnrampAdapter.class);

  private final OnrampServiceStub onrampService;

  @Autowired
  public OnrampAdapter(@GrpcSpringClient("onramp") OnrampServiceStub onrampService) {
    this.onrampService = onrampService;
  }

  public CompletionStage<Map<String, Object>> getOnrampEligibility(
      Boolean isSignupFlow, Boolean disableOnrampForSecondaryProfiles) {

    var country = ISOCountries.toProtobuf(Contexts.country().getId());
    var request = OnrampEligibilityRequest.newBuilder().setCountry(country);

    Contexts.customerId()
        .map(cid -> Visitor.newBuilder().setId(cid).build())
        .ifPresent(request::setVisitor);

    Optional.ofNullable(isSignupFlow).ifPresent(request::setIsSignUpFlow);

    Optional.ofNullable(disableOnrampForSecondaryProfiles)
        .map(
            disable ->
                MixedTypeMap.newBuilder()
                    .putMapOfBooleanValues(DISABLE_ONRAMP_FOR_SECONDARY_PROFILES, disable))
        .ifPresent(request::setAnnotations);

    return call(onrampService::getOnrampEligibility, request.build())
        .thenApply(
            response -> {
              Map<String, Object> map = HashMap.newHashMap(2);
              map.put(ONRAMP_ELIGIBILITY, response.getIsEligible());
              map.put(STATUSCODE, response.getStatus());
              return map;
            })
        .exceptionally(
            error -> {
              logger.debug("Error getting onramp eligibility", error);
              return Map.of(STATUSCODE, failure.name());
            });
  }

  public CompletionStage<List<Long>> getOnrampVideoIds() {
    var request = GetOnrampListRequest.newBuilder();

    Contexts.customerId()
        .map(cid -> Visitor.newBuilder().setId(cid).build())
        .ifPresent(request::setVisitor);

    Contexts.locale().map(Locale::getId).ifPresent(request::setLocale);

    return call(onrampService::getOnrampList, request.build())
        .thenApply(GetOnRampListReply::getVideosList)
        .exceptionally(
            error -> {
              logger.debug("Error getting onramp list", error);
              return List.of();
            });
  }

  public CompletionStage<Map<String, Object>> recordOnrampImpression(
      Boolean disableOnrampForSecondaryProfiles) {

    var country = ISOCountries.toProtobuf(Contexts.country().getId());
    var request = RecordOnrampImpressionRequest.newBuilder().setCountry(country);

    Contexts.customerId()
        .map(cid -> Visitor.newBuilder().setId(cid).build())
        .ifPresent(request::setVisitor);

    Optional.ofNullable(disableOnrampForSecondaryProfiles)
        .map(
            disable ->
                MixedTypeMap.newBuilder()
                    .putMapOfBooleanValues(DISABLE_ONRAMP_FOR_SECONDARY_PROFILES, disable))
        .ifPresent(request::setAnnotations);

    return call(onrampService::recordOnrampImpression, request.build())
        .thenApply(
            response -> {
              Map<String, Object> map = HashMap.newHashMap(2);
              map.put(ONRAMP_ELIGIBILITY, response.getOnrampEligibility());
              map.put(STATUSCODE, response.getStatus());
              return map;
            })
        .exceptionally(
            error -> {
              logger.debug("Error recording onramp impression", error);
              return Map.of(STATUSCODE, failure.name());
            });
  }
}
