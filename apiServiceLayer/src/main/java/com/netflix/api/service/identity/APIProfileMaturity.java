package com.netflix.api.service.identity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class APIProfileMaturity {

  private Integer maturityValue;
  private List<String> maturityLabel;
  private Boolean isLowestMaturity;
  private Boolean isHighestMaturity;

  public Integer getMaturityValue() {
    return maturityValue;
  }

  public void setMaturityValue(Integer maturityValue) {
    this.maturityValue = maturityValue;
  }

  public List<String> getMaturityLabel() {
    return Collections.unmodifiableList(maturityLabel);
  }

  public void setMaturityLabel(List<String> maturityLabel) {
    this.maturityLabel = maturityLabel;
  }

  public void addMaturityLabel(String maturityLabel) {
    if (this.maturityLabel == null) {
      this.maturityLabel = new ArrayList<>();
    }
    this.maturityLabel.add(maturityLabel);
  }

  public Boolean getLowestMaturity() {
    return isLowestMaturity;
  }

  public void setLowestMaturity(Boolean lowestMaturity) {
    isLowestMaturity = lowestMaturity;
  }

  public Boolean getHighestMaturity() {
    return isHighestMaturity;
  }

  public void setHighestMaturity(Boolean highestMaturity) {
    isHighestMaturity = highestMaturity;
  }
}
