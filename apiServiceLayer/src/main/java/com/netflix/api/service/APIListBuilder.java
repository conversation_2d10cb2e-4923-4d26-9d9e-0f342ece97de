package com.netflix.api.service;

import java.util.List;
import rx.Observable;

/**
 * A list builder is an {@code Observable} that emits {@code ListItem&amp;lt;item&amp;gt;} - this class
 * provides base functionality needed to create such a subscription given a list of references to
 * the items.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideoList.class.png"
 * alt="video list class diagrams">
 */
public interface APIListBuilder<Item> extends APIObservableBuilder<ListItem<Item>> {

  /**
   * Rather than using toList() which returns a {@link List} of potentially unordered {@link
   * ListItem}s this method returns a {@link List} of the items in the order intended.
   *
   * @return a {@code Observable} that emits a list ordered by the index on the ListItem
   */
  Observable<List<Item>> toOrderedList();
}
