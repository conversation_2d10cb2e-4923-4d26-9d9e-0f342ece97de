package com.netflix.api.service.identity;

import com.netflix.api.service.ListItem;
import rx.Observable;

/**
 * FIXME FIXME FIXME
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIUser.class.png"
 * alt="Class Diagram: APIUser">
 *
 * @see <a
 *     href="https://confluence.netflix.com/display/API/3.+How+to+write+API.Next+scripts+in+Groovy#id-3.HowtowriteAPI.NextscriptsinGroovy-Buildingspecializedmethodsfromthosethatusethe&#8220;builder&#8221;pattern">Building
 *     specialized methods from those that use the &#8220;builder&#8221; pattern</a>
 */
public interface APIUserProfileBuilder {
  /**
   * Set the name of the user.
   *
   * @param s the name of the user
   * @return the builder with a profile name
   */
  APIUserProfileBuilder setProfileName(String s);

  /**
   * Set the preferred experience of the user
   *
   * @param experience FIXME FIXME FIXME
   * @return FIXME FIXME FIXME
   */
  APIUserProfileBuilder setPreferredExperience(APIUser.Experience experience);

  /**
   * Set the avatar name of the user.
   *
   * @param s FIXME FIXME FIXME
   * @return FIXME FIXME FIXME
   */
  APIUserProfileBuilder setAvatarName(String s);

  /**
   * Set the maturity of the user.
   *
   * @param apiMaturity FIXME FIXME FIXME
   * @return FIXME FIXME FIXME
   */
  APIUserProfileBuilder setMaturity(APIMaturity apiMaturity);

  /**
   * Set the maturity of the user, passing in the raw integer value
   *
   * @param rawMaturityValue
   * @return
   */
  APIUserProfileBuilder setRawMaturityValue(Integer rawMaturityValue);

  /**
   * Set the boolean {@code isFirstUse} of the user.
   *
   * @param isFirstUse FIXME FIXME FIXME
   * @return FIXME FIXME FIXME
   */
  APIUserProfileBuilder setFirstUse(boolean isFirstUse);

  /**
   * Set the language of the user.
   *
   * @param s FIXME FIXME FIXME
   * @return FIXME FIXME FIXME
   */
  APIUserProfileBuilder setLanguage(String s);

  /**
   * Sets the user feature to enabled/disable auto play capability.
   *
   * @param autoplayEnabled
   * @return the same {@code APIUserProfileBuilder} updated with the new setting
   */
  APIUserProfileBuilder setAutoplayEnabled(Boolean autoplayEnabled);

  /**
   * Sets the cookie disclosure flag
   *
   * @return the same {@code APIUserProfileBuilder} updated with the new setting
   */
  APIUserProfileBuilder setCookieDisclosureAccepted(Boolean cookieDisclosureAccepted);

  /**
   * Updates the profile lock pin setting on this account, the state of this value can be access via
   * {@link APIUser#getProfileLockPin()}.
   *
   * @param pin the value must be 4 numeric characters
   * @return the same {@code APIUserProfileBuilder} modified with an updated profile lock pin value
   */
  APIUserProfileBuilder setProfileLockPin(String pin);

  /**
   * Updates the profile creation lock flag on this account, the state of this value can be access
   * via {@link APIUser#isProfileCreationLocked()}.
   *
   * @param locked true if profile creation is locked, false if it is not
   * @return @return the same {@code APIUserProfileBuilder} updated with an updated profile creation
   *     lock status
   */
  APIUserProfileBuilder setProfileCreationLocked(Boolean locked);

  /**
   * Removes the profile lock pin setting on this account, the state of this value can be access via
   * {@link APIUser#getProfileLockPin()}.
   *
   * @param removePin true if pin should be removed, false if it is not
   * @return the same {@code APIUserProfileBuilder} updated with the new setting
   */
  APIUserProfileBuilder removeProfileLockPin(Boolean removePin);

  /**
   * Request the built-up changes from the backend service and return an {@code Observable} about
   * the {@code APIUser}.
   *
   * @return an {@code Observable} that emits the edited/newly-created {@link APIUser} as a {@link
   *     ListItem}
   */
  Observable<ListItem<APIUser>> build();
}
