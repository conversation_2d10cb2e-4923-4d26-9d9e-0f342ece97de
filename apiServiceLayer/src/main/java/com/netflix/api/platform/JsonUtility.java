package com.netflix.api.platform;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/** Utility for generating JSON from Maps/Lists and converting JSON to a Map */
public class JsonUtility {

  public enum Feature {
    DO_NOT_SERIALIZE_POJOS,
    PRETTY_PRINT,
  }

  public static final Collection<Feature> DEFAULT_FEATURES = List.of();
  public static final Collection<Feature> LEGACY_FEATURES = List.of(Feature.DO_NOT_SERIALIZE_POJOS);

  /**
   * @deprecated You should use the more generic toJson method.
   */
  @Deprecated
  public static String jsonFromMap(Map obj) {
    return toJson(obj, LEGACY_FEATURES);
  }

  /**
   * @deprecated You should use the more generic fromJson method
   */
  @Deprecated
  @SuppressWarnings("unchecked")
  public static Map<String, Object> mapFromJson(String json) {
    return (Map<String, Object>) fromJson(json);
  }

  /**
   * Convert Java collection objects (Map, List, String, Numbers...) as well as <PERSON><PERSON><PERSON><PERSON>'s into a JSON
   * string.
   *
   * <p>see {@link #toJson(Object, Collection)}
   *
   * @param obj An object which will be converted to a json formatted string
   * @return a fully formed json string
   * @throws RuntimeException if there was a problem converting the Object into a json
   *     string
   */
  public static String toJson(Object obj) {
    return toJson(obj, DEFAULT_FEATURES);
  }

  /**
   * Convert Java collection objects (Map, List, String, Numbers...) as well as POJO's into a JSON
   * string.
   *
   * <p>Java primitive example: <code>
   *   Map&lt;String, Object&gt; m = new HashMap&lt;&gt;();
   *   m.put("foo", 1);
   *   String mapoutput = JsonUtility.toJson(m); // {"foo":1}
   *   List&lt;Integer&gt; l = Lists.newArrayList();
   *   l.add(5);
   *   l.add(3);
   *   String listoutput = JsonUtility.toJson(l); // [5,3]
   * </code>
   *
   * <p>Groovy primitive example: <code>
   * def mapoutput = JsonUtility.toJson(["foo": 1]) // {"foo":1}
   * def listoutput = JsonUtility.toJson([5, 3]) // [5,3]
   * </code>
   *
   * <p>Java POJO example: <code>
   *       String out = JsonUtility.toJson(new Object() {
   *       public int getFoo() {
   *           return 1;
   *       }
   *   }); // {"foo":1}
   * </code>
   *
   * @param obj An object which will be converted to a json formatted string
   * @param features used to customize the behavior of the json processing
   * @return a fully formed json string
   * @throws RuntimeException if there was a problem converting the Object into a json
   *     string
   */
  public static String toJson(Object obj, Collection<Feature> features) {
    try {
      return getWrapper(features).toJson(obj);
    } catch (Exception e) {
      throw new RuntimeException("Failed to parse JSON.", e);
    }
  }

  public static Object toMap(Object obj) {
    try {
      return getWrapper(DEFAULT_FEATURES).toMap(obj);
    } catch (Exception e) {
      throw new RuntimeException("Failed to parse JSON.", e);
    }
  }

  /**
   * Convert a JSON string into Java collection objects (Map, List, String, Numbers...).
   *
   * <p>see {@link #fromJson(String, Collection)}
   *
   * @param json a json formatted string
   * @return an appropriate Java collection object representing the string parameter
   * @throws RuntimeException if there was a problem converting the string into an Object
   */
  public static Object fromJson(String json) {
    return fromJson(json, DEFAULT_FEATURES);
  }

  /**
   * Convert a JSON string into Java collection objects (Map, List, String, Numbers...).
   *
   * <p>Java example: <code>
   *   Object m2 = JsonUtility.fromJson("{\"foo\":1}");
   *   assert m2 instanceof Map;
   *   Object l2 = JsonUtility.fromJson("[5, 3]");
   *   assert l2 instanceof List;
   * </code>
   *
   * <p>Groovy example: <code>
   * def m2 = JsonUtility.fromJson("{\"foo\": 1}")
   * assert m2 instanceof Map, "Should be a map ref"
   * def l2 = JsonUtility.fromJson("[5, 3]")
   * assert l2 instanceof List, "Should be a list ref"
   * </code>
   *
   * @param json a json formatted string
   * @param features for customization of behavior. use {@link #fromJson(String)} if you do not wish
   *     to customize conversion
   * @return an appropriate Java collection object representing the string parameter
   * @throws RuntimeException if there was a problem converting the string into an Object
   */
  public static Object fromJson(String json, Collection<Feature> features) {
    try {
      return getWrapper(features).fromJson(json);
    } catch (Exception e) {
      throw new RuntimeException("Failed to parse JSON.", e);
    }
  }

  /** Delegates the conversion to JSON to the object itself */
  public interface JSONCapableObject {
    String getJSON();
  }

  private static JsonParserWrapper getWrapper(Collection<Feature> features) {
    return JacksonWrapper.create(features);
  }
}
