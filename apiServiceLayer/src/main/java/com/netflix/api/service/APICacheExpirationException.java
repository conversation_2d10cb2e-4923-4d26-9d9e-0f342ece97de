package com.netflix.api.service;

import java.util.Map;

public class APICacheExpirationException extends APIAnnotatedException {

  private static final long serialVersionUID = -5663036062835547844L;

  public APICacheExpirationException(String message) {
    super(message);
  }

  public APICacheExpirationException(String message, Throwable cause) {
    super(message, cause);
  }

  public APICacheExpirationException(String message, Map<String, Object> objAnnotations) {
    super(message, objAnnotations);
  }
}
