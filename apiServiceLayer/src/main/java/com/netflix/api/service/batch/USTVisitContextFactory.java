package com.netflix.api.service.batch;

import com.netflix.api.platform.NetflixESN;
import com.netflix.api.platform.context.ImmutableRequestContext;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import com.netflix.geoclient.CurrentGeoData;
import com.netflix.geoclient.GeoData;
import com.netflix.map.annotation.MapAnnotationConstants;
import com.netflix.request.protogen.RequestAttributesProto;
import com.netflix.request.protogen.RequestAttributesProto.LocaleList;
import com.netflix.request.protogen.UiFlavor;
import com.netflix.request.protogen.VisitContext;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Registry;
import com.netflix.type.protogen.BasicTypes.DeviceType;
import com.netflix.type.protogen.BasicTypes.ISOCountry;
import com.netflix.type.protogen.BasicTypes.Visitor;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class USTVisitContextFactory implements VisitContextFactory {

  private final UiFlavorExtractor uiFlavorExtractor;
  private final Registry registry;

  private final Id ustListCallInputs;

  @Autowired
  USTVisitContextFactory(UiFlavorExtractor uiFlavorExtractor, Registry registry) {
    this.uiFlavorExtractor = uiFlavorExtractor;
    this.registry = registry;
    this.ustListCallInputs = registry.createId("ust.list.callInputs");
  }

  @Override
  public VisitContext getVisitContext(Map<String, Object> annotations) {
    return VisitContext.newBuilder()
        .setRequestAttributes(buildRequestAttributes(annotations))
        .build();
  }

  private RequestAttributesProto buildRequestAttributes(Map<String, Object> annotations) {
    final ImmutableRequestContext immutableRequestContext = RequestContextWrapper.get();
    final GeoData geoData = CurrentGeoData.get();
    RequestAttributesProto.Builder builder = RequestAttributesProto.newBuilder();
    builder.setRequestId(immutableRequestContext.getRequestId());
    builder.setCountry(
        ISOCountry.newBuilder().setId(immutableRequestContext.getCountry().getId()).build());

    Optional.ofNullable(immutableRequestContext.getLocale())
        .ifPresent(
            locale -> builder.setLocales(LocaleList.newBuilder().addLocales(locale).build()));

    NetflixESN netflixESN = NetflixESN.getCurrent();
    if (netflixESN == null) {
      Optional.ofNullable(APIRequest.getCurrentRequest())
          .map(APIRequest::getRequestContext)
          .map(APIRequestContext::getVisitorDeviceId)
          .ifPresent(builder::setDeviceId);
    } else {
      builder.setDeviceId(netflixESN.getESN());
    }

    Optional.ofNullable(immutableRequestContext.getDeviceType())
        .ifPresent(
            deviceType ->
                builder.setDeviceType(DeviceType.newBuilder().setId(deviceType.getId()).build()));

    builder.setIsVPNProxy(geoData != null && geoData.isBlockedProxy());

    // Prefer request annotation 'visitor' over the RequestVariable CurrentVisitor
    Optional.ofNullable((Long) annotations.get(MapAnnotationConstants.VISITOR))
        .or(() -> Optional.ofNullable(CurrentVisitor.get()).map(com.netflix.type.Visitor::getId))
        .ifPresent(visitorId -> builder.setVisitor(Visitor.newBuilder().setId(visitorId).build()));

    String uiFlavor = uiFlavorExtractor.extractUiFlavor(annotations);
    if (uiFlavor == null || uiFlavor.isEmpty()) {
      registry.counter(ustListCallInputs.withTags("uiFlavorValue", "missing")).increment();
    } else {
      UiFlavor uiFlavorEnum = uiFlavorExtractor.convertUiFlavor(uiFlavor);
      if (uiFlavorEnum == UiFlavor.UNKNOWN_UI_FLAVOR) {
        registry
            .counter(ustListCallInputs.withTags("uiFlavorValue", "unrecognized", "value", uiFlavor))
            .increment();
      } else {
        registry.counter(ustListCallInputs.withTags("uiFlavorValue", "converted")).increment();
      }
      builder.setUiFlavorEnum(uiFlavorEnum);
    }

    return builder.build();
  }
}
