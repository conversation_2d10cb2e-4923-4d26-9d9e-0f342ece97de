package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.identity.APIProfileMaturity */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class ProfileMaturity {
  private Integer maturityValue;

  private List<String> maturityLabel;

  private Boolean isLowestMaturity;

  private Boolean isHighestMaturity;

  public Integer getMaturityValue() {
    return maturityValue;
  }

  public List<String> getMaturityLabel() {
    return maturityLabel;
  }

  public Boolean getIsLowestMaturity() {
    return isLowestMaturity;
  }

  public Boolean getIsHighestMaturity() {
    return isHighestMaturity;
  }

  public ProfileMaturity setMaturityValue(Integer maturityValue) {
    this.maturityValue = maturityValue;
    return this;
  }

  public ProfileMaturity setMaturityLabel(List<String> maturityLabel) {
    this.maturityLabel = maturityLabel;
    return this;
  }

  public ProfileMaturity setIsLowestMaturity(Boolean isLowestMaturity) {
    this.isLowestMaturity = isLowestMaturity;
    return this;
  }

  public ProfileMaturity setIsHighestMaturity(Boolean isHighestMaturity) {
    this.isHighestMaturity = isHighestMaturity;
    return this;
  }
}
