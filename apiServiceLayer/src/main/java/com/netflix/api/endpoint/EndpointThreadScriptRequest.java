package com.netflix.api.endpoint;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.lang.RequestVariable;
import com.netflix.servo.DefaultMonitorRegistry;
import com.netflix.servo.monitor.Counter;
import com.netflix.servo.monitor.Monitors;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.LongAdder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.exceptions.CompositeException;

/**
 * Allows managing the thread state (how many are running, waiting for all threads to complete) of a
 * given EndpointScript request.
 */
public class EndpointThreadScriptRequest {
  private static final Property<Boolean> USE_REQUEST_BASED_EXPIRY_TIMEOUT =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.request.expiry.enabled", Boolean.class)
          .orElse(false);

  private static final String REQUEST_EXPIRY_HEADER_NAME = "X-Netflix.request.expiry.timeout";
  private static final Logger logger = LoggerFactory.getLogger(EndpointThreadScriptRequest.class);

  /* Use RequestVariable instead of ThreadLocal so it ensures cleanup via NFFilter and is available if accessed across threads in same request */
  private static final RequestVariable<EndpointThreadScriptRequest> requestThreadMap =
      new RequestVariable<>();

  private final ConcurrentLinkedQueue<EndpointThreadTask> futureQueue =
      new ConcurrentLinkedQueue<>();

  private final CompletableFuture<List<Throwable>> requestFuture = new CompletableFuture<>();
  private final AtomicBoolean requestFutureIsCreated = new AtomicBoolean();

  private final AtomicInteger numThreadsExecuted = new AtomicInteger();
  private final AtomicInteger numThreadsExecuting = new AtomicInteger();
  private final AtomicInteger maxNumThreadsExecuted = new AtomicInteger();
  private final LongAdder millisSpentInQueue = new LongAdder();

  private static final Integer maxRequestTimeout = 30000;

  private static final Integer minRequestTimeout = 100;

  private Long requestExpiryTimestamp = null;

  private static final Counter requestTimeoutCount =
      Monitors.newCounter("api.endpoint.request.timeout");

  private final long requestStartTime;

  static {
    DefaultMonitorRegistry.getInstance().register(requestTimeoutCount);
  }

  private EndpointThreadScriptRequest(HttpServletRequest request) {
    this.requestStartTime = System.currentTimeMillis();

    long requestTimeoutInMillis = -1; // init with default value
    if (request != null) {
      // try getting the request expiry duration from request header....
      try {
        requestTimeoutInMillis =
            USE_REQUEST_BASED_EXPIRY_TIMEOUT.get()
                ? Long.parseLong(
                    Optional.ofNullable(request.getHeader(REQUEST_EXPIRY_HEADER_NAME)).orElse("0"))
                : -1;
      } catch (Exception e) {
        // ignore
      }
    }

    if (requestTimeoutInMillis > 0) {
      this.requestExpiryTimestamp =
          requestStartTime
              + Math.min(maxRequestTimeout, Math.max(requestTimeoutInMillis, minRequestTimeout));
    }
  }

  /**
   * Every new request must call this at the beginning since threads are re-used and need to be
   * re-initialized.
   *
   * @param request servlet request.
   */
  public static void newRequest(HttpServletRequest request) {
    EndpointThreadScriptRequest scriptRequest = new EndpointThreadScriptRequest(request);
    requestThreadMap.set(scriptRequest);
  }

  /**
   * Every new request must call this at the beginning since threads are re-used and need to be
   * re-initialized.
   */
  public static void newRequest() {
    newRequest(null);
  }

  /**
   * Used by child threads to record the EndpointScriptRequest as given to it by their parent
   * thread.
   *
   * @param scriptRequest
   */
  public static void initializeThreadWithRequest(EndpointThreadScriptRequest scriptRequest) {
    requestThreadMap.set(scriptRequest);
  }

  public static EndpointThreadScriptRequest currentRequest() {
    return requestThreadMap.get();
  }

  public void registerTask(final EndpointThreadTask task) {
    futureQueue.offer(task);
  }

  public void markThreadStarted() {
    numThreadsExecuted.incrementAndGet();
    int currentlyExecuting = numThreadsExecuting.incrementAndGet();
    maxNumThreadsExecuted.updateAndGet(prevMax -> Math.max(currentlyExecuting, prevMax));
  }

  public void markThreadExecuted() {
    numThreadsExecuting.decrementAndGet();
  }

  public void recordMillisSpentInQueue(long duration) {
    millisSpentInQueue.add(duration);
  }

  private CompletableFuture<Void> findPendingFuture(List<Throwable> throwables) {
    EndpointThreadTask currentTask;
    while ((currentTask = futureQueue.poll()) != null) {
      if (currentTask.isDone()) {
        // Task is completed get any exception that could have occurred
        Throwable exception = currentTask.getException();
        if (exception != null) {
          throwables.add(exception);
        }
      } else {
        // The current task is not done
        return currentTask.futureWithTimeout(this.timeLeftInMillis());
      }
    }
    return null;
  }

  private CompletableFuture<List<Throwable>> getOrCreateRequestFuture() {
    if (!requestFutureIsCreated.getAndSet(true)) {
      pendingTaskCompletableFuture()
          .whenComplete(
              (exs, throwable) -> {
                try {
                  if (throwable != null) {
                    if (throwable instanceof CompletionException) {
                      Throwable cause = throwable.getCause();
                      if (cause != null) {
                        throwable = cause;
                      }
                    }
                    this.requestFuture.completeExceptionally(throwable);
                  } else {
                    this.requestFuture.complete(exs);
                  }
                } catch (Exception e) {
                  throw new RuntimeException(e);
                }
              });
    }
    return this.requestFuture;
  }

  private CompletableFuture<List<Throwable>> pendingTaskCompletableFuture() {

    // make sure all the threads in the futureQueue have completed or have been cancelled, else they
    // may set context on
    // parent tomcat thread that is processing some other subsequent request.
    // IOW, do you best not to have runaway threads
    // INC-1806: A runaway thread executing a task in APIEcomServiceImpl.executeAndGet() that
    // effectively
    // does APIRequest.getCurrentRequest().getServletResponse().addCookies() and adds identity
    // cookies to a subsequent request that belongs to another user.

    // Find the first non-completed future and collect all the exceptions on the way

    List<Throwable> collectedThrowables = new ArrayList<>();
    CompletableFuture<Void> currentFuture = findPendingFuture(collectedThrowables);

    if (currentFuture != null) {
      // Convert an exceptional completion to a normal completion
      CompletableFuture<List<Throwable>> currentFutureEx =
          currentFuture.handle(
              (_Void, ex) -> {
                if (ex != null) {
                  if (ex instanceof CompletionException) {
                    Throwable cause = ex.getCause();
                    if (cause != null) {
                      ex = cause;
                    }
                  }
                  collectedThrowables.add(ex);
                }
                return collectedThrowables;
              });

      return currentFutureEx.thenCompose(
          exceptions -> {
            CompletableFuture<List<Throwable>> next = this.pendingTaskCompletableFuture();
            return next.thenApply(
                nextExceptions -> {
                  if (exceptions.isEmpty() || nextExceptions.isEmpty()) {
                    return nextExceptions.isEmpty() ? exceptions : nextExceptions;
                  } else {
                    List<Throwable> copyThrowable =
                        new ArrayList<>(exceptions.size() + nextExceptions.size());
                    copyThrowable.addAll(exceptions);
                    copyThrowable.addAll(nextExceptions);
                    return copyThrowable;
                  }
                });
          });
    }

    // All the futures are completed
    return CompletableFuture.completedFuture(collectedThrowables);
  }

  // Used for testing only
  public void waitUntilAllThreadsCompleted() {
    CompletableFuture<List<Throwable>> future = getOrCreateRequestFuture();
    List<Throwable> exceptionsCompletingEndpointThreadCommandWork = future.join();
    if (!exceptionsCompletingEndpointThreadCommandWork.isEmpty()) {
      CompositeException ce = new CompositeException(exceptionsCompletingEndpointThreadCommandWork);
      logger.info("EndpointThreadCommand failed execution.", ce);
      throw ce;
    }
  }

  public Optional<Long> timeLeftInMillis() {
    if (requestExpiryTimestamp == null) return Optional.empty();

    return Optional.of(Math.max(0L, requestExpiryTimestamp - System.currentTimeMillis()));
  }

  public long scriptExecutionDuration() {
    return System.currentTimeMillis() - requestStartTime;
  }
}
