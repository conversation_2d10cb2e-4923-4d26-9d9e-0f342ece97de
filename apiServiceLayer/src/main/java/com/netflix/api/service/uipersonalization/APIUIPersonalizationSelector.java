package com.netflix.api.service.uipersonalization;

public interface APIUIPersonalizationSelector {
  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Selector.retrieve_video_merch_disabled"
   */
  Boolean getRetrieveVideoMerchDisabled();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Selector.retrieve_secondary_languages"
   */
  Boolean getRetrieveSecondaryLanguages();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Selector.retrieve_disabled_voting_titles"
   */
  Boolean getRetrieveDisabledVotingTitles();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Selector.retrieve_hidden_modules"
   */
  Boolean getRetrieveHiddenModules();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Selector.retrieve_info_density_toggle"
   */
  Boolean getRetrieveInfoDensityToggle();
}
