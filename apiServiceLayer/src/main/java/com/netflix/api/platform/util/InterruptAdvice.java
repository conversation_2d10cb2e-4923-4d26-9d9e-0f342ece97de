package com.netflix.api.platform.util;

import com.netflix.archaius.api.Property;
import rx.Producer;
import rx.Subscriber;
import rx.plugins.RxJavaHooks;

public class InterruptAdvice {
  private InterruptAdvice() {}

  private static final Property<Boolean> ON_ERROR_IF_INTERRUPTED_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.interrupt.onerror.enabled", Boolean.class)
          .orElse(true);

  public static boolean shouldAbort() {
    return Thread.currentThread().isInterrupted() && ON_ERROR_IF_INTERRUPTED_ENABLED.get();
  }

  public static void reinterruptIfNeeded(Throwable throwable) {
    if (!ON_ERROR_IF_INTERRUPTED_ENABLED.get()) {
      return;
    }

    boolean hasInterruptedException = false;
    int maxDepth = 10;
    while (throwable != null && --maxDepth >= 0) {
      if (throwable instanceof InterruptedException) {
        hasInterruptedException = true;
        break;
      }
      throwable = throwable.getCause();
    }

    if (hasInterruptedException && !Thread.currentThread().isInterrupted()) {
      Thread.currentThread().interrupt();
    }
  }

  /**
   * Wrap a subscriber to complete with an onError if an onNext occur while the thread have been
   * interrupted.
   *
   * @param subscriber
   * @return wrapped subscriber
   */
  public static <T> Subscriber<T> wrapSubscriber(Subscriber<T> subscriber) {
    return new InterruptableSubscriber<>(subscriber);
  }

  static class InterruptableSubscriber<T> extends Subscriber<T> {
    private final Subscriber<T> source;
    private boolean isCompleted;

    InterruptableSubscriber(Subscriber<T> source) {
      this.source = source;
    }

    @Override
    public void onCompleted() {
      if (!isCompleted) {
        isCompleted = true;
        source.onCompleted();
      }
    }

    @Override
    public void onError(Throwable e) {
      if (!isCompleted) {
        isCompleted = true;
        try {
          source.onError(e);
        } finally {
          unsubscribe();
        }
      } else {
        RxJavaHooks.onError(e);
      }
    }

    @Override
    public void onNext(T t) {
      if (source.isUnsubscribed()) return;
      if (!isCompleted && shouldAbort()) {
        isCompleted = true;
        try {
          source.onError(new InterruptedException("Interrupting observable on interrupted thread"));
        } finally {
          unsubscribe();
        }
      } else if (!isCompleted) {
        source.onNext(t);
      }
    }

    @Override
    public void setProducer(final Producer producer) {
      source.setProducer(producer);
    }
  }
}
