package dna.api.datasources;

import static com.netflix.api.platform.util.CompletionStageAdapter.from;

import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIUser;
import com.netflix.demograph.datasource.AsyncDataSource;
import com.netflix.demograph.datasource.batch.BatchDataSource;
import com.netflix.group.attribute.Rating;
import com.netflix.group.attribute.SimsContentTypes;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.type.ISOCountry;
import dna.api.SimilarsService;
import dna.api.datasources.utils.DataSources;
import dna.api.service.model.Thumbs;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
public class SimilarsDataSource {

  // datasources (must be singletons for demograph implicit caching to work)
  private final AsyncDataSource<Integer, Integer> similarVideosTracker;
  private final BatchDataSource<Integer, List<Integer>> similarVideos;
  private final BatchDataSource<SimilarVideosArgs, List<Integer>> similarVideosWithContext;

  @Autowired
  public SimilarsDataSource(final SimilarsService similarsService) {
    // init datasources
    similarVideosTracker = _similarVideosTracker(similarsService);
    similarVideos = _similarVideos(similarsService);
    similarVideosWithContext = _similarVideosWithContext(similarsService);
  }

  // datasources public accessors to support mocking / testing

  public AsyncDataSource<Integer, Integer> similarVideosTracker() {
    return similarVideosTracker;
  }

  public BatchDataSource<Integer, List<Integer>> similarVideos() {
    return similarVideos;
  }

  public BatchDataSource<SimilarVideosArgs, List<Integer>> similarVideosWithContext() {
    return similarVideosWithContext;
  }

  // datasources initializers

  private AsyncDataSource<Integer, Integer> _similarVideosTracker(
      final SimilarsService similarsService) {
    return new AsyncDataSource<>(
        // we may want to refine this in the future, if pagination becomes available. keep
        // data source in place
        (context, videoId) -> similarsService.getTrackId(APIRequest.getCurrentRequest().getUser()));
  }

  private BatchDataSource<Integer, List<Integer>> _similarVideos(
      final SimilarsService similarsService) {
    return DataSources.of(
        "similarVideos",
        (context, videoIds) ->
            from(
                similarsService.getSimilars(
                    APIRequest.getCurrentRequest().getUser(),
                    videoIds,
                    CurrentRequestContext.get().getRequestId(),
                    RequestContextWrapper.get().getCountry())));
  }

  private record ContentTypesThumbs(SimsContentTypes contentTypes, Thumbs thumbs) {}

  private BatchDataSource<SimilarVideosArgs, List<Integer>> _similarVideosWithContext(
      final SimilarsService similarsService) {
    return DataSources.of(
        "similarVideosWithContext",
        (context, args) -> {
          // each list in the collection is one request to gs2
          final Collection<List<SimilarVideosArgs>> batches =
              args.stream()
                  .collect(Collectors.groupingBy(
                      arg -> new ContentTypesThumbs(arg.contentTypes(), arg.thumbRating()),
                      LinkedHashMap::new,  // Preserve insertion order for better cache locality
                      Collectors.toList()))
                  .values();

          final String reqID = CurrentRequestContext.get().getRequestId();
          final ISOCountry country = RequestContextWrapper.get().getCountry();
          final APIUser user = APIRequest.getCurrentRequest().getUser();

          final Observable<Map<SimilarVideosArgs, List<Integer>>> mapObservable =
              Observable.from(batches)
                  .flatMap(
                      batchEntry -> {
                        // ref has the same data all other items except different video ids
                        final SimilarVideosArgs ref = batchEntry.get(0);
                        final SimsContentTypes simsContentTypes = ref.contentTypes();
                        final Thumbs thumbRating = ref.thumbRating();
                        final Set<Integer> videoIds =
                            batchEntry.stream()
                                .map(SimilarVideosArgs::videoId)
                                .collect(Collectors.toSet());

                        final Rating rating =
                            thumbRating == null
                                ? null
                                : switch (thumbRating) { // will cause NPE if thumbRating == null
                                  case THUMBS_UP -> Rating.THUMBS_UP;
                                  case THUMBS_WAY_UP -> Rating.THUMBS_WAY_UP;
                                  default -> null;
                                };

                        return similarsService
                            .getSimilars(user, videoIds, simsContentTypes, rating, reqID, country)
                            .map(
                                resp ->
                                    resp.entrySet().stream()
                                        .collect(
                                            Collectors.toMap(
                                                videoIdAndSimilarVideoIDs ->
                                                    new SimilarVideosArgs(
                                                        videoIdAndSimilarVideoIDs.getKey(),
                                                        simsContentTypes,
                                                        thumbRating),
                                                Entry::getValue)));
                      })
                  .reduce(
                      new HashMap<>(),
                      (a, v) -> {
                        a.putAll(v);
                        return a;
                      });

          return from(mapObservable);
        });
  }
}
