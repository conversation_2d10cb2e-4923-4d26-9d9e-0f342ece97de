package com.netflix.api.service.interactive;

import com.netflix.api.service.interactive.APIInteractiveExperienceService.APIInteractiveUserStateSnapshot;

public class APIInteractiveUserStateSnapshotImpl implements APIInteractiveUserStateSnapshot {
  byte[] persistentState;
  byte[] sessionState;
  byte[] globalState;

  @Override
  public byte[] getPersistentState() {
    return persistentState;
  }

  @Override
  public APIInteractiveUserStateSnapshot setPersistentState(byte[] persistentState) {
    this.persistentState = persistentState;
    return this;
  }

  @Override
  public byte[] getSessionState() {
    return sessionState;
  }

  @Override
  public APIInteractiveUserStateSnapshot setSessionState(byte[] sessionState) {
    this.sessionState = sessionState;
    return this;
  }

  @Override
  public byte[] getGlobalState() {
    return globalState;
  }

  @Override
  public APIInteractiveUserStateSnapshot setGlobalState(byte[] globalState) {
    this.globalState = globalState;
    return this;
  }
}
