package com.netflix.api.service.list;

import com.netflix.api.service.APIAnnotation;
import com.netflix.api.service.ListItem;
import java.util.List;
import rx.Observable;

public interface APIList<Item> extends APIEntity {

  /**
   * Get the name (or display title) for this list.
   *
   * @return the name of this entity as a String as an {@link Observable}
   */
  Observable<String> getListName();

  /**
   * Get the ID of this entity type.
   *
   * @return the ID of this content type of object. Can be {@code null} if the entity cannot be
   *     looked up by ID. You should not cache IDs as they change and migrate often.
   */
  Observable<String> getListId();

  /**
   * Rather than using <code>Observable.toList()</code> which returns a {@link java.util.List} of
   * potentially unordered {@link com.netflix.api.service.ListItem}s, this method returns a {@link
   * java.util.List} of the items in the order intended.
   *
   * @return an {@link rx.Observable} that emits a list ordered by the index on the <code>ListItem
   *     </code>
   */
  Observable<List<Item>> toOrderedList();

  /**
   * If the index is not needed and order is not important then you can use this method to get just
   * the {@link rx.Observable} of the items directly.
   *
   * @return an {@link rx.Observable} that just emits the items directly
   */
  Observable<Item> toItems();

  /**
   * Returns the size of this list. Since it may be expensive to calculate in some cases, it is
   * exposed via an <code>Observable</code>.
   *
   * @return an {@link rx.Observable} that emits the size of the list as an <code>Integer</code>
   */
  Observable<Integer> getSize();

  /**
   * Get list items of this list.
   *
   * <p>An exception encountered while getting the list results in a call to the Observer's <code>
   * onError</code> method. While creating items in the list, if one or more exceptions are
   * encountered creating items, all the exceptions are captured and held on to. After the last list
   * item is emitted, the exceptions are combined into a {@link rx.exceptions.CompositeException}
   * and thrown.
   *
   * @return an {@link rx.Observable} of items in the list as <code>ListItem</code> objects
   */
  Observable<ListItem<Item>> getListItems();

  /**
   * Returns an all annotations. Annotations are essentially extra metadata of this list that are
   * exposed as name-value pairs. Value of the object can be any type as dictated by {@link
   * APIAnnotation#getType()}
   *
   * @return the annotations or an empty {@link Observable} if none exist
   */
  Observable<APIAnnotation<String, Object>> getAnnotations();

  /**
   * This blocks, do not use
   *
   * @deprecated use {@link #getListId()}
   */
  @Override
  @Deprecated
  String getId();
}
