package com.netflix.api.service.ums;

import com.netflix.api.service.identity.APIUser;

public interface APIFilterNotificationsByIdsRequest {

  /**
   * The requested size of the boxart
   *
   * @param size FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationsByIdsRequest setBoxArtSize(String size);

  /**
   * The requested size of the boxart for NSA
   *
   * @param size FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationsByIdsRequest setNsaBoxArtSize(String size);

  /**
   * FIXME FIXME FIXME.
   *
   * @param secure FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationsByIdsRequest setIsSecure(boolean secure);

  /**
   * FIXME FIXME FIXME.
   *
   * @param eTag FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationsByIdsRequest setETag(String eTag);

  /**
   * FIXME FIXME FIXME.
   *
   * @return
   */
  APIUser getUser();

  /**
   * The requested boxart format, optional
   *
   * @param artWorkFormat
   * @return
   */
  APIFilterNotificationsByIdsRequest setArtWorkFormat(String artWorkFormat);

  /**
   * The uiPlatform making the request, optional
   *
   * @param uiPlatform
   * @return
   */
  APIFilterNotificationsByIdsRequest setUIPlatform(String uiPlatform);

  /**
   * HTML (default), TEXT, or OBELIX_KEY
   *
   * @param templateStringFormat
   * @return
   */
  APIFilterNotificationsByIdsRequest setTemplateStringFormat(String templateStringFormat);
}
