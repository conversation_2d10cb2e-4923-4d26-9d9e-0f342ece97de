package com.netflix.api.service.list;

import com.netflix.api.service.APICountry;
import com.netflix.api.service.identity.APIUser;
import java.util.Map;

/**
 * Request to refresh a lolomo list.
 *
 * <p>Some lists change as a result of user actions -- e.g., playlist changes when a user adds a
 * video to his/her playlist; Recently watched list changes when a user watches a movie. Users can
 * poll these lists to see if there were changes and invalidate their cached list if it has changed.
 *
 * <p>This object encapsulates data needed by the server to refresh a list. Some of the parameters
 * needed are for optimization purposes -- root lolomo id and list context can be looked up in the
 * database given a list id, but that adds to latency.
 *
 * <p><img
 * src="https://confluence.netflix.com/download/attachments/52216234/APILolomoService.class.png"
 * alt="Class Diagram: APILolomoService">
 */
public interface ListRefreshRequest {
  /**
   * The customer this list belongs to. This is needed to validate that a client can fetch the list.
   *
   * @return the customer this list belongs to, as an {@link APIUser}
   */
  APIUser getUser();

  /**
   * ID of the list that needs to be refreshed.
   *
   * @return FIXME FIXME FIXME
   */
  String getListId();

  /**
   * ID of the list's parent lolomo that needs to be refreshed.
   *
   * @return FIXME FIXME FIXME
   */
  String getRootLolomoId();

  /**
   * Does this list belong to a kid's lolomo?
   *
   * @return <code>true</code> if this list belongs to a kid's lolomo
   */
  boolean isForKids();

  /**
   * ESN of the device that is making this request.
   *
   * @return the ESN of the device that is making this request, as a String
   */
  String getEsn();

  /**
   * Get the country from which this request originates.
   *
   * @return the country from which this request originates, as an {@link APICountry}
   */
  APICountry getCountry();

  /**
   * List context of the list that needs to be refreshed. Used to determined whether a list can be
   * refreshed, and the refresh service delegate.
   *
   * @return the list context of the list to be refreshed (an enum)
   */
  APILolomoList.ListContext getListContext();

  /**
   * Get the request annotations added to this request.
   *
   * @return the request annotations that you set for this request
   */
  Map<String, Object> getAnnotations();
}
