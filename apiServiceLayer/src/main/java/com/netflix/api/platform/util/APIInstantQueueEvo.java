package com.netflix.api.platform.util;

import com.netflix.subscriber.types.protogen.Experience.Type;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import jakarta.annotation.Nullable;

public class APIInstantQueueEvo {

  private static final APIInstantQueueEvo INSTANCE = new APIInstantQueueEvo();

  public static APIInstantQueueEvo getInstance() {
    return INSTANCE;
  }

  private APIInstantQueueEvo() {}

  public boolean isInstantQueueAllowed(@Nullable AccountProfileRemote accountProfile) {
    if (accountProfile == null) {
      return false;
    }

    return Type.just_for_kids != accountProfile.getExperienceTypeEnum();
  }
}
