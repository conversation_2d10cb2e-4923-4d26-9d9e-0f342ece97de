package com.netflix.api.service.video;

import java.util.List;

public interface APIMovieRatingReason {

  /**
   * Reason IDs which caused the rating to be what it is.
   *
   * @return
   */
  List<Integer> getReasonIds();

  /**
   * True indicates the reason IDs will not have text in localization feed. Images will only be
   * displayed. Images for the reason IDs are made available out side of VMS data at this point (NOV
   * 2014)
   *
   * @return
   */
  boolean isDisplayImageOnly();

  /**
   * True indicates that the reason IDs should be displayed in the order they appear in the
   * reasonIds list.
   *
   * @return
   */
  boolean isDisplayOrderSpecific();

  /**
   * Returns the localized description for a reason id.
   *
   * @param reasonId
   * @param defaultString to be returned in case of error
   * @return
   */
  String getLocalizedReasonDescription(Integer reasonId, String defaultString);

  /**
   * Returns the localized level for a reason id.
   *
   * @param reasonId
   * @param defaultString to be returned in case of error
   * @return
   */
  String getLocalizedReasonLevel(Integer reasonId, String defaultString);
}
