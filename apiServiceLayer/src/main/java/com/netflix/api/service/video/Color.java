package com.netflix.api.service.video;

import com.google.common.base.Preconditions;
import java.util.List;

/**
 * RGB representation of color.
 *
 * @deprecated use the hexadecimal {@link APIVideoColors} instead.
 */
@Deprecated
public class Color {

  public final int r;
  public final int g;
  public final int b;

  public Color(int r, int g, int b) {
    this.r = r;
    this.g = g;
    this.b = b;
  }

  public Color(List<Integer> rgb) {
    Preconditions.checkArgument(rgb.size() == 3);
    this.r = rgb.get(0);
    this.g = rgb.get(1);
    this.b = rgb.get(2);
  }

  @Override
  public String toString() {
    return "Color{" + "r=" + r + ", g=" + g + ", b=" + b + '}';
  }
}
