package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaPostplayReportingData {
  private NapaPostplayReportingDataType reportingData;

  private List<SearchPageEntityId> selectedSupplementalVideos;

  private List<SearchPageEntityId> selectedPlayableVideos;

  private List<NapaPostplayRecommendationChannel> selectedChannelNames;

  private String detailedName;

  public NapaPostplayReportingDataType getReportingData() {
    return reportingData;
  }

  public List<SearchPageEntityId> getSelectedSupplementalVideos() {
    return selectedSupplementalVideos;
  }

  public List<SearchPageEntityId> getSelectedPlayableVideos() {
    return selectedPlayableVideos;
  }

  public List<NapaPostplayRecommendationChannel> getSelectedChannelNames() {
    return selectedChannelNames;
  }

  public String getDetailedName() {
    return detailedName;
  }

  public NapaPostplayReportingData setReportingData(NapaPostplayReportingDataType reportingData) {
    this.reportingData = reportingData;
    return this;
  }

  public NapaPostplayReportingData setSelectedSupplementalVideos(
      List<SearchPageEntityId> selectedSupplementalVideos) {
    this.selectedSupplementalVideos = selectedSupplementalVideos;
    return this;
  }

  public NapaPostplayReportingData setSelectedPlayableVideos(
      List<SearchPageEntityId> selectedPlayableVideos) {
    this.selectedPlayableVideos = selectedPlayableVideos;
    return this;
  }

  public NapaPostplayReportingData setSelectedChannelNames(
      List<NapaPostplayRecommendationChannel> selectedChannelNames) {
    this.selectedChannelNames = selectedChannelNames;
    return this;
  }

  public NapaPostplayReportingData setDetailedName(String detailedName) {
    this.detailedName = detailedName;
    return this;
  }
}
