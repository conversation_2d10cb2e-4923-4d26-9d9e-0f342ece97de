package com.netflix.api.platform.stats;

import com.google.common.base.Preconditions;
import jakarta.annotation.Nonnull;
import java.util.concurrent.atomic.AtomicLong;

public class OneInNSampler {
  private final AtomicLong counter = new AtomicLong(0);
  private final Integer samplingRate;

  public OneInNSampler(@Nonnull Integer samplingRate) {
    Preconditions.checkNotNull(samplingRate);
    this.samplingRate = samplingRate;
  }

  /**
   * Each time the function is called, we consider it a request for sampling, and determine whether
   * we should sample or not.
   *
   * @return true if in the sample. false otherwise
   */
  public boolean sample() {
    if (samplingRate <= 1) {
      return true;
    }
    final long count = this.counter.getAndIncrement();
    return (count % samplingRate) == 0;
  }
}
