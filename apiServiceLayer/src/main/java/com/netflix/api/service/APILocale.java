package com.netflix.api.service;

import java.text.DateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Each supported country has a default set of supported locales. The default set of supported
 * locales for members is bigger than that for non-members. Never-members (a.k.a. Free-Trial
 * Members, P1 subs) are also considered Netflix members for the purpose of locale resolution. Some
 * requests such as on-device sign-up requests are made on behalf of non-members.
 *
 * <p>By default, the locale used for a request will be the top matching locale supported in the
 * country for the request. You can use the <code>languages</code> URL parameter to override the
 * default.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APILocale.class.png">
 */
public interface APILocale {

  /**
   * Get a well-formed IETF BCP 47 language tag that can serve as an ID for this locale. If this
   * APILocale object has a language, country, or variant that does not satisfy the IETF BCP 47
   * language tag syntax requirements, this method handles these fields as described below:
   *
   * <dl>
   *   <dt>Language
   *   <dd>If language is ill-formed (for example "a" or "e2"), it will be emitted as "und"
   *       (Undetermined). If it is empty (""), the return value will also be ""
   *   <dt>Country
   *   <dd>If country is ill-formed (for example "12" or "USA"), it will be omitted.
   *   <dt>Variant
   *   <dd>Variant is treated as consisting of subtags separated by underscore and converted to
   *       lower case letters. "Well-formed" subtags consist of either an ASCII letter followed by
   *       4&ndash;7 ASCII characters, or an ASCII digit followed by 3&ndash;7 ASCII characters. If
   *       well-formed, the variant is emitted as each subtag in order (separated by hyphen).
   *       Otherwise:
   *       <ul>
   *         <li>if all sub-segments consist of 1 to 8 ASCII alphanumerics (for example "WIN",
   *             "WINDOWS_XP", "SOLARIS_10"), the first ill-formed variant subtag and all following
   *             sub-segments will be emitted as private use subtags prefixed by the special private
   *             use subtag "variant" followed by each subtag in order (separated by hyphen). For
   *             example, locale "en_US_WIN" is converted to language tag "en-US-x-variant-win",
   *             locale "de_WINDOWS_XP" is converted to language tag "de-windows-x-variant-xp". If
   *             this locale has a private use extension value, the special private use subtags
   *             prefixed by "variant" are appended after the locale's private use value.
   *         <li>if any subtag does not consist of 1 to 8 ASCII alphanumerics, the variant will be
   *             truncated and the problematic subtag and all following sub-segments will be
   *             omitted. If the remainder is non-empty, it will be emitted as a private use subtag
   *             as above (even if the remainder turns out to be well-formed). For example,
   *             "Solaris_isjustthecoolestthing" is emitted as "x-jvariant-Solaris", not as
   *             "solaris".
   *       </ul>
   * </dl>
   *
   * <b>Note:</b> Although the language tag created by this method satisfies the syntax requirements
   * defined by the IETF BCP 47 specification, it may not always be a valid BCP 47 language tag.
   *
   * @return a language tag for this locale formatted according to IETF BCP 47
   */
  String getId();

  /**
   * Get the country code representing the country of this locale.
   *
   * @return the country/region code for this locale, which will either be the empty string or an
   *     uppercase ISO 3166 2-letter code
   */
  String getCountry();

  /**
   * Constructs a {@link Locale} based off of this {@link APILocale} reference. NOTE: For formatting
   * purposes you should use {@link #toFormattingLocale()}
   *
   * @return a {@link Locale}
   */
  Locale toLocale();

  /**
   * Returns LocaleDisplaySeparator for this locale.
   *
   * @return locale display separator as a char.
   */
  String getLocaleSeparator();

  /**
   * A locale used for Netflix specific formatting
   *
   * @return JDK's implementation of {@link Locale} that can be used for formatting. Formatting
   *     locale adds Netflix specific rules for the given locale. For eg. Latn numbering system for
   *     arabic and so could be different from {@link #toLocale()}
   */
  Locale toFormattingLocale();

  /**
   * Constructs a {@link Locale} based off of this {@link APILocale} reference
   *
   * @param defaultCountry would be used if the {@link #getCountry()} is null or empty
   * @return a {@link Locale}
   */
  Locale toLocale(String defaultCountry);

  /**
   * Contructs a localized {@link DateFormat} based off of this {@link APILocale} reference and the
   * style parameter
   *
   * @param style the given formatting style. see {@link DateFormat#SHORT}, {@link
   *     DateFormat#MEDIUM}, {@link DateFormat#LONG} or {@link DateFormat#FULL}
   * @deprecated use {@link #formatDate(int, Date)}
   * @return a date formatter
   */
  @Deprecated
  DateFormat getDateInstance(int style);

  /**
   * Formats a date for this locale based on the style input
   *
   * @param dateStyle the given formatting style. see {@link DateFormat#SHORT}, {@link
   *     DateFormat#MEDIUM}, {@link DateFormat#LONG} or {@link DateFormat#FULL}
   * @param date the date to format
   * @return formatted date such as "7/9/15" for en_US locale
   */
  String formatDate(int dateStyle, Date date);

  /**
   * Formats a date for this locale based on the style input
   *
   * @param dateStyle the given formatting style. see {@link DateFormat#SHORT}, {@link
   *     DateFormat#MEDIUM}, {@link DateFormat#LONG} or {@link DateFormat#FULL}
   * @param date the date to format
   * @param timeZone optionally provide a the timezone (i.e. PST)
   * @return formatted date such as "7/9/15" for en_US locale
   */
  String formatDate(int dateStyle, Date date, String timeZone);

  /**
   * Formats a date for this locale based on the style input and optionally using a
   * GregorianCalendar.
   *
   * <p>Using the GregorianCalendar will become necessary as we expand to additional countries.
   * GregorianCalendar is required for locales such as Thai to get the Gregorian Calendar system,
   * otherwise by default it would use Buddhist Calendar, which for 2016 the year would be 2559
   *
   * @param dateStyle the given formatting style. see {@link DateFormat#SHORT}, {@link
   *     DateFormat#MEDIUM}, {@link DateFormat#LONG} or {@link DateFormat#FULL}
   * @param date the date to format
   * @param timeZone optionally provide a the timezone (i.e. PST)
   * @return formatted date such as "7/9/15" for en_US locale
   */
  String formatDate(int dateStyle, Date date, String timeZone, boolean useGregorian);

  /**
   * Formats a date for this locale based on the style input
   *
   * @param dateStyle the date formatting style. see {@link DateFormat#SHORT}, {@link
   *     DateFormat#MEDIUM}, {@link DateFormat#LONG} or {@link DateFormat#FULL}
   * @param timeStyle the time formatting style. see {@link DateFormat#SHORT}, {@link
   *     DateFormat#MEDIUM}, {@link DateFormat#LONG} or {@link DateFormat#FULL}
   * @param date the date to format
   * @param timeZone optionally provide a the timezone (i.e. "PST")
   * @return formatted date such as "7/9/15, 3:27:57 PM PDT" for en_US locale
   */
  String formatDateTime(int dateStyle, int timeStyle, Date date, String timeZone);

  /**
   * Formats a date for this locale based on the style input and optionally using a
   * GregorianCalendar.
   *
   * <p>Using the GregorianCalendar will become necessary as we expand to additional countries.
   * GregorianCalendar is required for locales such as Thai to get the Gregorian Calendar system,
   * otherwise by default it would use Buddhist Calendar, which for 2016 the year would be 2559
   *
   * @param dateStyle the given formatting style. see {@link DateFormat#SHORT}, {@link
   *     DateFormat#MEDIUM}, {@link DateFormat#LONG} or {@link DateFormat#FULL}
   * @param timeStyle the time formatting style. see {@link DateFormat#SHORT}, {@link
   *     DateFormat#MEDIUM}, {@link DateFormat#LONG} or {@link DateFormat#FULL}
   * @param date the date to format
   * @param timeZone optionally provide a the timezone (i.e. PST)
   * @return formatted date such as "7/9/15" for en_US locale
   */
  String formatDateTime(
      int dateStyle, int timeStyle, Date date, String timeZone, boolean useGregorian);

  /**
   * Formats a date for this locale based on the style input
   *
   * @param dateStyle the date formatting style. see {@link DateFormat#SHORT}, {@link
   *     DateFormat#MEDIUM}, {@link DateFormat#LONG} or {@link DateFormat#FULL}
   * @param timeStyle the time formatting style. see {@link DateFormat#SHORT}, {@link
   *     DateFormat#MEDIUM}, {@link DateFormat#LONG} or {@link DateFormat#FULL}
   * @param date the date to format
   * @return formatted date such as "7/9/15, 3:27:57 PM PDT" for en_US locale
   */
  String formatDateTime(int dateStyle, int timeStyle, Date date);

  /**
   * Get the language code representing the language of this locale.
   *
   * @return the language code for this locale, which will either be the empty string or a lowercase
   *     ISO 639 code
   */
  String getLanguage();

  /**
   * Returns a well-formed IETF BCP 47 language tag representing this locale.
   *
   * @return a BCP47 language tag representing the locale
   */
  String toLanguageTag();

  /**
   * Returns the fallback locale for this APILocale. If this locale is already the root locale, this
   * method will return null.
   *
   * @return an APILocale or <tt>null</tt>
   */
  APILocale getFallback();

  /**
   * Returns the fallback locales for this APILocale. If this locale is already the root locale,
   * this method will return an empty list
   *
   * @return a list of APILocale objects
   */
  List<APILocale> getFallbacks();

  /**
   * Get the full name of this locale.
   *
   * @return the (normalized) full name for this locale
   */
  String getName();

  /**
   * Return the display name of the locale for the current default locale.
   *
   * @return
   */
  String getDisplayName();

  /**
   * Get the display name of the locale in another locale.
   *
   * @param locale where the display name of this locale is to be shown
   * @return
   */
  String getDisplayName(APILocale locale);

  /**
   * Get the name of this locale, in localized form.
   *
   * @return the name of the locale, localized
   */
  String getDisplayNameWithDialect();

  /**
   * Get the name of this locale, in localized form.
   *
   * @param standAlone set to <code>true</code> if you want the name to be upper-cased appropriately
   *     for stand-alone (not embedded in a sentence) use
   * @return the name of the locale, localized
   */
  String getDisplayNameWithDialect(boolean standAlone);

  /**
   * Get the name of this locale, localized for a particular locale.
   *
   * @param locale the locale in which dialect you want the name of this locale
   * @return the name of this locale, in the dialect of <code>locale</code>
   */
  String getDisplayNameWithDialect(APILocale locale);

  /**
   * Get the name of this locale, localized for a particular locale.
   *
   * @param locale the locale in which dialect you want the name of this locale
   * @param standAlone set to <code>true</code> if you want the name to be upper-cased appropriately
   *     for stand-alone (not embedded in a sentence) use
   * @return the name of this locale, in the dialect of <code>locale</code>
   */
  String getDisplayNameWithDialect(APILocale locale, boolean standAlone);

  /**
   * Does the script backing this locale read right-to-left?
   *
   * @return true for right-to-left, false otherwise.
   */
  boolean isRightToLeft();
}
