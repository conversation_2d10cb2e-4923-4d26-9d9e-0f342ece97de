package com.netflix.api.platform.security;

import com.google.common.net.InetAddresses;
import java.net.InetAddress;

/** This was forklifted from whitecastle when that library was removed */
public class IpUtils {

  private IpUtils() {}

  public static InetAddress parseIpAddressSafely(String ipAddressStr) {
    if (ipAddressStr == null || ipAddressStr.isEmpty()) {
      return null;
    }

    try {
      return InetAddresses.forString(ipAddressStr);
    } catch (IllegalArgumentException e) {
      return null;
    }
  }

  private static boolean isPrivateIp(InetAddress address) {
    if (address == null) {
      return false;
    }

    return (address.isSiteLocalAddress()
        || address.isAnyLocalAddress()
        || address.isLinkLocalAddress()
        || address.isLoopbackAddress()
        || address.isMulticastAddress());
  }
}
