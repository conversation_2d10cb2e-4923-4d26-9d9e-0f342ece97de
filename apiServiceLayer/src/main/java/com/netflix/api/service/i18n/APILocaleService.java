package com.netflix.api.service.i18n;

import com.netflix.api.service.APILocale;
import jakarta.annotation.Nullable;
import java.util.List;
import rx.Observable;

/**
 * Each supported country has a default set of supported locales. The default set of supported
 * locales for members is bigger than that for non-members. Never-members (a.k.a. Free-Trial
 * Members, P1 subs) are also considered Netflix members for the purpose of locale resolution. Some
 * requests such as on-device sign-up requests are made on behalf of non-members.
 *
 * <p>By default, the locale used for a request will be the top matching locale supported in the
 * country for the request. You can use the <code>languages</code> URL parameter to override the
 * default.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APILocale.class.png"
 * alt="Class Diagram: APILocale">
 */
public interface APILocaleService {

  /**
   * Provides the ordered set of language tags considering the provided inputs, geolocation, and
   * user profile language preferences.
   *
   * <p>If the member is an NON_REGISTERED_MEMBER, member's language preferences are ignored
   * (default behavior).
   *
   * @param devicePreferredLanguageTags - Language preferences set by the device owner on the
   *     device.
   * @param deviceSupportedLanguages - Languages supported by the device.
   * @param useNonRegisteredMemberLanguagePreferences - true to member's language preferences in
   *     case user of this request is a NON_REGISTERED_MEMBER.
   */
  Observable<List<APILocale>> getMatchingLanguageTags(
      @Nullable final List<String> devicePreferredLanguageTags,
      @Nullable final List<String> deviceSupportedLanguages,
      final boolean useNonRegisteredMemberLanguagePreferences);

  Observable<List<APILocale>> getSecondaryLanguages();

  Observable<List<APILocale>> getProductSupportedLanguages(String productType);

  /**
   * Filters the languageTags provided based on the preferred language tags defined (per country) in
   * October Sky.
   *
   * @param languageTags languages that are supported by Netflix for the requester (user, device,
   *     geo).
   * @param productType A product type
   * @return filtered set of language tags. If an input product type is not defined, then the input
   *     language tags
   */
  Observable<List<APILocale>> getProductPreferredLanguageTags(
      List<APILocale> languageTags, String productType);

  /**
   * Returns the list of languages to be displayed in the drop-down of language preferences a
   * non-member would choose from.
   *
   * @return An observable of list of {@link APILocale}s that must be displayed. One and only one
   *     observable is emitted. If there are any issues, onError of the subscriber to this
   *     observable is called with the underlying exception.
   */
  Observable<List<APILocale>> getNonMemberPreferredLanguageTags();

  /**
   * Some Netflix apps do not support languages (scripts) that are default languages of countries
   * where Netflix has content available.
   *
   * <p>For instance, Netflix app on Webkit devices does not support ar, zh, ko.
   *
   * <p>We want to message the (non-member) user that there are other devices they can use to get
   * access to Netflix content (and shell any assumption on their part that Netflix content in their
   * language is not available in the country).
   *
   * <p>This API returns the url of the image that contains the message.
   *
   * <p>Requirements
   *
   * <p>1. If device preferred language is not in deviceSupported languages, but is in catalog for
   * the country, then user should get the message url in the preferred language
   *
   * <p>2. Device preferred language is, by default, set to a language that is not the user's
   * preference, but the user has not bothered to change it. If device does not support any of the
   * languages in the country's "product preferred languages", show this message. Use case:
   *
   * <ul>
   *   <li>Device preferred language = en-US
   *   <li>Device Supported languages = nl,fr,es,en
   *   <li>country's catalog preferred languages = zh-Hans,zh,en
   *   <li>Result - show the message in zh-Hans
   * </ul>
   *
   * <p>Implementation details:
   *
   * <p>If country's product preferred languages minus device supported languages is non-zero (i.e.,
   * one or more product preferred languages for a country are not supported by the device), then:
   * <br>
   * #1: If devicePreferredLanguage is one of the product preferred language, then show the message
   * in that locale.<br>
   * #2: If #1 is not true, then show the message in the most popular of the product preferred
   * languages.<br>
   * #3: If a message (image) is not available, return an empty observable.
   *
   * <p>Implement several fast property knobs.
   *
   * <ul>
   *   <li>Turn on/off the the logic.
   *   <li>Turn on/off just #2.
   *   <li>URL template is changeable via FP.
   *   <li>Mapping of language-country to locale directory is controlled via FP based mapping.
   * </ul>
   *
   * @param size -- desired size in the format wxh, e.g., 300x400
   * @param devicePreferredLanguageTags List of languages that device prefers. It is typically one.
   * @param deviceSupportedLanguages List of languages that device supports.
   * @param deviceSupportedFontGroups List of font groups that device supports. these are used to
   *     augment deviceSupportedLanguages
   * @param secure true if you want an https image, false otherwise
   * @return an observable that emits 0 or 1 items (url).
   */
  Observable<String> getUnsupportedLanguageMessagingImageUrl(
      final String size,
      @Nullable final List<String> devicePreferredLanguageTags,
      @Nullable final List<String> deviceSupportedLanguages,
      @Nullable final List<String> deviceSupportedFontGroups,
      boolean secure);

  /**
   * List of locales to display on a member's language preference selection UI.
   *
   * @return An observable that emits one list of {@link APILocale} objects.
   */
  Observable<List<APILocale>> getAvailableLanguagePreferenceLocales();
}
