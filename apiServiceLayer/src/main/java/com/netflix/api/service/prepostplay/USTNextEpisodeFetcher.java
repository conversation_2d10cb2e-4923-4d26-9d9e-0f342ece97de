package com.netflix.api.service.prepostplay;

import com.google.protobuf.util.Durations;
import com.netflix.api.context.USTContexts;
import com.netflix.api.grpc.GrpcCallHelpers.Future;
import com.netflix.api.platform.exception.Exceptions;
import com.netflix.api.service.batch.USTListUtils;
import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.napa.client.fallback.NextEpisodeFetcher;
import com.netflix.napa.client.fallback.data.NextEpisodeFallbackData;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.type.proto.Videos;
import com.netflix.ust.adapters.USTStatus;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ust.common.v1.VideoType;
import ust.common.v1.VideoType.Type;
import ust.video.core.v1.BaseResult;
import ust.video.core.v1.CoreResult;
import ust.video.core.v1.GetVideoBaseRequest;
import ust.video.core.v1.GetVideoBaseResponse;
import ust.video.core.v1.GetVideoCoreRequest;
import ust.video.core.v1.GetVideoCoreResponse;
import ust.video.core.v1.VideoAccess;
import ust.video.personalized.v1.EpisodeNumberResult;
import ust.video.personalized.v1.EpisodeResult;
import ust.video.personalized.v1.GetEpisodeNumberRequest;
import ust.video.personalized.v1.GetEpisodeNumberResponse;
import ust.video.personalized.v1.GetEpisodeRequest;
import ust.video.personalized.v1.GetEpisodeResponse;
import ust.video.personalized.v1.VideoPersonalizedAccess;
import ust.video.v1.VideoBase;
import ust.video.v1.VideoCore;
import ust.video.v1.VideoRequest;

@Component
public class USTNextEpisodeFetcher implements NextEpisodeFetcher {

  private static final Logger logger = LoggerFactory.getLogger(USTNextEpisodeFetcher.class);

  private final VideoAccess videoAccess;
  private final VideoPersonalizedAccess videoPersonalizedAccess;

  @Autowired
  public USTNextEpisodeFetcher(
      VideoAccess videoAccess, VideoPersonalizedAccess videoPersonalizedAccess) {
    this.videoAccess = videoAccess;
    this.videoPersonalizedAccess = videoPersonalizedAccess;
  }

  @Override
  public Optional<NextEpisodeFallbackData> getNextEpisode(Integer watchedVideoId) {

    MantisPublishContext.getCurrent().add("api.pp.nextEpisode.fallback.watched", watchedVideoId);
    Optional<NextEpisodeFallbackData> result = Optional.empty();

    try {
      if (isEpisode(watchedVideoId)) {
        result =
            nextEpisode(watchedVideoId)
                .map(
                    nextEpisodeId -> {
                      NextEpisodeFallbackData.Builder builder =
                          new NextEpisodeFallbackData.Builder();
                      builder.setNextEpisodeId(nextEpisodeId);

                      Optional<Integer> episodeSequenceNumber =
                          episodeSequenceNumber(nextEpisodeId);
                      episodeSequenceNumber.ifPresent(builder::setEpisodeSequenceNumber);

                      Map<Integer, VideoCore> episodes = videoCore(watchedVideoId, nextEpisodeId);
                      VideoCore watched = episodes.get(watchedVideoId);
                      if (watched != null) {
                        builder.setCurrentEpisodeRuntimeInMillis(
                            Durations.toMillis(watched.getViewableInfo().getRuntime()));
                      }
                      VideoCore next = episodes.get(nextEpisodeId);
                      if (next != null) {
                        builder.setSeasonSequenceNumber(
                            next.getEpisode().getBoxedSeasonSequenceNumber());
                        builder.setTopNodeId(next.getVideoBase().getBoxedTopNodeId());
                        builder.setNextEpisodeRuntimeInMillis(
                            Durations.toMillis(next.getViewableInfo().getRuntime()));
                      }
                      NextEpisodeFallbackData data = builder.createNextEpisodeFallbackData();
                      DynamicCounter.increment("api.pp.nextEpisode.fallback", "result", "success");
                      MantisPublishContext.getCurrent()
                          .add("api.pp.nextEpisode.fallback.result", "success");
                      return data;
                    });
      } else {
        DynamicCounter.increment("api.pp.nextEpisode.fallback", "result", "non-episode");
        MantisPublishContext.getCurrent().add("api.pp.nextEpisode.fallback.result", "non-episode");
      }
    } catch (Throwable t) {
      logger.error("Error fetching next episode for {}", watchedVideoId, t);
      DynamicCounter.increment("api.pp.nextEpisode.fallback", "result", "error");
      MantisPublishContext.getCurrent().add("api.pp.nextEpisode.fallback.result", "error");
      MantisPublishContext.getCurrent()
          .add("api.pp.nextEpisode.fallback.error", Exceptions.getStackTrace(t));
    }
    MantisPublishContext.getCurrent()
        .add(
            "api.pp.nextEpisode.fallback.data",
            result.isPresent() ? USTListUtils.toJson(result.get()) : "<empty>");

    return result;
  }

  private boolean isEpisode(Integer videoId) {
    var type =
        videoBase(videoId)
            .map(VideoBase::getVideoType)
            .map(VideoType::getType)
            .orElse(Type.TYPE_UNSPECIFIED);

    return type == Type.TYPE_EPISODE;
  }

  private Optional<Integer> nextEpisode(Integer episodeId) {
    Optional<Integer> result = Optional.empty();
    try {
      GetEpisodeResponse episodeResponse =
          Future.call(
                  videoPersonalizedAccess::getNextEpisode,
                  GetEpisodeRequest.newBuilder()
                      .setPersonalizedVideoContext(
                          USTContexts.personalizedVideoContext(
                              USTVideoMetadataAdapter.ENABLE_VXS_DEBUG.get()))
                      .setVideoRequest(
                          USTContexts.videoRequestBuilder(Collections.singletonList(episodeId)))
                      .build())
              .toCompletableFuture()
              .get();

      if (!episodeResponse.getResultsList().isEmpty()) {
        EpisodeResult episodeResult = episodeResponse.getResultsList().getFirst();
        if (USTStatus.isOk(episodeResult.getStatus())) {
          result = Optional.of(episodeResult.getResult().getEpisode().getId());
        }
      }
    } catch (Exception t) {
      // if failed, log it and return empty Optional
      logger.error("Error fetching next episode for watched {}", episodeId, t);
    }
    return result;
  }

  private Optional<Integer> episodeSequenceNumber(Integer episodeId) {
    Optional<Integer> result = Optional.empty();

    try {
      GetEpisodeNumberResponse episodeNumberResponse =
          Future.call(
                  videoPersonalizedAccess::getEpisodeNumber,
                  GetEpisodeNumberRequest.newBuilder()
                      .setPersonalizedVideoContext(
                          USTContexts.personalizedVideoContext(
                              USTVideoMetadataAdapter.ENABLE_VXS_DEBUG.get()))
                      .setVideoRequest(
                          USTContexts.videoRequestBuilder(Collections.singletonList(episodeId)))
                      .build())
              .toCompletableFuture()
              .get();

      if (!episodeNumberResponse.getResultsList().isEmpty()) {
        EpisodeNumberResult episodeNumberResult = episodeNumberResponse.getResultsList().getFirst();
        if (USTStatus.isOk(episodeNumberResult.getStatus())) {
          result = Optional.of(episodeNumberResult.getResult().getEpisodeNumber());
        }
      }
    } catch (Exception t) {
      // if failed, log it and return empty Optional
      logger.error("Error fetching episode number for watched episodeId={}", episodeId, t);
    }
    return result;
  }

  private Optional<VideoBase> videoBase(Integer videoId) {
    if (videoId == null) return Optional.empty();

    VideoRequest videoRequest =
        VideoRequest.newBuilder().addVideos(Videos.toProtobuf(videoId)).build();

    GetVideoBaseRequest baseRequest =
        GetVideoBaseRequest.newBuilder().setVideoRequest(videoRequest).build();

    try {
      return Future.call(videoAccess::getVideoBase, baseRequest)
          .thenApply(GetVideoBaseResponse::getResultsList)
          .thenApply(
              baseResults ->
                  baseResults.stream()
                      .filter(baseResult -> USTStatus.isOk(baseResult.getStatus()))
                      .findFirst()
                      .map(BaseResult::getResult))
          .toCompletableFuture()
          .get();
    } catch (Exception e) {
      return Exceptions.chainedSneakyThrow(e);
    }
  }

  private Map<Integer, VideoCore> videoCore(int... videoIds) {
    VideoRequest videoRequest =
        VideoRequest.newBuilder().addAllVideos(Videos.toProtobuf(videoIds)).build();

    GetVideoCoreRequest coreRequest =
        GetVideoCoreRequest.newBuilder().setVideoRequest(videoRequest).build();

    try {
      return Future.call(videoAccess::getVideoCore, coreRequest)
          .thenApply(GetVideoCoreResponse::getResultsList)
          .thenApply(
              coreResults ->
                  coreResults.stream()
                      .filter(coreResult -> USTStatus.isOk(coreResult.getStatus()))
                      .collect(
                          Collectors.toMap(
                              coreResult -> coreResult.getKey().getId(), CoreResult::getResult)))
          .toCompletableFuture()
          .get();
    } catch (Exception e) {
      return Exceptions.chainedSneakyThrow(e);
    }
  }
}
