package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.video.APICertification#APICertification */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class CertificationRating {
  private Integer boardId;

  private String boardName;

  private String maturityDescription;

  private Integer maturityLevel;

  private CertificationRatingReason ratingReason;

  private String value;

  private String videoSpecificRatingReason;

  private String certSystemConfirmationId;

  private Integer ratingId;

  private String shortDescription;

  public Integer getBoardId() {
    return boardId;
  }

  public String getBoardName() {
    return boardName;
  }

  public String getMaturityDescription() {
    return maturityDescription;
  }

  public Integer getMaturityLevel() {
    return maturityLevel;
  }

  public CertificationRatingReason getRatingReason() {
    return ratingReason;
  }

  public String getValue() {
    return value;
  }

  public String getVideoSpecificRatingReason() {
    return videoSpecificRatingReason;
  }

  public String getCertSystemConfirmationId() {
    return certSystemConfirmationId;
  }

  public Integer getRatingId() {
    return ratingId;
  }

  public String getShortDescription() {
    return shortDescription;
  }

  public CertificationRating setBoardId(Integer boardId) {
    this.boardId = boardId;
    return this;
  }

  public CertificationRating setBoardName(String boardName) {
    this.boardName = boardName;
    return this;
  }

  public CertificationRating setMaturityDescription(String maturityDescription) {
    this.maturityDescription = maturityDescription;
    return this;
  }

  public CertificationRating setMaturityLevel(Integer maturityLevel) {
    this.maturityLevel = maturityLevel;
    return this;
  }

  public CertificationRating setRatingReason(CertificationRatingReason ratingReason) {
    this.ratingReason = ratingReason;
    return this;
  }

  public CertificationRating setValue(String value) {
    this.value = value;
    return this;
  }

  public CertificationRating setVideoSpecificRatingReason(String videoSpecificRatingReason) {
    this.videoSpecificRatingReason = videoSpecificRatingReason;
    return this;
  }

  public CertificationRating setCertSystemConfirmationId(String certSystemConfirmationId) {
    this.certSystemConfirmationId = certSystemConfirmationId;
    return this;
  }

  public CertificationRating setRatingId(Integer ratingId) {
    this.ratingId = ratingId;
    return this;
  }

  public CertificationRating setShortDescription(String shortDescription) {
    this.shortDescription = shortDescription;
    return this;
  }
}
