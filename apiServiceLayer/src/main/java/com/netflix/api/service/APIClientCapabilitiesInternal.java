package com.netflix.api.service;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.server.context.CurrentRequestContext;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public class APIClientCapabilitiesInternal {

  // known capabilities
  public static final String INTERACTIVE_VIDEOS_FEATURE = "interactivePackageVideos";
  public static final String INTERACTIVE_ORIGINALS = "interactiveOriginals";
  public static final String DEVICE_CAPABILITIES = "titleCapabilities";
  public static final String FEATURE_CAPABILITIES = "featureCapabilities";
  public static final String SUPPORTED_LOCALIZATION_FEATURES = "supportedLocalizationFeatures";

  // features that we are (still) explicitly handling
  public static final String DEFAULT_KIDS_PROFILE_FEATURE = "defaultKidsProfile";
  public static final String COLLECTABLES = "collectibles";

  private static final String MULTI_LANG_FEATURE = "clientAppCanHandleMultiLanguageCatalog";

  private static final Property<Boolean> MULTI_LANG_MERCH_FEATURE =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.clientAppCanHandleMultiLanguageCatalog", Boolean.class)
          .orElse(true);

  private static final Property<Boolean> ALWAYS_SUPPORT_LOCALIZED_KIDS_PROFILE =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.supportedLocalizationFeatures.kidsProfile.defaultTrue.enabled", Boolean.class)
          .orElse(true);

  // FIXME make client capabilities immutable!!!!
  private final Map<String, Collection<String>> clientCapabilities = HashMap.newHashMap(10);

  APIClientCapabilitiesInternal() {}

  public static String getMultiLangFeatureName() {
    return MULTI_LANG_FEATURE;
  }

  /**
   * @return the current values in the request or empty if none are available.
   */
  public static Optional<APIClientCapabilitiesInternal> get(APIRequest apiRequest) {
    return Optional.ofNullable(apiRequest)
        .map(
            api ->
                ((APIRequestContextInternal) api.getRequestContext())
                    .getClientCapabilitiesInternal());
  }

  public static Optional<Collection<String>> getCapability(APIRequest apiRequest, String key) {
    return get(apiRequest)
        .map(APIClientCapabilitiesInternal::asMap)
        .filter(map -> map.containsKey(key))
        .flatMap(map -> Optional.of(map.get(key)));
  }

  public boolean currentInteractiveTitlesPresent() {
    return false;
  }

  public String getCurrentInteractiveTitlesAsString() {
    return "";
  }

  public Set<Integer> getAllInteractiveTitlesImmutable() {
    return Collections.emptySet();
  }

  public Map<String, Collection<String>> asMap() {
    return clientCapabilities;
  }

  public boolean deviceSupportsLocalizedKidsProfile() {
    if (ALWAYS_SUPPORT_LOCALIZED_KIDS_PROFILE.get()) {
      return true;
    }
    return clientCapabilities
        .getOrDefault(SUPPORTED_LOCALIZATION_FEATURES, Collections.emptySet())
        .contains(DEFAULT_KIDS_PROFILE_FEATURE);
  }

  public boolean clientAppCanHandleMultiLanguageCatalog() {
    if (!MULTI_LANG_MERCH_FEATURE.get()) {
      return false;
    }

    for (Collection<String> collection : this.clientCapabilities.values()) {
      if (collection != null && collection.contains(MULTI_LANG_FEATURE)) {
        return true;
      }
    }
    return false;
  }

  public void addClientCapabilities(String featureSetKey, Collection<String> clientCapabilities) {
    if (featureSetKey != null && clientCapabilities != null && !clientCapabilities.isEmpty()) {
      if (!INTERACTIVE_VIDEOS_FEATURE.equals(featureSetKey)) {
        Collection<String> features = this.clientCapabilities.get(featureSetKey);
        if (features == null) {
          features = new HashSet<>(clientCapabilities);
          this.clientCapabilities.put(featureSetKey, features);
        } else {
          features.addAll(clientCapabilities);
        }
        tryAddMultiLanguageCatalogCapability(features);
      }
    }
  }

  private static void tryAddMultiLanguageCatalogCapability(Collection<String> features) {
    if (!MULTI_LANG_MERCH_FEATURE.get() || (features == null)) {
      return;
    }

    if (features.contains(MULTI_LANG_FEATURE)) {
      CurrentRequestContext.get().addContext("VMS_NOLANGCAT", "t");
    }
  }
}
