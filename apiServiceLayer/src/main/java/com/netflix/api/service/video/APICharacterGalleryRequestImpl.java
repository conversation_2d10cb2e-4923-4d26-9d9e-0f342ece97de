package com.netflix.api.service.video;

import com.netflix.api.service.APICountry;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIUser;
import java.util.HashMap;
import java.util.Map;

/**
 * An <code>APICharacterGalleryRequestImpl</code> object defines how lists of videos associated with
 * particular {@code APICharacter}s will be generated by such methods as {@code
 * APICharacter#getGallery}, {@code APICharacter#getRecentlyWatchedVideos}, and {@code
 * APICharacter#getWatchNextVideos}. The country, user, and ESN are set to sensible values
 * automatically, but you have the option to override these defaults by using the methods of this
 * class.
 */
public class APICharacterGalleryRequestImpl implements APICharacterDataRequest {
  private String esn;
  private APIUser user;
  private APICountry country;
  private boolean continueWatchingIncludeIncomplete;
  private final Map<String, Object> annotations = new HashMap<>();

  APICharacterGalleryRequestImpl(APIRequest apiRequest) {
    if (apiRequest != null) {
      this.user = apiRequest.getUser();
      this.esn = apiRequest.getRequestContext().getESN();
      this.country = apiRequest.getRequestContext().getCountry();
    }
  }

  @Override
  public String getEsn() {
    return esn;
  }

  @Override
  public APICharacterGalleryRequestImpl overrideEsn(String esn) {
    this.esn = esn;
    return this;
  }

  @Override
  public APIUser getUser() {
    return user;
  }

  @Override
  public APICharacterGalleryRequestImpl overrideUser(APIUser user) {
    this.user = user;
    return this;
  }

  @Override
  public APICountry getCountry() {
    return country;
  }

  @Override
  public APICharacterGalleryRequestImpl overrideCountry(APICountry country) {
    this.country = country;
    return this;
  }

  @Override
  public APICharacterDataRequest setContinueWatching(boolean continueWatching) {
    return this;
  }

  @Override
  public boolean isContinueWatchingIncludeIncomplete() {
    return continueWatchingIncludeIncomplete;
  }

  @Override
  public APICharacterDataRequest setContinueWatchingIncludeIncomplete(
      boolean continueWatchingIncludeIncomplete) {
    this.continueWatchingIncludeIncomplete = continueWatchingIncludeIncomplete;
    return this;
  }

  @Override
  public APICharacterDataRequest addAnnotation(String name, Object value) {
    annotations.put(name, value);
    return this;
  }

  @Override
  public Map<String, Object> getAnnotations() {
    return annotations;
  }
}
