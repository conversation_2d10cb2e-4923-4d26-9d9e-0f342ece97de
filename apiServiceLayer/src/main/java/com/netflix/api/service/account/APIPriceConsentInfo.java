package com.netflix.api.service.account;

public interface APIPriceConsentInfo {
  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.plan_id"
   */
  Long getPlanId();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.price_tier"
   */
  String getPriceTier();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.currency"
   */
  String getCurrency();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.price_formatted"
   */
  String getPriceFormatted();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.consented_date"
   */
  Long getConsentedDate();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.notification_start_date"
   */
  Long getNotificationStartDate();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.extended_gf_date"
   */
  Long getExtendedGfDate();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.enforce_price_change_until_date"
   */
  Long getEnforcePriceChangeUntilDate();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.auto_consent_after_date"
   */
  Long getAutoConsentAfterDate();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.auto_consent_notification_date"
   */
  Long getAutoConsentNotificationDate();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.status"
   */
  APIConsentStatus getStatus();
}
