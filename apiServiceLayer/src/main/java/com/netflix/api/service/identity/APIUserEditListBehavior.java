package com.netflix.api.service.identity;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.service.identity.APIUser.Experience;
import com.netflix.api.service.identity.exceptions.DuplicateProfileIconException;
import com.netflix.api.service.identity.exceptions.DuplicateProfileNameException;
import com.netflix.api.service.identity.exceptions.EditProfileException;
import com.netflix.custeng.subscriber.model.ExperienceType;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.subscriber.types.protogen.ParentalControlState;
import com.netflix.subscriberservice.protogen.ProfileAttributes;
import com.netflix.subscriberservice.protogen.SetParentalControlAttributesRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA. User: mjacobs Date: 3/4/14 Time: 2:12 PM To change this template use
 * File | Settings | File Templates.
 */
public class APIUserEditListBehavior extends APIUserManagementBehavior {

  private static final String COUNTER_EDIT_PROFILE_VALIDATION_ERROR =
      "API.USERPROFILES.COUNTER_EDIT_PROFILE_VALIDATION_ERROR";
  private static final String COUNTER_EDIT_PROFILE_ERROR =
      "API.USERPROFILES.COUNTER_EDIT_PROFILE_ERROR";

  private final APIUserInternal userToEdit;

  private final UserEditor userEditor;
  private final CustomerGetter customerGetter;
  private final CacheInvalidator cacheInvalidator;

  @AssistedInject
  public APIUserEditListBehavior(
      @Assisted APIUser userToEdit,
      APIUserFactory userFactory,
      APIProfileErrors.Localizer localizer) {
    super(userFactory, localizer);
    if (userToEdit == null) {
      throw new APIServiceRuntimeException("User to edit must not be null");
    }
    this.userToEdit = (APIUserInternal) userToEdit;
    this.userEditor = new SubscriberUserEditor();
    this.customerGetter = new SubscriberCustomerGetter();
    this.cacheInvalidator = new ProductionCacheInvalidator();
  }

  @Override
  public APIUser loadItem(IdentityResult result) {
    return customerGetter.getByIdentityResult(result);
  }

  @Override
  public List<IdentityResult> getReferences() {
    List<IdentityResult> updatedCustomerIdList = new ArrayList<>();
    validateProfileOnEdit();
    try {
      Long updatedCustomerId = userEditor.editUser(iconName);
      if (updatedCustomerId != null) {
        cacheInvalidator.invalidateForUser(userToEdit);
        contextManager.refreshUserOnApiRequest();
        updatedCustomerIdList.add(userToEdit.getIdentityResult());
      } else {
        DynamicCounter.increment(COUNTER_EDIT_PROFILE_ERROR);
        String errorMsg = createUnknownProfileEditErrorMessage();
        throw new EditProfileException(errorMsg);
      }
    } catch (Exception e) {
      DynamicCounter.increment(COUNTER_EDIT_PROFILE_ERROR);
      if (isDuplicateNameException(e)) {
        String errorMsg = getDuplicateNameErrorMessage();
        throw new DuplicateProfileNameException(errorMsg);
      } else if (isDuplicateIconException(e)) {
        String errorMsg = getDuplicatedIconErrorMessage();
        throw new DuplicateProfileIconException(errorMsg);
      } else {
        String errorMsg = createUnknownProfileEditErrorMessage();
        throw new EditProfileException(errorMsg, e);
      }
    }
    return updatedCustomerIdList;
  }

  private String createUnknownProfileEditErrorMessage() {
    return localizer.localize(
        APIProfileErrors.UNKNOWN_PROFILE_EDIT_FAILURE_MESSAGE_KEY,
        APIProfileErrors.DEFAULT_UNKNOWN_EDIT_ERROR_MESSAGE);
  }

  private void validateProfileOnEdit() {
    try {
      if (profileName != null) {
        validateName();
      }
    } catch (APIServiceRuntimeException e) {
      DynamicCounter.increment(COUNTER_EDIT_PROFILE_VALIDATION_ERROR);
      throw e;
    }
  }

  interface UserEditor {

    Long editUser(String iconName);
  }

  class SubscriberUserEditor implements UserEditor {

    @Override
    public Long editUser(String iconName) {
      userFactory
          .getAccountProfileHandler()
          .updateProfileParentalControlAttributes(
              buildParentalControlAttributes(
                  APIUserUtil.getAccountOwnerId(userToEdit),
                  APIUserUtil.getCustomerId(userToEdit)));
      return userFactory
          .getAccountProfileHandler()
          .updateProfile(
              APIUserUtil.getAccountOwnerId(userToEdit),
              APIUserUtil.getCustomerId(userToEdit),
              buildProfileAttributes());
    }
  }

  private SetParentalControlAttributesRequest buildParentalControlAttributes(
      Long acctOwnerId, Long profileId) {
    SetParentalControlAttributesRequest.Builder builder =
        SetParentalControlAttributesRequest.newBuilder();
    boolean changed = false;
    builder.setAccountId(acctOwnerId).setProfileId(profileId);
    if (removeProfileLockPin != null) {
      builder.setRemoveProfileAccessPin(removeProfileLockPin);
      changed = true;
    }
    if (profileLockPin != null) {
      builder.setProfileAccessPin(profileLockPin);
      changed = true;
    }
    if (profileCreationLocked != null) {
      builder.setParentalControlState(
          ParentalControlState.newBuilder()
              .setIsProfileCreationLocked(profileCreationLocked)
              .build());
      changed = true;
    }
    return changed ? builder.build() : null;
  }

  private ProfileAttributes buildProfileAttributes() {
    ProfileAttributes.Builder builder = ProfileAttributes.newBuilder();
    boolean changed = false;

    if (profileName != null) {
      builder.setProfileName(profileName);
      changed = true;
    }

    if (experience != null) {
      if (experience == Experience.jfk) {
        builder.setExperienceType(ExperienceType.just_for_kids.name());
      } else if (experience == Experience.family) {
        builder.setExperienceType(ExperienceType.family.name());
      } else if (experience == Experience.watch_together) {
        builder.setExperienceType(ExperienceType.watch_together.name());
      } else {
        builder.setExperienceType(ExperienceType.regular.name());
      }
      changed = true;
    }

    if (rawMaturityValue != null) {
      builder.setMaxMaturityLevel(rawMaturityValue);
      changed = true;
    }

    if (maturityLevel != null) {
      builder.setMaturityLevel(maturityLevel.name());
      changed = true;
    }

    if (firstUse != null) {
      builder.setIsProfileFirstUse(firstUse);
      changed = true;
    }

    if (iconName != null) {
      builder.setAvatarImage(iconName);
      changed = true;
    }

    if (language != null) {
      builder.setPrimaryLanguage(language);
      changed = true;
    }

    if (autoplayEnabled != null) {
      builder.setHasAutoplayback(autoplayEnabled);
      changed = true;
    }

    if (cookieDisclosureAccepted != null) {
      builder.setCookieDisclosureAccepted(cookieDisclosureAccepted);
      changed = true;
    }

    return changed ? builder.build() : null;
  }

  interface CustomerGetter {

    APIUser getByIdentityResult(IdentityResult customer);
  }

  class SubscriberCustomerGetter implements CustomerGetter {

    @Override
    public APIUser getByIdentityResult(IdentityResult customer) {
      return userFactory.createUser(customer, true);
    }
  }
}
