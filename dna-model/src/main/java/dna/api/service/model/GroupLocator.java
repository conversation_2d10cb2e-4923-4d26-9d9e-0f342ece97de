package dna.api.service.model;

import javax.annotation.Generated;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
public enum GroupLocator {
  ALL,

  BILLBOARD,

  BULK_RATER,

  BASED_ON_RECENTLY_INTERACTED,

  BLAST_FROM_THE_PAST,

  CA<PERSON><PERSON>OG,

  CA<PERSON><PERSON>OR<PERSON>,

  CATEGORY_CRAVERS,

  CHAR<PERSON><PERSON>R,

  CHARACTER_VIDEO,

  COMING_SOON,

  CONTENT_PREVIEW,

  CONTINUE_PLAYING,

  CONTINUE_WATCHING,

  DOWNLOADS_FOR_YOU,

  EXTRAS,

  FAST_LAUGHS,

  FAVORITE_TITLES,

  GAMES_BILLBOARD,

  GAMES_FEATURE_EDUCATION,

  GAMES_GENRE,

  GAMES_PLAYLIST,

  GAMES_POPULAR,

  GAMES_RECENTLY_RELEASED,

  GAMES_TRAILER,

  GENRE,

  GO_BEHIND_THE_SCENES,

  <PERSON><PERSON><PERSON><PERSON>_GEMS,

  HIERARCHICAL_NEW_RELEASES,

  H<PERSON><PERSON>RCHIC<PERSON>_SUGGESTIONS,

  KIDS_FAVORITES,

  LOCAL_CONTENT_MOSIAC,

  LOCAL_LANGUAGE_MOSIAC,

  MOST_BINGED,

  MOST_REWATCHED,

  MOST_WATCHED,

  MOST_THUMBED,

  MOVIE_LOVERS_POSTER,

  MY_DOWNLOADS,

  REMINDERS,

  NETFLIX_ORIGINALS,

  NEW_RELEASES,

  NEW_THIS_WEEK,

  NOTIFICATIONS,

  PAGE_CONTAINER_EVIDENCE,

  PAGE_VIDEO_CONTAINER_EVIDENCE,

  PERSONALIZED_CATEGORY,

  PLAYLIST,

  POPULAR_EPISODES,

  POPULAR_TITLES,

  POSTER_CONTROL,

  PREDICTED_THUMBS_UP,

  RECENTLY_ADDED,

  RECENTLY_BROWSED,

  RECENTLY_AIRED,

  RECENTLY_WATCHED,

  RISK_AVERSE_PIVOTS,

  SAME_CAST,

  SIMS,

  SOCIAL_PROOF,

  STAFF_PICKS,

  TALENTS,

  TRAILERS,

  TITLE_GROUP,

  TOP_MATCHES_PIVOT,

  TOP_N,

  TOP_REWATCH_PICKS,

  TOP_SEARCHES,

  TRENDING_NOW,

  ULTRA_HD,

  VIEWING_ACTIVITY,

  WATCHATHON,

  WATCH_AGAIN,

  WATCH_NOW,

  WINDOWED_COMING_SOON,

  WINDOWED_NEW_RELEASES,

  YOUR_DAILY_PICKS
}
