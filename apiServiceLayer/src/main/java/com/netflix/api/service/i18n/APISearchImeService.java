package com.netflix.api.service.i18n;

import java.util.Map;
import rx.Observable;

/**
 * Service that provides auto-complete suggestions for certain Japanese and Chinese scripts.
 *
 * @see
 */
public interface APISearchImeService {
  /**
   * Get IME completions.
   *
   * @param input Map with structure like so: [input: "inputStringToBeTranslated", from:
   *     "fromScript", to: "toScript"]
   * @return a Json node of the following structure: <br>
   *     Example:
   *     <pre>
   * {
   *  "predictions": ["青葉区"],
   *  "segments": [
   *    {
   *      "key": "あお",
   *      "candidates": ["青"]
   *    }
   *  ]
   * }
   * </pre>
   */
  @Deprecated
  Observable<?> getCompletions(Map<String, String> input);
}
