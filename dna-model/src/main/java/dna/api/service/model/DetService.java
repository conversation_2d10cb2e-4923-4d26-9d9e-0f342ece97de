package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class DetService implements GraphData {
  public Map<String, Atom<List<EditorialPromotion>>> getPromotions;

  public Map<String, String> partnerData;
}
