package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PersonalizedVideosOptions {
  private Integer maxResults;

  private VideoListSortOrder sortOrder;

  private Map<String, Object> annotations;

  public Integer getMaxResults() {
    return maxResults;
  }

  public VideoListSortOrder getSortOrder() {
    return sortOrder;
  }

  public Map<String, Object> getAnnotations() {
    return annotations;
  }

  public PersonalizedVideosOptions setMaxResults(Integer maxResults) {
    this.maxResults = maxResults;
    return this;
  }

  public PersonalizedVideosOptions setSortOrder(VideoListSortOrder sortOrder) {
    this.sortOrder = sortOrder;
    return this;
  }

  public PersonalizedVideosOptions setAnnotations(Map<String, Object> annotations) {
    this.annotations = annotations;
    return this;
  }
}
