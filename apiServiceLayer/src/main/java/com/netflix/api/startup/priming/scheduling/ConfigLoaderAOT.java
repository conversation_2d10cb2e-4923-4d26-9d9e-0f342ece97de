package com.netflix.api.startup.priming.scheduling;

import com.google.inject.Binding;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.ProvisionException;
import com.google.inject.spi.ProvisionListener.ProvisionInvocation;
import com.netflix.archaius.NetflixCascadeStrategy;
import com.netflix.archaius.api.CascadeStrategy;
import com.netflix.archaius.api.Config;
import com.netflix.archaius.api.StrInterpolator;
import com.netflix.archaius.api.StrInterpolator.Lookup;
import com.netflix.archaius.api.annotations.ConfigurationSource;
import com.netflix.archaius.api.annotations.ConfigurationSource.NullCascadeStrategy;
import com.netflix.archaius.guice.Raw;
import com.netflix.archaius.interpolate.CommonsStrInterpolator;
import com.netflix.config.NetflixConfiguration;
import com.netflix.config.utils.PropertiesLoadUtil;
import com.netflix.lifecycle.ConfigurationPropertyLoader;
import com.netflix.lifecycle.NFProperties;
import com.netflix.lifecycle.NFPropertiesListener;
import java.io.FileNotFoundException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.configuration.AbstractConfiguration;
import org.apache.commons.configuration.ConfigurationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// this class a mostly a merge of Archaius1DelegatingConfigurationSourceListener
public class ConfigLoaderAOT {
  private static final Logger LOG = LoggerFactory.getLogger(ConfigLoaderAOT.class);

  private final CascadeStrategy strategy;
  private final ConfigurationPropertyLoader loader;
  private final NFPropertiesListener properlyListener;
  private final Lookup lookup;
  private static final StrInterpolator DEFAULT_INTERPOLATOR = CommonsStrInterpolator.INSTANCE;
  private final Injector injector;

  @Inject
  public ConfigLoaderAOT(
      @Raw Config config,
      Injector injector,
      ConfigurationPropertyLoader loader,
      NFPropertiesListener properlyListener,
      CascadeStrategy strategy) {
    this.lookup = key -> config.getString(key, null);
    if (strategy == null) {
      strategy = new NetflixCascadeStrategy();
    }
    this.strategy = strategy;
    this.loader = loader;
    this.injector = injector;
    this.properlyListener = properlyListener;
  }

  private void loadConfiguration(Class<?> cls, ConfigurationSource configSource) {
    List<String> resources = new ArrayList<>();
    if (configSource.value() != null && configSource.value().length > 0) {
      CascadeStrategy cascadeStrategy = getCascadeStrategy(configSource.cascading());
      for (String source : configSource.value()) {
        resources.addAll(cascadeStrategy.generate(source, DEFAULT_INTERPOLATOR, this.lookup));
      }
    }

    NetflixConfiguration config = NetflixConfiguration.getConfigInstance();
    for (String resourceName : resources) {
      URL url = NetflixConfiguration.getResource(resourceName + ".properties");
      if (url != null) {
        LOG.info("Loading configuration url={}", url);

        try {
          loadConfiguration(config, url, resourceName);
        } catch (FileNotFoundException e) {
          LOG.debug("Configuration not found for resource={} at source={}", resourceName, cls, e);
        } catch (Exception e) {
          throw new ProvisionException(
              "Unable to load configuration for " + resourceName + " at source " + cls, e);
        }
      }
    }
  }

  private void loadConfiguration(NetflixConfiguration config, URL url, String resourceName)
      throws ConfigurationException, FileNotFoundException {
    AbstractConfiguration propConfig = PropertiesLoadUtil.getConfigFromPropertiesFile(url);
    if (propConfig != null) {
      config.addConfiguration(propConfig, resourceName);
      String ls = System.getProperty("line.separator");
      if (ls != null) {
        config.setProperty("line.separator", ls);
      }
    }
  }

  private CascadeStrategy getCascadeStrategy(Class<? extends CascadeStrategy> clazz) {
    return NullCascadeStrategy.class.isAssignableFrom(clazz)
        ? strategy
        : injector.getInstance(clazz);
  }

  public void loadConfigForBinding(Binding<?> binding) {
    final Class<?> cls = binding.getKey().getTypeLiteral().getRawType();

    if (cls.isAnnotationPresent(ConfigurationSource.class)) {
      if (loader == null) {
        throw new RuntimeException(
            "@ConfigurationSource on "
                + cls.getName()
                + " cannot be processed as part of a static initialization path");
      }
      loadConfiguration(cls, cls.getAnnotation(ConfigurationSource.class));
    }

    if (cls.isAnnotationPresent(NFProperties.class)) {
      loadProperties(binding);
    }
  }

  private <T> void loadProperties(final Binding<T> binding) {
    properlyListener.onProvision(
        new ProvisionInvocation<T>() {
          @Override
          public Binding<T> getBinding() {
            return binding;
          }

          @Override
          public T provision() {
            return null;
          }
        });
  }
}
