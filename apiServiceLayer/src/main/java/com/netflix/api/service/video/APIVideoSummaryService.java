package com.netflix.api.service.video;

import com.google.inject.Provider;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.platform.util.CompletionStageAdapter;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.videometadata.l10n.VMSL10NProviderDelegate;
import com.netflix.videometadata.type.Certification;
import com.netflix.videometadata.type.CompleteVideo;
import jakarta.annotation.Nonnull;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletionStage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Single;
import rx.Subscriber;

/** Service for getting basic video metadata information. */
@Component
@EnableDeprecatedMetrics
public class APIVideoSummaryService {
  private static final Logger logger = LoggerFactory.getLogger(APIVideoSummaryService.class);

  public static final Property<Boolean> RETURN_TOP_NODE_FOR_SUPPL_ANCESTOR =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.return.top.node.for.supplemental.ancestor", Boolean.class)
          .orElse(true);

  private final Provider<APIVideoFactory> videoFactoryProvider;

  private final VMSL10NProviderDelegate vmsl10NProviderDelegate;

  @Autowired
  public APIVideoSummaryService(
      Provider<APIVideoFactory> videoFactoryProvider,
      VMSL10NProviderDelegate vmsl10NProviderDelegate) {
    this.videoFactoryProvider = videoFactoryProvider;
    this.vmsl10NProviderDelegate = vmsl10NProviderDelegate;
  }

  public Observable<APIVideo> getTopNode(CompleteVideo presentationVideo) {
    return Observable.just(getAncestors(List.of(presentationVideo)))
        .flatMap(
            map -> {
              Integer ref = map.get(presentationVideo.getId());
              return ref != null
                  ? CompletionStageAdapter.toObservable(getInstance(ref))
                  : Observable.just(null);
            });
  }

  private CompletionStage<APIVideo> getInstance(Integer ref) {
    return videoFactoryProvider
        .get()
        .getInstancesCollectionAsync(List.of(ref))
        .thenApply(m -> m != null && !m.isEmpty() ? m.iterator().next() : null);
  }

  public Map<Integer, Integer> getAncestors(final Collection<CompleteVideo> completeVideos) {
    DynamicCounter.increment("api.videoSummary.ancestor.call");
    Map<Integer, Integer> topNodes = new HashMap<>();
    for (CompleteVideo completeVideo : completeVideos) {
      getAncestor(completeVideo).ifPresent(parent -> topNodes.put(completeVideo.getId(), parent));
    }

    return topNodes;
  }

  public static Optional<Integer> getAncestor(@Nonnull CompleteVideo completeVideo) {
    if (RETURN_TOP_NODE_FOR_SUPPL_ANCESTOR.get()) {
      if (completeVideo.getTopNode() != null) {
        return Optional.of(completeVideo.getTopNode().getId());
      }
      return Optional.empty();
    } else {
      return getParent(completeVideo);
    }
  }

  public static Optional<Integer> getParent(@Nonnull CompleteVideo completeVideo) {
    if (completeVideo.isSupplementalVideo()
        && completeVideo.getSupplementalVideoParents() != null
        && !completeVideo.getSupplementalVideoParents().isEmpty()) {
      return Optional.of(completeVideo.getSupplementalVideoParents().getFirst().getId());
      // TODO fix for split seasons, see VideoRoutesUtil
    } else if (completeVideo.isEpisode() && completeVideo.getSeasonParent() != null) {
      return Optional.of(completeVideo.getSeasonParent().getId());
    } else if (completeVideo.getTopNode() != null) {
      return Optional.of(completeVideo.getTopNode().getId());
    }
    return Optional.empty();
  }

  public Single<Map<Integer, Integer>> getEpisodeBatchParents(
      final Single<Collection<CompleteVideo>> completeVideosObs) {
    return completeVideosObs.map(this::getEpisodeBatchParents);
  }

  public Map<Integer, Integer> getEpisodeBatchParents(
      final Collection<CompleteVideo> completeVideos) {
    Map<Integer, Integer> parents = new HashMap<>();
    if (completeVideos == null) {
      return parents;
    }
    for (CompleteVideo completeVideo : completeVideos) {
      if (completeVideo != null
          && completeVideo.isEpisodeBatch()
          && completeVideo.getSeasonParent() != null)
        parents.put(completeVideo.getId(), completeVideo.getSeasonParent().getId());
    }
    return parents;
  }

  /**
   * Get the certifications that ratings agencies (e.g.&nbsp;MPAA) have given to this video.
   *
   * @return a <code>Observable</code> of different ratings given to this video.
   */
  public Observable<APICertification> getCertificationRatings(CompleteVideo presentationVideo) {
    return Observable.unsafeCreate(
        new GetCertificationRatingsOnSubscribe(presentationVideo, vmsl10NProviderDelegate));
  }

  private record GetCertificationRatingsOnSubscribe(
      CompleteVideo presentationVideo, VMSL10NProviderDelegate vmsl10NProviderDelegate)
      implements Observable.OnSubscribe<APICertification> {

    @Override
    public void call(Subscriber<? super APICertification> subscriber) {
      try {
        List<Certification> certificationList = presentationVideo.getCertificationList();
        if (null != certificationList) {
          for (Certification certification : certificationList) {
            subscriber.onNext(new APICertificationImpl(certification, vmsl10NProviderDelegate));
          }
        }
        subscriber.onCompleted();
      } catch (Exception e) {
        logger.error(
            "Exception while getting certification ratings for videoId={}",
            presentationVideo.getId(),
            e);
        subscriber.onError(e);
      }
    }
  }
}
