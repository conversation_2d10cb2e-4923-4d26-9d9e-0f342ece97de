package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class MembershipUpdatePromoResponse {
  private Boolean success;

  private Boolean fallback;

  public Boolean getSuccess() {
    return success;
  }

  public Boolean getFallback() {
    return fallback;
  }

  public MembershipUpdatePromoResponse setSuccess(Boolean success) {
    this.success = success;
    return this;
  }

  public MembershipUpdatePromoResponse setFallback(Boolean fallback) {
    this.fallback = fallback;
    return this;
  }
}
