package com.netflix.api.service.i18n;

import java.util.List;
import java.util.Map;

public class APILocaleOptionsImpl implements APILocaleOptions {
  private List<String> deviceSupportedLanguages;

  private List<String> deviceSupportedFontGroups;

  private List<String> devicePreferredLanguages;

  private List<String> userPreferredLanguages;

  private Boolean useNRMLanguagePreferences;

  private List<String> supportedUILanguages;

  private Map<String, String> pseudoLocalizationLanguages;

  private String privateUseSubtag;

  public List<String> getDeviceSupportedLanguages() {
    return deviceSupportedLanguages;
  }

  public List<String> getDeviceSupportedFontGroups() {
    return deviceSupportedFontGroups;
  }

  public List<String> getDevicePreferredLanguages() {
    return devicePreferredLanguages;
  }

  public List<String> getUserPreferredLanguages() {
    return userPreferredLanguages;
  }

  public Boolean getUseNRMLanguagePreferences() {
    return useNRMLanguagePreferences;
  }

  public List<String> getSupportedUILanguages() {
    return supportedUILanguages;
  }

  public Map<String, String> getPseudoLocalizationLanguages() {
    return pseudoLocalizationLanguages;
  }

  public String getPrivateUseSubtag() {
    return privateUseSubtag;
  }

  public void setDeviceSupportedLanguages(List<String> deviceSupportedLanguages) {
    this.deviceSupportedLanguages = deviceSupportedLanguages;
  }

  public void setDeviceSupportedFontGroups(List<String> deviceSupportedFontGroups) {
    this.deviceSupportedFontGroups = deviceSupportedFontGroups;
  }

  public void setDevicePreferredLanguages(List<String> devicePreferredLanguages) {
    this.devicePreferredLanguages = devicePreferredLanguages;
  }

  public void setUserPreferredLanguages(List<String> userPreferredLanguages) {
    this.userPreferredLanguages = userPreferredLanguages;
  }

  public void setUseNRMLanguagePreferences(Boolean useNRMLanguagePreferences) {
    this.useNRMLanguagePreferences = useNRMLanguagePreferences;
  }

  public void setSupportedUILanguages(List<String> supportedUILanguages) {
    this.supportedUILanguages = supportedUILanguages;
  }

  public void setPseudoLocalizationLanguages(Map<String, String> pseudoLocalizationLanguages) {
    this.pseudoLocalizationLanguages = pseudoLocalizationLanguages;
  }

  public void setPrivateUseSubtag(String privateUseSubtag) {
    this.privateUseSubtag = privateUseSubtag;
  }
}
