package com.netflix.api.service.vps;

import com.google.common.base.Preconditions;
import com.netflix.vps.protogen.Resource;

public class APIVoiceResourceImpl implements APIVoiceResource {

  private APIVoiceResourceType voiceResourceType;
  private String id;
  private String value;

  public APIVoiceResourceImpl(APIVoiceResourceType type, String id, final String value) {
    this.voiceResourceType = type;
    this.id = id;
    this.value = value;
  }

  public APIVoiceResourceImpl(Resource resource) {
    Preconditions.checkNotNull(resource);
    this.voiceResourceType = APIVoiceResourceType.valueOf(resource.getType().toString());
    this.id = resource.getId();
    this.value = resource.getValue();
  }

  public APIVoiceResourceType getVoiceResourceType() {
    return voiceResourceType;
  }

  public String getId() {
    return id;
  }

  public String getValue() {
    return value;
  }

  public void setVoiceResourceType(APIVoiceResourceType voiceResourceType) {
    this.voiceResourceType = voiceResourceType;
  }

  public void setId(String id) {
    this.id = id;
  }

  public void setValue(String value) {
    this.value = value;
  }
}
