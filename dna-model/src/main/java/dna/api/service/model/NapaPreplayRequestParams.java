package dna.api.service.model;

import com.netflix.nes.section.protogen.SectionKind;
import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaPreplayRequestParams {
  /** Viewable watched by visitor */
  private Integer watchedVideoId;

  /**
   * Track Id pertaining to where play was originated from to allow for Postplay to differentiate
   * responses based on play location
   */
  private Integer trackId;

  /** NES-based sections supported by client */
  private List<SectionKind> supportedSectionKinds;

  /** Preplay Features supported by client */
  private List<NapaPrePlayFeatures> supportedFeatures;

  /** Interactive Device Type */
  private NapaInteractiveDeviceTypes interactiveDeviceType;

  public Integer getWatchedVideoId() {
    return watchedVideoId;
  }

  public Integer getTrackId() {
    return trackId;
  }

  public List<SectionKind> getSupportedSectionKinds() {
    return supportedSectionKinds;
  }

  public List<NapaPrePlayFeatures> getSupportedFeatures() {
    return supportedFeatures;
  }

  public NapaInteractiveDeviceTypes getInteractiveDeviceType() {
    return interactiveDeviceType;
  }

  public NapaPreplayRequestParams setWatchedVideoId(Integer watchedVideoId) {
    this.watchedVideoId = watchedVideoId;
    return this;
  }

  public NapaPreplayRequestParams setTrackId(Integer trackId) {
    this.trackId = trackId;
    return this;
  }

  public NapaPreplayRequestParams setSupportedSectionKinds(
      List<SectionKind> supportedSectionKinds) {
    this.supportedSectionKinds = supportedSectionKinds;
    return this;
  }

  public NapaPreplayRequestParams setSupportedFeatures(
      List<NapaPrePlayFeatures> supportedFeatures) {
    this.supportedFeatures = supportedFeatures;
    return this;
  }

  public NapaPreplayRequestParams setInteractiveDeviceType(
      NapaInteractiveDeviceTypes interactiveDeviceType) {
    this.interactiveDeviceType = interactiveDeviceType;
    return this;
  }
}
