package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PlaylistOperationOptions {
  private Integer trackId;

  /** Optional signature that will be verified prior to invoking the playlist operation */
  private String signature;

  private Map<String, String> metadata;

  /** Optional identifier of the playlist owner to apply this playlist operation on */
  private String profileGUID;

  public Integer getTrackId() {
    return trackId;
  }

  public String getSignature() {
    return signature;
  }

  public Map<String, String> getMetadata() {
    return metadata;
  }

  public String getProfileGUID() {
    return profileGUID;
  }

  public PlaylistOperationOptions setTrackId(Integer trackId) {
    this.trackId = trackId;
    return this;
  }

  public PlaylistOperationOptions setSignature(String signature) {
    this.signature = signature;
    return this;
  }

  public PlaylistOperationOptions setMetadata(Map<String, String> metadata) {
    this.metadata = metadata;
    return this;
  }

  public PlaylistOperationOptions setProfileGUID(String profileGUID) {
    this.profileGUID = profileGUID;
    return this;
  }
}
