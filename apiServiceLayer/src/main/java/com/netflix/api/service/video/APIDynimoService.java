package com.netflix.api.service.video;

import rx.Observable;

/**
 * Service that creates dynamic image derivatives for an image type and returns urls to the created
 * asset.
 *
 * @see APIDynimoArtworkRequest
 */
public interface APIDynimoService {
  /**
   * Create and return the location (url) of a dynamic derivative of the artwork type.
   *
   * <p>Used by clients during development. Typical pattern is for UI developers to create a
   * derivative that fits their UI needs. Once a derivatives meets their requirements, UI team will
   * work with Dynimo and other content platform teams to productize and create the derivative for
   * all content.
   *
   * @param request - Different input parameters needed by Dynimo service to create the asset.
   * @return
   */
  Observable<String> createUrl(APIDynimoArtworkRequest request);
}
