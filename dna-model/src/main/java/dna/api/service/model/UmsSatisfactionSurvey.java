package dna.api.service.model;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.netflix.demograph.graph.Atom;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class UmsSatisfactionSurvey implements GraphData {
  public Long created;

  public String messageName;

  public String messageGuid;

  public String eventGuid;

  public Boolean isRead;

  public String uiPlatform;

  public String templateFormat;

  public Atom<List<String>> capabilityTypes;

  public Atom<Map<String, Object>> templateJSON;

  /**
   * This method is only provided here for debugging and development purposes. Don't base production
   * code on this method - it's an unstable API. Subject to change
   */
  @Deprecated public Atom<Map<String, Object>> __all;

  private HashMap<String, Object> propertyToValue;

  public Long getCreated() {
    return created;
  }

  public String getMessageName() {
    return messageName;
  }

  public String getMessageGuid() {
    return messageGuid;
  }

  public String getEventGuid() {
    return eventGuid;
  }

  public Boolean getIsRead() {
    return isRead;
  }

  public String getUiPlatform() {
    return uiPlatform;
  }

  public String getTemplateFormat() {
    return templateFormat;
  }

  public Atom<List<String>> getCapabilityTypes() {
    return capabilityTypes;
  }

  public Atom<Map<String, Object>> getTemplateJSON() {
    return templateJSON;
  }

  @Deprecated
  public Atom<Map<String, Object>> get__all() {
    return __all;
  }

  public UmsSatisfactionSurvey setCreated(Long created) {
    this.created = created;
    return this;
  }

  public UmsSatisfactionSurvey setMessageName(String messageName) {
    this.messageName = messageName;
    return this;
  }

  public UmsSatisfactionSurvey setMessageGuid(String messageGuid) {
    this.messageGuid = messageGuid;
    return this;
  }

  public UmsSatisfactionSurvey setEventGuid(String eventGuid) {
    this.eventGuid = eventGuid;
    return this;
  }

  public UmsSatisfactionSurvey setIsRead(Boolean isRead) {
    this.isRead = isRead;
    return this;
  }

  public UmsSatisfactionSurvey setUiPlatform(String uiPlatform) {
    this.uiPlatform = uiPlatform;
    return this;
  }

  public UmsSatisfactionSurvey setTemplateFormat(String templateFormat) {
    this.templateFormat = templateFormat;
    return this;
  }

  public UmsSatisfactionSurvey setCapabilityTypes(Atom<List<String>> capabilityTypes) {
    this.capabilityTypes = capabilityTypes;
    return this;
  }

  public UmsSatisfactionSurvey setTemplateJSON(Atom<Map<String, Object>> templateJSON) {
    this.templateJSON = templateJSON;
    return this;
  }

  @Deprecated
  public UmsSatisfactionSurvey set__all(Atom<Map<String, Object>> __all) {
    this.__all = __all;
    return this;
  }

  @JsonAnyGetter
  public HashMap<String, Object> propertyToValue() {
    return this.propertyToValue;
  }

  public Object getProperty(String property) {
    return this.propertyToValue != null ? this.propertyToValue.get(property) : null;
  }

  @JsonAnySetter
  public void setProperty(String key, Object value) {
    if (propertyToValue == null) {
      propertyToValue = new HashMap<>();
    }
    this.propertyToValue.put(key, value);
  }
}
