package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class MembershipPriceConsentInfo {
  private MembershipConsentStatus status;

  private Long planId;

  private String priceTier;

  private Double price;

  private String currency;

  private String priceFormatted;

  private Long consentedDate;

  private Long notificationStartDate;

  private Long extendedGfDate;

  private Long enforcePriceChangeUntilDate;

  private Long autoConsentAfterDate;

  public MembershipConsentStatus getStatus() {
    return status;
  }

  public Long getPlanId() {
    return planId;
  }

  public String getPriceTier() {
    return priceTier;
  }

  public Double getPrice() {
    return price;
  }

  public String getCurrency() {
    return currency;
  }

  public String getPriceFormatted() {
    return priceFormatted;
  }

  public Long getConsentedDate() {
    return consentedDate;
  }

  public Long getNotificationStartDate() {
    return notificationStartDate;
  }

  public Long getExtendedGfDate() {
    return extendedGfDate;
  }

  public Long getEnforcePriceChangeUntilDate() {
    return enforcePriceChangeUntilDate;
  }

  public Long getAutoConsentAfterDate() {
    return autoConsentAfterDate;
  }

  public MembershipPriceConsentInfo setStatus(MembershipConsentStatus status) {
    this.status = status;
    return this;
  }

  public MembershipPriceConsentInfo setPlanId(Long planId) {
    this.planId = planId;
    return this;
  }

  public MembershipPriceConsentInfo setPriceTier(String priceTier) {
    this.priceTier = priceTier;
    return this;
  }

  public MembershipPriceConsentInfo setPrice(Double price) {
    this.price = price;
    return this;
  }

  public MembershipPriceConsentInfo setCurrency(String currency) {
    this.currency = currency;
    return this;
  }

  public MembershipPriceConsentInfo setPriceFormatted(String priceFormatted) {
    this.priceFormatted = priceFormatted;
    return this;
  }

  public MembershipPriceConsentInfo setConsentedDate(Long consentedDate) {
    this.consentedDate = consentedDate;
    return this;
  }

  public MembershipPriceConsentInfo setNotificationStartDate(Long notificationStartDate) {
    this.notificationStartDate = notificationStartDate;
    return this;
  }

  public MembershipPriceConsentInfo setExtendedGfDate(Long extendedGfDate) {
    this.extendedGfDate = extendedGfDate;
    return this;
  }

  public MembershipPriceConsentInfo setEnforcePriceChangeUntilDate(
      Long enforcePriceChangeUntilDate) {
    this.enforcePriceChangeUntilDate = enforcePriceChangeUntilDate;
    return this;
  }

  public MembershipPriceConsentInfo setAutoConsentAfterDate(Long autoConsentAfterDate) {
    this.autoConsentAfterDate = autoConsentAfterDate;
    return this;
  }
}
