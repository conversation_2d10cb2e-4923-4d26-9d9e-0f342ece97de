package com.netflix.api.dependencies.avatar;

import com.netflix.api.dependencies.subscriber.AccountProfileHandler;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.ust.profile.avatar.AvatarParameterMapper;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Single;
import ust.images.v1.Image;
import ust.profile.avatar.v1.AvatarKey;
import ust.profile.avatar.v1.AvatarSpec;

@Component
public class ProfileAvatarHandler {

  private final AccountProfileHandler accountProfileHandler;
  private final ProfileIconFactory profileIconFactory;
  private final USTProfileAvatarAdapter profileAvatarAdapter;

  @Autowired
  public ProfileAvatarHandler(
      AccountProfileHandler accountProfileHandler,
      ProfileIconFactory profileIconFactory,
      USTProfileAvatarAdapter profileAvatarAdapter) {
    this.accountProfileHandler = accountProfileHandler;
    this.profileIconFactory = profileIconFactory;
    this.profileAvatarAdapter = profileAvatarAdapter;
  }

  public Single<List<ProfileAvatar>> getAvailableProfileAvatars(
      int size, List<String> recipePreferences) {
    AccountProfileRemote accountProfile = accountProfileHandler.currentAccountProfile();
    if (accountProfile == null) {
      return Single.just(Collections.emptyList());
    }

    return getUsedAvatarNames(accountProfile)
        .flatMap(
            iconNames -> {
              List<ProfileIcon> iconList =
                  profileIconFactory.getAvailableIconsFromDefaultIconSet(iconNames);

              return getAvatars(
                      iconList.stream().map(ProfileIcon::getIconName).toList(),
                      new AvatarCriteria()
                          .setDimensions(AvatarParameterMapper.sizeToDimentions(size))
                          .setSecure(true)
                          .setRecipePreferences(recipePreferences))
                  .map(
                      imageMap ->
                          iconList.stream()
                              .map(
                                  avatar ->
                                      new ProfileAvatar(
                                          avatar.getIconName(),
                                          imageMap.get(avatar.getIconName()),
                                          true,
                                          avatar.isInDefaultSet()))
                              .toList());
            });
  }

  public Single<SortedMap<Long, String>> getUsedAvatarNames(AccountProfileRemote accountProfile) {
    return accountProfileHandler
        .getProfiles(accountProfile)
        .map(
            profiles ->
                profiles.stream()
                    .collect(
                        Collectors.toMap(
                            AccountProfileRemote::getBoxedProfileId,
                            AccountProfileRemote::getBoxedAvatarImg,
                            (existing, replacement) -> existing,
                            TreeMap::new)));
  }

  public Single<Map<String, Image>> getAvatars(
      Collection<String> avatarNames, AvatarCriteria criteria) {
    if (avatarNames == null || avatarNames.isEmpty() || criteria == null) {
      return Single.error(new IllegalArgumentException("Must provide avatarNames and criteria"));
    }
    Set<AvatarSpec> avatarSpecs =
        avatarNames.stream()
            .map(
                avatarName ->
                    AvatarSpec.newBuilder()
                        .setAvatarKey(AvatarKey.newBuilder().setKey(avatarName))
                        .build())
            .collect(Collectors.toSet());
    return profileAvatarAdapter
        .getAvatars(avatarSpecs, criteria)
        .map(
            avatars ->
                avatars.entrySet().stream()
                    .collect(
                        Collectors.toMap(
                            entry -> entry.getKey().getAvatarKey().getKey(), Entry::getValue)));
  }
}
