package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BundleInfoResponse extends EcomServiceResponse {
  private Boolean canChangePlan;

  private Boolean isOnBundle;

  private String partnerId;

  private String partnerDisplayName;

  private String bundleType;

  public Boolean getCanChangePlan() {
    return canChangePlan;
  }

  public Boolean getIsOnBundle() {
    return isOnBundle;
  }

  public String getPartnerId() {
    return partnerId;
  }

  public String getPartnerDisplayName() {
    return partnerDisplayName;
  }

  public String getBundleType() {
    return bundleType;
  }

  public BundleInfoResponse setCanChangePlan(Boolean canChangePlan) {
    this.canChangePlan = canChangePlan;
    return this;
  }

  public BundleInfoResponse setIsOnBundle(Boolean isOnBundle) {
    this.isOnBundle = isOnBundle;
    return this;
  }

  public BundleInfoResponse setPartnerId(String partnerId) {
    this.partnerId = partnerId;
    return this;
  }

  public BundleInfoResponse setPartnerDisplayName(String partnerDisplayName) {
    this.partnerDisplayName = partnerDisplayName;
    return this;
  }

  public BundleInfoResponse setBundleType(String bundleType) {
    this.bundleType = bundleType;
    return this;
  }
}
