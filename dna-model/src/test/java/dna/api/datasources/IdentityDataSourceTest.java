package dna.api.datasources;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.netflix.api.platform.MockPropertyRepositoryConfig;
import com.netflix.api.platform.identity.CurrentIdentityResult;
import com.netflix.demograph.Context;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.peas.auth.protogen.TokenScope;
import com.netflix.userauthservice2.protogen.CreateAutoLoginTokenReply;
import com.netflix.userauthservice2.protogen.CreateAutoLoginTokenRequest;
import com.netflix.userauthservice2.protogen.PhoneData;
import com.netflix.userauthservice2.protogen.UserAuthenticationServiceGrpc.UserAuthenticationServiceStub;
import com.netflix.userauthservice2.protogen.UserLoginWithPhoneCodeReply;
import com.netflix.userauthservice2.protogen.UserLoginWithPhoneCodeRequest;
import dna.api.service.model.PhoneNumberInfo;
import io.grpc.stub.StreamObserver;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class IdentityDataSourceTest {

  @BeforeAll
  static void beforeClass() {
    MockPropertyRepositoryConfig config = new MockPropertyRepositoryConfig();
    config.init();
  }

  @Test
  void
      autoLoginTokenViaRecoveryCodeAndPhoneNumber_requestsPassportThenAutoLoginToken_returnsAutoLoginToken() {
    var stub = mock(UserAuthenticationServiceStub.class);

    var mockPassport = mock(PassportIdentity.class);
    var passportValue = "some-passport";
    when(mockPassport.getPassportAsString()).thenReturn(passportValue);
    CurrentIdentityResult.setPassport(mockPassport);

    var updatedPassport = "some-updated-passport";
    doAnswer(
            args -> {
              StreamObserver<UserLoginWithPhoneCodeReply> observer = args.getArgument(1);
              var reply =
                  UserLoginWithPhoneCodeReply.newBuilder()
                      .setUpdatedPassport(updatedPassport)
                      .build();
              observer.onNext(reply);
              observer.onCompleted();
              return null;
            })
        .when(stub)
        .userLoginWithPhoneCode(any(), any());

    var token = "some auto login token";
    doAnswer(
            args -> {
              StreamObserver<CreateAutoLoginTokenReply> observer = args.getArgument(1);
              var reply = CreateAutoLoginTokenReply.newBuilder().setAutoLoginToken(token).build();
              observer.onNext(reply);
              observer.onCompleted();
              return null;
            })
        .when(stub)
        .createAutoLoginToken(any(), any());

    var identity = new IdentityDataSource(null, null, stub);
    var dataSource = identity.autoLoginTokenViaRecoveryCodeAndPhoneNumber();

    var ctx = Context.builder().build();
    var phone = new PhoneNumberInfo().setPhoneNumber("4152223333").setCountryCode("+1");
    var tokenRequest = new AutoLoginTokenForPasswordResetRequest("some-recovery-code", phone);
    var future = dataSource.lookup(ctx, tokenRequest).toCompletableFuture();

    var tokenResult = future.join();
    assertEquals(token, tokenResult);

    var data =
        PhoneData.newBuilder()
            .setCountryCode(phone.getCountryCode())
            .setPhoneNumber(phone.getCountryCode() + phone.getPhoneNumber());
    var expectedRequest =
        UserLoginWithPhoneCodeRequest.newBuilder()
            .setIncomingPassport(passportValue)
            .setPhoneCode(tokenRequest.recoveryCode())
            .setPhoneData(data)
            .build();

    verify(stub).userLoginWithPhoneCode(eq(expectedRequest), any());

    verify(stub)
        .createAutoLoginToken(
            eq(
                CreateAutoLoginTokenRequest.newBuilder()
                    .setPassport(updatedPassport)
                    .addScopes(TokenScope.CHANGE_PASSWORD)
                    .build()),
            any());
  }
}
