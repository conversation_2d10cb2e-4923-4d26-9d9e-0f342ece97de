apply plugin: 'com.netflix.api.proto2api'

// Set nonBreaking = false if you want new field collisions to replace old field definitions
// NOTE: protoFile must be the relative path to the target proto file

task trackTextEvidenceTagsEnum(type: proto2api.Task) {
    location "com.netflix.textevidence:text-evidence-proto-definition"

    protoTypes {
        protoFile = "tags.proto"
        protoEnum = "com.netflix.cms.TagsRecipe"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/text-evidence-tags-recipe.graphql"
        graphQLType = "TagsRecipe"
    }
}

task trackPuiBillboardModuleProto(type: proto2api.Task) {
    location "netflix:gps-proto-page-definition"

    protoTypes {
        protoFile = "com/netflix/gpsproto/page/capability/pui_billboard_module.proto"
        protoEnum = "com.netflix.gpsproto.page.capability.PuiBillboardModule"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/pui-gen.graphql"
        graphQLType = "PuiBillboardModule"
    }
}

task trackRequestAttrsApi(type: proto2api.Task) {
    location "com.netflix.request:request-attrs-api"

    protoTypes {
        protoFile = "com/netflix/request/ui_flavor.proto"
        protoEnum = "com.netflix.request.UiFlavor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/request-attrs.graphql"
        graphQLType = "UiFlavor"
    }
}

task trackPlayApiProtos(type: proto2api.Task) {
    location "com.netflix.playapi:playapi-client"

    protoTypes {
        protoFile = "com/netflix/playapi/manifest/response/video_downloadable.proto"
        protoMessage = "com.netflix.playapi.CropParams"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/playapi.graphql"
        graphQLType = "PlaybackVideoCropParams"
        isAtom = false
        nonBreaking = false
    }

    protoTypes {
        protoFile = "com/netflix/playapi/manifest/response/resolution.proto"
        protoMessage = "com.netflix.playapi.Resolution"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/playapi.graphql"
        graphQLType = "PlaybackVideoResolution"
        isAtom = false
        nonBreaking = false
    }

    protoTypes {
        protoFile = "com/netflix/playapi/manifest/response/resolution.proto"
        protoMessage = "com.netflix.playapi.PixelAspectRatio"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/playapi.graphql"
        graphQLType = "PlaybackVideoPixelAspectRatio"
        isAtom = false
        nonBreaking = false
    }

    protoTypes {
        protoFile = "com/netflix/playapi/manifest/response/encoding_profile.proto"
        protoMessage = "com.netflix.playapi.EncodingProfile"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/playapi.graphql"
        graphQLType = "PlaybackVideoEncodingProfile"
        isAtom = false
        nonBreaking = false
    }

    protoTypes {
        protoFile = "manifest/response/video_only_response.proto"
        protoMessage = "com.netflix.playapi.VideoOnlyDownloadable"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/playapi.graphql"
        graphQLType = "VideoOnlyDownloadable"
        isAtom = false
        nonBreaking = false
    }

    protoTypes {
        protoFile = "manifest/response/video_only_response.proto"
        protoMessage = "com.netflix.playapi.VideoOnlyTrack"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/playapi.graphql"
        graphQLType = "VideoOnlyTrack"
        isAtom = false
        nonBreaking = false
    }
}

task trackDnaNesProtos(type: proto2api.Task) {
    location "com.netflix.nes:nes-proto-definition"

    protoTypes {
        protoFile = "nes/instrument/debug_data.proto"
        protoMessage = "com.netflix.nes.instrument.DebugData.DebugEntry"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "DebugEntry"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/instrument/debug_data.proto"
        protoMessage = "com.netflix.nes.instrument.DebugData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesDebugData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/common/attribute/display_text_data.proto"
        protoMessage = "com.netflix.nes.common.attribute.DisplayTextData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesDisplayTextData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/instrument/performance_data.proto"
        protoEnum = "com.netflix.nes.instrument.DurationUnit"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesDurationUnit"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/instrument/performance_data.proto"
        protoMessage = "com.netflix.nes.instrument.Phase"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPhase"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/instrument/performance_data.proto"
        protoMessage = "com.netflix.nes.instrument.PerformanceData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPerformanceData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/action/action_kind.proto"
        protoEnum = "com.netflix.nes.action.ActionKind"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesActionKind"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/action/category/action_category.proto"
        protoEnum = "com.netflix.nes.action.category.ActionCategory"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesActionCategory"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/entity_kind.proto"
        protoEnum = "com.netflix.nes.entity.EntityKind"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesEntityKind"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/entity_id.proto"
        protoMessage = "com.netflix.nes.entity.EntityId"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesEntityId"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/attribute/entity_quantity.proto"
        protoMessage = "com.netflix.nes.section.descriptor.attribute.EntityQuantity"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesEntityQuantity"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/action/add_to_my_list_action_data.proto"
        protoMessage = "com.netflix.nes.action.AddToMyListActionData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesAddToMyListActionData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/action/more_info_action_data.proto"
        protoMessage = "com.netflix.nes.action.MoreInfoActionData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesMoreInfoActionData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/action/more_info_action_data.proto"
        protoMessage = "com.netflix.nes.action.MoreInfoActionData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesMoreInfoActionData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/action/more_info_action_data.proto"
        protoMessage = "com.netflix.nes.action.MoreInfoActionData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesMoreInfoActionData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "google/protobuf/timestamp.proto"
        protoMessage = "google.protobuf.Timestamp"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "ProtoTimestamp"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/attribute/selection_context.proto"
        protoMessage = "com.netflix.nes.entity.attribute.SelectionContext"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSelectionContext"

        ignoreFields = [ "preferredLocale", "launchDate" ]
    }

    protoTypes {
        protoFile = "nes/section/section_kind.proto"
        protoEnum = "com.netflix.nes.section.SectionKind"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSectionKind"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/billboard_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.BillboardSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesBillboardSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/entity_category.proto"
        protoEnum = "com.netflix.nes.entity.EntityCategory"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesEntityCategory"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/common/attribute/postplay/recommendation_tactic.proto"
        protoEnum = "com.netflix.nes.common.attribute.postplay.RecommendationTactic"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesRecommendationTactic"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/common/attribute/postplay/reporting_data.proto"
        protoEnum = "com.netflix.nes.common.attribute.postplay.ReportingData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesReportingData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/common/attribute/postplay/recommendation_source.proto"
        protoEnum = "com.netflix.nes.common.attribute.postplay.RecommendationSource"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesRecommendationSource"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/common/attribute/postplay/recommendation_channel.proto"
        protoEnum = "com.netflix.nes.common.attribute.postplay.RecommendationChannel"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesRecommendationChannel"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/common/attribute/tracking_data.proto"
        protoMessage = "com.netflix.nes.common.attribute.TrackingData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesTrackingData"
        ignoreFields = [ "supplemental_track_ids" ]
    }

    protoTypes {
        protoFile = "nes/page/descriptor/recently_added_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.NesRecentlyAddedPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesRecentlyAddedPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/recently_watched_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.RecentlyWatchedPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesRecentlyWatchedPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/common/attribute/seamless_play_data.proto"
        protoMessage = "com.netflix.nes.common.attribute.SeamlessPlayData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSeamlessPlayData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/action/play_action_data.proto"
        protoMessage = "com.netflix.nes.action.PlayActionData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPlayActionData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/action/more_info_action_data.proto"
        protoMessage = "com.netflix.nes.action.MoreInfoActionData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesMoreInfoActionData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/action/action.proto"
        protoMessage = "com.netflix.nes.action.Action"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesAction"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/action/action.proto"
        protoMessage = "com.netflix.nes.action.Action"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesAction"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/action/action.proto"
        protoMessage = "com.netflix.nes.action.Action"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesAction"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/common_entity_data.proto"
        protoMessage = "com.netflix.nes.entity.CommonEntityData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCommonEntityData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/layout/attribute/entity_height.proto"
        protoEnum = "com.netflix.nes.layout.attribute.EntityHeight"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesEntityHeight"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/layout/attribute/entity_width.proto"
        protoEnum = "com.netflix.nes.layout.attribute.EntityWidth"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesEntityWidth"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/layout/attribute/entity_information_density_type.proto"
        protoEnum = "com.netflix.nes.layout.attribute.EntityInformationDensityType"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesEntityInformationDensityType"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/layout/attribute/badge_type.proto"
        protoEnum = "com.netflix.nes.layout.attribute.BadgeType"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesBadgeType"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/layout/attribute/card_size.proto"
        protoEnum = "com.netflix.nes.layout.attribute.CardSize"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCardSize"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/layout/card_layout.proto"
        protoMessage = "com.netflix.nes.layout.CardLayout"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCardLayout"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/layout/grid_layout.proto"
        protoMessage = "com.netflix.nes.layout.GridLayout"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesGridLayout"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/layout/list_layout.proto"
        protoMessage = "com.netflix.nes.layout.ListLayout"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesListLayout"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/common/attribute/display_text_data.proto"
        protoMessage = "com.netflix.nes.layout.common.attribute.DisplayTextData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesDisplayTextData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/session/session_info.proto"
        protoMessage = "com.netflix.nes.session.SessionInfo"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSessionInfo"

        ignoreFields = [ "refresh_interval", "expiry" ]
    }

    protoTypes {
        protoFile = "nes/common/attribute/postplay/postplay_reporting_data.proto"
        protoMessage = "com.netflix.nes.common.attribute.postplay.PostplayReportingData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPostplayReportingData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/common/attribute/impression_data.proto"
        protoMessage = "com.netflix.nes.common.attribute.ImpressionData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesImpressionData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/common/attribute/next_season_supplemental_data.proto"
        protoMessage = "com.netflix.nes.common.attribute.NextSeasonSupplementalData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNextSeasonSupplementalData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/common/attribute/postplay/postplay_promotional_data.proto"
        protoMessage = "com.netflix.nes.common.attribute.postplay.PostplayPromotionalData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPostplayPromotionalData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/common/postplay/postplay_choicepoint_data.proto"
        protoMessage = "com.netflix.nes.entity.common.postplay.PostplayChoicepointData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPostplayChoicepointData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/common/postplay/postplay_entity_promotional_data.proto"
        protoMessage = "com.netflix.nes.entity.common.postplay.PostplayEntityPromotionalData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPostplayEntityPromotionalData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/common/postplay/common_postplay_entity_data.proto"
        protoMessage = "com.netflix.nes.entity.common.postplay.CommonPostplayEntityData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCommonPostplayEntityData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/common/postplay/common_postplay_section_data.proto"
        protoMessage = "com.netflix.nes.section.common.postplay.CommonPostplaySectionData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCommonPostplaySectionData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/recently_added_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.RecentlyAddedPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesRecentlyAddedPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/genre.proto"
        protoMessage = "com.netflix.nes.entity.Genre"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesGenre"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/attribute/common_section_descriptor_attributes.proto"
        protoMessage = "com.netflix.nes.section.descriptor.attribute.CommonSectionDescriptorAttributes"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCommonSectionDescriptorAttributes"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/category_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.CategorySectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCategorySectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/category_video_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.CategoryVideoSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCategoryVideoSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/character_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.CharacterSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCharacterSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/character_video_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.CharacterVideoSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCharacterVideoSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/content_preview_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.ContentPreviewSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesContentPreviewSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/content_preview_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.ContentPreviewSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesContentPreviewSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/continue_watching_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.ContinueWatchingSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesContinueWatchingSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/downloads_for_you_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.DownloadsForYouSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesDownloadsForYouSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/downloads_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.DownloadsSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesDownloadsSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/flashback_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.FlashbackSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesFlashbackSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/hidden_gems_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.HiddenGemsSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesHiddenGemsSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/mobile_previews_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.MobilePreviewsSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesMobilePreviewsSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/netflix_originals_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.NetflixOriginalsSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNetflixOriginalsSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/netflix_originals_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.NetflixOriginalsSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNetflixOriginalsSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/new_contents_section_descriptor.proto"
        protoEnum = "com.netflix.nes.section.descriptor.DurationName"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesDurationName"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/personalized_category_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.PersonalizedCategorySectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPersonalizedCategorySectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/new_releases_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.NewReleasesSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNewReleasesSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/new_this_week_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.NewThisWeekSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNewThisWeekSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/oscars_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.OscarsSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesOscarsSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/personalized_category_video_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.PersonalizedCategoryVideoSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPersonalizedCategoryVideoSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/playlist_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.PlaylistSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPlaylistSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/popular_episodes_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.PopularEpisodesSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPopularEpisodesSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/popular_titles_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.PopularTitlesSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPopularTitlesSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/predicted_thumbs_up_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.PredictedThumbsUpSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPredictedThumbsUpSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/profile_icons_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.ProfileIconsSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesProfileIconsSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/promotions_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.PromotionsSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPromotionsSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/random_episodes_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.RandomEpisodesSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesRandomEpisodesSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/recently_added_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.RecentlyAddedSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesRecentlyAddedSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/recently_aired_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.RecentlyAiredSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesRecentlyAiredSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/recently_launched_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.RecentlyLaunchedSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesRecentlyLaunchedSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/recently_watched_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.RecentlyWatchedSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesRecentlyWatchedSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/screensaver_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.ScreensaverSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesScreensaverSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/attribute/sims_purpose.proto"
        protoEnum = "com.netflix.nes.section.descriptor.attribute.SimsPurpose"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSimsPurpose"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/catalog_video.proto"
        protoMessage = "com.netflix.nes.entity.CatalogVideo"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCatalogVideo"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/title_group.proto"
        protoMessage = "com.netflix.nes.entity.TitleGroup"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesTitleGroup"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/assistive_audio_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.AssistiveAudioPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesAssistiveAudioPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/clips_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.ClipsPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesClipsPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/coming_soon_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.ComingSoonPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesComingSoonPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/downloads_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.DownloadsPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesDownloadsPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/home_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.HomePageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesHomePageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/mobile_collections_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.MobileCollectionsPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesMobileCollectionsPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/new_releases_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.NewReleasesPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNewReleasesPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/partner_preview_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.PartnerPreviewPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPartnerPreviewPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/playlist_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.PlaylistPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPlaylistPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/preapp_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.PreappPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPreappPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/video_messaging_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.VideoMessagingPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesVideoMessagingPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/navigation_hierarchy.proto"
        protoMessage = "com.netflix.nes.entity.NavigationHierarchy"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNavigationHierarchy"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/genre_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.GenrePageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesGenrePageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/turbo_navigation_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.TurboNavigationPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesTurboNavigationPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/layout/carousel_layout.proto"
        protoMessage = "com.netflix.nes.layout.CarouselLayout"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCarouselLayout"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/layout/common_layout_data.proto"
        protoMessage = "com.netflix.nes.layout.CommonLayoutData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCommonLayoutData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/layout/layout.proto"
        protoMessage = "com.netflix.nes.layout.Layout"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesLayout"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/attribute/pivot_type.proto"
        protoEnum = "com.netflix.nes.page.descriptor.attribute.PivotType"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPivotType"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/attribute/video_message_qualifier.proto"
        protoMessage = "com.netflix.nes.page.descriptor.attribute.VideoMessageQualifier"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesVideoMessageQualifier"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/video_messaging_page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.VideoMessagingPageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesVideoMessagingPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.PageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/descriptor/page_descriptor.proto"
        protoMessage = "com.netflix.nes.page.descriptor.PageDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPageDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/catalog_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.CatalogSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCatalogSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/category_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.NesCategorySectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCategorySectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/category_cravers_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.CategoryCraversSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCategoryCraversSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/collection.proto"
        protoMessage = "com.netflix.nes.entity.Collection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCollection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/clips_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.ClipsSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesClipsSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/coming_soon_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.ComingSoonSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesComingSoonSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/evergreen_collection_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.EvergreenCollectionSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesEvergreenCollectionSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/genre_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.GenreSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesGenreSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/most_watched_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.MostWatchedSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesMostWatchedSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/most_watched_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.MostWatchedSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesMostWatchedSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/new_contents_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.NewContentsDuration"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNewContentsDuration"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/netflix_originals_genre_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.NetflixOriginalsGenreSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNetflixOriginalsGenreSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/new_contents_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.NewContentsSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNewContentsSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/originals_collection_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.OriginalsCollectionSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesOriginalsCollectionSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/category_cravers_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.CategoryCraversSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCategoryCraversSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/coming_soon_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.ComingSoonSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesComingSoonSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/short_form_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.ShortFormSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesShortFormSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/attribute/content_time_window.proto"
        protoMessage = "com.netflix.nes.section.descriptor.attribute.ContentTimeWindow"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesContentTimeWindow"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.SectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/external_catalog_video.proto"
        protoMessage = "com.netflix.nes.entity.ExternalCatalogVideo"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesExternalCatalogVideo"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/section_category.proto"
        protoMessage = "com.netflix.nes.entity.SectionCategory"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSectionCategory"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/show_as_a_row_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.ShowAsARowSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesShowAsARowSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/sims_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.SimsSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSimsSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/title_group_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.TitleGroupSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesTitleGroupSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/trending_now_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.TitleGroupSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesTitleGroupSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/top_n_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.TopNSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesTopNSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/ultra_hd_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.UltraHdSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesUltraHdSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/watchathon_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.WatchathonSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesWatchathonSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/watch_again_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.WatchAgainSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesWatchAgainSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/watch_now_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.WatchNowSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesWatchNowSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/windowed_coming_soon_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.WindowedComingSoonSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesWindowedComingSoonSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/windowed_new_releases_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.WindowedNewReleasesSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesWindowedNewReleasesSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/windowed_new_releases_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.WindowedNewReleasesSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesWindowedNewReleasesSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/netflix_recommendations_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.NetflixRecommendationsSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNetflixRecommendationsSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/kids_favorites_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.KidsFavoritesSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesKidsFavoritesSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/originals_background_autoplay_trailer_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.OriginalsBackgroundAutoplayTrailerSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesOriginalsBackgroundAutoplayTrailerSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/two_up_choicepoint_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.TwoUpChoicepointSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesTwoUpChoicepointSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/hardwire_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.HardwireSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesHardwireSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/preview3_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.Preview3SectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPreview3SectionDescriptor"
        nonBreaking = false
    }


    protoTypes {
        protoFile = "nes/section/descriptor/next_episode_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.NextEpisodeSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNextEpisodeSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/next_episode_seamless_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.NextEpisodeSeamlessSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNextEpisodeSeamlessSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/character.proto"
        protoMessage = "com.netflix.nes.entity.Character"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCharacter"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/curated_collection.proto"
        protoMessage = "com.netflix.nes.entity.CuratedCollection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCuratedCollection"
        nonBreaking = false
    }


    protoTypes {
        protoFile = "nes/entity/episode.proto"
        protoMessage = "com.netflix.nes.entity.Episode"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesEpisode"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/movie.proto"
        protoMessage = "com.netflix.nes.entity.Movie"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesMovie"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/named_collection.proto"
        protoMessage = "com.netflix.nes.entity.NamedCollection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesNamedCollection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/person.proto"
        protoMessage = "com.netflix.nes.entity.Person"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPerson"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/season.proto"
        protoMessage = "com.netflix.nes.entity.Season"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSeason"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/show.proto"
        protoMessage = "com.netflix.nes.entity.Show"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesShow"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/supplemental_video.proto"
        protoMessage = "com.netflix.nes.entity.SupplementalVideo"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSupplementalVideo"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/page_category.proto"
        protoMessage = "com.netflix.nes.entity.PageCategory"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPageCategory"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/next_episode_seamless_section.proto"
        protoMessage = "com.netflix.nes.section.PostplaySeamlessData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPostplaySeamlessData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/trending_now_section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.TrendingNowSectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesTrendingNowSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/descriptor/section_descriptor.proto"
        protoMessage = "com.netflix.nes.section.descriptor.SectionDescriptor"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSectionDescriptor"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/entity/entity.proto"
        protoMessage = "com.netflix.nes.entity.Entity"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesEntity"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/common_section_data.proto"
        protoMessage = "com.netflix.nes.section.CommonSectionData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCommonSectionData"

        ignoreFields = ["performance_data"]
    }

    protoTypes {
        protoFile = "nes/section/preview3_section.proto"
        protoMessage = "com.netflix.nes.section.PostplayPreview3Section"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPostplayPreview3Section"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/two_up_choicepoint_section.proto"
        protoMessage = "com.netflix.nes.section.PostplayTwoUpChoicepointSection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPostplayTwoUpChoicepointSection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/originals_background_autoplay_trailer_section.proto"
        protoMessage = "com.netflix.nes.section.PostplayOriginalsBackgroundAutoplayTrailerSection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPostplayOriginalsBackgroundAutoplayTrailerSection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/hardwire_section.proto"
        protoMessage = "com.netflix.nes.section.PostplayHardwireSection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPostplayHardwireSection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/supplemental_video_section.proto"
        protoMessage = "com.netflix.nes.section.SupplementalVideoSection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSupplementalVideoSection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/playlist_section.proto"
        protoEnum = "com.netflix.nes.section.SortType"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSortType"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/playlist_section.proto"
        protoMessage = "com.netflix.nes.section.PlaylistSection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPlaylistSection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/next_episode_section.proto"
        protoEnum = "com.netflix.nes.section.PostplaySeamlessData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPostplaySeamlessData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/next_episode_section.proto"
        protoMessage = "com.netflix.nes.section.PostplayNextEpisodeSection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPostplayNextEpisodeSection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/entity_section.proto"
        protoMessage = "com.netflix.nes.section.EntitySection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesEntitySection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/entity_name_section.proto"
        protoMessage = "com.netflix.nes.section.EntityNameSection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesEntityNameSection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/category_cravers_section.proto"
        protoMessage = "com.netflix.nes.section.CategoryCraversSection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCategoryCraversSection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/billboard_section.proto"
        protoMessage = "com.netflix.nes.section.BillboardSection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesBillboardSection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/common_section_data.proto"
        protoMessage = "com.netflix.nes.section.CommonSectionData"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesCommonSectionData"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/next_episode_seamless_section.proto"
        protoMessage = "com.netflix.nes.section.PostplayNextEpisodeSeamlessSection"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPostplayNextEpisodeSeamlessSection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/section/section.proto"
        protoMessage = "com.netflix.nes.section.Section"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesSection"
        nonBreaking = false
    }

    protoTypes {
        protoFile = "nes/page/page.proto"
        protoMessage = "com.netflix.nes.page.Page"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/nes_gen.graphql"
        graphQLType = "NesPage"
        nonBreaking = false
    }
}

task trackDnaTextEvidenceProtos(type: proto2api.Task) {
    location "com.netflix.textevidence:text-evidence-proto-definition"

    protoTypes {
        protoFile = "text-messages.proto"
        protoEnum = "com.netflix.textevidence.Classification"

        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/cms.graphql"
        graphQLType = "TextMessageClassification"
    }
}


task trackDnaAuthTypes(type: proto2api.Task) {
    location "com.netflix.peas:auth-token-scopes"

    protoTypes {
        protoFile = "com/netflix/peas/auth/token_scopes.proto"
        protoEnum = "com.netflix.peas.auth.TokenScope"
        graphQLFile = project(':dna-router').getProjectDir().getAbsolutePath() + "/src/main/resources/auth.graphql"
        graphQLType = "AuthTokenScope"
        nonBreaking = false
    }
}

task runUstProto2Api {
    dependsOn trackDnaNesProtos
    dependsOn trackDnaTextEvidenceProtos
}
