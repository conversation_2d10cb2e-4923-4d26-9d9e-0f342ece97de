package dna.api.datasources;

import static com.netflix.api.platform.util.CompletionStageAdapter.from;
import static java.util.concurrent.CompletableFuture.completedFuture;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.netflix.api.facade.service.VideoLookup;
import com.netflix.api.facade.service.VideoLookupUtils;
import com.netflix.api.facade.service.collection.CollectionData;
import com.netflix.api.facade.service.collection.CollectionLookup;
import com.netflix.api.platform.context.Contexts;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.batch.USTListAdapter;
import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserInternal;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.demograph.datasource.AsyncDataSource;
import com.netflix.demograph.datasource.batch.BatchDataSource;
import com.netflix.map.annotation.MapAnnotationConstants;
import com.netflix.map.annotation.MapAnnotationConstants.ListContexts;
import com.netflix.map.datatypes.MapItem;
import com.netflix.map.datatypes.MapList;
import com.netflix.map.datatypes.MapResponse;
import com.netflix.type.ISOCountry;
import com.netflix.type.NFCountry;
import com.netflix.type.Video;
import com.netflix.videometadata.type.CompleteVideo;
import dna.api.ApiRequestUtil;
import dna.api.datasources.utils.DataSources;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Single;
import ust.common.v1.VideoType;

@Component
public class CharacterDataSource {
  private static final Logger logger = LoggerFactory.getLogger(CharacterDataSource.class);

  // datasources (must be singletons for demograph implicit caching to work)
  private final AsyncDataSource<Integer, List<Long>> allCharacters;
  private final BatchDataSource<Long, CollectionData> charactersById;
  private final AsyncDataSource<Long, GalleryResponse> characterGallery;
  private final BatchDataSource<Long, GalleryResponse> characterGalleryBatch;
  private final AsyncDataSource<SuggestedVideosRequest, List<Integer>> suggestedVideos;
  private final BatchDataSource<Long, Integer> characterEpisodeCount;
  private final BatchDataSource<Long, Integer> characterMovieCount;

  @Autowired
  CharacterDataSource(
      CollectionLookup collectionLookup,
      USTVideoMetadataAdapter videoMetadataAdapter,
      VideoLookup videoLookup,
      USTListAdapter listAdapter) {
    // init datasources
    allCharacters = _allCharacters(collectionLookup, listAdapter);
    charactersById = _charactersById(collectionLookup);
    characterGallery = _characterGallery(videoLookup, listAdapter);
    characterGalleryBatch = _characterGalleryBatch(videoLookup, listAdapter);
    suggestedVideos = _suggestedVideos(listAdapter);
    characterMovieCount = _characterMovieCount(videoMetadataAdapter);
    characterEpisodeCount = _characterEpisodeCount(videoMetadataAdapter);
  }

  // datasources public accessors to support mocking / testing

  public AsyncDataSource<Integer, List<Long>> allCharacters() {
    return allCharacters;
  }

  public BatchDataSource<Long, CollectionData> charactersById() {
    return charactersById;
  }

  public AsyncDataSource<Long, GalleryResponse> characterGallery() {
    return characterGallery;
  }

  public BatchDataSource<Long, GalleryResponse> characterGalleryBatch() {
    return characterGalleryBatch;
  }

  public BatchDataSource<Long, Integer> characterEpisodeCount() {
    return characterEpisodeCount;
  }

  public BatchDataSource<Long, Integer> characterMovieCount() {
    return characterMovieCount;
  }

  public AsyncDataSource<SuggestedVideosRequest, List<Integer>> suggestedVideos() {
    return suggestedVideos;
  }

  // datasources initializers

  private AsyncDataSource<Integer, List<Long>> _allCharacters(
      CollectionLookup collectionLookup, USTListAdapter listAdapter) {
    return new AsyncDataSource<>(
        (context, maxResults) ->
            from(
                getCharacterIds(maxResults, listAdapter, ApiRequestUtil.apiNextUser(context))
                    .flatMap(
                        characterIds -> {
                          if (characterIds.isEmpty()) {
                            return Single.just(List.of());
                          }
                          // get character metadata from turbo
                          return collectionLookup
                              .getCollections(characterIds)
                              .map(
                                  characterMap ->
                                      // Preserve the order of IDs we received from GPS
                                      characterIds.stream()
                                          .map(characterMap::get)
                                          .filter(Objects::nonNull)
                                          .filter(Optional::isPresent)
                                          .map(Optional::get)
                                          .map(CollectionData::getId)
                                          .distinct()
                                          .toList());
                        })));
  }

  private Single<List<Long>> getCharacterIds(
      Integer maxResults, USTListAdapter listAdapter, APIUser user) {
    Long customerId = Contexts.customerId().orElse(0L);

    Map<String, Object> annotations = getCharacterListRequestRequest(user, customerId, maxResults);
    annotations.put(MapAnnotationConstants.LISTCONTEXT, "charactersGallery");
    return listAdapter
        .getGallery(annotations)
        .map(CharacterDataSource::toCharacterIdList)
        .onErrorReturn(
            t -> {
              logger.error("Error fetching characters list from MAP", t);
              return List.of();
            });
  }

  private Map<String, Object> getCharacterListRequestRequest(
      APIUser user, Long customerId, Integer maxResults) {
    final Map<String, Object> annotations =
        USTListAdapter.getAccountProfileInfoToAnnotations(
            APIUserUtil.getAccountProfileRemote(user));
    annotations.put("country", RequestContextWrapper.get().getCountry().getId());
    annotations.put("visitor", customerId);
    annotations.put("esn", "API_APP_ID_null");
    annotations.put("listMax", maxResults == null || maxResults == 0 ? 50 : maxResults);
    return annotations;
  }

  private static List<Long> toCharacterIdList(MapResponse mapResponse) {
    return mapResponse.getAllLists().stream()
        .filter(CharacterDataSource::isCharacterList)
        .flatMap(mapList -> ((List<MapItem<?>>) mapList.getItem()).stream())
        .filter(mapItem -> mapItem.getItem() instanceof Long)
        .map(mapItem -> (MapItem<Long>) mapItem)
        .map(MapItem::getItem)
        .toList();
  }

  private static boolean isCharacterList(MapList<?> mapList) {
    String listType = mapList.getAnnotation(MapAnnotationConstants.LISTTYPE).toString();
    String itemType = mapList.getAnnotation(MapAnnotationConstants.ITEMTYPE).toString();
    return "flat".equals(listType) && ("character".equals(itemType));
  }

  private BatchDataSource<Long, Integer> _characterEpisodeCount(
      USTVideoMetadataAdapter videoMetadataAdapter) {
    return DataSources.of(
        "characterEpisodeCount",
        (context, characterIds) ->
            getVideoCount(characterIds, videoMetadataAdapter, VideoType.Type.TYPE_EPISODE));
  }

  private BatchDataSource<Long, Integer> _characterMovieCount(
      USTVideoMetadataAdapter videoMetadataAdapter) {
    return DataSources.of(
        "characterMovieCount",
        (context, characterIds) ->
            getVideoCount(characterIds, videoMetadataAdapter, VideoType.Type.TYPE_MOVIE));
  }

  private static CompletionStage<Map<Long, Integer>> getVideoCount(
      Collection<Long> characterIds,
      USTVideoMetadataAdapter videoMetadataAdapter,
      VideoType.Type type) {
    if (characterIds == null) {
      return completedFuture(Map.of());
    }
    return videoMetadataAdapter.getCharacterVideoCount(
        characterIds, VideoType.newBuilder().setType(type).build());
  }

  private BatchDataSource<Long, CollectionData> _charactersById(CollectionLookup collectionLookup) {
    return DataSources.of(
        "batchedCharactersById",
        (context, characterIds) ->
            // get character metadata from turbo
            from(collectionLookup.getCollections(characterIds))
                .thenApply(
                    map ->
                        map.entrySet().stream()
                            .filter(entry -> entry.getValue().isPresent())
                            .collect(
                                Collectors.toMap(
                                    Map.Entry::getKey, entry -> entry.getValue().get()))));
  }

  private AsyncDataSource<Long, GalleryResponse> _characterGallery(
      VideoLookup videoLookup, USTListAdapter listAdapter) {
    return new AsyncDataSource<>(
        (context, characterId) ->
            from(
                listAdapter
                    .getGallery(buildGalleryRequest(Set.of(characterId)))
                    .map(
                        mapResponse -> {
                          Preconditions.checkArgument(mapResponse.size() > 0);
                          return mapResponse.getList(0);
                        })
                    .map(list -> parseMapGallery(videoLookup, list))));
  }

  private BatchDataSource<Long, GalleryResponse> _characterGalleryBatch(
      VideoLookup videoLookup, USTListAdapter listAdapter) {
    return DataSources.of(
        "characterGallery",
        (context, characterIds) -> {
          // This path is not really supported yet by GPS
          Map<String, Object> _annotations = buildGalleryRequest(characterIds);
          return from(
              listAdapter
                  .getGallery(_annotations)
                  .map(
                      mapResponse ->
                          mapResponse.getAllLists().stream()
                              .filter(CharacterDataSource::containsCharacterId)
                              .collect(
                                  Collectors.toMap(
                                      CharacterDataSource::characterId,
                                      list -> parseMapGallery(videoLookup, list)))));
        });
  }

  private static Map<String, Object> buildGalleryRequest(Set<Long> characterIds) {
    APIRequest request = APIRequest.getCurrentRequest();
    APIUserInternal user = ((APIUserInternal) request.getUser());

    Map<String, Object> annotations = Maps.newHashMap();
    annotations.putAll(
        USTListAdapter.getAccountProfileInfoToAnnotations(
            APIUserUtil.getAccountProfileRemote(user)));
    annotations.put("visitor", user.getCustomerId());
    annotations.put("country", request.getRequestContext().getCountry().getId());
    annotations.put("locale", request.getRequestContext().getLocale().getId());
    annotations.put("esn", request.getRequestContext().getESN());
    annotations.put("galleryOrder", "pvr");
    annotations.put("listContext", MapAnnotationConstants.ListContexts.character.name());

    // If more than one, then execute it as a batch
    // otherwise execute as a single request
    if (characterIds.size() > 1) {
      annotations.put("bulkRequest", new ArrayList<>(characterIds));
    } else {
      annotations.put(
          "turboCollectionId",
          characterIds.stream()
              .findFirst()
              .orElseThrow(() -> new IllegalArgumentException("characterId is required")));
    }
    return annotations;
  }

  private static boolean containsCharacterId(MapList<?> mapList) {
    return mapList.hasAnnotation(MapAnnotationConstants.CHARACTERIDV2);
  }

  private static Long characterId(MapList<?> mapList) {
    return mapList.getAnnotation(MapAnnotationConstants.CHARACTERIDV2, Long.class);
  }

  private static GalleryResponse parseMapGallery(VideoLookup videoLookup, MapList<?> mapList) {
    GalleryResponse response = defaultGalleryResponse();

    if ("flat".equals(mapList.getAnnotation(MapAnnotationConstants.LISTTYPE))) {
      // assume that MAP returned only top nodes; otherwise we have to look them up ourselves!!
      List<Integer> videoIds = new ArrayList<>(mapList.size());
      for (int i = 0; i < mapList.size(); i++) {
        videoIds.add(((Video) mapList.getMapItem(i).getItem()).getId());
      }

      // TODO have filed GPS-4867 to ask for cmd to return top nodes directly
      List<Integer> topNodeIds = getTopNodes(videoLookup, videoIds);
      response.setVideos(topNodeIds);
      if (mapList.getAnnotation(MapAnnotationConstants.TRACKID) != null) {
        response.setTrackId((Integer) mapList.getAnnotation(MapAnnotationConstants.TRACKID));
      }
    }
    return response;
  }

  private static GalleryResponse defaultGalleryResponse() {
    GalleryResponse response = new GalleryResponse();
    response.setVideos(List.of());
    response.setTrackId(-1);
    return response;
  }

  private static List<Integer> getTopNodes(VideoLookup videoLookup, List<Integer> videoIds) {
    List<Integer> topNodes = new ArrayList<>(videoIds.size());
    Set<Integer> topNodeSet = new HashSet<>();

    Collection<CompleteVideo> videos = VideoLookupUtils.convert(videoLookup.getVideos(videoIds));
    for (CompleteVideo completeVideo : videos) {
      if (completeVideo != null) {
        Video topNode = completeVideo.getTopNode();
        if (topNode != null && topNode.getId() != null && !topNodeSet.contains(topNode.getId())) {
          topNodeSet.add(topNode.getId());
          topNodes.add(topNode.getId());
        }
      }
    }
    return topNodes;
  }

  private AsyncDataSource<SuggestedVideosRequest, List<Integer>> _suggestedVideos(
      USTListAdapter listAdapter) {

    return new AsyncDataSource<>(
        (context, request) ->
            from(
                listAdapter
                    .getCharacterData(buildCharacterDataRequest(request))
                    .map(
                        mapResponse -> {
                          for (MapList<?> list : mapResponse.getAllLists()) {
                            String foundListContext =
                                (String) list.getAnnotation(MapAnnotationConstants.LISTCONTEXT);
                            if (foundListContext != null
                                && foundListContext.equals(ListContexts.continueWatching.name())) {
                              return list;
                            }
                          }
                          return null;
                        })
                    .map(CharacterDataSource::parseMapList)));
  }

  private static Map<String, Object> buildCharacterDataRequest(
      SuggestedVideosRequest suggestedVideosRequest) {
    APIRequest request = APIRequest.getCurrentRequest();
    APIUserInternal user = (APIUserInternal) request.getUser();
    ISOCountry country = NFCountry.findInstance(request.getRequestContext().getCountry().getId());

    final Map<String, Object> mapAnnotations =
        USTListAdapter.getAccountProfileInfoToAnnotations(
            APIUserUtil.getAccountProfileRemote(user));
    mapAnnotations.put("country", country.getId());
    mapAnnotations.put("esn", request.getRequestContext().getESN());
    mapAnnotations.put("turboCollectionId", suggestedVideosRequest.characterId());
    mapAnnotations.put("visitor", user.getCustomerId());
    mapAnnotations.put("recentlyWatchedUseEpisodes", true);
    mapAnnotations.put(
        "characterDataCWIncomplete", suggestedVideosRequest.continueWatchingIncludeIncomplete());
    mapAnnotations.put("maxDaysLookback", 30);
    return mapAnnotations;
  }

  private static List<Integer> parseMapList(MapList<?> mapList) {
    if (mapList == null) {
      return List.of();
    }
    List<Integer> videoIds = new ArrayList<>(mapList.size());
    for (int i = 0; i < mapList.size(); i++) {
      videoIds.add(((Video) mapList.getMapItem(i).getItem()).getId());
    }
    return videoIds;
  }
}
