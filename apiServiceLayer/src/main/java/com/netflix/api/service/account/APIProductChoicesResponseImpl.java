package com.netflix.api.service.account;

import com.netflix.memberprice.protogen.GetProductChoicesResponse;
import com.netflix.memberprice.protogen.ProductChoice;
import java.util.List;

public class APIProductChoicesResponseImpl implements APIProductChoicesResponse {
  private final GetProductChoicesResponse getProductChoicesResponse;

  APIProductChoicesResponseImpl(GetProductChoicesResponse getProductChoicesResponse) {
    this.getProductChoicesResponse = getProductChoicesResponse;
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.customer_id"
   */
  @Override
  public Long getCustomerId() {
    return this.getProductChoicesResponse.getCustomerId();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.choices"
   */
  @Override
  public List<APIProductChoice> getChoices() {
    List<ProductChoice> list = this.getProductChoicesResponse.getChoicesList();
    if (list == null) {
      return null;
    }
    return list.stream().map(APIProductChoiceImpl::new).map(APIProductChoice.class::cast).toList();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.next_billing_date"
   */
  @Override
  public Long getNextBillingDate() {
    return this.getProductChoicesResponse.getBoxedNextBillingDate();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.last_plan_change_date"
   */
  @Override
  public Long getLastPlanChangeDate() {
    return this.getProductChoicesResponse.getBoxedLastPlanChangeDate();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.billing_partner"
   */
  @Override
  public String getBillingPartner() {
    return this.getProductChoicesResponse.getBillingPartner();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.is_upgrade_on_us"
   */
  @Override
  public Boolean getIsUpgradeOnUs() {
    return this.getProductChoicesResponse.getIsUpgradeOnUs();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.fallback"
   */
  @Override
  public Boolean getFallback() {
    return this.getProductChoicesResponse.getFallback();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.upsellChoice"
   */
  @Override
  public APIProductChoice getUpsellChoice() {
    ProductChoice productChoice = this.getProductChoicesResponse.getUpsellChoice();
    if (productChoice == null) {
      return null;
    }
    return new APIProductChoiceImpl(productChoice);
  }
}
