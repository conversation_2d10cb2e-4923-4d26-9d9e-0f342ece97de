package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaVideoWithSims {
  private String displayString;

  private SearchPageVideo video;

  private List<SearchPageVideo> simsVideos;

  public String getDisplayString() {
    return displayString;
  }

  public SearchPageVideo getVideo() {
    return video;
  }

  public List<SearchPageVideo> getSimsVideos() {
    return simsVideos;
  }

  public NapaVideoWithSims setDisplayString(String displayString) {
    this.displayString = displayString;
    return this;
  }

  public NapaVideoWithSims setVideo(SearchPageVideo video) {
    this.video = video;
    return this;
  }

  public NapaVideoWithSims setSimsVideos(List<SearchPageVideo> simsVideos) {
    this.simsVideos = simsVideos;
    return this;
  }
}
