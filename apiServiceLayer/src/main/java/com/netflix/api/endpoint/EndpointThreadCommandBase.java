package com.netflix.api.endpoint;

import com.netflix.api.platform.context.ImmutableRequestContext;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.servo.DefaultMonitorRegistry;
import com.netflix.servo.monitor.Counter;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.servo.monitor.Monitors;
import com.netflix.servo.tag.BasicTagList;
import java.util.Arrays;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;
import java.util.concurrent.RejectedExecutionException;
import org.apache.log4j.MDC;

public class EndpointThreadCommandBase<ReturnType, RequestArgumentType> {
  // this default is a very high number as it is ONLY to provide safety against runaway threads
  // and it has to be long-lived enough to support retrieving data from our longest dependencies
  public static final Property<Integer> defaultExecutionTimeoutInMilliseconds =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.endpoint.async.command.timeout.default", Integer.class)
          .orElse(20000);

  private static final Counter COUNTER_TOMCAT_ETCW_TIMEOUT =
      Monitors.newCounter("COUNTER-TOMCAT_ETCW_TIMEOUT");

  private static final String REQUEST_ID_MDC_KEY = "api.requestId";
  private final Executor executor;

  @SuppressWarnings("ImmutableEnumChecker")
  enum QueueRejectionType {
    local_tomcat(
        Monitors.newCounter(
            "api.endpoint.threadpool.queue.rejection",
            () -> BasicTagList.of("queue", "local", "parent", "tomcat"))),
    global_tomcat(
        Monitors.newCounter(
            "api.endpoint.threadpool.queue.rejection",
            () -> BasicTagList.of("queue", "global", "parent", "tomcat"))),
    local_endpoint_thread(
        Monitors.newCounter(
            "api.endpoint.threadpool.queue.rejection",
            () -> BasicTagList.of("queue", "local", "parent", "endpoint"))),
    global_endpoint_thread(
        Monitors.newCounter(
            "api.endpoint.threadpool.queue.rejection",
            () -> BasicTagList.of("queue", "global", "parent", "endpoint")));

    private final Counter counter;

    QueueRejectionType(Counter counter) {
      this.counter = counter;
    }

    public Counter getCounter() {
      return counter;
    }
  }

  static {
    DefaultMonitorRegistry.getInstance().register(COUNTER_TOMCAT_ETCW_TIMEOUT);
    Arrays.asList(QueueRejectionType.values())
        .forEach(c -> DefaultMonitorRegistry.getInstance().register(c.getCounter()));
  }

  EndpointThreadCommandBase(final Executor executor) {
    this.executor = executor;
  }

  /** What should be done when the thread is executed. */
  protected ReturnType invoke(RequestArgumentType arg) {
    return null;
  }

  /**
   * Used for asynchronous execution of command.
   *
   * <p>This will queue up the command on the thread pool and return an NFFuture to get the result
   * once it completes.
   *
   * @return K Result of run() execution
   */
  protected Future<ReturnType> queue(
      final RequestArgumentType arg, final Property<Integer> timeoutInMillis)
      throws RejectedExecutionException {
    EndpointFuture<ReturnType> future;

    boolean onEndpointThread = onEndpointThread();

    try {
      // get the current threads EndpointScriptRequest so we can pass it into the child thread
      final EndpointThreadScriptRequest scriptRequest =
          EndpointThreadScriptRequest.currentRequest();

      // a snapshot of time so the thread can measure how long it waited before executing
      final long timeBeforeExecution = System.currentTimeMillis();

      // wrap the synchronous execute() method in a Callable and execute in the threadpool
      final var observer = new FutureObserver();
      future =
          new EndpointFuture<>(
              "api.endpoint",
              executor,
              scriptRequest,
              new CancellableCallable<>(
                  () -> {
                    tryPutRequestIdInMDC();
                    try {
                      // assign the EndpointScriptRequest to this child thread we want all threads
                      // involved in a given request to use the same EndpointScriptRequest
                      EndpointThreadScriptRequest.initializeThreadWithRequest(scriptRequest);

                      // if we are out of time, throw an exception
                      long timeQueued = System.currentTimeMillis() - timeBeforeExecution;

                      if (scriptRequest != null) {
                        scriptRequest.recordMillisSpentInQueue(timeQueued);
                      }

                      // execute the command
                      return invoke(arg);
                    } catch (Exception e) {
                      throw new RuntimeException("EndpointThreadCommand Execution Failure", e);
                    } finally {
                      if (scriptRequest != null) {
                        scriptRequest.markThreadExecuted();
                      }
                      MDC.remove(REQUEST_ID_MDC_KEY);
                    }
                  }),
              observer,
              timeoutInMillis,
              COUNTER_TOMCAT_ETCW_TIMEOUT);

      // start the work
      final var endpointFuture = future;
      if (scriptRequest != null) {
        scriptRequest.registerTask(
            EndpointThreadTask.create(observer.getCompletableFuture(), endpointFuture));
      }
      endpointFuture.start();

      /* mark that this request has started a thread */
      if (scriptRequest != null) {
        scriptRequest.markThreadStarted();
      }
    } catch (RejectedExecutionException ree) {
      DynamicCounter.increment(
          "api.endpoint.threadpool.total.queue.rejection",
          "parent",
          onEndpointThread ? "endpoint" : "tomcat",
          "result",
          "exception");
      throw ree;
    }
    return future;
  }

  private static void tryPutRequestIdInMDC() {
    RequestContextWrapper.getOptional()
        .map(ImmutableRequestContext::getRequestId)
        .ifPresent(reqId -> MDC.put(REQUEST_ID_MDC_KEY, reqId));
  }

  public static boolean onEndpointThread() {
    return onEndpointThread(Thread.currentThread());
  }

  public static boolean onEndpointThread(Thread t) {
    return t instanceof Endpoint;
  }

  public static int getDefaultExecutionTimeoutInMilliseconds() {
    return defaultExecutionTimeoutInMilliseconds.get();
  }
}
