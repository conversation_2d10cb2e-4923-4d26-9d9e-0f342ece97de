package com.netflix.api.service.ratings;

import com.netflix.api.adapters.APIInternalMaturity;
import com.netflix.content.util.ContentResourceIDLookup;
import com.netflix.mulanratings.protogen.GetRatingsRequest;
import com.netflix.mulanratings.protogen.GetRatingsResponse;
import com.netflix.mulanratings.protogen.MulanRatingsServiceGrpc.MulanRatingsServiceBlockingStub;
import com.netflix.mulanratings.protogen.RatingInfo;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Registry;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.type.protogen.BasicTypes;
import com.netflix.videometadata.l10n.VMSL10NProviderDelegate;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MaturityRatingsDataFetcher {

  private final MulanRatingsServiceBlockingStub mulanRatingsServiceBlockingStub;
  private final Registry registry;
  private final VMSL10NProviderDelegate vmsl10NProviderDelegate;
  private final Id getRatingsTimer;

  @Autowired
  public MaturityRatingsDataFetcher(
      @GrpcSpringClient("ember") MulanRatingsServiceBlockingStub mulanRatingsServiceBlockingStub,
      Registry registry,
      VMSL10NProviderDelegate vmsl10NProviderDelegate) {
    this.mulanRatingsServiceBlockingStub = mulanRatingsServiceBlockingStub;
    this.registry = registry;
    this.vmsl10NProviderDelegate = vmsl10NProviderDelegate;
    getRatingsTimer = registry.createId("api.maturityratings");
  }

  public Map<Long, APIInternalMaturity> getRatingForCountry(String country) {
    Id timer = getRatingsTimer.withTags("country", country);
    long startRequestTime = registry.clock().monotonicTime();
    Map<Long, APIInternalMaturity> ratingsResponse = getRatingsForCountryFromEmber(country);
    long endRequestTime = registry.clock().monotonicTime();
    registry.timer(timer).record(endRequestTime - startRequestTime, TimeUnit.NANOSECONDS);
    return ratingsResponse;
  }

  private Map<Long, APIInternalMaturity> getRatingsForCountryFromEmber(String country) {
    BasicTypes.ISOCountry isoCountry = BasicTypes.ISOCountry.newBuilder().setId(country).build();
    GetRatingsRequest ratingsRequest =
        GetRatingsRequest.newBuilder().setCountry(isoCountry).build();
    GetRatingsResponse response = mulanRatingsServiceBlockingStub.getRatings(ratingsRequest);
    return mapRatingstoAPIInternalMaturityRatings(response);
  }

  private Map<Long, APIInternalMaturity> mapRatingstoAPIInternalMaturityRatings(
      GetRatingsResponse ratingsResponse) {
    Map<Long, APIInternalMaturity> result = new HashMap<>();
    Map<Long, RatingInfo> ratings = ratingsResponse.getRatingsMap();
    ratings.forEach(
        (ratingId, ratingInfo) -> {
          APIInternalMaturity apiInternalMaturity =
              new APIInternalMaturity(
                  ratingInfo.getMaturityLevel(), ratingInfo.getExcludeFromParentalControls());
          result.put(ratingId, apiInternalMaturity);
        });
    return result;
  }

  public Optional<String> getMaturityLabel(Long ratingId) {
    return Optional.ofNullable(
        vmsl10NProviderDelegate.getLocalizedResource(
            ContentResourceIDLookup.getCertificationNameID(ratingId.intValue())));
  }

  public Optional<String> getMaturityDesc(Long ratingId) {
    return Optional.ofNullable(
        vmsl10NProviderDelegate.getLocalizedResource(
            ContentResourceIDLookup.getCertificationDescriptionID(ratingId.intValue())));
  }
}
