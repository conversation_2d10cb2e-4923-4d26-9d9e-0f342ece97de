package com.netflix.api.service.batch;

import static com.google.common.collect.Sets.intersection;
import static com.netflix.map.annotation.MapAnnotationConstants.CLIENT_APP_CAPABILITY;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Sets.SetView;
import com.google.inject.Provider;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.dependencies.merchagg.MerchAggPlatformAdapter;
import com.netflix.api.service.APIClientCapabilitiesInternal;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.archaius.api.Property;
import com.netflix.gps.client.grps.page.ext.session.common.SessionKey;
import com.netflix.gps.client.grps.page.ext.session.common.SessionKeyParser;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.map.annotation.MapAnnotationConstants;
import com.netflix.map.datatypes.MapAnnotations;
import com.netflix.map.datatypes.MapResponse;
import com.netflix.servo.monitor.DynamicCounter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Single;

@Component
@EnableDeprecatedMetrics
public class APILolomoServiceImpl {
  private static final Logger logger = LoggerFactory.getLogger(APILolomoServiceImpl.class);

  public static final String UUID_SEP = "_";
  public static final String UUID_ROOT = "ROOT";

  private final Provider<APIRequest> currentRequest;
  private final OriginalsService originalsService;
  private final USTListGatekeeper listGatekeeper;
  private final USTListAdapter listAdapter;
  private final SessionKeyParser sessionKeyParser;

  // TODO remove once behavior is validated can contain billboardVideos, preApLolomo
  private final Property<List<String>> IMPLICIT_CLIENT_CAPABILITIES;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public APILolomoServiceImpl(
      com.netflix.archaius.api.PropertyRepository pr,
      Provider<APIRequest> currentRequest,
      OriginalsService originalsService,
      USTListGatekeeper listGatekeeper,
      USTListAdapter listAdapter,
      SessionKeyParser sessionKeyParser) {
    this.currentRequest = currentRequest;
    this.originalsService = originalsService;
    this.listGatekeeper = listGatekeeper;
    this.listAdapter = listAdapter;
    this.sessionKeyParser = sessionKeyParser;

    IMPLICIT_CLIENT_CAPABILITIES =
        pr.getList("request.context.client.capabilities", String.class).orElse(List.of());
  }

  public Observable<MapResponse> getNewLolomo(
      Map<String, Object> mapAnnotations,
      Map<String, String> ouiCommonAttributes,
      List<String> supportedLolomoFeatures,
      List<String> billboardTypesSupported,
      Map<String, Object> supportedClientCapabilities) {

    if (supportedLolomoFeatures == null) {
      supportedLolomoFeatures = Collections.emptyList();
    }
    if (billboardTypesSupported == null) {
      billboardTypesSupported = Collections.emptyList();
    }
    Map<String, Object> finalSupportedClientCapabilities =
        supportedClientCapabilities != null ? supportedClientCapabilities : Collections.emptyMap();

    if (billboardTypesSupported.isEmpty()
        && supportedLolomoFeatures.isEmpty()
        && finalSupportedClientCapabilities.isEmpty()) {
      return _getNewLolomo(mapAnnotations);
    }

    // filter annotations before sending them to MAP
    Observable<List<String>> filteredSupportedLolomoFeatures =
        originalsService.filterSupportedLolomoFeatures(
            supportedLolomoFeatures, ouiCommonAttributes);
    Observable<List<String>> filteredSupportedBillboards =
        originalsService.filterSupportedBillboards(billboardTypesSupported, ouiCommonAttributes);
    Observable<List<String>> filteredClientCapabilityKeys =
        originalsService.filterSupportedClientCapabilities(
            new ArrayList<>(finalSupportedClientCapabilities.keySet()), ouiCommonAttributes);
    return Observable.merge(
        Observable.zip(
            filteredSupportedBillboards,
            filteredSupportedLolomoFeatures,
            filteredClientCapabilityKeys,
            (b, lf, cc) -> {
              final Map<String, Object> filteredAnnotations = new HashMap<>(mapAnnotations);
              filteredAnnotations.put(APILoloUtils.SUPPORTED_BILLBOARDS_KEY, String.join(",", b));
              filteredAnnotations.put(
                  APILoloUtils.SUPPORTED_LOLOMO_FEATURES_KEY, String.join(",", lf));
              filteredAnnotations.put(
                  APILoloUtils.CLIENT_APP_CAPABILITY_KEY,
                  mergeFilteredMap(
                      (Map<String, Object>)
                          mapAnnotations.get(APILoloUtils.CLIENT_APP_CAPABILITY_KEY),
                      finalSupportedClientCapabilities,
                      cc));
              return _getNewLolomo(filteredAnnotations);
            }));
  }

  static Map<String, Object> mergeFilteredMap(
      Map<String, Object> existingClientCapabilities,
      Map<String, Object> supportedClientCapabilities,
      List<String> filteredClientCapabilityKeys) {
    Map<String, Object> result = new HashMap<>();
    if (existingClientCapabilities == null) {
      existingClientCapabilities = Collections.emptyMap();
    }

    // do any keys appear in both places? this is potentially a bug
    SetView<String> intersection =
        intersection(existingClientCapabilities.keySet(), supportedClientCapabilities.keySet());
    if (!intersection.isEmpty()) {
      DynamicCounter.increment("COUNTER_dna_conflictingLolomoClientCapabilities");
      MantisPublishContext.getCurrent()
          .add("conflictingLolomoClientCapabilities", intersection.toString());
    }

    // keep values from initial input but filter the keys
    for (String filteredKey : filteredClientCapabilityKeys) {
      result.put(filteredKey, supportedClientCapabilities.get(filteredKey));
    }

    // are there any explicit inputs sent, whose keys do not show up in the supported set? keep
    // them... and hope that the next platform does not want exactly the opposite
    for (var entry : existingClientCapabilities.entrySet()) {
      var existingKey = entry.getKey();
      if (!supportedClientCapabilities.containsKey(existingKey)) {
        result.put(existingKey, entry.getValue());
      }

      // if there was an explicit input sent, and also sent to ORF but it got filtered out there,
      // ORF wins - do NOT keep the key. and again, hope that the next platform is ok with this.
    }
    return result;
  }

  private Observable<MapResponse> _getNewLolomo(Map<String, Object> annotations) {
    Map<String, Object> mapAnnotations =
        APILoloUtils.buildAnnotations(annotations, currentRequest, true);

    mapAnnotations.putIfAbsent(MapAnnotationConstants.LISTCONTEXT, "home");

    if (isNonMember(mapAnnotations)) {
      // Copy-paste from NDCMapGetNonMemberNewLolomoFactory to match inputs
      mapAnnotations.put(MapAnnotationConstants.GPS_MODEL, "saget");
      mapAnnotations.put("GPSMODEL", "saget");
      mapAnnotations.put(MapAnnotationConstants.GETFALLBACK, true);
      mapAnnotations.put(MapAnnotationConstants.NON_MEMBER_REQUEST, true);
      // don't need playlist row for non-member galleries. GPS returns playlist in
      // fallback now
      mapAnnotations.remove(MapAnnotationConstants.USEPLAYLISTROW);
      mapAnnotations.put(MapAnnotationConstants.USEPLAYLISTROW, false);
    }
    return listAdapter.getNewLolomo(mapAnnotations).toObservable();
  }

  private static boolean isNonMember(Map<String, Object> mapAnnotations) {
    return MerchAggPlatformAdapter.nonMemberRequest(mapAnnotations)
        || APIUserUtil.getAccountProfileLegacy() == null;
  }

  // TODO adopt for .next by delegating from APIMapLolomoServiceImpl
  public Observable<MapResponse> refreshListsById(
      Set<String> ids, Map<String, Object> requestAnnotations) {

    Map<String, Object> annotations =
        APILoloUtils.buildAnnotations(requestAnnotations, currentRequest, false);

    // at least one id exists in list
    String firstListId = ids.iterator().next();
    annotations.put(MapAnnotationConstants.UUID, getRootUUID(firstListId));

    annotations.putIfAbsent(MapAnnotationConstants.REFRESH_VOLATILE_CONTENT, true);

    Map<String, Object> listIdsAnnotations = new HashMap<>();
    listIdsAnnotations.put(MapAnnotationConstants.LIST_UUID, ids);

    MapAnnotations mapAnnotations = new MapAnnotations().addAllAnnotationsFromMap(annotations);
    mapAnnotations.addAllAnnotationsFromMap(listIdsAnnotations);

    Observable<MapResponse> observable;
    if (listGatekeeper.shouldCallUstForList(firstListId)) {
      Map<String, Object> _annotations = mapAnnotations.getAnnotationsAsMap();
      observable = listAdapter.getListById(_annotations).toObservable();
    } else {
      observable = Observable.error(new UnsupportedOperationException("Unsupported listId format"));
    }
    return observable;
  }

  public String getRootUUID(final String uuid) {
    if (isGpsGroupSessionKey(uuid)) {
      final SessionKey sessionKey = sessionKeyParser.parse(uuid);

      final Optional<SessionKey> parentKey = sessionKey.getParentKey();
      if (parentKey.isPresent()) {
        return parentKey.get().getCanonicalValue();
      }
    }

    if (!uuid.endsWith(UUID_ROOT)) {
      return getUUIDBase(uuid) + UUID_SEP + UUID_ROOT;
    }
    return uuid;
  }

  public boolean isGpsGroupSessionKey(final String uuid) {
    if (sessionKeyParser.isSessionKey(uuid)) {
      final SessionKey sessionKey = sessionKeyParser.parse(uuid);
      return sessionKey.getParentKey().isPresent();
    }
    return false;
  }

  public static String getUUIDBase(final String uuid) {
    return uuid.split(UUID_SEP)[0];
  }

  public Observable<MapResponse> refreshVolatileContent(String lolomoId) {
    return refreshVolatileContent(lolomoId, null);
  }

  public Observable<MapResponse> refreshVolatileContent(
      String lolomoId, Map<String, Object> inputAnnotations) {
    Map<String, Object> annotations =
        APILoloUtils.buildAnnotations(inputAnnotations, currentRequest, false);

    annotations.put(MapAnnotationConstants.UUID, lolomoId);
    annotations.put(MapAnnotationConstants.LIST_UUID, lolomoId);
    annotations.put(MapAnnotationConstants.REFRESH_VOLATILE_CONTENT, true);

    Observable<MapResponse> observable;
    if (listGatekeeper.shouldCallUstForList(lolomoId)) {
      observable = listAdapter.refreshLolomo(annotations).toObservable();
    } else {
      observable = Observable.error(new UnsupportedOperationException("Unsupported listId format"));
    }
    return observable;
  }

  // Today this is GPS' best known API for refreshing various parts of the LOLOMO
  // They are building off of refreshVolatileContent for Adaptive Recommendations
  public Observable<MapResponse> refreshAdaptiveContent(
      String lolomoId, Map<String, Object> annotations) {
    return refreshVolatileContent(lolomoId, annotations);
  }

  // TODO adopt for .next by delegating from APIMapLolomoServiceImpl
  public Observable<MapResponse> getListById(String id, Map<String, Object> annotations) {
    Map<String, Object> mapAnnotations =
        APILoloUtils.buildAnnotations(annotations, currentRequest, false);

    mapAnnotations.put(MapAnnotationConstants.UUID, id);

    Observable<MapResponse> observable;
    if (listGatekeeper.shouldCallUstForList(id)) {
      observable = listAdapter.getListById(mapAnnotations).toObservable();
    } else {
      observable = Observable.error(new UnsupportedOperationException("Unsupported listId format"));
    }
    return observable;
  }

  public Observable<MapResponse> getCategories(final Map<String, Object> requestAnnotations) {

    Map<String, Object> annotations =
        APILoloUtils.buildAnnotations(requestAnnotations, currentRequest, false);

    return listAdapter.getCategories(annotations).toObservable();
  }

  public Observable<MapResponse> getCategory(
      String categoryId, Map<String, Object> requestAnnotations) {

    Map<String, Object> annotations =
        APILoloUtils.buildAnnotations(requestAnnotations, currentRequest, false);

    annotations.putIfAbsent("categoryID", categoryId);
    annotations.put(MapAnnotationConstants.LISTCONTEXT, categoryId);
    return listAdapter.getGallery(annotations).toObservable().cache();
  }

  public Observable<MapResponse> getPreAppLolomo(
      Map<String, Object> requestAnnotations,
      Map<String, Object> deviceAttributes,
      String profileGuid) {

    // request annotations
    Map<String, Object> annotations = new HashMap<>(requestAnnotations);
    setDeviceAttributes(
        deviceAttributes,
        annotations,
        !IMPLICIT_CLIENT_CAPABILITIES.get().contains("preAppLolomo"));

    // incoming annotations take precedence over request context ones
    Map<String, Object> allAnnotations =
        APILoloUtils.getRequestContextEntities(currentRequest, false);

    // incoming annotations take precedence over request context ones
    allAnnotations.putAll(annotations);

    // adjust user related annotations if profileGuid is other than the current user
    APIUser user = currentRequest.get().getUser();
    if (user != null && profileGuid != null && !user.getCustomerGUID().equals(profileGuid)) {
      return user.getUserByGuid(profileGuid)
          .onErrorReturn(
              error -> {
                logger.warn("Customer {} not found", profileGuid);
                return user;
              })
          .flatMap(
              otherUser -> {
                if (otherUser != user) {
                  Long customerId = APIUserUtil.getCustomerId(otherUser);
                  allAnnotations.put(
                      MapAnnotationConstants.IS_KIDS_DEPRECATED,
                      otherUser.isJustForKidsExperience());
                  allAnnotations.put(MapAnnotationConstants.VISITOR, customerId);
                  allAnnotations.put(
                      MapAnnotationConstants.IS_KIDS, otherUser.isJustForKidsExperience());
                }
                return listAdapter.getPreAppLolomo(allAnnotations).toObservable();
              });
    } else {
      return listAdapter.getPreAppLolomo(allAnnotations).toObservable();
    }
  }

  public Single<MapResponse> getBillboardVideos(
      Map<String, Object> requestAnnotations, Map<String, Object> deviceAttributes) {

    // request annotations
    Map<String, Object> annotations = new HashMap<>(requestAnnotations);
    setDeviceAttributes(
        deviceAttributes,
        annotations,
        !IMPLICIT_CLIENT_CAPABILITIES.get().contains("billboardVideos"));
    Map<String, Object> allAnnotations =
        APILoloUtils.getRequestContextEntities(currentRequest, false);

    // incoming annotations take precedence over request context ones
    allAnnotations.putAll(annotations);
    allAnnotations.put(MapAnnotationConstants.LISTCONTEXT, "billboard");
    return listAdapter.getGallery(allAnnotations).cache();
  }

  private void setDeviceAttributes(
      Map<String, Object> deviceAttributes,
      Map<String, Object> annotations,
      boolean preserveExplicitClientCapabilities) {
    // device attributes are passthrough
    Map<String, Object> allDeviceAttributes = new HashMap<>(deviceAttributes);

    // add the request context capabilities on top as well IF explicit ones do not take precedence -
    // DNA-540
    if (!preserveExplicitClientCapabilities) {
      APIClientCapabilitiesInternal.get(APIRequest.getCurrentRequest())
          .ifPresent(clientCapabilities -> allDeviceAttributes.putAll(clientCapabilities.asMap()));
    }

    annotations.put(
        CLIENT_APP_CAPABILITY, getAllClientCapabilities(allDeviceAttributes, annotations));
  }

  // combine deviceAttributes and existing clientAppCapabilities node
  @VisibleForTesting
  @SuppressWarnings("unchecked")
  static Map<String, Object> getAllClientCapabilities(
      Map<String, Object> deviceAttributes, Map<String, Object> allAnnotations) {
    Map<String, Object> existingNode = deviceAttributes;
    if (allAnnotations.get(CLIENT_APP_CAPABILITY) instanceof Map map) {
      try {
        existingNode = (Map<String, Object>) map.get(CLIENT_APP_CAPABILITY);
        // device attributes take precedence over existing ones
        existingNode.putAll(deviceAttributes);
      } catch (Exception e) {
        logger.warn(
            CLIENT_APP_CAPABILITY + " value in mapAnnotations is not a Map<String, Object>");
      }
    }
    return existingNode;
  }
}
