package com.netflix.api.service;

import rx.Observable;

public abstract class APIObservableBuilderImpl<Item, Behavior extends APIObservableBehavior<Item>>
    extends Observable<Item> implements APIObservableBuilder<Item> {
  protected final Behavior behavior;

  protected APIObservableBuilderImpl(Behavior behavior) {
    super(behavior);
    this.behavior = behavior;
  }

  public Behavior getBehavior() {
    return behavior;
  }

  @Override
  public Observable<Item> build() {
    return this;
  }
}
