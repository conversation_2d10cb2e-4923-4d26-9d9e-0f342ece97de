package com.netflix.api.service.uipersonalization;

import com.netflix.api.grpc.GrpcCallHelpers;
import com.netflix.api.platform.identity.CurrentIdentityResult;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIUser;
import com.netflix.archaius.api.Property;
import com.netflix.i18n.NFLocale;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.uipersonalization.protogen.DeletePersonalizationRequest;
import com.netflix.uipersonalization.protogen.GetPersonalizationRequest;
import com.netflix.uipersonalization.protogen.GetPersonalizationResponse;
import com.netflix.uipersonalization.protogen.Personalization;
import com.netflix.uipersonalization.protogen.Selector;
import com.netflix.uipersonalization.protogen.SetPersonalizationRequest;
import com.netflix.uipersonalization.protogen.UiPersonalizationServiceGrpc;
import com.netflix.uipersonalization.protogen.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Completable;
import rx.Observable;

@Component
public class APIUIPersonalizationServiceImpl implements APIUIPersonalizationService {

  private static final Property<Boolean> DISABLE_COUNTRY_CATALOG =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.uipersonalization.countrycatalog.disable", Boolean.class)
          .orElse(false);
  private final UiPersonalizationServiceGrpc.UiPersonalizationServiceStub uiPersonalizationService;

  @Autowired
  public APIUIPersonalizationServiceImpl(
      @GrpcSpringClient("uipersonalization")
          UiPersonalizationServiceGrpc.UiPersonalizationServiceStub uiPersonalizationService) {
    this.uiPersonalizationService = uiPersonalizationService;
  }

  @Override
  public Observable<APIUIPersonalization> getPersonalization(
      APIUIPersonalizationSelector selector, boolean consistentRead) {
    if (selector == null) {
      return Observable.error(new IllegalArgumentException("Must provide selector"));
    }

    return getPersonalization(
        APIUIPersonalizationRequest.builder()
            .user(APIRequest.getCurrentRequest().getUser())
            .consistentRead(consistentRead)
            .retrieveDisabledVotingTitles(selector.getRetrieveDisabledVotingTitles())
            .retrieveHiddenModules(selector.getRetrieveHiddenModules())
            .retrieveSecondaryLanguages(selector.getRetrieveSecondaryLanguages())
            .retrieveVideoMerchDisabled(selector.getRetrieveVideoMerchDisabled())
            .build());
  }

  @Override
  public Observable<APIUIPersonalization> getPersonalization(APIUIPersonalizationRequest request) {
    if (request == null || request.getUser() == null) {
      return Observable.error(new IllegalArgumentException("Must provide user"));
    }
    GetPersonalizationRequest.Builder getRequestBuilder =
        GetPersonalizationRequest.newBuilder()
            .setUser(User.newBuilder().setProfileGuid(request.getUser().getCustomerGUID()).build())
            .setConsistentRead(request.isConsistentRead())
            .setFieldSelector(
                Selector.newBuilder()
                    .setRetrieveDisabledVotingTitles(request.isRetrieveDisabledVotingTitles())
                    .setRetrieveHiddenModules(request.isRetrieveHiddenModules())
                    .setRetrieveSecondaryLanguages(request.isRetrieveSecondaryLanguages())
                    .setRetrieveVideoMerchDisabled(request.isRetrieveVideoMerchDisabled())
                    .build());
    return GrpcCallHelpers.RxObservable.defer(
            uiPersonalizationService::getPersonalization, getRequestBuilder.build())
        .onErrorReturn(ignore -> GetPersonalizationResponse.getDefaultInstance())
        .map(
            getPersonalizationResponse ->
                new APIUIPersonalizationImpl(getPersonalizationResponse.getPersonalization()));
  }

  public Observable<Void> setPersonalization(
      APIUIPersonalization personalization, String profileGuid) {
    SetPersonalizationRequest.Builder setPersonalizationBuilder =
        SetPersonalizationRequest.newBuilder()
            .setUser(User.newBuilder().setProfileGuid(profileGuid))
            .setPersonalization(fromAPIPersonalization(personalization));
    return GrpcCallHelpers.RxObservable.defer(
            uiPersonalizationService::setPersonalization, setPersonalizationBuilder.build())
        .map(setPersonalizationResponse -> null);
  }

  @Override
  // Passport does not always provide a profile GUID, we should have the ability to pass it in
  // explicitly
  public Observable<Completable> setPersonalization(APIUIPersonalization personalization) {
    return setPersonalization(personalization, CurrentIdentityResult.get().getCustomerGuid())
        .map(ignore -> Completable.complete());
  }

  @Override
  public Observable<Completable> deletePersonalization(APIUIPersonalization personalization) {
    DeletePersonalizationRequest.Builder deleteRequestBuilder =
        DeletePersonalizationRequest.newBuilder()
            .setUser(
                User.newBuilder().setProfileGuid(CurrentIdentityResult.get().getCustomerGuid()))
            .setPersonalization(fromAPIPersonalization(personalization));
    return GrpcCallHelpers.RxObservable.defer(
            uiPersonalizationService::deletePersonalization, deleteRequestBuilder.build())
        .map(deletePersonalizationResponse -> Completable.complete());
  }

  @Override
  @Deprecated
  public Observable<Boolean> isCountryCatalogAvailable(APIUser user) {
    if (user == null || DISABLE_COUNTRY_CATALOG.get()) {
      return Observable.just(false);
    }

    DynamicCounter.increment("api.uipersonalization.countrycatalog");
    final NFLocale primaryLanguage = NFLocale.findInstance(user.getLanguage());
    return getPersonalization(
            APIUIPersonalizationRequest.builder()
                .user(user)
                .retrieveSecondaryLanguages(true)
                .build())
        .map(APIUIPersonalization::getSecondaryLanguages)
        .map(APIUIPersonalizationSecondaryLanguages::getLanguages)
        .map(
            secondaryLanguage ->
                !NFLocale.localesAreMutuallyIntelligible(NFLocale.ENGLISH, primaryLanguage)
                    && secondaryLanguage.stream()
                        .map(NFLocale::findInstance)
                        .anyMatch(
                            x -> NFLocale.localesAreMutuallyIntelligible(x, NFLocale.ENGLISH)));
  }

  private Personalization fromAPIPersonalization(APIUIPersonalization personalization) {
    return APIUIPersonalizationImpl.fromAPIPersonalization(personalization);
  }
}
