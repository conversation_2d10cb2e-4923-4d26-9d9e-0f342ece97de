package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchPillCarouselSection extends SearchPageSection {
  private List<SearchPageEntity> pills;

  public List<SearchPageEntity> getPills() {
    return pills;
  }

  public SearchPillCarouselSection setPills(List<SearchPageEntity> pills) {
    this.pills = pills;
    return this;
  }
}
