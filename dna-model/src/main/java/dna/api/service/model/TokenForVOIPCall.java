package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class TokenForVOIPCall {
  private String token;

  private Long expirationTimestamp;

  private Map<String, String> attributes;

  public String getToken() {
    return token;
  }

  public Long getExpirationTimestamp() {
    return expirationTimestamp;
  }

  public Map<String, String> getAttributes() {
    return attributes;
  }

  public TokenForVOIPCall setToken(String token) {
    this.token = token;
    return this;
  }

  public TokenForVOIPCall setExpirationTimestamp(Long expirationTimestamp) {
    this.expirationTimestamp = expirationTimestamp;
    return this;
  }

  public TokenForVOIPCall setAttributes(Map<String, String> attributes) {
    this.attributes = attributes;
    return this;
  }
}
