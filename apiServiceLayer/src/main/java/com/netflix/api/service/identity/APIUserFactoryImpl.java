package com.netflix.api.service.identity;

import com.netflix.api.dependencies.subscriber.AccountProfileHandler;
import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.api.service.APICountryFactory;
import com.netflix.api.service.plan.APIPlanInfoFactory;
import com.netflix.api.util.Cryptex2Manager;
import com.netflix.lang.RequestVariable;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.subscriberservice.protogen.SubscriberManagementServiceGrpc.SubscriberManagementServiceBlockingStub;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class APIUserFactoryImpl implements APIUserFactory {
  private final Cryptex2Manager cryptex2;
  private final APICountryFactory countryFactory;
  private final AccountProfileHandler accountProfileHandler;
  private final SubtitlesPreferencesHandler subtitlesPreferencesHandler;
  private final APIPlanInfoFactory planInfoFactory;
  private final LocalizedProfileLogic ab8960LocalizedDefaultKidsProfileTestLogic;
  private final SubscriberManagementServiceBlockingStub subscriberManagementServiceBlockingStub;

  private final RequestVariable<Lock> locks =
      new RequestVariable<>() {
        @Override
        protected Lock initialValue() {
          return new ReentrantLock();
        }
      };
  private final RequestVariable<ConcurrentHashMap<Long, APIUserImpl>> cachedUserSubscriberObjects =
      new RequestVariable<>() {
        @Override
        protected ConcurrentHashMap<Long, APIUserImpl> initialValue() {
          return new ConcurrentHashMap<>();
        }
      };

  @Autowired
  private APIUserFactoryImpl(
      APICountryFactory countryFactory,
      Cryptex2Manager cryptex2,
      AccountProfileHandler accountProfileHandler,
      SubtitlesPreferencesHandler subtitlesPreferencesHandler,
      APIPlanInfoFactory planInfoFactory,
      LocalizedProfileLogic ab8960LocalizedDefaultKidsProfileTestLogic,
      SubscriberManagementServiceBlockingStub subscriberManagementServiceBlockingStub) {
    this.countryFactory = countryFactory;
    this.cryptex2 = cryptex2;
    this.accountProfileHandler = accountProfileHandler;
    this.planInfoFactory = planInfoFactory;
    this.ab8960LocalizedDefaultKidsProfileTestLogic = ab8960LocalizedDefaultKidsProfileTestLogic;
    this.subscriberManagementServiceBlockingStub = subscriberManagementServiceBlockingStub;
    this.subtitlesPreferencesHandler = subtitlesPreferencesHandler;
  }

  @Override
  public SubscriberManagementServiceBlockingStub getSubscriberManagementService() {
    return subscriberManagementServiceBlockingStub;
  }

  @Override
  public AccountProfileHandler getAccountProfileHandler() {
    return accountProfileHandler;
  }

  @Override
  public APIUserInternal createUser(long customerId) {
    return createUser(customerId, false);
  }

  @Override
  public APIUserInternal createUser(long customerId, Long accountOwnerId) {
    return createUser(customerId, accountOwnerId, false);
  }

  @Override
  public APIUserInternal createUser(IdentityResult<?> identityResult, boolean invalidateCache) {
    if (invalidateCache) {
      final Lock l = locks.get();
      l.lock();
      try {
        APIUserImpl result =
            new APIUserImpl(
                identityResult,
                true,
                this,
                countryFactory,
                planInfoFactory,
                cryptex2,
                subtitlesPreferencesHandler,
                ab8960LocalizedDefaultKidsProfileTestLogic);
        // overwrite the value in the cache if present
        cachedUserSubscriberObjects.get().put(identityResult.getCustomerId(), result);
        return result;
      } finally {
        l.unlock();
      }
    }
    return getOrCreateCachedUserSubscriber(identityResult);
  }

  @Override
  public APIUserInternal createUser(long customerId, boolean invalidateCache) {
    return createUser(customerId, null, invalidateCache);
  }

  @Override
  public APIUserInternal createUser(long customerId, Long accountOwnerId, boolean invalidateCache) {
    if (invalidateCache) {
      final Lock l = locks.get();
      l.lock();
      try {
        APIUserImpl result =
            new APIUserImpl(
                customerId,
                accountOwnerId,
                true,
                this,
                countryFactory,
                planInfoFactory,
                cryptex2,
                subtitlesPreferencesHandler,
                ab8960LocalizedDefaultKidsProfileTestLogic);
        // overwrite the value in the cache if present
        cachedUserSubscriberObjects.get().put(customerId, result);
        return result;
      } finally {
        l.unlock();
      }
    }

    return getOrCreateCachedUserSubscriber(customerId, accountOwnerId);
  }

  @Override
  public APIUserInternal createUser(AccountProfileRemote accountProfile) {
    return getOrCreateCachedUser(accountProfile);
  }

  private APIUserInternal getOrCreateCachedUserSubscriber(IdentityResult<?> identityResult) {
    Long customerId = identityResult.getCustomerId();
    if (customerId == null) {
      return null;
    }
    APIUserImpl result = cachedUserSubscriberObjects.get().get(customerId);
    if (result != null) {
      return result;
    }
    final Lock l = locks.get();
    l.lock();
    try {
      result = cachedUserSubscriberObjects.get().get(customerId);
      if (result == null) {
        result =
            new APIUserImpl(
                identityResult,
                false,
                this,
                countryFactory,
                planInfoFactory,
                cryptex2,
                subtitlesPreferencesHandler,
                ab8960LocalizedDefaultKidsProfileTestLogic);
        cachedUserSubscriberObjects.get().putIfAbsent(customerId, result);
      }
    } finally {
      l.unlock();
    }
    return result;
  }

  private APIUserInternal getOrCreateCachedUserSubscriber(long customerId, Long accountOwnerId) {
    APIUserImpl result = cachedUserSubscriberObjects.get().get(customerId);
    if (result != null) {
      return result;
    }
    final Lock l = locks.get();
    l.lock();
    try {
      result = cachedUserSubscriberObjects.get().get(customerId);
      if (result == null) {
        result =
            new APIUserImpl(
                customerId,
                accountOwnerId,
                false,
                this,
                countryFactory,
                planInfoFactory,
                cryptex2,
                subtitlesPreferencesHandler,
                ab8960LocalizedDefaultKidsProfileTestLogic);
        cachedUserSubscriberObjects.get().putIfAbsent(customerId, result);
      }
    } finally {
      l.unlock();
    }
    return result;
  }

  private APIUserInternal getOrCreateCachedUser(AccountProfileRemote accountProfile) {
    Long customerId = accountProfile.getOptionalProfileId().orElse(null);
    if (customerId == null) {
      return null;
    }
    APIUserImpl result = cachedUserSubscriberObjects.get().get(customerId);
    if (result != null) {
      return result;
    }
    final Lock l = locks.get();
    l.lock();
    try {
      result = cachedUserSubscriberObjects.get().get(customerId);
      if (result == null) {
        result =
            new APIUserImpl(
                accountProfile,
                this,
                countryFactory,
                planInfoFactory,
                cryptex2,
                subtitlesPreferencesHandler,
                ab8960LocalizedDefaultKidsProfileTestLogic);
        cachedUserSubscriberObjects.get().putIfAbsent(customerId, result);
      }
    } finally {
      l.unlock();
    }
    return result;
  }
}
