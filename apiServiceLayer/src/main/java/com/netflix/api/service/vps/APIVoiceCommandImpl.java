package com.netflix.api.service.vps;

import com.netflix.vps.protogen.Intent;
import com.netflix.vps.protogen.Resource;
import java.util.Map;

public class APIVoiceCommandImpl implements APIVoiceCommand {

  private APIVoiceCommandType commandType;
  private APIVoiceIntent intent;
  private APIVoiceResource resource;
  private final Map<String, String> additionalAttributes;

  public APIVoiceCommandImpl(
      APIVoiceCommandType commandType,
      Intent intent,
      Resource resource,
      Map<String, String> additionalAttributes) {

    if (commandType != null) this.commandType = commandType;
    if (intent != null) this.intent = new APIVoiceIntentImpl(intent);
    if (resource != null) this.resource = new APIVoiceResourceImpl(resource);
    this.additionalAttributes = additionalAttributes;
  }

  public APIVoiceCommandType getVoiceCommandType() {
    return commandType;
  }

  public APIVoiceIntent getIntent() {
    return intent;
  }

  public APIVoiceResource getResource() {
    return resource;
  }

  public Map<String, String> getAdditionalAttributes() {
    return additionalAttributes;
  }
}
