package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class Season implements GraphData {
  /** @dotnext com.netflix.api.service.video.APISeason#getSeasonNumber */
  public Integer number;

  /**
   * total display runtime of the season in seconds, summing up all launched episodes (no DAB
   * episodes)
   */
  public Integer totalDisplayRuntime;

  public Video currentEpisode;

  public Map<String, Video> episodes;

  public Map<String, Video> availableAndUpcomingEpisodes;

  public Map<String, String> numberLabel;

  public Map<String, Atom<NumberLabelWithOptionsResponse>> numberLabelWithOptions;

  public Integer getNumber() {
    return number;
  }

  public Integer getTotalDisplayRuntime() {
    return totalDisplayRuntime;
  }

  public Season setNumber(Integer number) {
    this.number = number;
    return this;
  }

  public Season setTotalDisplayRuntime(Integer totalDisplayRuntime) {
    this.totalDisplayRuntime = totalDisplayRuntime;
    return this;
  }
}
