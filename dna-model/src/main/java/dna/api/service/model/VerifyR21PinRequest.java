package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class VerifyR21PinRequest {
  private String enteredPin;

  private Boolean clientAgeVerificationCapable;

  public String getEnteredPin() {
    return enteredPin;
  }

  public Boolean getClientAgeVerificationCapable() {
    return clientAgeVerificationCapable;
  }

  public VerifyR21PinRequest setEnteredPin(String enteredPin) {
    this.enteredPin = enteredPin;
    return this;
  }

  public VerifyR21PinRequest setClientAgeVerificationCapable(Boolean clientAgeVerificationCapable) {
    this.clientAgeVerificationCapable = clientAgeVerificationCapable;
    return this;
  }
}
