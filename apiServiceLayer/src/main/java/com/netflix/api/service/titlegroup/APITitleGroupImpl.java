package com.netflix.api.service.titlegroup;

import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.vxs.protogen.TitleGroup;
import com.netflix.vxs.protogen.TitleGroupCategory;
import rx.Observable;

public class APITitleGroupImpl implements APITitleGroup {

  private final CompleteVideo _internalCompleteVideo;
  private final Observable<TitleGroup> _titleGroup;

  public APITitleGroupImpl(CompleteVideo completeVideo, TitleGroup titleGroup) {
    this._internalCompleteVideo = completeVideo;
    this._titleGroup = Observable.just(titleGroup);
  }

  @Override
  public Integer getId() {
    return _internalCompleteVideo.getId();
  }

  /** Auto-generated from gRPC proto */
  public Observable<APITitleGroupCategory> getTitleGroupCategory() {
    return _titleGroup.map(
        item -> {
          if (item.getCategory() == TitleGroupCategory.UNRECOGNIZED)
            return APITitleGroupCategory.UNRECOGNIZED;
          else
            try {
              return Enum.valueOf(
                  APITitleGroupCategory.class, item.getCategory().getValueDescriptor().getName());
            } catch (IllegalArgumentException e) {
              return APITitleGroupCategory.UNRECOGNIZED;
            }
        });
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result =
        prime * result
            + ((_internalCompleteVideo == null) ? 0 : _internalCompleteVideo.getId().hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    APITitleGroupImpl other = (APITitleGroupImpl) obj;
    if (_internalCompleteVideo == null) {
      if (other._internalCompleteVideo != null) return false;
    } else if (!_internalCompleteVideo.getId().equals(other._internalCompleteVideo.getId()))
      return false;
    return true;
  }
}
