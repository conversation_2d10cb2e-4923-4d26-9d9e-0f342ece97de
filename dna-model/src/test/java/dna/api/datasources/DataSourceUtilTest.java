package dna.api.datasources;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Test;

class DataSourceUtilTest {

  @Test
  void buildMultiValuedMap_withValidItems_createsCorrectMap() {
    // Arrange
    List<String> items = List.of("apple", "apricot", "banana", "blueberry");

    // Act
    Map<Character, Set<String>> result =
        DataSourceUtil.buildMultiValuedMap(
            items,
            s -> s.charAt(0), // key extractor - first character
            s -> s, // value extractor - the string itself
            k -> new HashSet<>() // value set factory
            );

    // Assert
    assertNotNull(result);
    assertEquals(2, result.size());

    assertTrue(result.containsKey('a'));
    assertTrue(result.containsKey('b'));

    Set<String> aValues = result.get('a');
    assertEquals(2, aValues.size());
    assertTrue(aValues.contains("apple"));
    assertTrue(aValues.contains("apricot"));

    Set<String> bValues = result.get('b');
    assertEquals(2, bValues.size());
    assertTrue(bValues.contains("banana"));
    assertTrue(bValues.contains("blueberry"));
  }

  @Test
  void buildMultiValuedMap_withEmptyItems_returnsEmptyMap() {
    // Arrange
    List<String> items = List.of();

    // Act
    Map<Character, Set<String>> result =
        DataSourceUtil.buildMultiValuedMap(items, s -> s.charAt(0), s -> s, k -> new HashSet<>());

    // Assert
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void buildMultiValuedMap_withDifferentSetFactory_usesCorrectSetType() {
    // Arrange
    List<Integer> items = List.of(1, 2, 3, 1, 2);

    // Act
    Map<Boolean, Set<Integer>> result =
        DataSourceUtil.buildMultiValuedMap(
            items,
            i -> i % 2 == 0, // even/odd grouping
            i -> i,
            k -> new LinkedHashSet<>() // ordered set
            );

    // Assert
    assertNotNull(result);
    assertEquals(2, result.size());

    Set<Integer> evenSet = result.get(true);
    assertTrue(evenSet instanceof LinkedHashSet);
    assertEquals(1, evenSet.size());
    assertTrue(evenSet.contains(2));

    Set<Integer> oddSet = result.get(false);
    assertTrue(oddSet instanceof LinkedHashSet);
    assertEquals(2, oddSet.size());
    assertTrue(oddSet.contains(1));
    assertTrue(oddSet.contains(3));
  }

  @Test
  void mapOrEmpty_withNullMap_returnsEmptyMap() {
    // Act
    Map<String, String> result = DataSourceUtil.mapOrEmpty(null);

    // Assert
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void mapOrEmpty_withNonNullMap_returnsSameMap() {
    // Arrange
    Map<String, String> originalMap = Map.of("key1", "value1", "key2", "value2");

    // Act
    Map<String, String> result = DataSourceUtil.mapOrEmpty(originalMap);

    // Assert
    assertEquals(originalMap, result);
    assertEquals(2, result.size());
    assertEquals("value1", result.get("key1"));
    assertEquals("value2", result.get("key2"));
  }

  @Test
  void convertList_withNullInput_returnsNull() {
    // Act
    List<String> result = DataSourceUtil.convertList(null, Object::toString);

    // Assert
    assertNull(result);
  }

  @Test
  void convertList_withValidInput_transformsCorrectly() {
    // Arrange
    List<Integer> input = List.of(1, 2, 3, 4, 5);

    // Act
    List<String> result = DataSourceUtil.convertList(input, i -> "number_" + i);

    // Assert
    assertNotNull(result);
    assertEquals(5, result.size());
    assertEquals("number_1", result.get(0));
    assertEquals("number_2", result.get(1));
    assertEquals("number_3", result.get(2));
    assertEquals("number_4", result.get(3));
    assertEquals("number_5", result.get(4));
  }

  @Test
  void convertList_withEmptyInput_returnsEmptyList() {
    // Arrange
    List<Integer> input = List.of();

    // Act
    List<String> result = DataSourceUtil.convertList(input, Object::toString);

    // Assert
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void convertListWithFactory_withNullInput_returnsNull() {
    // Act
    List<String> result = DataSourceUtil.convertList(null, Object::toString, ArrayList::new);

    // Assert
    assertNull(result);
  }

  @Test
  void convertListWithFactory_withValidInput_usesCorrectFactory() {
    // Arrange
    List<Integer> input = List.of(10, 20, 30);

    // Act
    List<String> result =
        DataSourceUtil.convertList(input, i -> String.valueOf(i * 2), ArrayList::new);

    // Assert
    assertNotNull(result);
    assertTrue(result instanceof ArrayList);
    assertEquals(3, result.size());
    assertEquals("20", result.get(0));
    assertEquals("40", result.get(1));
    assertEquals("60", result.get(2));
  }

  private enum TestEnum1 {
    VALUE_A,
    VALUE_B,
    VALUE_C
  }

  private enum TestEnum2 {
    VALUE_A,
    VALUE_B,
    VALUE_C,
    VALUE_D
  }

  @Test
  void enumConverter_withValidEnum_convertsCorrectly() {
    // Arrange
    var converter = DataSourceUtil.<TestEnum1, TestEnum2>enumConverter(TestEnum2.class);

    // Act
    TestEnum2 result1 = converter.apply(TestEnum1.VALUE_A);
    TestEnum2 result2 = converter.apply(TestEnum1.VALUE_B);
    TestEnum2 result3 = converter.apply(TestEnum1.VALUE_C);

    // Assert
    assertEquals(TestEnum2.VALUE_A, result1);
    assertEquals(TestEnum2.VALUE_B, result2);
    assertEquals(TestEnum2.VALUE_C, result3);
  }

  @Test
  void enumConverter_withNullEnum_returnsNull() {
    // Arrange
    var converter = DataSourceUtil.<TestEnum1, TestEnum2>enumConverter(TestEnum2.class);

    // Act
    TestEnum2 result = converter.apply(null);

    // Assert
    assertNull(result);
  }

  @Test
  void enumConverter_withMismatchedEnum_throwsException() {
    // Arrange - this test verifies the enum converter works with same-named values
    var converter = DataSourceUtil.<TestEnum1, TestEnum2>enumConverter(TestEnum2.class);

    // Act & Assert
    TestEnum2 result = converter.apply(TestEnum1.VALUE_A);
    assertEquals(TestEnum2.VALUE_A, result);
  }

  // Test proto enum converter with mock protocol buffer enum
  @Test
  void protoEnumConverter_withNullInput_returnsNull() {
    // Arrange
    var converter = DataSourceUtil.protoEnumConverter(TestEnum1.class);

    // Act
    TestEnum1 result = converter.apply(null);

    // Assert
    assertNull(result);
  }

  @Test
  void constructor_isPrivate() {
    // This tests that the utility class constructor is private
    // and cannot be instantiated directly

    // We can't directly test private constructor without reflection,
    // but we can verify the class is designed as a utility class
    // by ensuring all methods are static

    // Just verify the class exists and has the expected static methods
    assertNotNull(DataSourceUtil.class.getDeclaredMethods());

    // All public methods should be static
    boolean allMethodsStatic =
        java.util.Arrays.stream(DataSourceUtil.class.getDeclaredMethods())
            .filter(m -> java.lang.reflect.Modifier.isPublic(m.getModifiers()))
            .allMatch(m -> java.lang.reflect.Modifier.isStatic(m.getModifiers()));

    assertTrue(allMethodsStatic, "All public methods should be static in utility class");
  }
}
