package com.netflix.api.endpoint;

import com.netflix.api.service.APIRequest;
import java.util.concurrent.CompletionStage;
import net.jcip.annotations.ThreadSafe;

/**
 * Representation of a script, the endpoint it serves and the mechanism for executing the script.
 *
 * <p>This class is intended to be instantiated once and executed many times, similar to how a
 * servlet works.
 */
@ThreadSafe
public class EndpointScript {

  private final String uri;
  private final String group;
  private final APIEndpoint endpoint;

  /**
   * Constructor for Primer Endpoints
   *
   * @param endpointURI the URI for this script
   * @param endpointGroup the endpoint group for this script
   * @param endpoint the endpoint script to execute
   */
  public EndpointScript(String endpointURI, String endpointGroup, APIEndpoint endpoint) {
    this.uri = endpointURI;
    this.group = endpointGroup;
    this.endpoint = endpoint;
  }

  /**
   * Executes the script with the given arguments accessible to it.
   *
   * @param apiRequest
   */
  public CompletionStage<Void> execute(APIRequest apiRequest) {

    // TODO: This won't be needed when we stop relying on ETC to run biz calls async
    EndpointThreadScriptRequest.newRequest(apiRequest.getServletRequest());
    // TODO: This won't be needed when we stop tracking Observables-in-flight per request
    RxTomcatHooks.clear();

    return endpoint.execute(apiRequest);
  }

  public String getEndpointURI() {
    return uri;
  }

  public String getEndpointGroup() {
    return group;
  }
}
