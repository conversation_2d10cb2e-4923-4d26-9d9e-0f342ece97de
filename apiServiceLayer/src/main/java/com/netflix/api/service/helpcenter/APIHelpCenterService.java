package com.netflix.api.service.helpcenter;

import com.fasterxml.jackson.databind.JsonNode;
import rx.Observable;

public interface APIHelpCenterService {

  /**
   * Returns a legal document from help center, for the current user's country and locale. The help
   * center may also return an error; callers should parse the payload accordingly. A <code>
   * HelpCenterClientException instance</code> is thrown in case of communication error with the
   * help center service (no fallback); callers should be prepared to handle it.
   *
   * @param docType one of termsofuse, privacy, giftterms, eula, notices, dvdterms
   * @param format one of text, html
   * @return JSON payload in format specified in
   *     https://docs.google.com/document/d/1xLY5WDdVoAog7psXspuG-SA1sCXd_uvOdkqwoWCKDA4/edit
   */
  Observable<JsonNode> getLegalDoc(String docType, String format);
}
