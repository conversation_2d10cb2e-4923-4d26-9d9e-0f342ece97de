package com.netflix.api.service.batch;

import static com.google.common.collect.Maps.transformValues;
import static java.util.Optional.ofNullable;

import com.google.common.base.Preconditions;
import com.google.inject.Provider;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.facade.service.BaseVideoLookup;
import com.netflix.api.facade.service.LookupContext;
import com.netflix.api.grpc.GrpcCallHelpers.RxSingle;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.exception.Exceptions;
import com.netflix.api.platform.util.CompletionStageAdapter;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.service.batch.model.PromoVideo;
import com.netflix.api.service.batch.model.PromoVideoImpl;
import com.netflix.api.service.cms.APICmsService;
import com.netflix.api.service.identity.APIAuthorizationException;
import com.netflix.api.service.identity.APIAuthorizationException.AuthorizationExceptionCause;
import com.netflix.api.service.playlist.APIPlaylistServiceImpl;
import com.netflix.api.service.video.APICachedVideoImpl;
import com.netflix.api.service.video.APIVideo;
import com.netflix.api.service.video.APIVideoFactory;
import com.netflix.api.service.video.APIVideoSummaryService;
import com.netflix.api.service.video.LabelType;
import com.netflix.archaius.api.Property;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.trailerpark.protogen.GetSupplementalVideoListReply;
import com.netflix.trailerpark.protogen.GetSupplementalVideoListRequest;
import com.netflix.trailerpark.protogen.PromoVideoByContextReply;
import com.netflix.trailerpark.protogen.PromoVideoByContextRequest;
import com.netflix.trailerpark.protogen.SupplementalList;
import com.netflix.trailerpark.protogen.TrailerParkServiceGrpc.TrailerParkServiceStub;
import com.netflix.type.ISOCountry;
import com.netflix.type.NFCountry;
import com.netflix.type.proto.Countries;
import com.netflix.type.proto.Locales;
import com.netflix.type.proto.Visitors;
import com.netflix.util.Pair;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Single;
import rx.functions.Func1;

@Component
@EnableDeprecatedMetrics
public class APIVideoServiceImpl implements APIVideoService {

  private static final Property<Boolean> PROMOVIDEOS_DEBUG_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.ust.PromoVideos.enableDebug", Boolean.class)
          .orElse(false);

  private final APIPlaylistServiceImpl playlistService;
  private final APICmsService cmsService;
  private final APIVideoSummaryService apiVideoSummaryService;
  private final Provider<APIVideoFactory> videoFactoryProvider;
  private final TrailerParkServiceStub trailerParkClient;
  private final USTVideoMetadataAdapter videoMetadataAdapter;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public APIVideoServiceImpl(
      @GrpcSpringClient("trailerpark") TrailerParkServiceStub trailerParkClient,
      APICmsService cmsService,
      APIVideoSummaryService apiVideoSummaryService,
      Provider<APIVideoFactory> videoFactoryProvider,
      APIPlaylistServiceImpl playlistService,
      USTVideoMetadataAdapter videoMetadataAdapter) {
    this.trailerParkClient = trailerParkClient.withWaitForReady();
    this.cmsService = cmsService;
    this.apiVideoSummaryService = apiVideoSummaryService;
    this.videoFactoryProvider = videoFactoryProvider;
    this.playlistService = playlistService;
    this.videoMetadataAdapter = videoMetadataAdapter;
  }

  @Override
  public Single<Map<Long, PromoVideo>> getPromoVideos(
      Collection<Long> ids, String context, String lolomoId, Map<String, String> annotations) {

    if (ids == null || ids.isEmpty()) {
      return Single.just(Map.of());
    }

    ISOCountry country = ofNullable(RequestContextWrapper.get().getCountry()).orElse(NFCountry.US);
    String locale = ofNullable(RequestContextWrapper.get().getLocale()).orElse("en-US");
    String uiFlavor =
        ofNullable(APIRequest.getCurrentRequest().getRequestContext().getUIFlavor())
            .map(Enum::name)
            .orElse(null); // TODO should it be something else?

    PromoVideoByContextRequest.Builder builder =
        PromoVideoByContextRequest.newBuilder()
            .setCountry(Countries.toProtobuf(country))
            .addAllContextid(ids)
            .setContext(context != null ? context : "browse")
            .setLocaleType(Locales.toProtobuf(locale))
            .setUiFlavor(uiFlavor);
    Optional.ofNullable(Visitors.toProtobuf(CurrentVisitor.get())).ifPresent(builder::setVisitor);
    if (lolomoId != null) {
      builder.setLolomoId(lolomoId);
    }
    if (annotations != null) {
      builder.putAllAnnotations(annotations);
    }

    PromoVideoByContextRequest request = builder.build();

    var now = System.nanoTime();
    if (PROMOVIDEOS_DEBUG_ENABLED.get()) {
      MantisPublishContext.getCurrent()
          .add("api.trailer-park.request." + now, USTListUtils.toJson(request));
    }

    return RxSingle.defer(trailerParkClient::getPromoVideosByContext, request)
        .doOnSuccess(
            response -> {
              if (PROMOVIDEOS_DEBUG_ENABLED.get()) {
                MantisPublishContext.getCurrent()
                    .add("api.trailer-park.response." + now, USTListUtils.toJson(response));
              }
            })
        .doOnError(
            t -> {
              if (PROMOVIDEOS_DEBUG_ENABLED.get()) {
                MantisPublishContext.getCurrent()
                    .add("api.trailer-park.error." + now, Exceptions.getStackTrace(t));
              }
            })
        .map(APIVideoServiceImpl::transformPromoVideoWithContext);
  }

  private static Map<Long, PromoVideo> transformPromoVideoWithContext(
      PromoVideoByContextReply reply) {
    Map<Long, PromoVideoByContextReply.PromoVideo> input = reply.getPromoVideosMap();

    return transformValues(
        input,
        source ->
            new PromoVideoImpl(
                source.getVideo().getId(),
                source.getType(),
                source.getStartOffsetMs(),
                source.getComputeId(),
                source.getPlayIntoMDP(),
                source.getAnnotationsMap()));
  }

  public Observable<Map<Integer, Boolean>> isAgeVerificationProtected(Set<Integer> videoIds) {
    return APIRequest.getCurrentRequest()
        .getObservableUser()
        .flatMap(
            user -> {
              if (user == null) {
                return Observable.error(
                    new APIAuthorizationException(
                        AuthorizationExceptionCause.NO_USER_CREDENTIALS,
                        "Must be logged in to check age verification based protection status for viewables"));
              }

              ISOCountry country = RequestContextWrapper.get().getCountry();
              if (country == null) {
                return Observable.error(
                    new APIServiceRuntimeException(
                        "Country of request null when getting age verification flag"));
              }
              return cmsService.isAgeVerificationRequired(videoIds);
            });
  }

  @Override
  public Single<Map<Integer, Set<Integer>>> getSupplementalVideos(
      Set<Integer> videoIds, Set<String> subTypes, boolean nonMember, String uiContext) {
    Preconditions.checkArgument(videoIds != null && !videoIds.isEmpty());
    ISOCountry country = ofNullable(RequestContextWrapper.get().getCountry()).orElse(NFCountry.US);
    String locale = ofNullable(RequestContextWrapper.get().getLocale()).orElse("en-US");

    boolean useDefaultSubtypes = subTypes == null || subTypes.isEmpty();

    GetSupplementalVideoListRequest.Builder builder =
        GetSupplementalVideoListRequest.newBuilder()
            .addAllVideoId(videoIds)
            .setDefaultSubtypes(useDefaultSubtypes)
            .setIsNonmember(nonMember)
            .setCountry(country.getId())
            .setLocale(locale);

    if (CurrentVisitor.get() != null) {
      builder.setVisitorId(CurrentVisitor.get().getId());
    }
    if (uiContext != null && !uiContext.isEmpty()) {
      builder.setUiContext(uiContext);
    }

    return RxSingle.defer(trailerParkClient::getSupplementalVideos, builder.build())
        .map(GetSupplementalVideoListReply::getSupplementalVideoIdsMap)
        .map(APIVideoServiceImpl::transformSupplementalVideosReply)
        .map(filterSupplementalTypes(useDefaultSubtypes, subTypes));
  }

  private static Map<Integer, Set<Integer>> transformSupplementalVideosReply(
      Map<Integer, SupplementalList> reply) {
    return transformValues(
        reply,
        supplementalList -> new LinkedHashSet<>(supplementalList.getSupplementalVideoIdsList()));
  }

  private Func1<Map<Integer, Set<Integer>>, Map<Integer, Set<Integer>>> filterSupplementalTypes(
      boolean useDefaultSubtypes, Set<String> subTypes) {
    if (useDefaultSubtypes) {
      return supplementalsMap -> supplementalsMap;
    }
    return supplementalsMap ->
        transformValues(
            supplementalsMap,
            supplementalIds ->
                filterBySubtypes(supplementalIds, subTypes)
                    .collect(Collectors.toCollection(LinkedHashSet::new))); // the order matters
  }

  private Stream<Integer> filterBySubtypes(Set<Integer> supplementalIds, Set<String> knownTypes) {
    // order preserved
    Collection<APIVideo> supplementalVideos =
        videoFactoryProvider.get().getInstancesCollection(supplementalIds);
    return supplementalVideos.stream()
        .filter(video -> knownTypes.contains(video.asSupplementalVideo().getSubType()))
        .map(APIVideo::getId);
  }

  private Single<Collection<APIVideo>> buildAPIVideos(Collection<Integer> videoIds) {
    return CompletionStageAdapter.toSingle(
        videoFactoryProvider.get().getInstancesCollectionAsync(videoIds));
  }

  @Override
  public Single<Map<Integer, Integer>> getEpisodeBatchParents(Collection<Integer> videoIds) {
    LookupContext context = BaseVideoLookup.buildLookupContext();
    return apiVideoSummaryService.getEpisodeBatchParents(
        CompletionStageAdapter.toSingle(
            videoFactoryProvider
                .get()
                .getVideoLookup()
                .getVideosAsync(videoIds, context)
                .thenApply(map -> BaseVideoLookup.buildVideosCollection(videoIds, map))));
  }

  @Override
  public Single<Map<Integer, Integer>> getUpcomingSeasons(Collection<Integer> videoIds) {
    return CompletionStageAdapter.toSingle(videoMetadataAdapter.getUpcomingSeason(videoIds));
  }

  @Override
  public Observable<Map<Integer, String>> getNumSeasonsLabel(
      String labelType, Map<Integer, List<Integer>> showSeasonsMap) {
    Map<Integer, Integer> firstSeasonIds =
        showSeasonsMap.entrySet().stream()
            .collect(
                Collectors.toMap(
                    Entry::getKey,
                    v -> {
                      if (!v.getValue().isEmpty()) {
                        return v.getValue().getFirst();
                      } else {
                        return -1; // bogus video id, will not be looked up anyway
                      }
                    }));
    Map<Integer, Optional<APIVideo>> firstSeasons =
        videoFactoryProvider.get().getInstances(firstSeasonIds.values());

    List<Observable<Pair<Integer, String>>> numLabelsObservables = new ArrayList<>();
    for (Map.Entry<Integer, List<Integer>> showsSeasonsEntry : showSeasonsMap.entrySet()) {
      Integer showId = showsSeasonsEntry.getKey();
      int seasonsLength = showsSeasonsEntry.getValue().size();
      APICachedVideoImpl firstSeason = null;
      if (seasonsLength > 0) {
        Integer firstSeasonId = showsSeasonsEntry.getValue().getFirst();
        firstSeason = (APICachedVideoImpl) (firstSeasons.get(firstSeasonId).orElse(null));
      }
      if (firstSeason != null) {
        Observable<Pair<Integer, String>> numSeasonsLabel =
            firstSeason.getNumSeasonsLabel(
                showId, LabelType.valueOf(labelType), seasonsLength, firstSeason);
        numLabelsObservables.add(numSeasonsLabel);
      } else {
        // no seasons, get the label off the show video
        numLabelsObservables.add(getNumSeasonsLabel(labelType, showId));
      }
    }
    Observable<Pair<Integer, String>> allLabelsObs = Observable.merge(numLabelsObservables);
    return allLabelsObs.collect(
        HashMap::new, (map, entry) -> map.put(entry.first(), entry.second()));
  }

  private Observable<Pair<Integer, String>> getNumSeasonsLabel(String labelType, Integer showId) {
    APIVideo show = videoFactoryProvider.get().getInstance(showId);
    if (show == null) { // should not happen
      return Observable.just(new Pair<>(showId, ""));
    }
    return show.asShow()
        .getNumSeasonsLabel(LabelType.valueOf(labelType))
        .map(label -> new Pair<>(showId, label));
  }

  @Override
  public Single<Void> removeFromContinueWatchingList(
      Collection<Integer> videoIds, String uiVersion, Integer trackId) {

    if (videoIds == null || videoIds.isEmpty()) {
      return Single.just(null);
    }
    return buildAPIVideos(videoIds)
        .toObservable()
        .flatMap(videos -> playlistService.removeFromContinueWatching(videos, uiVersion, trackId))
        .toSingle();
  }
}
