package com.netflix.api.service.account;

import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIParentalControlMaturity;
import com.netflix.api.service.identity.APIProfileMaturity;
import com.netflix.api.service.identity.APIUser;
import java.util.List;
import java.util.Map;
import rx.Observable;

public interface APIAccountManagementService {

  /**
   * Gets the set maturity levels applicable for parental controls and the corresponding labels for
   * the current country of APIUser.
   *
   * @return a list (sorted by maturity level) with the available localized maturity level, rating
   *     and, localized description
   */
  List<APIParentalControlMaturity> getParentalControlMaturityRatings(APIUser apiUser);

  /**
   * Returns the corresponding maturity labels for the given user's country &amp; user's maturity
   * value.
   *
   * @param apiUser user whose maturity value &amp; signup country the label(s) is requested for
   * @return Maturity label(s) for the given maturity value and whether the it is lowest or highest
   *     rating
   */
  Observable<APIProfileMaturity> getAPIProfileMaturityRating(APIUser apiUser);

  /**
   * @return the personalized configuration information for the provided user
   */
  Observable<APIProfileConfiguration> getProfileConfiguration(APIUser user);

  /**
   * Updates the current profile configuration
   *
   * @return the modified profile configuration item or an error if the transaction failed
   */
  Observable<UpdateProfileConfigurationResponse> updateProfileConfiguration(
      UpdateProfileConfigurationRequest update);

  /**
   * @return the "add on" profiles for the account associated with the user parameter, will be an
   *     empty list if none such add on users exist
   */
  Observable<List<APIUser>> getAddOnBeneficiaries();

  /**
   * @return the plan information for the currently logged in user, or empty() if there is no plan
   *     or no currently logged in user
   */
  Observable<APIPlan> getPlan();

  /**
   * @return the plan information for provided request, or empty() if there is no plan or no
   *     currently logged in user
   */
  Observable<APIPlan> getPlan(APIRequest apiRequest);

  /**
   * Get a service code for users to use when calling Customer Support
   *
   * @return String representing the service code
   */
  Observable<String> getCSServiceCode(APIUser user);

  /**
   * Retrieves the phone number entry for the user. If this is a NRM or no user is known, you can
   * pass in null
   *
   * @param user the current user or null
   * @return the formatted phone number for Customer Support
   */
  Observable<String> getCSPhoneNumber(APIUser user);

  /**
   * Retrieves encrypted VOIP member credential for the current user.
   *
   * @param attributes map of application-specific parameters, optional
   * @return the token corresponding to customerId if the user is a member and logged in; defaults
   *     to non-member (ESN-based) token otherwise this token needs to be deleted from device
   *     storage at log-out
   */
  Observable<APIVailToken> getVailAuthToken(Map<String, Object> attributes);

  /**
   * Retrieves encrypted VOIP non-member credential for the current user.
   *
   * @param attributes map of application-specific parameters, optional
   * @return the ESN-based token this token can remain stored on the device even after log-out
   */
  Observable<APIVailToken> getVailNonMemberAuthToken(Map<String, Object> attributes);

  /**
   * Retrieves an auth token and routing information for a specific call that will be made
   *
   * @param attributes map of application-specific parameters, optional
   * @return the token
   */
  Observable<APITokenForCall> getAuthTokenForCall(Map<String, Object> attributes);

  /**
   * Record post-call information.
   *
   * @param authToken the auth token obtained from getAuthTokenForCall
   * @param callData stringified JSON
   */
  Observable<Void> recordPostCallInfo(String authToken, String callData);

  Observable<Map<String, Object>> getPartnerMetadata(String esnPrefix);

  /**
   * Executes a GET-like operation.
   *
   * @param endpoint operation name
   * @param args operation arguments
   */
  @Deprecated
  Observable<Map<String, Object>> executeGet(String endpoint, Map<String, Object> args);

  Observable<Map<String, Object>> getProductChoicesMap(Map<String, String> requestMap);

  Observable<Map<String, Object>> updateProductChoiceMap(Map<String, String> updateMap);

  Observable<Map<String, Object>> getReferralId(Map<String, String> requestMap);
}
