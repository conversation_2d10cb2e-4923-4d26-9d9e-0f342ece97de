package com.netflix.api.service.prepostplay;

import com.netflix.aballocator.protogen.Alloc;
import com.netflix.aballocator.protogen.AllocStatus;
import com.netflix.api.grpc.GrpcCallHelpers.Future;
import com.netflix.evidence.protogen.TextMessages;
import com.netflix.evidence.protogen.TextMessagesReply;
import com.netflix.evidence.protogen.TextMessagesRequest;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.resolver.MicrocontextResolver;
import com.netflix.p3.converter.fetcher.PostplaySeasonRenewalMessageFetcher;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.textevidence.protogen.TextEvidenceServiceGrpc.TextEvidenceServiceStub;
import com.netflix.type.proto.Videos;
import com.netflix.type.protogen.BasicTypes;
import com.netflix.ust.adapters.USTStatus;
import java.util.concurrent.CompletableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ust.video.core.v1.BaseResult;
import ust.video.core.v1.GetVideoBaseRequest;
import ust.video.core.v1.GetVideoBaseResponse;
import ust.video.core.v1.VideoAccess;
import ust.video.v1.VideoBase;
import ust.video.v1.VideoRequest;

@Component
public class USTSeasonRenewalMessageFetcher implements PostplaySeasonRenewalMessageFetcher {

  private final TextEvidenceServiceStub textEvidence;
  private final MicrocontextResolver resolver;
  private final VideoAccess videoAccess;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public USTSeasonRenewalMessageFetcher(
      @GrpcSpringClient("textevidence") TextEvidenceServiceStub textEvidence,
      MicrocontextResolver resolver,
      VideoAccess videoAccess) {

    this.textEvidence = textEvidence;
    this.resolver = resolver;
    this.videoAccess = videoAccess;
  }

  @Override
  public CompletableFuture<String> getSeasonRenewalMessage(Integer watchedVideoId) {
    var ctx = CurrentMicrocontext.get();
    var country = ctx.getCountry();
    var localeId = ctx.getLocale().map(BasicTypes.Locale::getId);
    var cid = ctx.getAuth().getCurrentAuth().getCustomerId();

    var videoRequest =
        VideoRequest.newBuilder().addVideos(Videos.toProtobuf(watchedVideoId)).build();

    var baseRequest = GetVideoBaseRequest.newBuilder().setVideoRequest(videoRequest).build();

    var topNodeIdFuture =
        Future.call(videoAccess::getVideoBase, baseRequest)
            .thenApply(GetVideoBaseResponse::getResultsList)
            .thenApply(
                baseResults ->
                    baseResults.stream()
                        .filter(baseResult -> USTStatus.isOk(baseResult.getStatus()))
                        .findFirst()
                        .map(BaseResult::getResult)
                        .map(VideoBase::getBoxedTopNodeId)
                        .orElse(watchedVideoId))
            .toCompletableFuture();

    var experimentationContextFuture = resolver.resolveExperimentation().toCompletableFuture();

    return CompletableFuture.allOf(topNodeIdFuture, experimentationContextFuture)
        .thenCompose(
            _ignore -> {
              var topNodeId = topNodeIdFuture.join();
              var builder =
                  TextMessagesRequest.newBuilder()
                      .setCountryId(country.getId())
                      .setUiContext("POSTPLAY_COMPLETION")
                      .addVideoIds(topNodeId)
                      .setVisitorId(cid);

              localeId.ifPresent(builder::setLocaleId);

              var experimentationContext = experimentationContextFuture.join();
              experimentationContext
                  .getAccountAllocations()
                  .getTestToCellMap()
                  .forEach(
                      (experimentId, cellId) ->
                          builder.putAbAllocations(
                              experimentId,
                              Alloc.newBuilder()
                                  .setCell(cellId)
                                  .setAllocStatus(AllocStatus.ALLOCATED)
                                  .build()));

              return Future.call(textEvidence::getTextMessages, builder.build())
                  .thenApply(TextMessagesReply::getTextMessagesMap)
                  .thenApply(
                      messagesMap ->
                          messagesMap.getOrDefault(topNodeId, TextMessages.getDefaultInstance()))
                  .handle(
                      (textMessages, throwable) ->
                          throwable == null ? textMessages : TextMessages.getDefaultInstance())
                  .thenApply(TextMessages::getMessage);
            });
  }
}
