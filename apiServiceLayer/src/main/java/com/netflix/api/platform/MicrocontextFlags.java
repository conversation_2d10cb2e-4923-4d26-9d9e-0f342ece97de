package com.netflix.api.platform;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;

public class MicrocontextFlags {
  private MicrocontextFlags() {}

  public static final Property<Boolean> MICROCONTEXT_SHADOW_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.microcontext.shadow.enabled", Boolean.class)
          .orElse(true);
  public static final Property<Boolean> MICROCONTEXT_DNA_AUGMENTED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.microcontext.dna.augmented", Boolean.class)
          .orElse(false);
}
