package com.netflix.api.service.video;

import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.APIRequest;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.dynimo.proto.proxy.GenerateURLReply;
import com.netflix.dynimo.proto.proxy.GenerateURLRequest;
import com.netflix.dynimo.proto.proxy.ProxyServiceGrpc.ProxyServiceStub;
import com.netflix.dynimo.proto.proxy.Value;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
public class APIDynimoServiceImpl implements APIDynimoService {
  private static final Logger logger = LoggerFactory.getLogger(APIDynimoServiceImpl.class);

  private final ProxyServiceStub dynimoClientService;
  private final Property<Boolean> isCallDynimoWithCDNHostnameParamEnabled;

  @Autowired
  public APIDynimoServiceImpl(
      @GrpcSpringClient("dynimoproxy") ProxyServiceStub dynimoClientService,
      PropertyRepository propertyRepository) {
    this.dynimoClientService = dynimoClientService;
    isCallDynimoWithCDNHostnameParamEnabled =
        propertyRepository
            .get("api.isCallDynimoWithCDNHostnameParamEnabled", Boolean.class)
            .orElse(true);
  }

  @Override
  public Observable<String> createUrl(final APIDynimoArtworkRequest request) {
    Observable<String> steeredCdnHostname = Observable.just("");
    if (APIRequest.getCurrentRequest() != null
        && APIRequest.getCurrentRequest().getRequestContext() != null)
      steeredCdnHostname =
          APIRequest.getCurrentRequest().getRequestContext().getSteeredCdnHostname();

    return steeredCdnHostname.flatMap(
        cdnHostname -> {
          try {
            GenerateURLRequest.Builder dynimoproxyRequest =
                GenerateURLRequest.newBuilder().setRecipeName(request.getType());

            if (isCallDynimoWithCDNHostnameParamEnabled.get()) {
              logger.debug("attempting to set cdn hostname on dynimo request");
              if (cdnHostname != null) {
                logger.debug("setting cdn hostname {} on dynimo request", cdnHostname);
                dynimoproxyRequest.setOcaHostname(cdnHostname);
              } else {
                logger.debug("cdn hostname is null");
              }
            } else {
              logger.trace("not setting cdn hostname on dynimo request, property disabled");
            }

            // maintain previous behavior where country and locale are always set
            String country =
                (request.isCountrySet())
                    ? request.getCountry()
                    : RequestContextWrapper.get().getCountry().getId();
            String locale =
                (request.isLocaleSet())
                    ? request.getLocale()
                    : RequestContextWrapper.get().getLocale();

            // new recipes do not use metaId, so it too can be omitted if not set,
            dynimoproxyRequest.putParameters(
                "country", Value.newBuilder().setStringValue(country).build());
            dynimoproxyRequest.putParameters(
                "locale", Value.newBuilder().setStringValue(locale).build());

            // but leave it as a number to get better compression in the URL
            if (request.getVideoId() > 0) {
              dynimoproxyRequest.putParameters(
                  "metaId", Value.newBuilder().setNumberValue(request.getVideoId()).build());
            }

            // only set if width is present
            // setting this too early could prevent future calls from working
            if (request.getWidth() != 0) {
              dynimoproxyRequest.setWidth(request.getWidth());
            }

            // only set if height is present
            // setting this too early could prevent future calls from working
            if (request.getHeight() != 0) {
              dynimoproxyRequest.setHeight(request.getHeight());
            }

            if (request.getParameters() != null) {
              request
                  .getParameters()
                  .forEach(
                      (key, value) -> {
                        switch (value) {
                          case String s -> {
                            dynimoproxyRequest.putParameters(
                                key, Value.newBuilder().setStringValue(s).build());
                          }
                          case Integer i -> {
                            dynimoproxyRequest.putParameters(
                                key, Value.newBuilder().setNumberValue(i).build());
                          }
                          case Boolean b -> {
                            dynimoproxyRequest.putParameters(
                                key, Value.newBuilder().setBoolValue(b).build());
                          }
                          default ->
                              logger.warn(
                                  "Trying to add non support value to dynimo {}", value.getClass());
                        }
                      });
            }

            return RxObservable.defer(dynimoClientService::generateURL, dynimoproxyRequest.build())
                .map(GenerateURLReply::getUrl);

          } catch (Exception e) {
            logCreateUrlError(request, e);
            return Observable.error(e);
          }
        });
  }

  private static void logCreateUrlError(APIDynimoArtworkRequest request, Exception e) {
    var type = request.getType();
    var videoId = request.getVideoId();
    var country = request.getCountry();
    var locale = request.getLocale();
    var height = request.getHeight();
    var width = request.getWidth();
    var params = request.getParameters();
    var isSecure = request.isSecure();

    logger.error(
        "type={} videoId={} country={} locale={} height={} width={} isSecure={} params={} dynimo createUrl error",
        type,
        videoId,
        country,
        locale,
        height,
        width,
        isSecure,
        params,
        e);
  }
}
