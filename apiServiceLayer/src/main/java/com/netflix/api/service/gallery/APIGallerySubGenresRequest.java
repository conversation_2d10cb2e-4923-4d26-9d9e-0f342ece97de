package com.netflix.api.service.gallery;

/** Request for a list of languages that you can filter genre galleries by. */
public class APIGallerySubGenresRequest {
  private final String listContext;
  private Long genreId;
  private Boolean isKids = false;
  private String locale;
  private boolean supportsCulturalMomentPages;
  private Long navId;
  private boolean includeDownloadsSubGenre = true;

  /**
   * @param listContext - one of {@link ListContext} enum value's name.
   */
  public APIGallerySubGenresRequest(String listContext) {
    this.listContext = listContext;
  }

  public String getListContext() {
    return listContext;
  }

  /** DOCUMENTATION enum - contains a list of valid list context strings. */
  public enum ListContext {
    SUB_GENRES,
    ASSISTIVE_AUDIO_SUB_GENRES
  }

  public APIGallerySubGenresRequest setGenreId(Long genreId) {
    this.genreId = genreId;
    return this;
  }

  public APIGallerySubGenresRequest setIsKids(Boolean kids) {
    isKids = kids;
    return this;
  }

  public APIGallerySubGenresRequest setLocale(String locale) {
    this.locale = locale;
    return this;
  }

  public APIGallerySubGenresRequest setSupportsCulturalMomentPages(boolean supports) {
    this.supportsCulturalMomentPages = supports;
    return this;
  }

  public APIGallerySubGenresRequest setIncludeDownloadsSubGenre(boolean includeDownloadsSubGenre) {
    this.includeDownloadsSubGenre = includeDownloadsSubGenre;
    return this;
  }

  public Long getGenreId() {
    return genreId;
  }

  public Boolean getKids() {
    return isKids;
  }

  public String getLocale() {
    return locale;
  }

  public Long getNavId() {
    return navId;
  }

  public boolean getIncludeDownloadsSubGenre() {
    return includeDownloadsSubGenre;
  }

  /**
   * NavId is the turbo navigational heirarchy id used by a device / UI. The list is at
   * https://turbo.netflix.com/ws/0/onSite/hierarchies#
   *
   * @param navId
   * @return the request to be further decorated.
   */
  public APIGallerySubGenresRequest setNavId(Long navId) {
    this.navId = navId;
    return this;
  }

  public Boolean getSupportsCulturalMomentPages() {
    return supportsCulturalMomentPages;
  }
}
