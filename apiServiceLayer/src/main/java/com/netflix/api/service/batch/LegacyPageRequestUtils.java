package com.netflix.api.service.batch;

import com.netflix.api.platform.JsonUtility;
import com.netflix.gps.client.grps.page.ext.session.common.SessionKey;
import com.netflix.gps.client.grps.page.ext.session.common.SessionKeyParser;
import com.netflix.gpsproto.common.session.protogen.SessionInput;
import com.netflix.gpsproto.page.io.protogen.LegacyPageRequest;
import com.netflix.map.annotation.MapAnnotationConstants;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ust.list.v1.LegacyCachedListRequest;

@Component
public class LegacyPageRequestUtils {

  private final VisitContextFactory visitContextFactory;
  private final SessionKeyParser sessionKeyParser;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  LegacyPageRequestUtils(
      VisitContextFactory visitContextFactory, SessionKeyParser sessionKeyParser) {
    this.visitContextFactory = visitContextFactory;
    this.sessionKeyParser = sessionKeyParser;
  }

  public LegacyPageRequest buildLegacyPageRequest(Map<String, Object> annotations) {
    return LegacyPageRequest.newBuilder()
        .putAllLegacyAnnotations(convertApiAnnotations(annotations))
        .setVisitContext(visitContextFactory.getVisitContext(annotations))
        .setSessionInput(getSessionInput(annotations))
        .build();
  }

  public LegacyCachedListRequest buildLegacyCachedListRequest(Map<String, Object> annotations) {
    return LegacyCachedListRequest.newBuilder()
        .setVisitContext(visitContextFactory.getVisitContext(annotations))
        .setListId((String) annotations.get(MapAnnotationConstants.UUID))
        .build();
  }

  public static Map<String, String> convertApiAnnotations(Map<String, Object> annotations) {
    if (annotations == null) {
      return null;
    }
    return annotations.entrySet().stream()
        .collect(
            Collectors.toMap(
                Map.Entry::getKey,
                entry -> {
                  Object value = entry.getValue();
                  if (value instanceof String s) return s;

                  return JsonUtility.toJson(value);
                }));
  }

  private SessionInput getSessionInput(Map<String, Object> annotations) {
    String uuid = (String) annotations.get(MapAnnotationConstants.UUID);
    if (uuid == null || uuid.isEmpty()) {
      return SessionInput.getDefaultInstance();
    }
    if (isRootUUID(uuid) || sessionKeyParser.parse(uuid).getParentKey().isEmpty()) {
      return SessionInput.newBuilder().setPageSessionId(uuid).build();
    }
    return SessionInput.newBuilder().addSectionSessionIdsV2(uuid).build();
  }

  private boolean isRootUUID(String uuid) {
    boolean result = false;
    if (StringUtils.isNotBlank(uuid)) {
      result = uuid.endsWith("ROOT") || isGpsPageSessionKey(uuid);
    }
    return result;
  }

  private boolean isGpsPageSessionKey(String uuid) {
    if (sessionKeyParser != null && sessionKeyParser.isSessionKey(uuid)) {
      final SessionKey sessionKey = sessionKeyParser.parse(uuid);
      return sessionKey.getParentKey().isEmpty();
    }
    return false;
  }
}
