package dna.api.converters;

import static java.util.concurrent.CompletableFuture.completedFuture;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.netflix.editorialpromotion.protogen.EditorialPromotion;
import com.netflix.editorialpromotion.protogen.EditorialPromotionAsset;
import com.netflix.editorialpromotion.protogen.EditorialPromotionPlacement;
import com.netflix.editorialpromotion.protogen.EditorialPromotionTile;
import com.netflix.editorialpromotion.protogen.EditorialPromotionsResponse;
import dna.api.datasources.EditorialPromotionsHydrator;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class EditorialPromotionConverterTest {
  private final EditorialPromotionsHydrator hydrator = mock(EditorialPromotionsHydrator.class);
  private final EditorialPromotionConverter converter = new EditorialPromotionConverter(hydrator);

  private final EditorialPromotionsResponse editorialResp =
      EditorialPromotionsResponse.newBuilder()
          .addEditorialPromotions(
              EditorialPromotion.newBuilder()
                  .setPromotionId("promotion 1")
                  .setPartnerId("some partner")
                  .setStartDate(1000L)
                  .setEndDate(2000L)
                  .setDeepLink("some deep link")
                  .addPromotionPlacements(
                      EditorialPromotionPlacement.newBuilder()
                          .setPlacementId("some placement")
                          .addEditorialPromotionTiles(
                              EditorialPromotionTile.newBuilder()
                                  .setKey("tile key 1")
                                  .setAssetId(11)
                                  .addAssets(
                                      EditorialPromotionAsset.newBuilder()
                                          .setKey("some asset key 1")
                                          .setUrl("some asset key 1")
                                          .setHeight(100)
                                          .setWidth(200)
                                          .setFileSize(1024)
                                          .setFileType("some file type 1")
                                          .setId(111)
                                          .setRegion("some region 1")
                                          .setCountry("some country 1")))
                          .addEditorialPromotionTiles(
                              EditorialPromotionTile.newBuilder()
                                  .setKey("tile key 2")
                                  .setAssetId(22)
                                  .addAssets(
                                      EditorialPromotionAsset.newBuilder()
                                          .setKey("some asset key 2")
                                          .setUrl("some url 2")
                                          .setHeight(200)
                                          .setWidth(400)
                                          .setFileSize(2048)
                                          .setFileType("some file type 2")
                                          .setId(222)
                                          .setRegion("some region 2")
                                          .setCountry("some country 2")))
                          .addEditorialPromotionTiles(
                              EditorialPromotionTile.newBuilder()
                                  .setKey("tile key 3 - no title!")
                                  .setAssetId(33)
                                  .addAssets(
                                      EditorialPromotionAsset.newBuilder()
                                          .setKey("some asset key 2")
                                          .setUrl("some url 2")
                                          .setHeight(200)
                                          .setWidth(400)
                                          .setFileSize(2048)
                                          .setFileType("some file type 2")
                                          .setId(222)
                                          .setRegion("some region 2")
                                          .setCountry("some country 2"))))
                  .build())
          .build();

  @BeforeEach
  void setup() {
    when(hydrator.getEvidenceForAssetIDs(any(), anyString()))
        .thenReturn(
            completedFuture(
                Map.of(
                    11, new EditorialPromotionsHydrator.Evidence("TITLE 1", "SYNOPSIS 1"),
                    22, new EditorialPromotionsHydrator.Evidence("TITLE 2", "SYNOPSIS 2"))));
  }

  @Test
  void convert_buildsPromotionList() throws ExecutionException, InterruptedException {
    var future = converter.convert("some country id", editorialResp);

    assertNotNull(future);

    var actualEditorials = future.toCompletableFuture().get();
    assertEquals(1, actualEditorials.size());

    var actualEditorial = actualEditorials.getFirst();

    assertEquals("promotion 1", actualEditorial.getPromotionId());
    assertEquals("some partner", actualEditorial.getPartnerId());
    assertEquals(1000L, actualEditorial.getStartDate().longValue());
    assertEquals(2000L, actualEditorial.getEndDate().longValue());
    assertEquals("some deep link", actualEditorial.getDeepLink());

    var actualPlacements = actualEditorial.getPromotionPlacements();
    assertEquals(1, actualPlacements.size());

    var actualPlacement = actualPlacements.getFirst();
    assertEquals("some placement", actualPlacement.getPlacementId());

    var actualTiles = actualPlacement.getTiles();
    assertEquals(3, actualTiles.size());

    var actualTile1 = actualTiles.getFirst();
    assertEquals("tile key 1", actualTile1.getKey());
    assertEquals(11, actualTile1.getAssetId().intValue());
    assertEquals("TITLE 1", actualTile1.getTitle());
    assertEquals("SYNOPSIS 1", actualTile1.getSynopsis());

    var actualAssets1 = actualTile1.getAssets();
    assertEquals(1, actualAssets1.size());

    // tile 1, asset 11: has title and synopsis
    var actualAsset1 = actualAssets1.getFirst();
    assertEquals("some asset key 1", actualAsset1.getKey());
    assertEquals("some asset key 1", actualAsset1.getUrl());
    assertEquals(100, actualAsset1.getHeight().intValue());
    assertEquals(200, actualAsset1.getWidth().intValue());
    assertEquals(1024, actualAsset1.getFileSize().intValue());
    assertEquals("some file type 1", actualAsset1.getFileType());
    assertEquals(111, actualAsset1.getId().intValue());
    assertEquals("some region 1", actualAsset1.getRegion());
    assertEquals("some country 1", actualAsset1.getCountry());

    // tile 2, asset 22: has title and synopsis
    var actualTile2 = actualTiles.get(1);
    assertEquals("tile key 2", actualTile2.getKey());
    assertEquals(22, actualTile2.getAssetId().intValue());
    assertEquals("TITLE 2", actualTile2.getTitle());
    assertEquals("SYNOPSIS 2", actualTile2.getSynopsis());

    var actualAssets2 = actualTile2.getAssets();
    assertEquals(1, actualAssets2.size());

    var actualAsset2 = actualAssets2.getFirst();
    assertEquals("some asset key 2", actualAsset2.getKey());
    assertEquals("some url 2", actualAsset2.getUrl());
    assertEquals(200, actualAsset2.getHeight().intValue());
    assertEquals(400, actualAsset2.getWidth().intValue());
    assertEquals(2048, actualAsset2.getFileSize().intValue());
    assertEquals("some file type 2", actualAsset2.getFileType());
    assertEquals(222, actualAsset2.getId().intValue());
    assertEquals("some region 2", actualAsset2.getRegion());
    assertEquals("some country 2", actualAsset2.getCountry());

    // tile 3, asset 33: no title/synopsis
    var actualTile3 = actualTiles.get(1);
    assertEquals("tile key 2", actualTile3.getKey());
    assertEquals(22, actualTile3.getAssetId().intValue());
    assertEquals("TITLE 2", actualTile3.getTitle());
    assertEquals("SYNOPSIS 2", actualTile3.getSynopsis());

    var actualAssets3 = actualTile3.getAssets();
    assertEquals(1, actualAssets3.size());

    var actualAsset3 = actualAssets3.getFirst();
    assertEquals("some asset key 2", actualAsset3.getKey());
    assertEquals("some url 2", actualAsset3.getUrl());
    assertEquals(200, actualAsset3.getHeight().intValue());
    assertEquals(400, actualAsset3.getWidth().intValue());
    assertEquals(2048, actualAsset3.getFileSize().intValue());
    assertEquals("some file type 2", actualAsset3.getFileType());
    assertEquals(222, actualAsset3.getId().intValue());
    assertEquals("some region 2", actualAsset3.getRegion());
    assertEquals("some country 2", actualAsset3.getCountry());

    verify(hydrator).getEvidenceForAssetIDs(editorialResp, "some country id");
  }

  @Test
  void convert_whenNoPromotions_returnsEmptyList() throws ExecutionException, InterruptedException {
    var future =
        converter.convert("some country id", EditorialPromotionsResponse.getDefaultInstance());

    assertNotNull(future);

    var actualResp = future.toCompletableFuture().get();
    assertTrue(actualResp.isEmpty());

    verify(hydrator)
        .getEvidenceForAssetIDs(
            EditorialPromotionsResponse.getDefaultInstance(), "some country id");
  }

  @Test
  void convert_whenNoPlacements_returnsEmptyList() throws ExecutionException, InterruptedException {
    var editorialSvcResp =
        EditorialPromotionsResponse.newBuilder()
            .addEditorialPromotions(EditorialPromotion.newBuilder().setPromotionId("some promo id"))
            .build();
    var future = converter.convert("some country id", editorialSvcResp);

    assertNotNull(future);

    var actualResp = future.toCompletableFuture().get();
    assertEquals(1, actualResp.size());

    var promotion = actualResp.getFirst();
    assertEquals("some promo id", promotion.getPromotionId());

    assertNotNull(promotion.getPromotionPlacements());
    assertTrue(promotion.getPromotionPlacements().isEmpty());

    verify(hydrator).getEvidenceForAssetIDs(editorialSvcResp, "some country id");
  }

  @Test
  void convert_whenNoTiles_returnsEmptyList() throws ExecutionException, InterruptedException {
    var editorialSvcResp =
        EditorialPromotionsResponse.newBuilder()
            .addEditorialPromotions(
                EditorialPromotion.newBuilder()
                    .setPromotionId("some promo id")
                    .addPromotionPlacements(
                        EditorialPromotionPlacement.newBuilder()
                            .setPlacementId("some placement id")))
            .build();
    var future = converter.convert("some country id", editorialSvcResp);

    assertNotNull(future);

    var actualResp = future.toCompletableFuture().get();
    assertEquals(1, actualResp.size());

    var promotion = actualResp.getFirst();
    assertEquals("some promo id", promotion.getPromotionId());

    var placements = promotion.getPromotionPlacements();
    assertEquals(1, placements.size());

    var placement = placements.getFirst();
    assertEquals("some placement id", placement.getPlacementId());

    assertNotNull(placement.getTiles());
    assertEquals(0, placement.getTiles().size());

    verify(hydrator).getEvidenceForAssetIDs(editorialSvcResp, "some country id");
  }

  @Test
  void convert_whenNoAssets_returnsEmptyList() throws ExecutionException, InterruptedException {
    var editorialSvcResp =
        EditorialPromotionsResponse.newBuilder()
            .addEditorialPromotions(
                EditorialPromotion.newBuilder()
                    .setPromotionId("some promo id")
                    .addPromotionPlacements(
                        EditorialPromotionPlacement.newBuilder()
                            .setPlacementId("some placement id")
                            .addEditorialPromotionTiles(
                                EditorialPromotionTile.getDefaultInstance())))
            .build();
    var future = converter.convert("some country id", editorialSvcResp);

    assertNotNull(future);

    var actualResp = future.toCompletableFuture().get();
    assertEquals(1, actualResp.size());

    var promotion = actualResp.getFirst();
    assertEquals("some promo id", promotion.getPromotionId());

    var placements = promotion.getPromotionPlacements();
    assertEquals(1, placements.size());

    var placement = placements.getFirst();
    assertEquals("some placement id", placement.getPlacementId());

    var tiles = placement.getTiles();
    assertEquals(1, tiles.size());

    var tile = tiles.getFirst();
    assertNull(tile.getAssetId());

    assertNotNull(tile.getAssets());
    assertEquals(0, tile.getAssets().size());

    verify(hydrator).getEvidenceForAssetIDs(editorialSvcResp, "some country id");
  }
}
