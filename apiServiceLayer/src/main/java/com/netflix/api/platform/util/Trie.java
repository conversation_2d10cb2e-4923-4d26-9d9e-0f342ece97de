package com.netflix.api.platform.util;

/** Simple trie implementation adapted from https://forums.oracle.com/thread/2070706 */
class Trie {
  final TrieNode root;

  /** Constructor */
  public Trie() {
    root = new TrieNode();
  }

  /**
   * Adds a word to the Trie
   *
   * @param word
   */
  public void addWord(String word) {
    root.addWord(word.toLowerCase(), 0);
  }

  /**
   * Matches the longest prefix possible from the given word. For example, if the trie contains
   * both: NFAPPL-D1 (esn prefix) NFAPPL-D1-IPAD3=2-5D (full esn)
   *
   * <p>then the following are matches: word = NFAPPL-D1-FOO-BAR (matches esn prefix) word =
   * NFAPPL-D1-IPAD3=2-5D (matches full esn)
   *
   * @param word
   * @return the longest possible match
   */
  public String matchPrefix(String word) {
    String lower = word.toLowerCase();
    TrieNode lastNode = root;
    String match = null;

    for (int i = 0; i < lower.length(); i++) {
      lastNode = lastNode.getNode(lower.charAt(i));

      if (lastNode == null) {
        return match;
      } else if (lastNode.isWord && lastNode.isLeaf) {
        match = lastNode.getWord();
      }
    }

    return match;
  }
}
