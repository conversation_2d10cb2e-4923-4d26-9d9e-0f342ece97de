#!/usr/bin/env bash

function usage() {
  echo "Usage: $(basename $0) /path/to/dna-router/classes"
  exit 1
}

ENDPOINT="/$(whoami)/nodequark/pathEvaluator"
if [ -z "$1" ]
then
  usage
else
  cd $1
  rm dna-router.jar
  jar cvf dna-router.jar *
  metatron curl -a primerblobproducer -X POST -d @dna-router.jar https://primerblobproducer-test.us-east-1.dyntest.netflix.net:7004/REST/primer-blob-side-publish/publish$ENDPOINT
fi
