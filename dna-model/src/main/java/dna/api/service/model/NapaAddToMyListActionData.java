package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NapaAddToMyListActionData extends NapaActionData {
  private Boolean inMyList;

  public Boolean getInMyList() {
    return inMyList;
  }

  public NapaAddToMyListActionData setInMyList(Boolean inMyList) {
    this.inMyList = inMyList;
    return this;
  }
}
