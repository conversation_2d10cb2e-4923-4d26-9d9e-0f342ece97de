package dna.api.util;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Map;
import org.junit.jupiter.api.Test;

class EnumUtilTest {

  enum TestEnum {
    VALUE1,
    VALUE2
  }

  @Test
  void testCreateIndexedEnums() {
    final Map<String, TestEnum> index = EnumUtil.createIndexedEnums(TestEnum.class);
    assertEquals(2, index.size());
    assertEquals(TestEnum.VALUE1, index.get(TestEnum.VALUE1.name()));
    assertEquals(TestEnum.VALUE2, index.get(TestEnum.VALUE2.name()));
  }
}
