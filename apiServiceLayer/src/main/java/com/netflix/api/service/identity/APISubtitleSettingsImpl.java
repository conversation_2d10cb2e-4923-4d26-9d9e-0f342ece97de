package com.netflix.api.service.identity;

import com.google.common.base.MoreObjects;

public class APISubtitleSettingsImpl implements APISubtitleSettings {

  private static final APISubtitleSettings EMPTY = new APISubtitleSettingsImpl();

  private Color characterColor;
  private Opacity characterOpacity;
  private Size characterSize;
  private Style characterStyle;
  private EdgeAttributes characterEdgeAttributes;
  private Color characterEdgeColor;
  private Color backgroundColor;
  private Opacity backgroundOpacity;
  private Color windowColor;
  private Opacity windowOpacity;

  public static APISubtitleSettings empty() {
    return EMPTY;
  }

  APISubtitleSettingsImpl() {}

  @Override
  public void setCharacterColor(Color color) {
    this.characterColor = color;
  }

  @Override
  public Color getCharacterColor() {
    return characterColor;
  }

  @Override
  public void setCharacterOpacity(Opacity opacity) {
    characterOpacity = opacity;
  }

  @Override
  public Opacity getCharacterOpacity() {
    return characterOpacity;
  }

  @Override
  public void setCharacterSize(Size size) {
    characterSize = size;
  }

  @Override
  public Size getCharacterSize() {
    return characterSize;
  }

  @Override
  public void setCharacterStyle(Style style) {
    characterStyle = style;
  }

  @Override
  public Style getCharacterStyle() {
    return characterStyle;
  }

  @Override
  public void setCharacterEdgeAttributes(EdgeAttributes edgeAttributes) {
    characterEdgeAttributes = edgeAttributes;
  }

  @Override
  public EdgeAttributes getCharacterEdgeAttributes() {
    return characterEdgeAttributes;
  }

  @Override
  public void setCharacterEdgeColor(Color color) {
    characterEdgeColor = color;
  }

  @Override
  public Color getCharacterEdgeColor() {
    return characterEdgeColor;
  }

  @Override
  public void setBackgroundColor(Color color) {
    backgroundColor = color;
  }

  @Override
  public Color getBackgroundColor() {
    return backgroundColor;
  }

  @Override
  public void setBackgroundOpacity(Opacity opacity) {
    backgroundOpacity = opacity;
  }

  @Override
  public Opacity getBackgroundOpacity() {
    return backgroundOpacity;
  }

  @Override
  public void setWindowColor(Color color) {
    windowColor = color;
  }

  @Override
  public Color getWindowColor() {
    return windowColor;
  }

  @Override
  public void setWindowOpacity(Opacity opacity) {
    windowOpacity = opacity;
  }

  @Override
  public Opacity getWindowOpacity() {
    return windowOpacity;
  }

  @Override
  public String toString() {
    return MoreObjects.toStringHelper(this)
        .add("characterColor", characterColor)
        .add("characterOpacity", characterOpacity)
        .add("characterSize", characterSize)
        .add("characterStyle", characterStyle)
        .add("characterEdgeAttributes", characterEdgeAttributes)
        .add("characterEdgeColor", characterEdgeColor)
        .add("backgroundColor", backgroundColor)
        .add("backgroundOpacity", backgroundOpacity)
        .add("windowColor", windowColor)
        .add("windowOpacity", windowOpacity)
        .toString();
  }
}
