package com.netflix.api.service.batch;

import com.google.protobuf.Duration;
import com.google.protobuf.util.Durations;
import com.netflix.api.grpc.GrpcCallHelpers.Future;
import com.netflix.type.proto.Videos;
import com.netflix.ust.adapters.USTStatus;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ust.video.core.v1.GetVideoTimecodeRequest;
import ust.video.core.v1.GetVideoTimecodeResponse;
import ust.video.core.v1.VideoAccess;
import ust.video.v1.Timecode;
import ust.video.v1.TimecodeType;
import ust.video.v1.VideoRequest;

@Component
public class USTVideoAdapter {
  private static final Duration ZERO_MS = Durations.fromMillis(0);

  static final Timecode DEFAULT_SKIP_CREDIT_TIMECODE =
      Timecode.newBuilder()
          .setType(TimecodeType.TIMECODE_TYPE_SKIP_CREDIT)
          .setRawType("skipcredits")
          .setStart(ZERO_MS)
          .setEnd(ZERO_MS)
          .build();

  static final Timecode DEFAULT_RECAP_TIMECODE =
      Timecode.newBuilder()
          .setType(TimecodeType.TIMECODE_TYPE_RECAP)
          .setRawType("recap")
          .setStart(ZERO_MS)
          .setEnd(ZERO_MS)
          .build();

  private final VideoAccess videoAccess;

  @Autowired
  public USTVideoAdapter(VideoAccess videoAccess) {
    this.videoAccess = videoAccess;
  }

  public CompletionStage<Map<Integer, List<Timecode>>> getTimecodes(Collection<Integer> videoIds) {
    GetVideoTimecodeRequest request =
        GetVideoTimecodeRequest.newBuilder()
            .setVideoRequest(
                VideoRequest.newBuilder()
                    .addAllVideos(videoIds.stream().map(Videos::toProtobuf).toList()))
            .build();

    return Future.call(videoAccess::getVideoTimecode, request)
        .thenApply(USTVideoAdapter::convertTimecodeResponse);
  }

  static Map<Integer, List<Timecode>> convertTimecodeResponse(GetVideoTimecodeResponse response) {
    return response.getResultsList().stream()
        .filter(timecodeResult -> USTStatus.isOk(timecodeResult.getStatus()))
        .collect(
            Collectors.toMap(
                timecodeResult -> timecodeResult.getKey().getId(),
                timecodeResult -> withDefaults(timecodeResult.getResult().getTimecodesList())));
  }

  private static List<Timecode> withDefaults(List<Timecode> timecodes) {
    List<Timecode> result = timecodes;
    boolean hasSkipCredit = false;
    boolean hasRecap = false;
    for (Timecode timecode : timecodes) {
      switch (timecode.getType()) {
        case TIMECODE_TYPE_SKIP_CREDIT:
          hasSkipCredit = true;
          break;
        case TIMECODE_TYPE_RECAP:
          hasRecap = true;
      }
    }
    if (!hasSkipCredit || !hasRecap) {
      result = new ArrayList<>(timecodes.size() + 2);
      result.addAll(timecodes);
      if (!hasSkipCredit) {
        result.add(DEFAULT_SKIP_CREDIT_TIMECODE);
      }
      if (!hasRecap) {
        result.add(DEFAULT_RECAP_TIMECODE);
      }
    }
    return result;
  }
}
