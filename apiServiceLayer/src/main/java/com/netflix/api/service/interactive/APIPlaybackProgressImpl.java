package com.netflix.api.service.interactive;

public class APIPlaybackProgressImpl implements APIPlaybackProgress {
  public static final APIPlaybackProgressImpl NONE = new APIPlaybackProgressImpl(0);

  private final int progressPercentage;

  APIPlaybackProgressImpl(int progressPercentage) {
    this.progressPercentage = progressPercentage;
  }

  @Override
  public int getProgressPercentage() {
    return progressPercentage;
  }
}
