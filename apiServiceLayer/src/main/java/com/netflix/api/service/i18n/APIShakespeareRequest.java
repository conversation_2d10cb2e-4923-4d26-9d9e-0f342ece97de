package com.netflix.api.service.i18n;

import com.netflix.api.service.identity.APIUser;
import java.util.Optional;

public class APIShakespeareRequest {

  private final APIUser user;
  private final String language;
  private final String vdid;
  private final boolean merged;
  private final boolean keysAsValues;

  private APIShakespeareRequest(
      APIUser user, String language, String vdid, boolean merged, boolean keysAsValues) {
    this.user = user;
    this.language = language;
    this.vdid = vdid;
    this.merged = merged;
    this.keysAsValues = keysAsValues;
  }

  public APIUser getUser() {
    return user;
  }

  public String getLanguage() {
    return language;
  }

  public Optional<String> getVdid() {
    return Optional.ofNullable(vdid);
  }

  public boolean isMerged() {
    return merged;
  }

  public boolean isKeysAsValues() {
    return keysAsValues;
  }

  public static APIShakespeareRequestBuilder builder() {
    return new APIShakespeareRequestBuilder();
  }

  @Override
  public String toString() {
    return "APIShakespeareRequest{"
        + "user="
        + (user == null ? "null" : user.getCustomerGUID())
        + ", language='"
        + language
        + ", vdid='"
        + vdid
        + '\''
        + ", merged="
        + merged
        + '}';
  }

  public static class APIShakespeareRequestBuilder {

    private APIUser user;
    private String language;
    private String vdid;
    private boolean merged = false;
    private boolean keysAsValues = false;

    private APIShakespeareRequestBuilder() {}

    public APIShakespeareRequestBuilder user(APIUser user) {
      this.user = user;
      return this;
    }

    public APIShakespeareRequestBuilder language(String language) {
      this.language = language;
      return this;
    }

    public APIShakespeareRequestBuilder vdid(String vdid) {
      this.vdid = vdid;
      return this;
    }

    public APIShakespeareRequestBuilder merged(boolean merged) {
      this.merged = merged;
      return this;
    }

    public APIShakespeareRequestBuilder keysAsValues(boolean keysAsValues) {
      this.keysAsValues = keysAsValues;
      return this;
    }

    public APIShakespeareRequest build() {
      return new APIShakespeareRequest(user, language, vdid, merged, keysAsValues);
    }

    public String toString() {
      return "APIShakespeareRequest.APIShakespeareRequestBuilder(user="
          + this.user
          + ", language="
          + this.language
          + ", vdid="
          + this.vdid
          + ")";
    }
  }
}
