package com.netflix.api.service.identity;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import com.netflix.api.platform.identity.IdentityResult;

public class APIUserHolderImpl implements APIUserHolder {
  private final APIUser user;

  @AssistedInject
  private APIUserHolderImpl(@Assisted APIUser user) {
    this.user = user;
  }

  @AssistedInject
  @VisibleForTesting
  APIUserHolderImpl(@Assisted IdentityResult result, APIUserFactory userFactory) {
    Preconditions.checkNotNull(result);
    if (result.isSuccess()) {
      this.user = !result.isDeviceBound() ? userFactory.createUser(result, true) : null;
    } else {
      this.user = null;
    }
  }

  @Override
  public APIUser getUser() {
    return user;
  }
}
