package dna.api.datasources;

import static com.netflix.myratings.protogen.ThumbRatingV2Value.THUMBS_V2_DOWN;
import static com.netflix.myratings.protogen.ThumbRatingV2Value.THUMBS_V2_UNRATED;
import static com.netflix.myratings.protogen.ThumbRatingV2Value.THUMBS_V2_WAY_UP;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.netflix.api.platform.MockPropertyRepositoryConfig;
import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.myratings.protogen.EntityThumbRating;
import com.netflix.myratings.protogen.MyRatingsQueryServiceGrpc.MyRatingsQueryServiceStub;
import com.netflix.myratings.protogen.QueryThumbRatingsReply;
import com.netflix.myratings.protogen.QueryThumbRatingsRequest;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.introspect.PassportIdentityFactory;
import com.netflix.passport.introspect.PassportIntrospectionException;
import com.netflix.passport.introspect.PassportIntrospector;
import com.netflix.passport.introspect.SingletonPassportIntrospectorFactory;
import com.netflix.passport.protobuf.Passport;
import com.netflix.server.context.RequestContext;
import com.netflix.type.proto.Videos;
import com.netflix.type.proto.Visitors;
import com.netflix.type.protogen.BasicTypes.Country;
import com.netflix.type.protogen.BasicTypes.EntityStruct;
import com.netflix.type.protogen.BasicTypes.Locale;
import io.grpc.stub.StreamObserver;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import netflix.context.Context;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class RatingsDataSourceHandlerTest {
  private final MyRatingsQueryServiceStub stub = mock(MyRatingsQueryServiceStub.class);
  private final SingletonPassportIntrospectorFactory introspectorFactory =
      mock(SingletonPassportIntrospectorFactory.class);
  private final PassportIdentityFactory identityFactory = mock(PassportIdentityFactory.class);

  private final RatingsDataSourceHandler handler =
      new RatingsDataSourceHandler(stub, introspectorFactory, identityFactory);

  private final PassportIntrospector introspector = mock(PassportIntrospector.class);
  private final PassportIdentity identity = mock(PassportIdentity.class);
  private final Passport passport = mock(Passport.class);

  private final Long customerId = 123L;

  @BeforeAll
  static void beforeClass() {
    MockPropertyRepositoryConfig config = new MockPropertyRepositoryConfig();
    config.init();
  }

  @BeforeEach
  void before() throws Exception {
    BindingContexts.push();
    CurrentMicrocontext.set(
        Context.newBuilder()
            .setCountry(Country.newBuilder().setId("some country"))
            .addLocales(Locale.newBuilder().setId("some locale").build())
            .build());

    when(introspector.getPassport()).thenReturn(passport);
    when(identity.getProfileId()).thenReturn(Optional.of(customerId));
    when(introspectorFactory.createPassportIntrospector(any(RequestContext.class)))
        .thenReturn(Optional.of(introspector));
    when(identityFactory.createPassportIdentity(any(Passport.class))).thenReturn(identity);

    setupMyRatingStubWithOneRating();
  }

  @AfterEach
  void after() {
    BindingContexts.pop();
  }

  @Test
  void thumbRatings()
      throws ExecutionException, InterruptedException, PassportIntrospectionException {
    setupMyRatingsStubWithTwoRatings();

    final var entity1 = EntityStruct.newBuilder().setVideo(Videos.toProtobuf(1)).build();
    final var entity2 = EntityStruct.newBuilder().setVideo(Videos.toProtobuf(2)).build();

    final var future = handler.thumbRatings(Set.of(entity1, entity2)).toCompletableFuture();

    final Map<EntityStruct, EntityThumbRating> results = future.get();

    assertEquals(2, results.size());

    assertTrue(results.containsKey(entity1));
    var entity1ThumbRating = results.get(entity1);
    assertEquals(THUMBS_V2_WAY_UP, entity1ThumbRating.getRating());
    assertEquals(999L, entity1ThumbRating.getTimestamp());
    assertEquals(entity1, entity1ThumbRating.getEntity());

    assertTrue(results.containsKey(entity2));
    var entity2ThumbRating = results.get(entity2);
    assertEquals(THUMBS_V2_DOWN, entity2ThumbRating.getRating());
    assertEquals(1000L, entity2ThumbRating.getTimestamp());
    assertEquals(entity2, entity2ThumbRating.getEntity());

    verify(introspectorFactory).createPassportIntrospector(any(RequestContext.class));
    verify(identity).getProfileId();
    verify(stub)
        .queryThumbRatings(
            eq(
                QueryThumbRatingsRequest.newBuilder()
                    .setVisitor(Visitors.toProtobuf(customerId))
                    .setCountry(Country.newBuilder().setId("some country"))
                    .setLocale(Locale.newBuilder().setId("some locale"))
                    .addAllEntities(Set.of(entity1, entity2))
                    .build()),
            any());
  }

  @Test
  void testRatings_whenNoPassportIdentity()
      throws ExecutionException, InterruptedException, PassportIntrospectionException {
    when(introspectorFactory.createPassportIntrospector(any(RequestContext.class)))
        .thenReturn(Optional.empty());

    final var entity = EntityStruct.newBuilder().setVideo(Videos.toProtobuf(1)).build();
    final var future = handler.thumbRatings(Set.of(entity)).toCompletableFuture();

    var results = future.get();

    assertEquals(1, results.size());

    assertTrue(results.containsKey(entity));

    var entityThumbRating = results.get(entity);
    assertEquals(THUMBS_V2_UNRATED, entityThumbRating.getRating());
    assertEquals(0L, entityThumbRating.getTimestamp());
    assertEquals(entity, entityThumbRating.getEntity());

    verify(introspectorFactory).createPassportIntrospector(any(RequestContext.class));
    verifyNoInteractions(stub);
    verifyNoInteractions(introspector);
  }

  @Test
  void testRatings_whenPassportIntrospectorThrows()
      throws ExecutionException, InterruptedException, PassportIntrospectionException {
    when(introspectorFactory.createPassportIntrospector(any(RequestContext.class)))
        .thenThrow(new PassportIntrospectionException());

    final var entity = EntityStruct.newBuilder().setVideo(Videos.toProtobuf(1)).build();

    final var future = handler.thumbRatings(Set.of(entity)).toCompletableFuture();

    var results = future.get();

    assertEquals(1, results.size());

    assertTrue(results.containsKey(entity));

    var entityThumbRating = results.get(entity);
    assertEquals(THUMBS_V2_UNRATED, entityThumbRating.getRating());
    assertEquals(0L, entityThumbRating.getTimestamp());
    assertEquals(entity, entityThumbRating.getEntity());

    verify(introspectorFactory).createPassportIntrospector(any(RequestContext.class));
    verifyNoInteractions(stub);
    verifyNoInteractions(introspector);
  }

  @Test
  void testRatings_whenPassportIdentityMissingCustomerId()
      throws ExecutionException, InterruptedException, PassportIntrospectionException {
    when(identity.getProfileId()).thenReturn(Optional.empty());

    final var entity = EntityStruct.newBuilder().setVideo(Videos.toProtobuf(1)).build();

    final var future = handler.thumbRatings(Set.of(entity)).toCompletableFuture();

    var results = future.get();

    assertEquals(1, results.size());

    assertTrue(results.containsKey(entity));

    var entityThumbRating = results.get(entity);
    assertEquals(THUMBS_V2_UNRATED, entityThumbRating.getRating());
    assertEquals(0L, entityThumbRating.getTimestamp());
    assertEquals(entity, entityThumbRating.getEntity());

    verify(introspectorFactory).createPassportIntrospector(any(RequestContext.class));
    verifyNoInteractions(stub);
    verify(identity).getProfileId();
  }

  @Test
  void testRatings_noLocale()
      throws ExecutionException, InterruptedException, PassportIntrospectionException {
    CurrentMicrocontext.set(
        Context.newBuilder().setCountry(Country.newBuilder().setId("some other country")).build());

    final var entity = EntityStruct.newBuilder().setVideo(Videos.toProtobuf(1)).build();

    final var future = handler.thumbRatings(Set.of(entity)).toCompletableFuture();

    var results = future.get();

    assertEquals(1, results.size());

    assertTrue(results.containsKey(entity));

    var entityThumbRating = results.get(entity);
    assertEquals(THUMBS_V2_WAY_UP, entityThumbRating.getRating());
    assertEquals(999L, entityThumbRating.getTimestamp());
    assertEquals(entity, entityThumbRating.getEntity());

    verify(introspectorFactory).createPassportIntrospector(any(RequestContext.class));
    verify(stub)
        .queryThumbRatings(
            eq(
                QueryThumbRatingsRequest.newBuilder()
                    .setVisitor(Visitors.toProtobuf(customerId))
                    .setCountry(Country.newBuilder().setId("some other country"))
                    .addAllEntities(Set.of(entity))
                    .build()),
            any());
  }

  private void setupMyRatingStubWithOneRating() {
    doAnswer(
            answer -> {
              final StreamObserver<QueryThumbRatingsReply> streamObs = answer.getArgument(1);
              streamObs.onNext(
                  QueryThumbRatingsReply.newBuilder()
                      .addRatings(
                          EntityThumbRating.newBuilder()
                              .setRating(THUMBS_V2_WAY_UP)
                              .setEntity(
                                  EntityStruct.newBuilder().setVideo(Videos.toProtobuf(1)).build())
                              .setTimestamp(999L)
                              .build())
                      .build());
              streamObs.onCompleted();
              return null;
            })
        .when(stub)
        .queryThumbRatings(any(), any());
  }

  private void setupMyRatingsStubWithTwoRatings() {
    doAnswer(
            answer -> {
              final StreamObserver<QueryThumbRatingsReply> streamObs = answer.getArgument(1);
              streamObs.onNext(
                  QueryThumbRatingsReply.newBuilder()
                      .addRatings(
                          EntityThumbRating.newBuilder()
                              .setRating(THUMBS_V2_WAY_UP)
                              .setEntity(
                                  EntityStruct.newBuilder().setVideo(Videos.toProtobuf(1)).build())
                              .setTimestamp(999L)
                              .build())
                      .addRatings(
                          EntityThumbRating.newBuilder()
                              .setRating(THUMBS_V2_DOWN)
                              .setEntity(
                                  EntityStruct.newBuilder().setVideo(Videos.toProtobuf(2)).build())
                              .setTimestamp(1000L)
                              .build())
                      .build());
              streamObs.onCompleted();
              return null;
            })
        .when(stub)
        .queryThumbRatings(any(), any());
  }
}
