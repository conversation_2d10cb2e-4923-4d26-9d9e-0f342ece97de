package dna.api.service.model;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.netflix.demograph.graph.Atom;
import java.util.HashMap;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

/** @deprecated do not use */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class AnnotatedLocale implements GraphData {
  /**
   * This method is only provided here for debugging and development purposes. Don't base production
   * code on this method - it's an unstable API. Subject to change
   */
  public Atom<Object> __all;

  public Locale locale;

  private HashMap<String, Object> annotationToValue;

  public Atom<Object> get__all() {
    return __all;
  }

  public AnnotatedLocale set__all(Atom<Object> __all) {
    this.__all = __all;
    return this;
  }

  @JsonAnyGetter
  public HashMap<String, Object> annotationToValue() {
    return this.annotationToValue;
  }

  public Object getAnnotation(String annotation) {
    return this.annotationToValue != null ? this.annotationToValue.get(annotation) : null;
  }

  @JsonAnySetter
  public void setAnnotation(String key, Object value) {
    if (annotationToValue == null) {
      annotationToValue = new HashMap<>();
    }
    this.annotationToValue.put(key, value);
  }
}
