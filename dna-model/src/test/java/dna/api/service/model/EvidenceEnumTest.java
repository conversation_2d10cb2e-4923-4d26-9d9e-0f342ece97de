package dna.api.service.model;

import static dna.api.service.model.EnumChecker.noMissingValues;
import static dna.api.service.model.EnumChecker.noMissingValuesSkippingUnrecognized;

import org.junit.jupiter.api.Test;

class EvidenceEnumTest {

  @Test
  void evidenceCanvas() {
    // make sure that we have all the enum values that text evidence does, otherwise add to
    // api.graphql and
    // run ./gradlew generateFalcor
    noMissingValues(
        com.netflix.evidence.protogen.EvidenceCanvas.values(),
        EvidenceCanvas.class,
        "dna-router/src/main/resources/api.graphql");
  }

  @Test
  void evidenceType() {
    // make sure that we have all the enum values that text evidence does, otherwise add to
    // api.graphql and
    // run ./gradlew generateFalcor
    noMissingValues(
        com.netflix.evidence.protogen.EvidenceType.values(),
        EvidenceType.class,
        "dna-router/src/main/resources/api.graphql");
  }

  @Test
  void textMessageClassification() {
    // make sure that we have all the enum values that text evidence does, otherwise add to
    // video.graphql and
    // run ./gradlew generateFalcor
    noMissingValues(
        com.netflix.evidence.protogen.Classification.values(),
        TextMessageClassification.class,
        "dna-router/src/main/resources/video.graphql");
  }

  @Test
  void uiContext() {
    // make sure that we have all the enum values that text evidence does, otherwise add to
    // api.graphql and
    // run ./gradlew generateFalcor
    noMissingValuesSkippingUnrecognized(
        com.netflix.evidence.protogen.UiContext.values(),
        UIContext.class,
        "dna-router/src/main/resources/api.graphql");
  }

  @Test
  void staffPicksEvidence() {
    noMissingValuesSkippingUnrecognized(
        com.netflix.evidence.protogen.StaffPicksEvidenceTypes.values(),
        StaffPicksEvidenceTypes.class,
        "dna-router/src/main/resources/api.graphql");
  }

  @Test
  void socialProofEvidenceType() {
    noMissingValuesSkippingUnrecognized(
        com.netflix.evidence.protogen.SocialProofEvidenceType.values(),
        SocialProofEvidenceTypes.class,
        "dna-router/src/main/resources/api.graphql");
  }
}
