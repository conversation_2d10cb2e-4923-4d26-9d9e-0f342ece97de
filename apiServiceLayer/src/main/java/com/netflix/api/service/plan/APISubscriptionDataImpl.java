package com.netflix.api.service.plan;

import com.netflix.membership.memberdata.protogen.MemberData;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneId;
import java.util.Date;

public class APISubscriptionDataImpl implements APISubscriptionData {

  private final MemberData data;

  public APISubscriptionDataImpl(MemberData data) {
    this.data = data;
  }

  @Override
  public Integer getMemberDays() {
    return data.getOptionalStartTs()
        .map(startTs -> periodBetweenThenAndNow(startTs).getDays())
        .orElse(-1);
  }

  @Override
  public Integer getMemberMonths() {
    return data.getOptionalStartTs()
        .map(startTs -> periodBetweenThenAndNow(startTs).getMonths())
        .orElse(-1);
  }

  @Override
  public Long pendingPlanId() {
    return data.getOptionalPendingPlanId().orElse(null);
  }

  @Override
  public Date getBillingPeriodEndDate() {
    return data.getOptionalPaidThroughDate().map(Date::new).orElse(null);
  }

  private static Period periodBetweenThenAndNow(long then) {
    return Period.between(
        LocalDate.from(LocalDateTime.ofInstant(Instant.ofEpochMilli(then), ZoneId.systemDefault())),
        LocalDate.now());
  }
}
