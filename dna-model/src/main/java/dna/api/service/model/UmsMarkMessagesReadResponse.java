package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class UmsMarkMessagesReadResponse {
  private Integer markedMessagesCount;

  public Integer getMarkedMessagesCount() {
    return markedMessagesCount;
  }

  public UmsMarkMessagesReadResponse setMarkedMessagesCount(Integer markedMessagesCount) {
    this.markedMessagesCount = markedMessagesCount;
    return this;
  }
}
