package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaTrackingData {
  /** The tracking ID of this element, see http://go/trackid for more information on track IDs. */
  private Long trackId;

  /**
   * A mapping between any device specified tracking strings to the respective tracking ID," see
   * http://go/trackid for more information on track IDs.
   */
  private Map<String, Long> supplementalTrackIds;

  /**
   * The tracking system tracker path for this element, see http://go/trackid for more information.
   */
  private String trackerPath;

  public Long getTrackId() {
    return trackId;
  }

  public Map<String, Long> getSupplementalTrackIds() {
    return supplementalTrackIds;
  }

  public String getTrackerPath() {
    return trackerPath;
  }

  public NapaTrackingData setTrackId(Long trackId) {
    this.trackId = trackId;
    return this;
  }

  public NapaTrackingData setSupplementalTrackIds(Map<String, Long> supplementalTrackIds) {
    this.supplementalTrackIds = supplementalTrackIds;
    return this;
  }

  public NapaTrackingData setTrackerPath(String trackerPath) {
    this.trackerPath = trackerPath;
    return this;
  }
}
