package com.netflix.api.service.storage;

import java.util.Map;
import java.util.Set;
import rx.Single;

public interface APIStorageService {

  /**
   * Retrieves TVUI from the EVCACHE_TVUI_STARTUP evcache app. Note: this is a temporary solution
   * until we integrate a generic data gateway solution, at which point this method will be
   * deprecated
   *
   * @param key the key with which to fetch the startup data within the tvui application
   * @return a Single with the startup data or an onError if the call failed
   * @deprecated use batched version instead
   */
  @Deprecated
  Single<String> getTVUIStartupData(String key);

  /**
   * Retrieves TVUI from the EVCACHE_TVUI_STARTUP evcache app. Note: this is a temporary solution
   * until we integrate a generic data gateway solution, at which point this method will be
   * deprecated
   *
   * @param keys the keys with which to fetch the startup data within the tvui application
   * @return a Single with the startup data or an onError if the call failed
   */
  Single<Map<String, String>> getTVUIStartupData(Set<String> keys);

  /**
   * @param key used for DET property lookup (DNA-1416)
   * @returna Single with the property value or an onError if the call failed
   */
  Single<String> getDETPartnerData(String key);
}
