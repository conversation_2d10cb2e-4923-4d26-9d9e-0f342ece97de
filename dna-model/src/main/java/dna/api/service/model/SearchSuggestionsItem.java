package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SearchSuggestionsItem implements GraphData {
  /** @dotnext com.netflix.api.service.searchnew.APISearchItem#getDisplayString */
  public String displayName;

  /** @dotnext com.netflix.api.service.searchnew.APISearchItem#getEntityId */
  public String entityId;

  /** @dotnext com.netflix.api.service.searchnew.APISearchItem#getType */
  public String kind;

  /** @dotnext com.netflix.api.service.searchnew.APISearchItem#getOptionalAttributes */
  public Atom<Object> optionalAttributes;

  public SearchResults relatedSearchResults;

  public SearchRequestByEntity searchRequestByEntity;

  public Video video;

  public String getDisplayName() {
    return displayName;
  }

  public String getEntityId() {
    return entityId;
  }

  public String getKind() {
    return kind;
  }

  public Atom<Object> getOptionalAttributes() {
    return optionalAttributes;
  }

  public SearchSuggestionsItem setDisplayName(String displayName) {
    this.displayName = displayName;
    return this;
  }

  public SearchSuggestionsItem setEntityId(String entityId) {
    this.entityId = entityId;
    return this;
  }

  public SearchSuggestionsItem setKind(String kind) {
    this.kind = kind;
    return this;
  }

  public SearchSuggestionsItem setOptionalAttributes(Atom<Object> optionalAttributes) {
    this.optionalAttributes = optionalAttributes;
    return this;
  }
}
