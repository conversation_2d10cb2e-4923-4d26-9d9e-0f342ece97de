package com.netflix.api.service.identity;

import com.netflix.api.annotations.ExcludeFromCoverage;
import java.util.List;

public class APIRegistrationException extends Exception {

  private static final long serialVersionUID = -8434495544891639376L;
  private final List<Reason> reasons;

  @ExcludeFromCoverage
  public enum Reason {
    NULL_EMAIL(),
    NULL_PASSWORD(),
    NULL_REGCOUNTRY(),
    NULL_PRIMARYLOCALE(),
    INVALID_COOKIES(),
    EMAIL_TOO_SHORT(),
    EMAIL_TOO_LONG(),
    EMAIL_INVALID_CHARS(),
    PASSWORD_TOO_SHORT(),
    PASSWORD_TOO_LONG(),
    PASSWORD_INVALID_CHARS(),
    INVALID_REGCOUNTRY(),
    INVALID_PRIMARYLOCALE(),
    FIRSTNAME_TOO_SHORT(),
    FIRSTNAME_TOO_LONG(),
    FIRSTNAME_CONTAINS_BAD_LANGUAGE(),
    LASTNAME_TOO_SHORT(),
    LASTNAME_TOO_LONG(),
    LASTNAME_CONTAINS_BAD_LANGUAGE(),
    NULL_COOKIES_PASSED(),
    ACCT_ALREADY_EXISTS(),
    SUBSCRIBER_COMMUNICATION_ERROR(),
    INTERNAL_ERROR(),
    SUBSCRIBER_ADD_HANDLE_FAILED(),
    UNKNOWN_REASON(),
    FACEBOOK_MAPPING_ERROR(),
    EUAT_INVALID(),
    INVALID_CSTOKEN(),
    INVALID_CUSTOMER_ID(),
    ACTION_DISALLOWED(),
    PROFILENAME_TOO_LONG(),
    XBOX_SAML_INVALID(),
    INVALID_PHONE_NUMBER(),
    PASSWORD_UPDATE_TO_SAME_VAL(),
    WRONG_PASSWORD();

    Reason() {}
  }

  public APIRegistrationException(String message, Reason reason, Throwable cause) {
    super(message, cause);
    reasons = List.of(reason);
  }

  public List<Reason> getReasons() {
    return reasons;
  }
}
