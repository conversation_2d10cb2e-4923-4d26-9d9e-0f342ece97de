package com.netflix.api.service.ums;

import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.tela.interstitials.protogen.GetNetflixInAppInterstitialsReply;
import com.netflix.tela.interstitials.protogen.GetNetflixInAppInterstitialsRequest;
import com.netflix.tela.interstitials.protogen.TelaInterstitialsServiceGrpc;
import com.netflix.ums.common.model.UmsResponseCode;
import com.netflix.ums.common.requestresponse.GetSatisfactionSurveysRequest;
import com.netflix.ums.common.requestresponse.GetSatisfactionSurveysResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
public class APIAsyncGetSatisfactionSurveysTelaImpl {

  private final TelaInterstitialsServiceGrpc.TelaInterstitialsServiceStub
      telaInterstitialsServiceStub;

  private static final APISatisfactionSurveysResponse FallbackResponse;

  static {
    GetSatisfactionSurveysResponse errorResponse = new GetSatisfactionSurveysResponse();
    errorResponse.setMsgs(Collections.emptyList());
    errorResponse.setResponseCode(UmsResponseCode.SUCCESS);
    FallbackResponse = new APISatisfactionSurveysResponseImpl(errorResponse);
  }

  @Autowired
  public APIAsyncGetSatisfactionSurveysTelaImpl(
      @GrpcSpringClient("tela")
          TelaInterstitialsServiceGrpc.TelaInterstitialsServiceStub telaInterstitialsServiceStub) {
    this.telaInterstitialsServiceStub = telaInterstitialsServiceStub;
  }

  public Observable<APISatisfactionSurveysResponse> getSurveys(
      APISatisfactionSurveysRequest apiSatisfactionSurveysRequest) {
    GetSatisfactionSurveysRequest satisfactionSurveysRequest =
        APISatisfactionSurveysRequestImpl.createFrom(apiSatisfactionSurveysRequest);
    GetNetflixInAppInterstitialsRequest request = createFrom(satisfactionSurveysRequest);

    return RxObservable.defer(telaInterstitialsServiceStub::getNetflixInAppInterstitials, request)
        .map(this::processTelaResponse)
        .onErrorReturn(throwable -> FallbackResponse);
  }

  @SuppressWarnings({"deprecation"})
  private GetNetflixInAppInterstitialsRequest createFrom(
      GetSatisfactionSurveysRequest satisfactionSurveysRequest) {
    return GetNetflixInAppInterstitialsRequest.newBuilder()
        .setProfileId(satisfactionSurveysRequest.getCustomerId())
        .setCountry(satisfactionSurveysRequest.getCountry().getId())
        .setLocale(satisfactionSurveysRequest.getLocale().toLanguageTag())
        .addAlertTags("SURVEY")
        .setAppVersion(
            Optional.ofNullable(satisfactionSurveysRequest.getAppVersion()).orElse("0.0.0"))
        .setUiPlatform(
            Optional.ofNullable(satisfactionSurveysRequest.getUiPlatform()).orElse("unknown"))
        .build();
  }

  @SuppressWarnings({"unchecked"})
  private APISatisfactionSurveysResponse processTelaResponse(
      GetNetflixInAppInterstitialsReply reply) {
    GetSatisfactionSurveysResponse response = new GetSatisfactionSurveysResponse();
    try {
      Map<String, Object> map =
          APIUniversalMessagingServiceImpl.UMS_MAPPER.readValue(reply.getLegacyJson(), Map.class);
      List<Map<String, Object>> alerts = (List<Map<String, Object>>) map.get("alerts");
      if (alerts == null) {
        throw new Exception("No \"alerts\" field in the Tela response");
      }
      response.setMsgs(alerts);
      response.setResponseCode(UmsResponseCode.SUCCESS);
    } catch (Exception e) {
      response.setMsgs(List.of());
      response.setResponseCode(UmsResponseCode.FAILED);
    }
    return new APISatisfactionSurveysResponseImpl(response);
  }
}
