package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NapaPostplayPreviewNSection extends NapaPostplaySection {
  private List<NapaPostplayItemData> videoItems;

  public List<NapaPostplayItemData> getVideoItems() {
    return videoItems;
  }

  public NapaPostplayPreviewNSection setVideoItems(List<NapaPostplayItemData> videoItems) {
    this.videoItems = videoItems;
    return this;
  }
}
