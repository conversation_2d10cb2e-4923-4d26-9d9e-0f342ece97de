package com.netflix.api.service.list;

import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.service.APICacheExpirationException;
import com.netflix.api.service.APIClientCapabilitiesInternal;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.service.ListItem;
import com.netflix.api.service.batch.USTListAdapter;
import com.netflix.api.service.batch.USTListGatekeeper;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.map.annotation.MapAnnotationConstants;
import com.netflix.map.datatypes.MapResponse;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Registry;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
public class APIMAPLolomoServiceImpl implements APILolomoService {

  private final APIMAPListFactory mapListFactory;
  private final USTListGatekeeper listGatekeeper;
  private final USTListAdapter listAdapter;

  private final Counter prefixedListtokenCall;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public APIMAPLolomoServiceImpl(
      APIMAPListFactory mapListFactory,
      USTListGatekeeper listGatekeeper,
      USTListAdapter listAdapter,
      Registry registry) {
    this.mapListFactory = mapListFactory;
    this.listGatekeeper = listGatekeeper;
    this.listAdapter = listAdapter;
    this.prefixedListtokenCall = registry.counter("COUNTERS-LOLOMOSERVICE.PREFIXED.LISTTOKEN");
  }

  private APILolomoList<? extends APIEntity> refreshLolomo(
      ListRefreshRequest refreshRequest, APIUser user) {
    final Map<String, Object> annotations =
        USTListAdapter.getAccountProfileInfoToAnnotations(
            APIUserUtil.getAccountProfileRemote(user));
    annotations.put("country", refreshRequest.getCountry().getId());
    if (refreshRequest.getEsn() == null) {
      return new APILolomoListWithError<>(
          new APIServiceRuntimeException(
              "ESN is required - pass in either double bound cookies or an esn override as a request parameter or header."));
    }
    if (refreshRequest.getUser() == null) {
      return new APILolomoListWithError<>(
          new APIServiceRuntimeException(
              "User is required - pass in either user/double bound cookies."));
    }

    annotations.put("esn", refreshRequest.getEsn());
    annotations.put("deviceId", refreshRequest.getEsn());
    annotations.put("visitor", APIUserUtil.getCustomerId(refreshRequest.getUser()));
    if (refreshRequest.getListContext() != null) {
      annotations.put("listContext", refreshRequest.getListContext().name());
    } else {
      return new APILolomoListWithError<>(
          new APIServiceRuntimeException(
              "ListContext is required - set a non-null ListContext in the ListRefreshRequest."));
    }

    String listId = refreshRequest.getListId();
    if (listId != null) {
      String uuid = stripPrefix(listId);
      annotations.put("listUuid", uuid);
    } else {
      return new APILolomoListWithError<>(
          new APIServiceRuntimeException("Set a non-null id of the list you want to refresh."));
    }
    if (refreshRequest.getRootLolomoId() != null) {
      annotations.put("rootUUID", refreshRequest.getRootLolomoId());
    } else {
      return new APILolomoListWithError<>(
          new APIServiceRuntimeException(
              "Set a non-null id of the root lolomo that the list you want to refresh belongs to."));
    }
    annotations.put("ISKIDS", refreshRequest.isForKids());

    // Add interactive title annotations
    APIClientCapabilitiesInternal.get(APIRequest.getCurrentRequest())
        .ifPresent(
            apiClientCapabilitiesInternal -> {
              if (apiClientCapabilitiesInternal.currentInteractiveTitlesPresent()) {
                annotations.put(
                    APIClientCapabilitiesInternal.INTERACTIVE_VIDEOS_FEATURE,
                    apiClientCapabilitiesInternal.getAllInteractiveTitlesImmutable());
              }
            });

    Observable<MapResponse> observable;

    String method = "APIMAPLolomoServiceImpl.refreshLolomo";
    if (listGatekeeper.shouldCallUstForList(listId)) {
      long ts = listAdapter.markUstCall(method);
      observable = listAdapter.refreshList(annotations, ts).toObservable();
    } else {
      var t =
          new APICacheExpirationException(
              "Map cache miss", new UnsupportedOperationException("Unsupported listId format"));
      observable = Observable.error(t);
    }
    return mapListFactory.getSingleRow(observable);
  }

  // TODO Deprecate once the cutover to async is complete
  @Override
  public Observable<ListItem<APILolomoList<? extends APIEntity>>> refreshLolomoList(
      final ListRefreshRequest request, APIUser user) {
    APILolomoList<? extends APIEntity> lolomoList = refreshLolomo(request, user);
    Observable<Integer> listIndex =
        lolomoList
            .getAnnotations()
            .filter(ann -> ann.name().equals(MapAnnotationConstants.LIST_INDEX))
            .map(ann -> Integer.valueOf((String) ann.value()))
            .singleOrDefault(0);
    return listIndex.map(li -> new ListItem<>(lolomoList, li));
  }

  private String stripPrefix(String token) {
    if (token == null) return null;
    int indexOf = token.indexOf(':');
    if (indexOf == -1) return token;
    prefixedListtokenCall.increment();
    return token.substring(indexOf + 1);
  }

  @Override
  public ListRefreshRequest buildListRefreshRequest(
      String listId, String rootLolomoId, APILolomoList.ListContext listContext) {
    return new ListRefreshRequestImpl(listId, rootLolomoId, listContext);
  }
}
