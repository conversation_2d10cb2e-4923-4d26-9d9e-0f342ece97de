package com.netflix.api.service.account;

import com.netflix.membership.skuservice.protogen.Plan;

public class APIPlanImpl implements APIPlan {
  private final Plan plan;

  APIPlanImpl(Plan plan) {
    this.plan = plan;
  }

  @Override
  public APIPlanVideoQuality getVideoQuality() {
    return APIPlanVideoQuality.valueOf(plan.getVideoQuality().name());
  }

  @Override
  public int getMaxConcurrentStreams() {
    return plan.getMaxConcurrentStream();
  }

  @Override
  public String toString() {
    return (((("APIPlan{" + "plan=") + plan.getName()) + ", description=") + plan.getDescription())
        + '}';
  }
}
