package dna.api.datasources;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.test.utils.TestCurrentMicrocontext;
import com.netflix.microcontext.test.utils.TestMicrocontext;
import com.netflix.textevidence.protogen.SynopsisType;
import com.netflix.type.protogen.BasicTypes;
import java.util.Set;
import netflix.context.Context;
import netflix.context.user.User;
import netflix.context.user.UserContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class GetSynopsisRequestTest {
  @BeforeEach
  void before() {
    BindingContexts.push();
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setUser(UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(999)).build())
            .setContext(
                Context.newBuilder()
                    .setCountry(BasicTypes.Country.newBuilder().setId("US"))
                    .addLocales(BasicTypes.Locale.newBuilder().setId("en-US"))
                    .build()));
  }

  @AfterEach
  void after() {
    BindingContexts.pop();
  }

  @Test
  void createHookRequest_whenLocaleMissing_throwsIllegalArgument() {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setUser(UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(999)).build())
            .setContext(
                Context.newBuilder()
                    .setCountry(BasicTypes.Country.newBuilder().setId("US"))
                    .build()));

    var request = new GetSynopsisRequest(Set.of(1, 2, 3), CurrentMicrocontext.get());
    assertNotNull(request);

    try {
      request.createHookRequest();
      fail("expected exception to be thrown");
    } catch (IllegalArgumentException e) {
      assertTrue(
          e.getMessage().contains("Must provide locale"),
          "expected \"Must provide locale\", got: " + e.getMessage());
    }
  }

  @Test
  void createHookRequest_whenLocaleIsDefaultValue_throwsIllegalArgument() {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setUser(UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(999)).build())
            .setContext(
                Context.newBuilder()
                    .addLocales(BasicTypes.Locale.getDefaultInstance())
                    .setCountry(BasicTypes.Country.newBuilder().setId("US"))
                    .build()));

    var request = new GetSynopsisRequest(Set.of(1, 2, 3), CurrentMicrocontext.get());
    assertNotNull(request);

    try {
      request.createHookRequest();
      fail("expected exception to be thrown");
    } catch (IllegalArgumentException e) {
      assertTrue(
          e.getMessage().contains("Must provide locale"),
          "expected \"Must provide locale\", got: " + e.getMessage());
    }
  }

  @Test
  void createHookRequest_whenCountryMissing_throwsIllegalArgument() {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setUser(UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(999)).build())
            .setContext(
                Context.newBuilder()
                    .addLocales(BasicTypes.Locale.newBuilder().setId("en-US"))
                    .build()));

    var request = new GetSynopsisRequest(Set.of(1, 2, 3), CurrentMicrocontext.get());
    assertNotNull(request);

    try {
      request.createHookRequest();
      fail("expected exception to be thrown");
    } catch (IllegalArgumentException e) {
      assertTrue(
          e.getMessage().contains("Must provide country"),
          "expected \"Must provide country\", got: " + e.getMessage());
    }
  }

  @Test
  void createHookRequest_returnsGetSynopsisRequest() {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setUser(UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(999)).build())
            .setContext(
                Context.newBuilder()
                    .addLocales(BasicTypes.Locale.newBuilder().setId("en-UK"))
                    .setCountry(BasicTypes.Country.newBuilder().setId("UK"))
                    .build()));

    var request = new GetSynopsisRequest(Set.of(1, 2, 3), CurrentMicrocontext.get());
    assertNotNull(request);

    var synopsisReq = request.createHookRequest();

    assertEquals(
        com.netflix.textevidence.protogen.GetSynopsisRequest.newBuilder()
            .setVisitorId(999L)
            .setLocaleId("en-UK")
            .setCountryId("UK")
            .setType(SynopsisType.HOOK)
            .addAllVideoIds(Set.of(1, 2, 3))
            .build(),
        synopsisReq);
  }
}
