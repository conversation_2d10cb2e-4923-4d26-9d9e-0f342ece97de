package com.netflix.api.startup.priming;

import com.google.common.base.Preconditions;
import com.google.inject.Binding;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Provider;
import com.google.inject.ProvisionException;
import com.google.inject.Scopes;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Named;
import com.google.inject.spi.DefaultBindingTargetVisitor;
import com.google.inject.spi.Dependency;
import com.google.inject.spi.LinkedKeyBinding;
import com.google.inject.spi.ProviderBinding;
import com.google.inject.spi.ProviderKeyBinding;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.startup.priming.scheduling.ConfigLoaderAOT;
import com.netflix.api.startup.priming.scheduling.ScheduledDependencyInitialization;
import com.netflix.api.startup.priming.scheduling.Scheduler;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.annotations.ConfigurationSource;
import com.netflix.governator.guice.lazy.LazySingleton;
import com.netflix.governator.guice.lazy.LazySingletonScope;
import com.netflix.lifecycle.NFProperties;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class PrimedDependencyManager {

  private static final Logger LOGGER = LoggerFactory.getLogger(PrimedDependencyManager.class);

  private static final Property<Boolean> ENABLE_RETRY_PROVISIONING =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.startup.enableretry", Boolean.class)
          .orElse(false);

  private static final Property<Long> RETRY_PROVISIONING_AFTER_MS =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.startup.retryafterms", Long.class)
          .orElse(2000l);

  private final WeakReference<Thread> myThread;

  private final Set<ImplicitDependency> implicitDependencies;
  private final Set<Key<?>> constructingKeys;
  private final Map<Key<?>, ScheduledDependencyInitialization> futureProviders;
  private final Scheduler scheduler;
  private final List<CompletableFuture<Void>> waitForCompletions;

  private final ConfigLoaderAOT configLoader;

  private final PrimingDependencyInitializationLogger flightLogger;
  private final Set<String> optoutPackagePrefixes;

  @Inject
  public PrimedDependencyManager(
      PrimingDependencyInitializationLogger flightLogger,
      Set<ImplicitDependency> implicitDependencies,
      @Named("primingOptoutPackages") Set<String> optoutPackagePrefixes,
      Scheduler scheduler,
      ConfigLoaderAOT configLoader) {
    this.futureProviders = new HashMap<>();
    this.configLoader = configLoader;
    this.myThread = new WeakReference<>(Thread.currentThread());
    this.implicitDependencies = implicitDependencies;
    this.constructingKeys = new HashSet<>();
    this.scheduler = scheduler;
    this.flightLogger = flightLogger;
    this.optoutPackagePrefixes = optoutPackagePrefixes;
    this.waitForCompletions = new ArrayList<>();
  }

  public void reset() {
    this.scheduler.reset();
    this.waitForCompletions.clear();
  }

  /**
   * Create a future that monitor the priming/initialization of all the singleton involved in the
   * provision of `entryPointKey` binding
   *
   * @param entryPointKey Key for the binding that needs to be loaded
   * @param provider Provider to create the instance of this binding
   * @return
   */
  protected <T> CompletableFuture<Void> schedule(
      Injector myInjector, Key<?> entryPointKey, Provider<T> provider) {
    ScheduledDependencyInitialization execution =
        _getOrCreateScheduledExecution(myInjector, entryPointKey, provider);
    this.waitForCompletions.add(execution.getFuture());
    return execution.getFuture();
  }

  private <T> ScheduledDependencyInitialization _getOrCreateScheduledExecution(
      Injector myInjector, Key<?> entryPointKey, Provider<T> provider) {

    Preconditions.checkState(this.myThread.get() == Thread.currentThread());

    Binding<?> myBinding = resolveBinding(myInjector, myInjector.getBinding(entryPointKey));
    final Key<?> key = myBinding.getKey();

    ScheduledDependencyInitialization future = this.futureProviders.get(key);
    if (future != null) return future;

    Set<Dependency<?>> deps = findDependencies(myInjector, key);

    constructingKeys.add(entryPointKey);
    constructingKeys.add(key);

    var allBindingDeps =
        Stream.concat(deps.stream().map(Dependency::getKey), findImplicitDependency(key).stream())
            .map(myInjector::getBinding)
            .filter(b -> !constructingKeys.contains(b.getKey()))
            .toList();

    final Set<Key<?>> dependenciesKeys =
        allBindingDeps.stream().map(Binding::getKey).collect(Collectors.toSet());
    PrimingMetricsLogger.visitStart(key, dependenciesKeys);

    configurationLoadingAheadOfTime(myBinding);

    var scheduledDependencies =
        allBindingDeps.stream()
            .map(b -> this._getOrCreateScheduledExecution(myInjector, b.getKey(), b.getProvider()))
            .toList();

    constructingKeys.remove(entryPointKey);
    constructingKeys.remove(key);

    // Check if we are loading an api's singleton
    Optional<String> cls =
        Optional.ofNullable(key.getTypeLiteral())
            .map(TypeLiteral::getRawType)
            .flatMap(c -> Optional.ofNullable(c.getPackage()))
            .map(Package::getName)
            .filter(p -> p != null && optoutPackagePrefixes.stream().anyMatch(p::startsWith));

    boolean imSingleton =
        Scopes.isSingleton(myBinding)
            || Scopes.isScoped(myBinding, LazySingletonScope.get(), LazySingleton.class);

    boolean willLoadSingletonAsync = cls.isEmpty() && imSingleton;

    return futureProviders.computeIfAbsent(
        key,
        k -> {
          ScheduledDependencyInitialization schedExec;
          if (!willLoadSingletonAsync) {
            // Do not initialize api's singleton and non-singleton binding
            PrimingMetricsLogger.visitEnd(key, dependenciesKeys);
            schedExec = this.scheduler.schedule(key, scheduledDependencies);
          } else {
            schedExec = invokeWithRetry(scheduledDependencies, key, provider);
            PrimingMetricsLogger.visitEnd(key, dependenciesKeys);
          }
          return schedExec;
        });
  }

  private ScheduledDependencyInitialization invokeWithRetry(
      List<ScheduledDependencyInitialization> dependencies, Key<?> key, Provider<?> provider) {
    long totalTime = System.nanoTime();
    Supplier<ProvisionException> callProvider =
        () -> {
          long localTime = System.nanoTime();
          try {
            flightLogger.logInitialization(key, provider::get);
          } catch (ProvisionException e) {
            LOGGER.error("Can't instantiate key={}", key, e);
            return e;
          }

          LOGGER.debug(
              "Dependency {} primed in {} ms and {} ms with dependencies",
              key,
              TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - localTime),
              TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - totalTime));
          return null;
        };

    return scheduler.schedule(
        key,
        () -> {
          ProvisionException ex = callProvider.get();
          if (ex == null) return;
          if (!ENABLE_RETRY_PROVISIONING.get()) {
            throw ex;
          }
          LOGGER.warn("Can't instantiate key={}, will retry once", key, ex);

          try {
            Thread.sleep(RETRY_PROVISIONING_AFTER_MS.get());
          } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
          }
          ProvisionException e = callProvider.get();
          if (e != null) {
            LOGGER.error("Can't instantiate key={} after retry", key, e);
            throw e;
          }
        },
        dependencies);
  }

  private Collection<? extends Key<?>> findImplicitDependency(Key<?> key) {
    Set<Key<?>> deps = new HashSet<>();
    for (ImplicitDependency entry : implicitDependencies) {
      if (entry.getClassMatcher().matches(key.getTypeLiteral().getRawType())
          || entry.getTypeLiteralMatcher().matches(key.getTypeLiteral())) {
        deps.addAll(entry.getTargets());
      }
    }
    return deps;
  }

  /**
   * NFLibrary and Archaius uses annotation to load properties right before the instance is
   * provisioned
   *
   * <p>Because we are loading the singleton concurrently the creation order is non-deterministic if
   * there is no explicit dependency between them. It's possible to load first a singleton that
   * relies on the configuration provided by a not-yet provisioned singleton.
   *
   * <p>To avoid issue with the configuration, we are loading ahead of time the configuration before
   * starting to provision the singletons concurrently.
   *
   * @param binding that may lead to a configuration class
   */
  private void configurationLoadingAheadOfTime(Binding<?> binding) {
    Class<?> type = binding.getKey().getTypeLiteral().getRawType();

    if (type.isAnnotationPresent(ConfigurationSource.class)
        || type.isAnnotationPresent(NFProperties.class)) {

      LOGGER.info("Loading configuration aot {}", type);

      configLoader.loadConfigForBinding(binding);
    }
  }

  private Binding<?> resolveBinding(Injector myInjector, Binding<?> myBinding) {
    return myBinding.acceptTargetVisitor(
        new DefaultBindingTargetVisitor<Object, Binding<?>>() {

          @Override
          public Binding<?> visit(ProviderKeyBinding<?> providerKeyBinding) {
            return myInjector
                .getBinding(providerKeyBinding.getProviderKey())
                .acceptTargetVisitor(this);
          }

          @Override
          public Binding<?> visit(LinkedKeyBinding<?> linkedKeyBinding) {
            if (linkedKeyBinding.getKey().equals(linkedKeyBinding.getLinkedKey()))
              return linkedKeyBinding;
            return myInjector.getBinding(linkedKeyBinding.getLinkedKey()).acceptTargetVisitor(this);
          }

          @Override
          public Binding<?> visit(ProviderBinding<?> providerBinding) {
            return myInjector
                .getBinding(providerBinding.getProvidedKey())
                .acceptTargetVisitor(this);
          }

          @Override
          public Binding<?> visitOther(Binding<?> binding) {
            return binding;
          }
        });
  }

  private Set<Dependency<?>> findDependencies(Injector myInjector, Key<?> key) {
    return myInjector
        .getBinding(key)
        .acceptTargetVisitor(new DependencyBindingTargetVisitor(myInjector));
  }

  public static class Barrier {
    private final CompletableFuture<Void> completionFuture;

    private Barrier(CompletableFuture<Void> completionFuture) {
      this.completionFuture = completionFuture;
    }

    public CompletableFuture<Void> toFuture() {
      return completionFuture;
    }
  }

  /**
   * The barrier provider allows us to add a barrier during the injection process that wait for all
   * the concurrent initialization finish
   */
  public static class BarrierProvider implements Provider<Barrier> {
    private final PrimedDependencyManager manager;

    @Inject
    private BarrierProvider(PrimedDependencyManager manager) {
      this.manager = manager;
    }

    @Override
    public Barrier get() {
      this.manager.scheduler.start();
      CompletableFuture<?>[] futures =
          this.manager.waitForCompletions.toArray(new CompletableFuture[0]);
      return new Barrier(CompletableFuture.allOf(futures));
    }
  }
}
