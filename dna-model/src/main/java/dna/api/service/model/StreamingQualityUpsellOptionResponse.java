package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class StreamingQualityUpsellOptionResponse {
  /** Given account owner id */
  private Long customerID;

  /** Status of availability for quality upsell for this user */
  private Boolean isUpsellAvailable;

  /** Detailed status of upsell. This is set when upsell is unavailable. */
  private String statusDetail;

  /** Used to track the request and response of upsells. This is set when upsell is available. */
  private String upsellUUID;

  /** Name of billing partner. This is set when upsell is available. */
  private String billingPartner;

  /** Category of the billing partner. This is set when upsell is available. */
  private MemberCommerceBillingPartnerType billingPartnerType;

  /**
   * Display name of billing partner (only set when billing partner type is BPT_BUNDLE). This is set
   * when upsell is available.
   */
  private String billingPartnerDisplayName;

  /** Country for billing. This is set when upsell is available. */
  private String billingCountry;

  /** Currency code for billing. This is set when upsell is available. */
  private String currencyCode;

  /**
   * The next billing time, if switching to an upsell plan. This is set when upsell is available.
   */
  private Long nextBillingTS;

  /**
   * This is true when "nextBillingTS" is an approximation. This is set when upsell is available.
   */
  private Boolean isNextBillingDateApproximate;

  /** ID of the current plan for the account. This is set when upsell is available. */
  private Long currentPlanID;

  /** Current price tier for the account. This is set when upsell is available. */
  private String currentPriceTier;

  /**
   * Price of the current plan for the account in U.S. cents. This is set when upsell is available.
   */
  private Long currentTotalPriceInCents;

  /** Video resolution for current plan for the account. This is set when upsell is available. */
  private MembershipStreamQuality currentVideoResolution;

  /**
   * Max number of concurrent streams currently supported for the account. This is set when upsell
   * is available.
   */
  private Long currentMaxConcurrentStreams;

  /** Current plan is eligible for changing plans. This is set when upsell is available. */
  private Boolean currentIsEligibleForChangePlan;

  /**
   * ID of plan that account is attempting to be upsold to. This is set when upsell is available.
   */
  private Long targetPlanID;

  /**
   * Price tier of plan that account is attempting to be upsold to. This is set when upsell is
   * available.
   */
  private String targetPriceTier;

  /**
   * Price in cents of plan that account is attempting to be upsold to. This is set when upsell is
   * available.
   */
  private Long targetTotalPriceInCents;

  /**
   * Video resolution of plan that account is attempting to be upsold to. This is set when upsell is
   * available.
   */
  private MembershipStreamQuality targetVideoResolution;

  /**
   * Max number of concurrent streams the account is attempting to be upsold to. This is set when
   * upsell is available.
   */
  private Long targetMaxConcurrentStreams;

  /**
   * The difference in price between current plan and the plan that account is attempting to be
   * upsold to. This is set when upsell is available.
   */
  private Long deltaPriceInCents;

  /** Upsell message formatted. */
  private UpsellOptionFormatted upsellFormatted;

  public Long getCustomerID() {
    return customerID;
  }

  public Boolean getIsUpsellAvailable() {
    return isUpsellAvailable;
  }

  public String getStatusDetail() {
    return statusDetail;
  }

  public String getUpsellUUID() {
    return upsellUUID;
  }

  public String getBillingPartner() {
    return billingPartner;
  }

  public MemberCommerceBillingPartnerType getBillingPartnerType() {
    return billingPartnerType;
  }

  public String getBillingPartnerDisplayName() {
    return billingPartnerDisplayName;
  }

  public String getBillingCountry() {
    return billingCountry;
  }

  public String getCurrencyCode() {
    return currencyCode;
  }

  public Long getNextBillingTS() {
    return nextBillingTS;
  }

  public Boolean getIsNextBillingDateApproximate() {
    return isNextBillingDateApproximate;
  }

  public Long getCurrentPlanID() {
    return currentPlanID;
  }

  public String getCurrentPriceTier() {
    return currentPriceTier;
  }

  public Long getCurrentTotalPriceInCents() {
    return currentTotalPriceInCents;
  }

  public MembershipStreamQuality getCurrentVideoResolution() {
    return currentVideoResolution;
  }

  public Long getCurrentMaxConcurrentStreams() {
    return currentMaxConcurrentStreams;
  }

  public Boolean getCurrentIsEligibleForChangePlan() {
    return currentIsEligibleForChangePlan;
  }

  public Long getTargetPlanID() {
    return targetPlanID;
  }

  public String getTargetPriceTier() {
    return targetPriceTier;
  }

  public Long getTargetTotalPriceInCents() {
    return targetTotalPriceInCents;
  }

  public MembershipStreamQuality getTargetVideoResolution() {
    return targetVideoResolution;
  }

  public Long getTargetMaxConcurrentStreams() {
    return targetMaxConcurrentStreams;
  }

  public Long getDeltaPriceInCents() {
    return deltaPriceInCents;
  }

  public UpsellOptionFormatted getUpsellFormatted() {
    return upsellFormatted;
  }

  public StreamingQualityUpsellOptionResponse setCustomerID(Long customerID) {
    this.customerID = customerID;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setIsUpsellAvailable(Boolean isUpsellAvailable) {
    this.isUpsellAvailable = isUpsellAvailable;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setStatusDetail(String statusDetail) {
    this.statusDetail = statusDetail;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setUpsellUUID(String upsellUUID) {
    this.upsellUUID = upsellUUID;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setBillingPartner(String billingPartner) {
    this.billingPartner = billingPartner;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setBillingPartnerType(
      MemberCommerceBillingPartnerType billingPartnerType) {
    this.billingPartnerType = billingPartnerType;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setBillingPartnerDisplayName(
      String billingPartnerDisplayName) {
    this.billingPartnerDisplayName = billingPartnerDisplayName;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setBillingCountry(String billingCountry) {
    this.billingCountry = billingCountry;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setCurrencyCode(String currencyCode) {
    this.currencyCode = currencyCode;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setNextBillingTS(Long nextBillingTS) {
    this.nextBillingTS = nextBillingTS;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setIsNextBillingDateApproximate(
      Boolean isNextBillingDateApproximate) {
    this.isNextBillingDateApproximate = isNextBillingDateApproximate;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setCurrentPlanID(Long currentPlanID) {
    this.currentPlanID = currentPlanID;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setCurrentPriceTier(String currentPriceTier) {
    this.currentPriceTier = currentPriceTier;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setCurrentTotalPriceInCents(
      Long currentTotalPriceInCents) {
    this.currentTotalPriceInCents = currentTotalPriceInCents;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setCurrentVideoResolution(
      MembershipStreamQuality currentVideoResolution) {
    this.currentVideoResolution = currentVideoResolution;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setCurrentMaxConcurrentStreams(
      Long currentMaxConcurrentStreams) {
    this.currentMaxConcurrentStreams = currentMaxConcurrentStreams;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setCurrentIsEligibleForChangePlan(
      Boolean currentIsEligibleForChangePlan) {
    this.currentIsEligibleForChangePlan = currentIsEligibleForChangePlan;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setTargetPlanID(Long targetPlanID) {
    this.targetPlanID = targetPlanID;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setTargetPriceTier(String targetPriceTier) {
    this.targetPriceTier = targetPriceTier;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setTargetTotalPriceInCents(
      Long targetTotalPriceInCents) {
    this.targetTotalPriceInCents = targetTotalPriceInCents;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setTargetVideoResolution(
      MembershipStreamQuality targetVideoResolution) {
    this.targetVideoResolution = targetVideoResolution;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setTargetMaxConcurrentStreams(
      Long targetMaxConcurrentStreams) {
    this.targetMaxConcurrentStreams = targetMaxConcurrentStreams;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setDeltaPriceInCents(Long deltaPriceInCents) {
    this.deltaPriceInCents = deltaPriceInCents;
    return this;
  }

  public StreamingQualityUpsellOptionResponse setUpsellFormatted(
      UpsellOptionFormatted upsellFormatted) {
    this.upsellFormatted = upsellFormatted;
    return this;
  }
}
