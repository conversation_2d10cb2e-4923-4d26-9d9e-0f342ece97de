package com.netflix.api.service;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.cms.APICmsService;
import com.netflix.api.service.os.APIOctoberSkyServiceImpl;
import com.netflix.i18n.NFLocale;
import com.netflix.type.ISOCountry;
import com.netflix.type.NFCountry;
import java.util.Map;
import rx.Observable;

/**
 * Implementation based on October Sky that will become the source of truth for countries. Current
 * state: only use OS for country status. The rest will be implemented when OS data becomes robust.
 *
 * <AUTHOR>
 */
public class APICountryImpl implements APICountry {
  private final ISOCountry country;
  private String status;
  private final APIOctoberSkyServiceImpl apiOctoberSkyServiceImpl;
  private final APICmsService cmsService;

  @AssistedInject
  APICountryImpl(
      @Assisted ISOCountry country,
      APIOctoberSkyServiceImpl apiOctoberSkyServiceImpl,
      APICmsService cmsService) {
    // status not provided; lazy loading
    this(country, null, apiOctoberSkyServiceImpl, cmsService);
  }

  @AssistedInject
  APICountryImpl(
      @Assisted ISOCountry country,
      @Assisted String status,
      APIOctoberSkyServiceImpl apiOctoberSkyServiceImpl,
      APICmsService cmsService) {

    this.country = country;

    // if status already provided from OS, it wil not be reloaded again
    this.status = status;
    this.apiOctoberSkyServiceImpl = apiOctoberSkyServiceImpl;

    this.cmsService = cmsService;
  }

  @AssistedInject
  APICountryImpl(
      @Assisted String countryCode,
      APIOctoberSkyServiceImpl apiOctoberSkyServiceImpl,
      APICmsService cmsService) {
    this(NFCountry.findInstance(countryCode), apiOctoberSkyServiceImpl, cmsService);
  }

  @Override
  public String getId() {
    return country.getId();
  }

  @Override
  public APILocale getDefaultLocale() {
    return new APILocaleImpl(NFLocale.getDefaultLocale(country));
  }

  @Override
  public String getDisplayName(String locale) {
    if (locale == null) {
      return null;
    }
    return NFLocale.getLocale("", "", country.getId())
        .getDisplayCountry(NFLocale.findInstance(locale));
  }

  @Override
  public String toString() {
    return getId();
  }

  @Override
  public Observable<Map<String, String>> getBrandLinks() {
    return cmsService.getBrandLinks(country, RequestContextWrapper.get().getLocale());
  }

  @Override
  @Deprecated
  public String getStatus() {
    // lazy loading from OS
    if (status == null) {
      status = apiOctoberSkyServiceImpl.getGeoStatus(country.getId());
    }
    // TODO consider hardcoding this to ALLOW
    return status;
  }

  @Override
  public ISOCountry getISOCountry() {
    return country;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    APICountryImpl that = (APICountryImpl) o;
    return country.equals(that.country);
  }

  @Override
  public int hashCode() {
    return country.hashCode();
  }
}
