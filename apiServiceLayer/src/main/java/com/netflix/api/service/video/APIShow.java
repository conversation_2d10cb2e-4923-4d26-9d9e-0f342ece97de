package com.netflix.api.service.video;

import rx.Observable;

/**
 * This interface extends {@link APIVideo} by adding operations that make sense for a "Show" type of
 * object in the Netflix catalog.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.varieties.png"
 * alt="varieties of object that implement the APIVideo interface">
 *
 * <p>A show has one or more seasons, and one or more episodes, one of which is the current episode.
 * A season has one show parent and has one or more episodes, one of which is the current episode. A
 * (show) episode has a show parent and a season parent.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.class.png"
 * alt="APIVideo class diagram"> <img
 * src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.cluster2.png"
 * alt="relationships in the APIVideo class cluster">
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.cluster.png"
 * alt="relationships in the APIVideo class cluster">
 */
public interface APIShow extends APIVideo {

  /**
   * Returns the label that the UI must display for number of seasons. The season collection may be
   * labeled Part, Collection, Season etc. based on title and country. Type indicates casing (caps,
   * camelcase, etc.) and length (e.g., Collection, Coll., C, etc.). A UI must know what type to ask
   * for based on the real estate on the app/page.
   *
   * @param labelType - Type of label, see {@link com.netflix.api.service.video.LabelType} for more
   *     info.
   * @return An observable that emits the label to be displayed - e.g., 3 Seasons. Always returns
   *     one emission.
   */
  Observable<String> getNumSeasonsLabel(LabelType labelType);
}
