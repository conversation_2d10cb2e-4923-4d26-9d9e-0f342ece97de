package com.netflix.api.service.vhs;

import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;
import com.netflix.api.service.APIListBuilderBehaviorImpl;
import com.netflix.api.service.APIListBuilderImpl;
import com.netflix.api.service.ListItem;
import java.util.List;
import rx.Observable;

public class APIViewingsImpl implements APIViewings {

  private final List<ViewingHolder> localViewings;
  private final APIViewingFactory viewingFactory;

  @Inject
  public APIViewingsImpl(
      @Assisted List<ViewingHolder> localViewings, APIViewingFactory viewingFactory) {
    this.localViewings = localViewings;
    this.viewingFactory = viewingFactory;
  }

  @Override
  public Observable<ListItem<APIViewing>> getLocalViewings() {

    class LocalViewingsBehavior extends APIListBuilderBehaviorImpl<APIViewing, ViewingHolder> {
      @Override
      public APIViewing loadItem(ViewingHolder reference) {
        return viewingFactory.getInstance(reference);
      }

      @Override
      public List<ViewingHolder> getReferences() {
        return localViewings;
      }
    }

    class LocalViewings extends APIListBuilderImpl<APIViewing, LocalViewingsBehavior> {
      LocalViewings() {
        super(new LocalViewingsBehavior());
      }
    }
    return new LocalViewings().build();
  }

  @Override
  public Observable<ListItem<APIViewing>> getRemoteViewings() {

    class RemoteViewingsBehavior extends APIListBuilderBehaviorImpl<APIViewing, ViewingHolder> {
      @Override
      public APIViewing loadItem(ViewingHolder reference) {
        return viewingFactory.getInstance(reference);
      }

      @Override
      public List<ViewingHolder> getReferences() {
        return List.of();
      }
    }

    class RemoteViewings extends APIListBuilderImpl<APIViewing, RemoteViewingsBehavior> {
      RemoteViewings() {
        super(new RemoteViewingsBehavior());
      }
    }
    return new RemoteViewings().build();
  }
}
