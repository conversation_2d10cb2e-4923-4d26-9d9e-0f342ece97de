package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SearchOptions {
  private Boolean isDvdInfoIncluded;

  private Boolean isOdpAware;

  private Boolean includeSourceInfo;

  private Integer count;

  public Boolean getIsDvdInfoIncluded() {
    return isDvdInfoIncluded;
  }

  public Boolean getIsOdpAware() {
    return isOdpAware;
  }

  public Boolean getIncludeSourceInfo() {
    return includeSourceInfo;
  }

  public Integer getCount() {
    return count;
  }

  public SearchOptions setIsDvdInfoIncluded(Boolean isDvdInfoIncluded) {
    this.isDvdInfoIncluded = isDvdInfoIncluded;
    return this;
  }

  public SearchOptions setIsOdpAware(Boolean isOdpAware) {
    this.isOdpAware = isOdpAware;
    return this;
  }

  public SearchOptions setIncludeSourceInfo(Boolean includeSourceInfo) {
    this.includeSourceInfo = includeSourceInfo;
    return this;
  }

  public SearchOptions setCount(Integer count) {
    this.count = count;
    return this;
  }
}
