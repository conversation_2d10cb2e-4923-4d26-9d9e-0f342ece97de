package com.netflix.api.platform.http.throttling;

import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import java.lang.Thread.State;
import java.lang.management.LockInfo;
import java.lang.management.ManagementFactory;
import java.lang.management.MonitorInfo;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/** Throttles requests based on concurrent requests. */
@Component
public class ConcurrentRequestThrottler {
  private static final Logger logger = LoggerFactory.getLogger(ConcurrentRequestThrottler.class);

  private final AtomicLong LAST_PRINTED = new AtomicLong(0);
  private final AtomicLong LAST_PRINTED_BLOCKED = new AtomicLong(0);

  private final Property<Boolean> enableDumps;
  private final Property<Boolean> enableBlocked;
  private final Property<Integer> throttleLimit;
  private final Property<Double> targetPercentage;
  private final Property<Double> maxBackgroundPercentage;

  public ConcurrentRequestThrottler(PropertyRepository propertyRepository) {
    enableDumps = propertyRepository.get("api.throttle.dump", Boolean.class).orElse(false);
    enableBlocked = propertyRepository.get("api.throttle.blocked", Boolean.class).orElse(false);
    throttleLimit = propertyRepository.get("api.request.throttle.size", Integer.class).orElse(250);
    targetPercentage =
        propertyRepository.get("api.request.throttle.target.percentage", Double.class).orElse(20.0);
    maxBackgroundPercentage =
        propertyRepository
            .get("api.request.throttle.background.percentage", Double.class)
            .orElse(60.0);
  }

  public ThrottleResult shouldThrottle(
      double percentUtilized, int concurrentRequestCount, boolean isBackground) {
    if (enableBlocked.get()) {
      processBlockedThreads();
    }
    int currentThrottleLimit = getThrottleLimit();
    if (concurrentRequestCount > currentThrottleLimit) {
      logger.debug(
          "RequestThrottle Shedding Load. Concurrent requests exceeds limit.  Current Requests {} background {} limit {}",
          concurrentRequestCount,
          isBackground,
          currentThrottleLimit);
      if (enableDumps.get()) {
        long currentMillis = System.currentTimeMillis();
        long lastPrinted = LAST_PRINTED.get();
        if (lastPrinted + 5000 < currentMillis) {
          if (LAST_PRINTED.compareAndSet(lastPrinted, currentMillis)) {
            if (logger.isErrorEnabled()) {
              logger.error("THROTTLE DUMP GENERATED:\n {}\n ***END DUMP", generateThreadDump());
            }
          }
        }
      }
      return ThrottleResult.MAX_CONCURRENT;
    }
    if (percentUtilized >= maxBackgroundPercentage.get() && isBackground) {
      logger.debug(
          "RequestThrottle Shedding Load. Concurrent requests above threshold shedding background task.  Current Requests {} limit {}",
          concurrentRequestCount,
          currentThrottleLimit);
      return ThrottleResult.BACKGROUND;
    }

    return ThrottleResult.PROCEED;
  }

  public void processBlockedThreads() {
    long currentMillis = System.currentTimeMillis();
    long lastPrinted = LAST_PRINTED_BLOCKED.get();
    if (lastPrinted + 5000 < currentMillis
        && LAST_PRINTED_BLOCKED.compareAndSet(lastPrinted, currentMillis)) {

      final ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
      final ThreadInfo[] threadInfos =
          threadMXBean.getThreadInfo(threadMXBean.getAllThreadIds(), 1000);
      List<ThreadInfo> blockedThreads = new ArrayList<>();
      for (ThreadInfo threadInfo : threadInfos) {
        if (threadInfo != null && Objects.equals(threadInfo.getThreadState(), State.BLOCKED)) {
          blockedThreads.add(threadInfo);
        }
      }
      if (blockedThreads.isEmpty()) {
        logger.error("No blocked threads\n");
      } else {
        StringBuilder out = new StringBuilder("THREAD BLOCKED GENERATED\n");
        for (ThreadInfo threadInfo : blockedThreads) {
          out.append(toString(threadInfo));
        }
        out.append("\n***END BLOCKED");
        logger.error("{}", out);
      }
    }
  }

  private static String toString(ThreadInfo info) {
    StringBuilder sb =
        new StringBuilder(
            "\""
                + info.getThreadName()
                + "\" Id="
                + info.getThreadId()
                + " "
                + info.getThreadState());
    if (info.getLockName() != null) {
      sb.append(" on ").append(info.getLockName());
    }
    if (info.getLockOwnerName() != null) {
      sb.append(" owned by \"")
          .append(info.getLockOwnerName())
          .append("\" Id=")
          .append(info.getLockOwnerId());
    }
    if (info.isSuspended()) {
      sb.append(" (suspended)");
    }
    if (info.isInNative()) {
      sb.append(" (in native)");
    }
    sb.append('\n');
    int i = 0;
    final StackTraceElement[] stackTrace = info.getStackTrace();
    for (; i < stackTrace.length; i++) {
      StackTraceElement ste = stackTrace[i];
      sb.append("\tat ").append(ste.toString());
      sb.append('\n');
      if (i == 0 && info.getLockInfo() != null) {
        Thread.State ts = info.getThreadState();
        switch (ts) {
          case BLOCKED:
            sb.append("\t-  blocked on ").append(info.getLockInfo()).append('\n');
            break;
          case WAITING, TIMED_WAITING:
            sb.append("\t-  waiting on ").append(info.getLockInfo()).append('\n');
            break;
          default:
        }
      }

      for (MonitorInfo mi : info.getLockedMonitors()) {
        if (mi.getLockedStackDepth() == i) {
          sb.append("\t-  locked ").append(mi).append('\n');
        }
      }
    }

    LockInfo[] locks = info.getLockedSynchronizers();
    if (locks.length > 0) {
      sb.append("\n\tNumber of locked synchronizers = ").append(locks.length).append('\n');
      for (LockInfo li : locks) {
        sb.append("\t- ").append(li).append('\n');
      }
    }
    sb.append('\n');
    return sb.toString();
  }

  public static String generateThreadDump() {
    final StringBuilder dump = new StringBuilder();
    final ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
    final ThreadInfo[] threadInfos =
        threadMXBean.getThreadInfo(threadMXBean.getAllThreadIds(), 1000);
    for (ThreadInfo threadInfo : threadInfos) {
      dump.append('"');
      dump.append(threadInfo.getThreadName());
      dump.append("\" ");
      final Thread.State state = threadInfo.getThreadState();
      dump.append("\n   java.lang.Thread.State: ");
      dump.append(state);
      final StackTraceElement[] stackTraceElements = threadInfo.getStackTrace();
      for (final StackTraceElement stackTraceElement : stackTraceElements) {
        dump.append("\n        at ");
        dump.append(stackTraceElement);
      }
      dump.append("\n\n");
    }
    return dump.toString();
  }

  int getThrottleLimit() {
    return throttleLimit.get();
  }

  double getTargetPercentage() {
    return targetPercentage.get();
  }
}
