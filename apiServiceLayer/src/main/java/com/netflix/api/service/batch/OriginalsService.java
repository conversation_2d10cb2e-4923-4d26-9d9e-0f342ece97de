package com.netflix.api.service.batch;

import java.util.List;
import java.util.Map;
import rx.Observable;

/** Author: sgudiboina Date: 11/17/17 Time: 12:03 PM */
public interface OriginalsService {
  Observable<List<String>> filterSupportedBillboards(
      List<String> unfilteredSupportedBillboards, Map<String, String> ouiAnnotations);

  Observable<List<String>> filterSupportedLolomoFeatures(
      List<String> unfilteredSupportedLolomoFeatures, Map<String, String> ouiAnnotations);

  Observable<List<String>> filterSupportedClientCapabilities(
      List<String> unfilteredSupportedClientCapabilities, Map<String, String> ouiAnnotations);
}
