package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PacsTraceRecord {
  private String featureName;

  private String evaluation;

  public String getFeatureName() {
    return featureName;
  }

  public String getEvaluation() {
    return evaluation;
  }

  public PacsTraceRecord setFeatureName(String featureName) {
    this.featureName = featureName;
    return this;
  }

  public PacsTraceRecord setEvaluation(String evaluation) {
    this.evaluation = evaluation;
    return this;
  }
}
