package dna.api.datasources.bookmark;

import static dna.api.datasources.DataSourceUtil.buildMultiValuedMap;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class BookmarkDataSourceBatcher {
  public Map<String, Set<Integer>> byProfileGUID(final Set<BookmarkWithOptionsRequest> reqs) {
    return reqs.stream()
        .filter((req) -> req.profileGUID() != null && !req.profileGUID().isBlank())
        .collect(Collectors.groupingBy(
            BookmarkWithOptionsRequest::profileGUID,
            Collectors.mapping(
                BookmarkWithOptionsRequest::videoId,
                Collectors.toSet())));
  }
}
