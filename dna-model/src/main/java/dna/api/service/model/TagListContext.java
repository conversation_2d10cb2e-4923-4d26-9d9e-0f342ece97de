package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class TagListContext {
  private Map<String, String> annotations;

  private TagsRecipe tagsRecipe;

  public Map<String, String> getAnnotations() {
    return annotations;
  }

  public TagsRecipe getTagsRecipe() {
    return tagsRecipe;
  }

  public TagListContext setAnnotations(Map<String, String> annotations) {
    this.annotations = annotations;
    return this;
  }

  public TagListContext setTagsRecipe(TagsRecipe tagsRecipe) {
    this.tagsRecipe = tagsRecipe;
    return this;
  }
}
