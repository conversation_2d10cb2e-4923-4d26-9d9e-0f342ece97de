package com.netflix.api.service;

/**
 * <code>APIAnnotation</code> represents a contextual property (name-value pair) on an {@link
 * com.netflix.api.service.list.APIEntity}. Any property that is not intrinsic to an object can be
 * tacked on as an APIAnnotation.
 *
 * <p>For example, a LoLoMo list may have a "<code>watchedEvidence</code>" annotation with a value
 * of videos that the user watched which resulted in Netflix merch-ing the list.
 *
 * @param <N> an enumeration of all the annotation names
 * @param <V> type of the value of the annotation
 */
public interface APIAnnotation<N, V> {
  /**
   * Name of the annotation.
   *
   * @return the name of the annotation
   */
  N name();

  /**
   * Value of the annotation.
   *
   * @return the value of the annotation
   */
  V value();

  /**
   * The type of the annotation value
   *
   * @return the value type
   */
  Class<V> getType();
}
