package com.netflix.api.service.device;

import com.google.common.base.Preconditions;
import com.netflix.streaming.dts.common.model.DeviceType;

public class APIDeviceTypeImpl implements APIDeviceType {
  private final DeviceType deviceType;

  public APIDeviceTypeImpl(DeviceType deviceType) {
    this.deviceType =
        Preconditions.checkNotNull(deviceType, "Must construct APIDeviceType with a valid object");
  }

  @Override
  public int getId() {
    return deviceType.getDeviceTypeId();
  }

  @Override
  public String getDescription() {
    return deviceType.getDescription();
  }

  @Override
  public int getCategoryId() {
    return deviceType.getNrdPortalCategoryId();
  }

  @Override
  public String getCUPCategory() {
    return deviceType.getDeviceCUPCategory();
  }

  @Override
  public String getReportingName() {
    return deviceType.getReportingName();
  }

  public DeviceType getDeviceType() {
    return deviceType;
  }

  @Override
  public String toString() {
    return "APIDeviceType{" + "deviceTypeId=" + getId() + '}';
  }
}
