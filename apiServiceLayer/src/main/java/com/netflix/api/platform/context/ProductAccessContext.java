package com.netflix.api.platform.context;

import com.netflix.api.grpc.GrpcCallHelpers;
import com.netflix.api.platform.identity.CurrentIdentityResult;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.pacs.protogen.ContentGrants;
import com.netflix.pacs.protogen.GetContentGrantsReply;
import com.netflix.pacs.protogen.GetContentGrantsRequest;
import com.netflix.pacs.protogen.PacsServiceGrpc.PacsServiceStub;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ProductAccessContext {

  private static final Logger logger = LoggerFactory.getLogger(ProductAccessContext.class);

  /*
   * Replaces the image PACS tracing FPs `api.images.pacsTracing` and `dna.videoRoutes.pacsTracing`
   */
  private static final Property<Boolean> INCLUDE_TRACING =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.pacs.tracing", Boolean.class)
          .orElse(false);

  private final PacsServiceStub pacsServiceStub;

  @Autowired
  public ProductAccessContext(@GrpcSpringClient("pacs") PacsServiceStub pacsServiceStub) {
    this.pacsServiceStub = pacsServiceStub;
  }

  /**
   * `ProductAccessContext.currentContentGrants` reads the current request context and calls PACS to
   * receive ContentGrants. The PACS client handles request-scoped caching.
   *
   * @return ContentGrants future
   */
  public CompletableFuture<ContentGrants> currentContentGrants() {
    // At least passport is required for fetching grants
    PassportIdentity passport = CurrentIdentityResult.getPassportIdentity();
    if (passport == null) {
      return CompletableFuture.completedFuture(ContentGrants.getDefaultInstance());
    }
    String passportString = passport.getPassportAsString();
    GetContentGrantsRequest request =
        GetContentGrantsRequest.newBuilder()
            .setPassport(passportString)
            .setIncludeTracing(INCLUDE_TRACING.get())
            .build();

    return GrpcCallHelpers.Future.call(pacsServiceStub::getContentGrants, request)
        .toCompletableFuture()
        .thenApply(GetContentGrantsReply::getContentGrants)
        .exceptionally(
            err -> {
              logger.error("Error fetching ContentGrants", err);
              return ContentGrants.getDefaultInstance();
            });
  }
}
