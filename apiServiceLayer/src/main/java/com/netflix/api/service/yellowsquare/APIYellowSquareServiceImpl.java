package com.netflix.api.service.yellowsquare;

import com.google.common.annotations.VisibleForTesting;
import com.google.protobuf.ByteString;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.archaius.api.Property;
import com.netflix.dgw.common.client.idempotency.IdempotencyTokenFactory;
import com.netflix.dgw.kv.protogen.GetRecordRequest;
import com.netflix.dgw.kv.protogen.IdempotentPutRequest;
import com.netflix.dgw.kv.protogen.Item;
import com.netflix.dgw.kv.protogen.KeyValueServiceGrpc.KeyValueServiceStub;
import com.netflix.dgw.kv.protogen.MutationOptions;
import com.netflix.dgw.kv.protogen.PutItemsRequest;
import com.netflix.dgw.kv.protogen.ReadOptions;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.servo.tag.BasicTag;
import com.netflix.servo.tag.BasicTagList;
import com.netflix.servo.tag.Tag;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.ust.util.ExceptionLogger;
import io.grpc.Status.Code;
import io.grpc.StatusException;
import io.grpc.StatusRuntimeException;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
public class APIYellowSquareServiceImpl implements APIYellowSquareService {

  private final KeyValueServiceStub keyValueService;

  private static final Logger logger = LoggerFactory.getLogger(APIYellowSquareServiceImpl.class);
  private static final Boolean ENABLE_METRICS = true;
  private final Property<List<String>> UNPROVISIONED_NAMESPACES;
  private final Property<List<String>> EXCLUDED_NAMESPACES;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public APIYellowSquareServiceImpl(
      com.netflix.archaius.api.PropertyRepository pr,
      @GrpcSpringClient("dgwkv") KeyValueServiceStub keyValueService) {
    this.keyValueService = keyValueService;
    UNPROVISIONED_NAMESPACES =
        pr.getList("api.yellowsquare.provisioned.namespaces", String.class)
            .orElse(List.of("FEATURE_EDUCATION"));
    EXCLUDED_NAMESPACES =
        pr.getList("api.yellowsquare.excluded.namespaces", String.class)
            .orElse(List.of("EXPIRY_NOTICE"));
  }

  @Override
  public Observable<Void> setTag(
      final String namespace, final String key, final Map<String, String> tags, final Integer ttl) {
    logMetric("set", namespace, tags.keySet(), false);
    return setTagInternal(namespace, key, tags, ttl).map(ignore -> null);
  }

  @Override
  public Observable<Void> setTag(
      final String namespace,
      final APIUser user,
      boolean useAccountOwnerAsKey,
      final Map<String, String> tags,
      final Integer ttl) {
    logMetric("set", namespace, tags.keySet(), true);
    return setTagInternal(
            namespace,
            useAccountOwnerAsKey
                ? String.valueOf(APIUserUtil.getAccountOwnerId(user))
                : String.valueOf(APIUserUtil.getCustomerId(user)),
            tags,
            ttl)
        .map(ignore -> null);
  }

  @Override
  public Observable<APIYellowSquareTag> getTags(
      final String namespace, final String key, final List<String> names) {
    logMetric("get", namespace, names, false);
    return getTagsInternal(namespace, key, names);
  }

  @Override
  public Observable<APIYellowSquareTag> getTags(
      final String namespace,
      final APIUser user,
      boolean useAccountOwnerAsKey,
      final List<String> names) {
    logMetric("get", namespace, names, true);
    return getTagsInternal(
        namespace,
        useAccountOwnerAsKey
            ? String.valueOf(APIUserUtil.getAccountOwnerId(user))
            : String.valueOf(APIUserUtil.getCustomerId(user)),
        names);
  }

  private static void logMetric(
      String method, String namespace, Collection<String> tags, boolean user) {
    if (ENABLE_METRICS) {
      int size;
      String all;
      if (tags == null) {
        size = 1;
        all = "true";
      } else {
        size = tags.size();
        all = "false";
      }
      try {
        List<Tag> list =
            List.of(
                new BasicTag("method", method),
                new BasicTag("namespace", namespace),
                new BasicTag("user", Boolean.toString(user)),
                new BasicTag("all", all));
        DynamicCounter.increment("api.yellowsquare.access", new BasicTagList(list), size);
      } catch (Exception e) {
        var cause = ExceptionLogger.getCause(e);
        if (cause != null) {
          logger.error("Error writing y2 metrics", cause);
        }
      }
    }
  }

  private Observable<APIYellowSquareTag> getTagsInternal(
      final String namespace, final String key, final List<String> names) {
    if (namespace == null || key == null || EXCLUDED_NAMESPACES.get().contains(namespace)) {
      return Observable.empty();
    }

    return RxObservable.defer(
            keyValueService::getRecord,
            GetRecordRequest.newBuilder()
                .setNamespace(namespace.toUpperCase())
                .setId(key)
                .setOptions(
                    ReadOptions.newBuilder()
                        .setRetrieveWriteTime(true)
                        .setRetrieveTtl(true)
                        .build())
                .build())
        .map(rec -> rec.getItemsList().stream().map(this::convertItem).toList())
        .onErrorResumeNext(e -> handleNotFound(e, namespace, key))
        .map((List<APIYellowSquareTag> tags) -> filterTags(names, tags))
        .flatMap(Observable::from);
  }

  @VisibleForTesting
  static List<APIYellowSquareTag> filterTags(
      @Nullable List<String> names, @Nullable List<APIYellowSquareTag> tags) {
    if (tags == null || tags.isEmpty() || names == null || names.isEmpty()) {
      return tags;
    }
    return tags.stream().filter(tag -> names.contains(tag.getName())).toList();
  }

  private Observable<Boolean> setTagInternal(
      String namespace, String key, Map<String, String> tags, final Integer ttl) {
    if (namespace == null
        || key == null
        || tags == null
        || UNPROVISIONED_NAMESPACES.get().contains(namespace)) {
      return Observable.just(false);
    }

    PutItemsRequest.Builder builder =
        PutItemsRequest.newBuilder()
            .setNamespace(namespace.toUpperCase())
            .setId(key)
            .setOptions(
                MutationOptions.newBuilder()
                    .setIdempotencyToken(IdempotencyTokenFactory.createToken())
                    .build());
    for (Entry<String, String> entry : tags.entrySet()) {
      builder.addItems(
          Item.newBuilder()
              .setTtl(ttl)
              .setKey(ByteString.copyFromUtf8(entry.getKey().toUpperCase()))
              .setValue(ByteString.copyFromUtf8(entry.getValue())));
    }
    return RxObservable.call(
            keyValueService::idempotentPut,
            IdempotentPutRequest.newBuilder().setPutItemsRequest(builder).build())
        .map(
            response -> {
              DynamicCounter.increment(
                  "api.yellowsquare.put", "result", "success", "namespace", namespace);
              return true;
            })
        .doOnError(
            e -> {
              var cause = ExceptionLogger.getCause(e);
              if (cause != null) {
                logger.error(
                    "idempotentPut error for namespace={} key={} tags={}", namespace, key, tags, e);
              }

              DynamicCounter.increment(
                  "api.yellowsquare.put", "result", "error", "namespace", namespace);
            });
  }

  private APIYellowSquareTag convertItem(Item item) {
    return new APIYellowSquareTagImpl(
        item.getKey().toStringUtf8(),
        item.getValue().toStringUtf8(),
        item.getTtl(),
        item.getWriteTimeMicros());
  }

  private static Observable<? extends List<APIYellowSquareTag>> handleNotFound(
      Throwable t, String namespace, String key) {
    Optional<Code> code = code(t);
    if (code.isPresent() && code.get() == Code.NOT_FOUND) {
      logger.warn("KeyValueService#GetRecord NOT_FOUND namespace={} key={}", namespace, key);
      return Observable.just(List.of());
    }
    return Observable.error(t);
  }

  private static Optional<Code> code(Throwable throwable) {
    return switch (throwable) {
      case StatusRuntimeException e -> Optional.of(e.getStatus().getCode());
      case StatusException e -> Optional.of(e.getStatus().getCode());
      default -> Optional.empty();
    };
  }
}
