package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** Turbo collections beyond Genres. */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NesCollectionInput {
  private Long id;

  public Long getId() {
    return id;
  }

  public NesCollectionInput setId(Long id) {
    this.id = id;
    return this;
  }
}
