package com.netflix.api.service.experimental;

import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.dgw.common.client.idempotency.IdempotencyTokenFactory;
import com.netflix.dgw.kv.protogen.GetItemRequest;
import com.netflix.dgw.kv.protogen.Item;
import com.netflix.dgw.kv.protogen.KeyValueServiceGrpc.KeyValueServiceStub;
import com.netflix.dgw.kv.v2.protogen.GetItemsRequest;
import com.netflix.dgw.kv.v2.protogen.KeySet;
import com.netflix.dgw.kv.v2.protogen.KeyValueServiceV2Grpc;
import com.netflix.dgw.kv.v2.protogen.Predicate;
import com.netflix.dgw.kv.v2.protogen.PutItemsRequest;
import com.netflix.dgw.kv.v2.protogen.Selection;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import java.nio.BufferUnderflowException;
import java.nio.ByteBuffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
public class APIExperimentalServiceImpl {
  private static final Logger log = LoggerFactory.getLogger(APIExperimentalServiceImpl.class);

  private static final String MUTE_SUBTITLES_NAMESPACE = "tvui_mute_subtitles";
  private static final String GAME_IMPRESSION_NAMESPACE = "tvui_game_impressions";
  private static final JsonFormat.Printer JSON =
      JsonFormat.printer().omittingInsignificantWhitespace();

  private final KeyValueServiceStub keyValueServiceStub;

  // Prefer to use the v2 KeyValueServiceStub for any new functionality.
  private final KeyValueServiceV2Grpc.KeyValueServiceV2Stub keyValueServiceV2Stub;

  @Autowired
  public APIExperimentalServiceImpl(
      @GrpcSpringClient("dgwkv") KeyValueServiceStub keyValueServiceStub,
      @GrpcSpringClient("dgwkv")
          KeyValueServiceV2Grpc.KeyValueServiceV2Stub keyValueServiceV2Stub) {
    this.keyValueServiceStub = keyValueServiceStub;
    this.keyValueServiceV2Stub = keyValueServiceV2Stub;
  }

  private static final Selection SIZE_1MIB =
      Selection.newBuilder().setPageSizeBytes(1024L * 1024L).build();

  // namespace: GAME_IMPRESSION_NAMESPACE
  // id: profileGuid
  // key: game id
  public Observable<Void> setGameImpression(
      String profileGuid, Integer gameId, GameImpression impression) {
    ByteString key = encodeInteger(gameId);
    ByteString value = impression.encode();

    PutItemsRequest req =
        PutItemsRequest.newBuilder()
            .setNamespace(GAME_IMPRESSION_NAMESPACE)
            .setId(profileGuid)
            .addItems(Item.newBuilder().setKey(key).setValue(value))
            .setIdempotencyToken(IdempotencyTokenFactory.createToken())
            .build();

    debug("setGameImpression request", req);

    return RxObservable.defer(keyValueServiceV2Stub::putItems, req)
        .map(
            resp -> {
              debug("setGameImpression response", resp);
              return null;
            });
  }

  // namespace: GAME_IMPRESSION_NAMESPACE
  // id: profileGuid
  // key: game id
  public Observable<GameImpression> getGameImpression(String profileGuid, Integer gameId) {
    GetItemsRequest req =
        GetItemsRequest.newBuilder()
            .setNamespace(GAME_IMPRESSION_NAMESPACE)
            .setId(profileGuid)
            .setPredicate(
                Predicate.newBuilder()
                    .setMatchKeys(KeySet.newBuilder().addKeys(encodeInteger(gameId))))
            .setSelection(SIZE_1MIB)
            .build();

    debug("getGameImpression request", req);

    return RxObservable.defer(keyValueServiceV2Stub::getItems, req)
        .map(
            resp -> {
              debug("getGameImpression response", resp);

              if (resp.getItemsCount() == 0) {
                log.debug(
                    "getGameImpression not found: profileGuid={} gameId={}", profileGuid, gameId);
                return null;
              }

              try {
                return new GameImpression(resp.getItems(0).getValue());
              } catch (IndexOutOfBoundsException | BufferUnderflowException e) {
                log.debug("getGameImpression response parsing error", e);
                return null;
              }
            });
  }

  // namespace: MUTE_SUBTITLES_NAMESPACE
  // id:  profile guid
  // key:
  public Observable<Boolean> getHasDisabledSubtitlesOnMute(String profileGuid) {
    GetItemRequest request =
        GetItemRequest.newBuilder()
            .setNamespace(MUTE_SUBTITLES_NAMESPACE)
            .setId(profileGuid)
            .build();
    return RxObservable.defer(keyValueServiceStub::getItemIfPresent, request)
        .map(
            item -> {
              if (item.hasItem()) {
                return item.getItem().getValue().toByteArray()[0] == 1;
              } else {
                return Boolean.FALSE;
              }
            });
  }

  // Log the protobuf message as a JSON object.
  private static void debug(String description, MessageOrBuilder message) {
    if (log.isDebugEnabled()) {
      try {
        log.debug("{}: {}", description, JSON.print(message));
      } catch (InvalidProtocolBufferException e) {
        log.debug("{} printer error", description, e);
      }
    }
  }

  private static ByteString encodeInteger(int key) {
    ByteBuffer buffer = ByteBuffer.allocate(4).putInt(key);
    buffer.rewind();
    return ByteString.copyFrom(buffer);
  }
}
