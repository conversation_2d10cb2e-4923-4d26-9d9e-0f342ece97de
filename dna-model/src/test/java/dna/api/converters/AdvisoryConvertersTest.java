package dna.api.converters;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import com.netflix.cms.protogen.Type;
import dna.api.service.model.AdvisoryType;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

class AdvisoryConvertersTest {

  @ParameterizedTest
  @EnumSource(Type.class)
  void advisoryTypeMapsToCmsType(Type type) {
    assertDoesNotThrow(() -> AdvisoryType.valueOf(type.name()));
  }
}
