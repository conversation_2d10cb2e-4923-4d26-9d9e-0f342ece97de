package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class MembershipUpdatePromoRequest {
  private Long planId;

  private String name;

  private String action;

  private String context;

  /** False by default, true if the plan change is immediate. */
  private Boolean isChargedUpgrade;

  public Long getPlanId() {
    return planId;
  }

  public String getName() {
    return name;
  }

  public String getAction() {
    return action;
  }

  public String getContext() {
    return context;
  }

  public Boolean getIsChargedUpgrade() {
    return isChargedUpgrade;
  }

  public MembershipUpdatePromoRequest setPlanId(Long planId) {
    this.planId = planId;
    return this;
  }

  public MembershipUpdatePromoRequest setName(String name) {
    this.name = name;
    return this;
  }

  public MembershipUpdatePromoRequest setAction(String action) {
    this.action = action;
    return this;
  }

  public MembershipUpdatePromoRequest setContext(String context) {
    this.context = context;
    return this;
  }

  public MembershipUpdatePromoRequest setIsChargedUpgrade(Boolean isChargedUpgrade) {
    this.isChargedUpgrade = isChargedUpgrade;
    return this;
  }
}
