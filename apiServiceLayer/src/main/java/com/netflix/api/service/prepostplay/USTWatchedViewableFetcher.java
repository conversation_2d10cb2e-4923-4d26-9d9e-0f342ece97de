package com.netflix.api.service.prepostplay;

import com.netflix.api.grpc.GrpcCallHelpers.Future;
import com.netflix.api.platform.exception.Exceptions;
import com.netflix.api.service.batch.USTListUtils;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.napa.client.fallback.WatchedViewableFetcher;
import com.netflix.napa.client.fallback.data.WatchedViewableFallbackData;
import com.netflix.p3.converter.fetcher.PostplayViewableDataFetcher;
import com.netflix.p3.converter.fetcher.data.PostplayLiveEventData;
import com.netflix.p3.converter.fetcher.data.PostplayViewableData;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.type.proto.Videos;
import com.netflix.ust.adapters.USTStatus;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ust.video.core.v1.BaseResult;
import ust.video.core.v1.CoreResult;
import ust.video.core.v1.GetVideoBaseRequest;
import ust.video.core.v1.GetVideoBaseResponse;
import ust.video.core.v1.GetVideoCoreRequest;
import ust.video.core.v1.GetVideoCoreResponse;
import ust.video.core.v1.VideoAccess;
import ust.video.v1.VideoBase;
import ust.video.v1.VideoCore;
import ust.video.v1.VideoRequest;

@Component
public class USTWatchedViewableFetcher
    implements WatchedViewableFetcher, PostplayViewableDataFetcher {

  private static final Logger logger = LoggerFactory.getLogger(USTWatchedViewableFetcher.class);

  private final VideoAccess videoAccess;

  @Autowired
  public USTWatchedViewableFetcher(VideoAccess videoAccess) {
    this.videoAccess = videoAccess;
  }

  @Override
  public Optional<WatchedViewableFallbackData> getWatchedViewableData(Integer watchedVideoId) {
    MantisPublishContext.getCurrent().add("api.pp.topNode.fallback.watched", watchedVideoId);
    Optional<WatchedViewableFallbackData> result = Optional.empty();

    try {
      result =
          topNode(watchedVideoId)
              .map(
                  topNodeCore -> {
                    var data =
                        new WatchedViewableFallbackData.Builder()
                            .setWatchedViewableId(watchedVideoId)
                            .setWatchedTopNodeId(topNodeCore.getVideoBase().getBoxedTopNodeId())
                            .setTopNodeType(
                                topNodeCore.getVideoBase().getVideoType().getType().name())
                            .createWatchedViewableFallbackData();
                    DynamicCounter.increment("api.pp.topNode.fallback", "result", "success");
                    MantisPublishContext.getCurrent()
                        .add("api.pp.topNode.fallback.result", "success");
                    return data;
                  });
    } catch (Throwable t) {
      logger.error("Error fetching watched viewable data for {}", watchedVideoId, t);
      DynamicCounter.increment("api.pp.topNode.fallback", "result", "error");
      MantisPublishContext.getCurrent().add("api.pp.topNode.fallback.result", "error");
      MantisPublishContext.getCurrent()
          .add("api.pp.topNode.fallback.error", Exceptions.getStackTrace(t));
    }
    MantisPublishContext.getCurrent()
        .add(
            "api.pp.topNode.fallback.data",
            result.isPresent() ? USTListUtils.toJson(result.get()) : "<empty>");

    return result;
  }

  @Override
  public Optional<PostplayViewableData> getViewableData(Integer watchedVideoId) {
    var mantis = MantisPublishContext.getCurrent();
    mantis.add("api.pp.topNode.fallback.watched", watchedVideoId);
    Optional<PostplayViewableData> result = Optional.empty();

    try {
      result =
          videoCore(watchedVideoId)
              .map(
                  videoCore -> {
                    var topNodeId = videoCore.getVideoBase().getBoxedTopNodeId();
                    var topNode = videoBase(topNodeId).orElse(videoCore.getVideoBase());
                    var dataBuilder =
                        new PostplayViewableData.Builder()
                            .setWatchedViewableId(watchedVideoId)
                            .setWatchedTopNodeId(topNodeId)
                            .setViewableNodeType(
                                videoCore.getVideoBase().getVideoType().getType().name())
                            .setTopNodeType(topNode.getVideoType().getType().name());
                    // TODO(tony): Replace isAvailableForED fetch from VMS with a fetch from
                    // Dataprism.
                    // See document titled "Technical Proposal for Early Access" for context.
                    // https://docs.google.com/document/d/1ChNjLSY04bArHv0qkn46v74g-3m14vOgES7ZkjxT8fA/edit?usp=sharing
                    var availabilityInfo = videoCore.getVideoInfo().getAvailability();
                    dataBuilder.setIsAvailableForEd(availabilityInfo.getAvailable());

                    // TODO(tony): Replace availabilityDate fetch from VMS with a fetch from
                    // Dataprism.
                    dataBuilder.setAvailabilityStartTime(
                        availabilityInfo.getAvailabilityStartTime());
                    dataBuilder.setAvailabilityEndTime(availabilityInfo.getAvailabilityEndTime());

                    boolean hasLiveEvent = availabilityInfo.getLiveEvent();
                    dataBuilder.setHasLiveEvent(hasLiveEvent);
                    if (hasLiveEvent) {
                      dataBuilder
                          .setLaunchDate(availabilityInfo.getLaunchTime())
                          .setPostplayViewableLiveEventData(
                              new PostplayLiveEventData.Builder()
                                  .setLiveEventState(availabilityInfo.getLiveStatus().name())
                                  .setStartTime(availabilityInfo.getLiveStartTime())
                                  .setEndTime(availabilityInfo.getLiveEndTime())
                                  .build());
                    }
                    DynamicCounter.increment("api.pp.topNode.fallback", "result", "success");
                    mantis.add("api.pp.topNode.fallback.result", "success");
                    return dataBuilder.build();
                  });
    } catch (Throwable t) {
      logger.error("Error fetching watched viewable data for {}", watchedVideoId, t);
      DynamicCounter.increment("api.pp.topNode.fallback", "result", "error");
      mantis.add("api.pp.topNode.fallback.result", "error");
      mantis.add("api.pp.topNode.fallback.error", Exceptions.getStackTrace(t));
    }
    mantis.add(
        "api.pp.topNode.fallback.data",
        result.isPresent() ? USTListUtils.toJson(result.get()) : "<empty>");

    return result;
  }

  private Optional<VideoCore> topNode(int videoId) {
    return videoBase(videoId).flatMap(base -> videoCore(base.getBoxedTopNodeId()));
  }

  private Optional<VideoBase> videoBase(Integer videoId) {
    if (videoId == null) return Optional.empty();

    VideoRequest videoRequest =
        VideoRequest.newBuilder().addVideos(Videos.toProtobuf(videoId)).build();

    GetVideoBaseRequest baseRequest =
        GetVideoBaseRequest.newBuilder().setVideoRequest(videoRequest).build();

    try {
      return Future.call(videoAccess::getVideoBase, baseRequest)
          .thenApply(GetVideoBaseResponse::getResultsList)
          .thenApply(
              baseResults ->
                  baseResults.stream()
                      .filter(baseResult -> USTStatus.isOk(baseResult.getStatus()))
                      .findFirst()
                      .map(BaseResult::getResult))
          .toCompletableFuture()
          .get();
    } catch (Exception e) {
      return Exceptions.chainedSneakyThrow(e);
    }
  }

  private Optional<VideoCore> videoCore(Integer videoId) {
    if (videoId == null) return Optional.empty();

    VideoRequest videoRequest =
        VideoRequest.newBuilder().addVideos(Videos.toProtobuf(videoId)).build();

    GetVideoCoreRequest baseRequest =
        GetVideoCoreRequest.newBuilder().setVideoRequest(videoRequest).build();

    try {
      return Future.call(videoAccess::getVideoCore, baseRequest)
          .thenApply(GetVideoCoreResponse::getResultsList)
          .thenApply(
              coreResults ->
                  coreResults.stream()
                      .filter(coreResult -> USTStatus.isOk(coreResult.getStatus()))
                      .findFirst()
                      .map(CoreResult::getResult))
          .toCompletableFuture()
          .get();
    } catch (Exception e) {
      return Exceptions.chainedSneakyThrow(e);
    }
  }
}
