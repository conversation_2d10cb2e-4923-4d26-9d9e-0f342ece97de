package dna.api.datasources;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.google.protobuf.util.Timestamps;
import com.netflix.api.platform.MockPropertyRepositoryConfig;
import com.netflix.demograph.Context;
import com.netflix.uipersonalization.protogen.Device;
import com.netflix.uipersonalization.protogen.Feature;
import com.netflix.uipersonalization.protogen.IncrementFeatureSeenRequest;
import com.netflix.uipersonalization.protogen.IncrementFeatureSeenResponse;
import com.netflix.uipersonalization.protogen.ShouldShowFeatureRequest;
import com.netflix.uipersonalization.protogen.ShouldShowFeatureResponse;
import com.netflix.uipersonalization.protogen.UiCategory;
import com.netflix.uipersonalization.protogen.UiPersonalizationServiceGrpc.UiPersonalizationServiceStub;
import com.netflix.uipersonalization.protogen.User;
import dna.api.service.model.ToolTipFeature;
import io.grpc.stub.StreamObserver;
import java.util.Date;
import netflix.context.client.category.ClientCategory;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class ProfileDataSourceTest {
  private final UiPersonalizationServiceStub mockUIP = mock(UiPersonalizationServiceStub.class);
  private final ProfileDataSource profileDataSource =
      new ProfileDataSource(null, null, null, null, mockUIP);

  @BeforeAll
  static void beforeClass() {
    MockPropertyRepositoryConfig config = new MockPropertyRepositoryConfig();
    config.init();
  }

  @Test
  void shouldShowToolTip_getsBooleanFromUIPersonalization() {
    doAnswer(
            arg -> {
              StreamObserver<ShouldShowFeatureResponse> streamObs = arg.getArgument(1);
              streamObs.onNext(
                  ShouldShowFeatureResponse.newBuilder().setShouldShowFeature(true).build());
              streamObs.onCompleted();
              return null;
            })
        .when(mockUIP)
        .shouldShowFeature(any(), any());

    var currentDate = new Date();

    var future =
        profileDataSource
            .shouldShowToolTip()
            .lookup(
                Context.builder().build(),
                new ToolTipRequest(
                    "some-profile-guid",
                    Timestamps.fromDate(currentDate),
                    ToolTipFeature.PAUSE_AD,
                    ClientCategory.TV))
            .toCompletableFuture();

    var actual = future.join();
    assertTrue(actual);

    verify(mockUIP)
        .shouldShowFeature(
            eq(
                ShouldShowFeatureRequest.newBuilder()
                    .setUser(
                        User.newBuilder()
                            .setProfileGuid("some-profile-guid")
                            .setProfileCreationTime(Timestamps.fromDate(currentDate)))
                    .setDevice(Device.newBuilder().setUiCategory(UiCategory.TV))
                    .setFeature(Feature.PAUSE_AD_EXPLANATION)
                    .build()),
            any());
  }

  @Test
  void toolTipSeen_recordToToolTipSeen() {
    doAnswer(
            arg -> {
              StreamObserver<IncrementFeatureSeenResponse> streamObs = arg.getArgument(1);
              streamObs.onNext(IncrementFeatureSeenResponse.newBuilder().setCount(10101L).build());
              streamObs.onCompleted();
              return null;
            })
        .when(mockUIP)
        .incrementFeatureSeen(any(), any());

    var currentDate = new Date();
    var future =
        profileDataSource
            .toolTipSeen()
            .lookup(
                Context.builder().build(),
                new ToolTipRequest(
                    "some-profile-guid",
                    Timestamps.fromDate(currentDate),
                    ToolTipFeature.MOST_LIKED,
                    ClientCategory.IOS))
            .toCompletableFuture();

    var actual = future.join();
    assertEquals(10101L, actual.longValue());

    verify(mockUIP)
        .incrementFeatureSeen(
            eq(
                IncrementFeatureSeenRequest.newBuilder()
                    .setUser(
                        User.newBuilder()
                            .setProfileGuid("some-profile-guid")
                            .setProfileCreationTime(Timestamps.fromDate(currentDate)))
                    .setDevice(Device.newBuilder().setUiCategory(UiCategory.IOS))
                    .setFeature(Feature.MOST_LIKED_EXPLANATION)
                    .build()),
            any());
  }
}
