package com.netflix.api.service.searchnew;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.util.List;
import java.util.Map;

/** Author: sgudiboina Date: 9/23/16 Time: 5:06 PM */
public class APISearchListImpl implements APISearchList {
  private String referenceId;

  private String displayString;

  private APISearchConstants.SearchListType type;

  @JsonDeserialize(contentAs = APISearchItemImpl.class)
  private List<APISearchItem> items;

  private Integer length;

  private Map<String, String> optionalAttributes;

  @Override
  public String getReferenceId() {
    return referenceId;
  }

  public void setReferenceId(String referenceId) {
    this.referenceId = referenceId;
  }

  @Override
  public String getDisplayString() {
    return displayString;
  }

  public void setDisplayString(String displayString) {
    this.displayString = displayString;
  }

  @Override
  public APISearchConstants.SearchListType getType() {
    return type;
  }

  public void setType(APISearchConstants.SearchListType type) {
    this.type = type;
  }

  @Override
  public List<APISearchItem> getItems() {
    return items;
  }

  public void setItems(List<APISearchItem> items) {
    this.items = items;
  }

  @Override
  public Integer getLength() {
    return length;
  }

  public void setLength(Integer length) {
    this.length = length;
  }

  @Override
  public Map<String, String> getOptionalAttributes() {
    return optionalAttributes;
  }

  public void setOptionalAttributes(Map<String, String> optionalAttributes) {
    this.optionalAttributes = optionalAttributes;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (!(o instanceof APISearchListImpl)) return false;

    APISearchListImpl that = (APISearchListImpl) o;

    return referenceId.equals(that.referenceId);
  }

  @Override
  public int hashCode() {
    return referenceId.hashCode();
  }
}
