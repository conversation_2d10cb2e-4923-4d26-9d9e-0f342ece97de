package com.netflix.api.dependencies.avatar;

import com.google.common.annotations.VisibleForTesting;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class ProfileIconFactory {

  public ProfileIconFactory() {}

  @VisibleForTesting
  List<ProfileIcon> getAvailableIconsFromDefaultIconSet(SortedMap<Long, String> iconNames) {
    List<ProfileIcon> availableIcons = new ArrayList<>();
    Collection<ProfileIcon> usedAvatars = getCurrentlyUsedIcons(iconNames);
    Collection<ProfileIcon> autoAssignedSilhouettes =
        getAutoAssignedSilhouettes(iconNames).values();
    for (ProfileIcon icon : ProfileIcon.ALL_ICONS) {
      if (doesNotContain(icon, usedAvatars) && doesNotContain(icon, autoAssignedSilhouettes)) {
        availableIcons.add(icon);
      }
    }
    return availableIcons;
  }

  private boolean doesNotContain(ProfileIcon icon, Collection<ProfileIcon> collection) {
    String iconName = icon.getIconName();
    for (ProfileIcon potential : collection) {
      if (iconName.equals(potential.getIconName()) || iconName.equals(potential.getMappedName())) {
        return false;
      }
    }
    return true;
  }

  /**
   * Returns all icons that are already taken by any profile on the given users's account This does
   * not include any silhouettes that get auto-assigned to profiles without icons
   *
   * @return the set of all icons which should not be shown to the user for selection
   */
  private Set<ProfileIcon> getCurrentlyUsedIcons(SortedMap<Long, String> iconNames) {
    return iconNames.values().stream()
        .map(this::getIconAvatarByName)
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());
  }

  @VisibleForTesting
  ProfileIcon getIconAvatarByName(String iconName) {
    return Optional.ofNullable(iconName)
        .map(name -> getIconAvatarFromSet(name, ProfileIcon.ALL_ICONS))
        .orElse(getIconAvatarFromSet(iconName, ProfileIcon.HATERS_ICONS));
  }

  private Set<ProfileIcon> getCurrentlyUsedSilhouettes(SortedMap<Long, String> iconNames) {
    Set<ProfileIcon> currentlyUsedSilhouettes = new HashSet<>();
    for (ProfileIcon icon : getCurrentlyUsedIcons(iconNames)) {
      if (icon.isInDefaultSet()) {
        currentlyUsedSilhouettes.add(icon);
      }
    }
    return currentlyUsedSilhouettes;
  }

  /**
   * This concept only exists in V2. It is the set of silhouettes which are given out to profiles
   * with null icons and should not appear available to users on that account.
   */
  private SortedMap<Long, ProfileIcon> getAutoAssignedSilhouettes(
      SortedMap<Long, String> iconNames) {

    SortedMap<Long, ProfileIcon> autoAssignedSilhouettes = new TreeMap<>();
    List<Long> customersWithNullIcon =
        iconNames.entrySet().stream()
            .filter(
                entry -> {
                  String iconName = entry.getValue();
                  return iconName == null
                      || iconName.equals(ProfileIcon.DefaultKidsIcon.getIconName())
                      || iconName.equals(ProfileIcon.WatchTogetherIcon.getIconName());
                })
            .map(Entry::getKey)
            .toList();

    int numAutoAssigned = 0;
    Set<ProfileIcon> takenSilhouettes = getCurrentlyUsedSilhouettes(iconNames);
    for (ProfileIcon silhouette : ProfileIcon.ALL_SILHOUETTES) {
      if (!takenSilhouettes.contains(silhouette)
          && numAutoAssigned < customersWithNullIcon.size()) {
        Long customerId = customersWithNullIcon.get(numAutoAssigned);
        numAutoAssigned++;
        autoAssignedSilhouettes.put(customerId, silhouette);
      }
    }
    return autoAssignedSilhouettes;
  }

  private static ProfileIcon getIconAvatarFromSet(
      String iconName, Collection<ProfileIcon> profileIcons) {
    return Optional.ofNullable(iconName)
        .map(String::trim)
        .flatMap(
            trimmed ->
                profileIcons.stream()
                    .filter(
                        profileIcon ->
                            profileIcon.getIconName().equalsIgnoreCase(trimmed)
                                || profileIcon.getMappedName().equalsIgnoreCase(trimmed))
                    .findFirst())
        .orElse(null);
  }
}
