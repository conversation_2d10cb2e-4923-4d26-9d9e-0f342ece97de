package com.netflix.api.service.video;

import com.netflix.videometadata.image.AspectRatio;
import com.netflix.videometadata.image.ImageFormat;

public enum APIAspectRatio {
  SD(AspectRatio.SD),
  HD(AspectRatio.HD),
  UNKNOWN(AspectRatio.UNKNOWN);

  private final AspectRatio underlying;

  APIAspectRatio(AspectRatio aspectRatio) {
    this.underlying = aspectRatio;
  }

  /* package private */ AspectRatio getUnderlying() {
    return underlying;
  }

  @Deprecated
  public static APIAspectRatio valueOf(AspectRatio aspectRatio) {
    return switch (aspectRatio) {
      case HD -> APIAspectRatio.HD;
      case SD -> APIAspectRatio.SD;
      default -> APIAspectRatio.UNKNOWN;
    };
  }

  public static APIAspectRatio valueOf(APIImageFormat format) {
    return switch (format) {
      case HD -> APIAspectRatio.HD;
      case SD -> APIAspectRatio.SD;
      default -> APIAspectRatio.UNKNOWN;
    };
  }

  @Deprecated
  public static APIAspectRatio valueOf(ImageFormat format) {
    return switch (format) {
      case HD -> APIAspectRatio.HD;
      case SD -> APIAspectRatio.SD;
      default -> APIAspectRatio.UNKNOWN;
    };
  }

  public static APIImageFormat asImageFormat(APIAspectRatio aspectRatio) {
    return switch (aspectRatio) {
      case HD -> APIImageFormat.HD;
      case SD -> APIImageFormat.SD;
      default -> APIImageFormat.UNKNOWN;
    };
  }
}
