package com.netflix.api.service.video;

import com.google.common.base.Strings;
import com.ibm.icu.util.ULocale;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.dependencies.device.DeviceTypeRequest;
import com.netflix.api.platform.NetflixESN;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.deprecated.DeprecatedMethodTracker;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.api.service.identity.APIPlanInfo;
import com.netflix.api.service.identity.APIUser;
import com.netflix.i18n.NFFormattingHelper;
import com.netflix.i18n.NFICUMessageFormat;
import com.netflix.i18n.NFLocale;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.streaming.dts.common.model.DeviceType;
import com.netflix.textevidence.protogen.TitleType;
import com.netflix.textevidence.protogen.VideoType;
import com.netflix.type.ISOCountry;
import com.netflix.type.NFCountry;
import com.netflix.util.Pair;
import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.vms.provider.VideoFormatDescriptorProvider;
import com.netflix.vms.type.VideoFormatDescriptor;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import rx.Observable;

@SuppressWarnings({"deprecation"})
@EnableDeprecatedMetrics
public abstract class APICachedVideoImpl implements APIVideo {

  protected final CompleteVideo completeVideo;
  protected final ISOCountry country;
  protected final NFLocale locale;

  /* Internal model that we will forward calls to */
  private final Integer id;
  protected final APIVideoFactoryImpl videoFactory;
  private final PlanHelper planHelper = ProdPlanHelper.getInstance();
  private final VideoFormatDescriptorProvider videoFormatDescriptorProvider;
  protected final USTVideoMetadataAdapter videoMetadataAdapter;

  protected APICachedVideoImpl(
      CompleteVideo video,
      APIVideoFactory videoFactory,
      USTVideoMetadataAdapter videoMetadataAdapter,
      VideoFormatDescriptorProvider videoFormatDescriptorProvider) {
    this.videoFactory = (APIVideoFactoryImpl) videoFactory;
    this.completeVideo = video;
    this.videoFormatDescriptorProvider = videoFormatDescriptorProvider;
    this.id = video.getId();
    NFLocale l = RequestContextWrapper.get().getNfLocale();
    this.locale = l != null ? l : NFLocale.ENGLISH;
    ISOCountry c = RequestContextWrapper.get().getCountry();
    this.country = c == null ? NFCountry.US : c;
    this.videoMetadataAdapter = videoMetadataAdapter;
  }

  protected com.netflix.evidence.protogen.LabelType buildLabelType(LabelType labelType) {
    if (labelType == null) {
      return com.netflix.evidence.protogen.LabelType.UNRECOGNIZED; // should never happen
    }
    return com.netflix.evidence.protogen.LabelType.valueOf(labelType.name());
  }

  protected Integer getTopNode(List<Integer> o, List<Integer> s) {
    if (s == null) s = List.of();
    if (o == null) o = List.of();

    List<Integer> mergedList = new ArrayList<>(s);
    mergedList.addAll(o);

    var map = videoFactory.getVideoLookup().getVideos(mergedList);
    for (Integer v : mergedList) {
      if (v == null) continue;

      CompleteVideo topNodeCompleteVideo = map.get(v).orElse(null);
      if (topNodeCompleteVideo != null) {
        return topNodeCompleteVideo.getId();
      }
    }
    return null;
  }

  // BASIC METADATA API implementations
  @Override
  public Integer getId() {
    return id;
  }

  @Override
  public boolean isViewable() {
    return this instanceof APIViewable;
  }

  @Override
  public APISupplementalVideo asSupplementalVideo() {
    if (this instanceof APISupplementalVideo supplementalVideo) {
      return supplementalVideo;
    }
    return null;
  }

  @Override
  public APIShow asShow() {
    if (this instanceof APIShow show) {
      return show;
    }
    return null;
  }

  @Override
  @Deprecated
  public String getName() {
    return getTitle();
  }

  private String getTitle() {
    return videoMetadataAdapter
        .getTitle(TitleType.REGULAR, VideoType.COMPLETE_VIDEO, this.getId())
        .toBlocking()
        .first();
  }

  @Deprecated
  private boolean _isAvailableInFormat(int videoFormatId) {
    return _isAvailableInFormat(videoFormatId, false);
  }

  @Deprecated
  private boolean _isAvailableInFormat(int videoFormatId, boolean ignoreDevice) {
    return _cupToken(ignoreDevice).filter(value -> _format(videoFormatId, value)).isPresent();
  }

  private boolean _format(int videoFormatId, String cupToken) {
    return completeVideo.isAvailableInFormat(
        cupToken, videoFormatDescriptorProvider.getData(videoFormatId));
  }

  private Optional<String> _cupToken(boolean ignoreDevice) {
    Integer deviceTypeId = null;
    String esn = null;
    if (!ignoreDevice) {
      deviceTypeId = APIRequest.getCurrentRequest().getRequestContext().getDeviceTypeId();
      NetflixESN netflixEsn = NetflixESN.getCurrent();
      if (netflixEsn != null) {
        if (!Strings.isNullOrEmpty(netflixEsn.getESN())) {
          esn = netflixEsn.getESN();
        }
      }
    }

    boolean synthetic = deviceTypeId == null && esn == null;
    DynamicCounter.increment(
        "api.video.cupToken.synthetic", "synthetic", Boolean.toString(synthetic));
    return Optional.ofNullable(
            videoFactory.deviceTypeFactory.get(
                new DeviceTypeRequest()
                    .withDeviceTypeId(deviceTypeId)
                    .withEsn(esn)
                    .withSyntheticDeviceOnNullInput()))
        .map(DeviceType::getDeviceCUPCategory);
  }

  @Override
  @Deprecated
  public boolean isHD() {
    trackDeprecated("isHD");
    if (planHelper.isHDEnabledPlan(APIRequest.getCurrentRequest())) {
      return false;
    } else {
      return _isAvailableInFormat(VideoFormatDescriptor.ID_HD);
    }
  }

  @Override
  @Deprecated
  public boolean isSuperHD() {
    trackDeprecated("isSuperHD");
    if (planHelper.isHDEnabledPlan(APIRequest.getCurrentRequest())) {
      return false;
    } else {
      return _isAvailableInFormat(VideoFormatDescriptor.ID_SUPER_HD);
    }
  }

  @Override
  @Deprecated
  public boolean isUltraHD() {
    trackDeprecated("isUltraHD");
    if (!planHelper.isUHDEnabledPlan(APIRequest.getCurrentRequest())) {
      return false;
    } else {
      return _isAvailableInFormat(VideoFormatDescriptor.ID_ULTRA_HD);
    }
  }

  protected void trackDeprecated(String clazz, String method, List<String> params) {
    DeprecatedMethodTracker.incrementDeprecatedMetrics(
        clazz,
        method,
        params,
        APIRequest.getCurrentRequest().getRequestContext().getEndpointPath());
  }

  private void trackDeprecated(String method) {
    trackDeprecated(APICachedVideoImpl.class.getName(), method, Collections.emptyList());
  }

  String getFormattedString(String locale, String template, Map<String, Object> variables) {
    ULocale originalULocale = NFLocale.findInstance(locale).toULocale();

    NFLocale formattingLocale =
        NFLocale.fromULocale(
            Optional.ofNullable(NFFormattingHelper.localeOverrides(originalULocale))
                .orElse(originalULocale));
    return new NFICUMessageFormat(template, formattingLocale).format(variables);
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (!(o instanceof APICachedVideoImpl apiVideo)) return false;

    return getId().equals(apiVideo.getId());
  }

  @Override
  public int hashCode() {
    return completeVideo.getId();
  }

  private interface PlanHelper {

    boolean isHDEnabledPlan(APIRequest currentRequest);

    boolean isUHDEnabledPlan(APIRequest currentRequest);
  }

  public static class ProdPlanHelper implements PlanHelper {

    private static final ProdPlanHelper INSTANCE = new ProdPlanHelper();

    private ProdPlanHelper() {}

    static PlanHelper getInstance() {
      return INSTANCE;
    }

    @Override
    public boolean isHDEnabledPlan(APIRequest currentRequest) {
      APIPlanInfo planInfo = getInternalPlanInfo(currentRequest);
      return planInfo != null && !planInfo.isHDEnabled();
    }

    @Override
    public boolean isUHDEnabledPlan(APIRequest currentRequest) {
      APIPlanInfo planInfo = getInternalPlanInfo(currentRequest);
      return planInfo == null || planInfo.isUHDEnabled();
    }

    private static APIPlanInfo getInternalPlanInfo(APIRequest currentRequest) {
      if (currentRequest == null) return null;
      APIUser user = currentRequest.getUser();
      if (user == null) return null;
      return user.getPlanInfo().toBlocking().firstOrDefault(null);
    }
  }

  public CompleteVideo getSource() {
    return completeVideo;
  }

  public NFLocale getLocale() {
    return locale;
  }

  public Observable<String> getNumSeasonsLabel(
      LabelType labelType, int numSeasons, APICachedVideoImpl firstSeason) {
    return getNumSeasonsLabel(labelType, numSeasons, firstSeason.completeVideo);
  }

  // TODO move elsewhere but we need access to completeVideo
  public Observable<Pair<Integer, String>> getNumSeasonsLabel(
      Integer showId, LabelType labelType, int numSeasons, APICachedVideoImpl firstSeason) {
    Observable<String> obs = getNumSeasonsLabel(labelType, numSeasons, firstSeason.completeVideo);
    return obs.map(label -> new Pair<>(showId, label));
  }

  public Observable<String> getNumSeasonsLabel(
      LabelType labelType, int numSeasons, CompleteVideo firstSeason) {
    // get showMemberType
    String showMemberType = "Season";
    if (firstSeason != null) {
      String temp =
          firstSeason.getShowMemberType() == null
              ? "Season"
              : firstSeason.getShowMemberType().name();
      if (!("Unknown".equalsIgnoreCase(temp)
          || "NO_NAV_LABEL".equalsIgnoreCase(temp)
          || temp.isEmpty())) showMemberType = temp;
    }

    // default template to use if obelix barfs
    String defaultTemplate = "{numSeasons, plural, =0 {} one {# Season} other {# Seasons}}";

    // resolve locale
    String loc =
        APIRequest.getCurrentRequest().getRequestContext() == null
                || APIRequest.getCurrentRequest().getRequestContext().getLocale() == null
            ? "en-US"
            : APIRequest.getCurrentRequest().getRequestContext().getLocale().getId();
    // request resource value from obelix, and format it using ICUMessageFormat
    return videoFactory
        .obelixServiceProvider
        .get()
        .getSimpleStringProperty(
            "Default", // namespace name
            "SeasonLabelsV2", // bundle name
            loc, // locale
            "numSeasonsLabel." + labelType.name() + "." + showMemberType, // property name
            defaultTemplate // default value
            )
        .onErrorResumeNext(e -> Observable.just(defaultTemplate))
        .defaultIfEmpty(defaultTemplate)
        .map(
            s -> {
              String template = s == null ? defaultTemplate : s;
              return getFormattedString(loc, template, Map.of("numSeasons", numSeasons));
            });
  }
}
