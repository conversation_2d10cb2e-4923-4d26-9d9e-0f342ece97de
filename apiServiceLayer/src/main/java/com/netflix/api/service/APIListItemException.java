package com.netflix.api.service;

/**
 * Wrapper around an exception when loading a list item. Has the root cause, and the index which
 * failed to be loaded.
 */
public class APIListItemException extends RuntimeException {
  private final int index;
  private static final String ERROR_MESSAGE_TEMPLATE = "Index : %d, Error Message : %s";

  public APIListItemException(int index, Throwable rootCause) {
    super(rootCause);
    this.index = index;
  }

  @Override
  public String getMessage() {
    return String.format(ERROR_MESSAGE_TEMPLATE, index, getCause().getMessage());
  }

  public int getIndex() {
    return index;
  }
}
