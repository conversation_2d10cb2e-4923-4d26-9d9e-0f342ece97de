package com.netflix.api.startup.priming;

import com.google.inject.Injector;
import com.google.inject.multibindings.MapBinderBinding;
import com.google.inject.multibindings.MultibinderBinding;
import com.google.inject.multibindings.MultibindingsTargetVisitor;
import com.google.inject.multibindings.OptionalBinderBinding;
import com.google.inject.spi.ConstructorBinding;
import com.google.inject.spi.ConvertedConstantBinding;
import com.google.inject.spi.Dependency;
import com.google.inject.spi.ExposedBinding;
import com.google.inject.spi.HasDependencies;
import com.google.inject.spi.InstanceBinding;
import com.google.inject.spi.LinkedKeyBinding;
import com.google.inject.spi.ProviderBinding;
import com.google.inject.spi.ProviderInstanceBinding;
import com.google.inject.spi.ProviderKeyBinding;
import com.google.inject.spi.UntargettedBinding;
import com.netflix.governator.providers.AdvicesDependenciesFinder;
import java.util.HashSet;
import java.util.Set;

public class DependencyBindingTargetVisitor
    implements MultibindingsTargetVisitor<Object, Set<Dependency<?>>> {

  private final Injector injector;

  public DependencyBindingTargetVisitor(Injector injector) {
    this.injector = injector;
  }

  public Set<Dependency<?>> visit(InstanceBinding<?> instanceBinding) {
    return instanceBinding.getDependencies();
  }

  public Set<Dependency<?>> visit(ProviderInstanceBinding<?> providerInstanceBinding) {
    Set<Dependency<?>> dependencies = providerInstanceBinding.getDependencies();
    Set<Dependency<?>> advicesDependency =
        AdvicesDependenciesFinder.findDependencies(injector, providerInstanceBinding);
    if (advicesDependency != null) {
      var set = new HashSet<>(dependencies);
      set.addAll(advicesDependency);
      return Set.copyOf(set);
    }
    return dependencies;
  }

  public Set<Dependency<?>> visit(ProviderKeyBinding<?> providerKeyBinding) {
    return injector.getBinding(providerKeyBinding.getProviderKey()).acceptTargetVisitor(this);
  }

  public Set<Dependency<?>> visit(LinkedKeyBinding<?> linkedKeyBinding) {
    return injector.getBinding(linkedKeyBinding.getLinkedKey()).acceptTargetVisitor(this);
  }

  public Set<Dependency<?>> visit(ExposedBinding<?> exposedBinding) {
    return Set.of();
  }

  public Set<Dependency<?>> visit(UntargettedBinding<?> untargettedBinding) {
    return Set.of();
  }

  public Set<Dependency<?>> visit(ConstructorBinding<?> constructorBinding) {
    return constructorBinding.getDependencies();
  }

  public Set<Dependency<?>> visit(ConvertedConstantBinding<?> convertedConstantBinding) {
    return convertedConstantBinding.getDependencies();
  }

  public Set<Dependency<?>> visit(ProviderBinding<?> providerBinding) {
    return injector.getBinding(providerBinding.getProvidedKey()).acceptTargetVisitor(this);
  }

  @Override
  public Set<Dependency<?>> visit(MultibinderBinding<?> multibinding) {
    if (multibinding instanceof HasDependencies binding) {
      return binding.getDependencies();
    }
    return Set.of();
  }

  @Override
  public Set<Dependency<?>> visit(MapBinderBinding<?> mapbinding) {
    if (mapbinding instanceof HasDependencies binding) {
      return binding.getDependencies();
    }
    return Set.of();
  }

  @Override
  public Set<Dependency<?>> visit(OptionalBinderBinding<?> optionalbinding) {
    if (optionalbinding instanceof HasDependencies binding) {
      return binding.getDependencies();
    }
    return Set.of();
  }
}
