package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class ContentAdvisory {
  private String rating;

  /** @deprecated Use playbackAdvisory.advisories instead */
  @Deprecated private String advisories;

  /** @deprecated Use playbackAdvisory.displayDurationInSeconds instead */
  @Deprecated private Integer displayDurationInSeconds;

  private PlaybackAdvisory playbackAdvisory;

  private ContentAdvisoryIcons icons;

  public String getRating() {
    return rating;
  }

  @Deprecated
  public String getAdvisories() {
    return advisories;
  }

  @Deprecated
  public Integer getDisplayDurationInSeconds() {
    return displayDurationInSeconds;
  }

  public PlaybackAdvisory getPlaybackAdvisory() {
    return playbackAdvisory;
  }

  public ContentAdvisoryIcons getIcons() {
    return icons;
  }

  public ContentAdvisory setRating(String rating) {
    this.rating = rating;
    return this;
  }

  @Deprecated
  public ContentAdvisory setAdvisories(String advisories) {
    this.advisories = advisories;
    return this;
  }

  @Deprecated
  public ContentAdvisory setDisplayDurationInSeconds(Integer displayDurationInSeconds) {
    this.displayDurationInSeconds = displayDurationInSeconds;
    return this;
  }

  public ContentAdvisory setPlaybackAdvisory(PlaybackAdvisory playbackAdvisory) {
    this.playbackAdvisory = playbackAdvisory;
    return this;
  }

  public ContentAdvisory setIcons(ContentAdvisoryIcons icons) {
    this.icons = icons;
    return this;
  }
}
