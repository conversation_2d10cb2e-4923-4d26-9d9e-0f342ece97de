package com.netflix.api.service.vhs.async;

import com.google.common.base.Preconditions;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.ksclient.EventMap;
import com.netflix.ksclient.KsProducer;
import com.netflix.ksclient.KsProducerBuilder;
import com.netflix.ksclient.exception.BufferExhaustedException;
import com.netflix.ksclient.exception.SerializationException;
import com.netflix.playapi.events.streaming.EventQueueKey;
import com.netflix.playapi.events.streaming.PlaybackEvent;
import com.netflix.playapi.events.user.ViewingHistoryHideRequest;
import com.netflix.spectator.api.Registry;
import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ** <code>EnqueuePlaybackEventProvider</code> is responsible for publishing event to kafka topic
 */
@Component
public class EnqueuePlaybackEventProvider {
  private static final Logger LOG = LoggerFactory.getLogger(EnqueuePlaybackEventProvider.class);
  private static final String ON_SENT_TAG = "onSent";
  private static final String ON_ATTEMPT_TAG = "onAttempt";
  private static final String REASON_TAG = "reason";

  public static class Holder {
    protected static volatile EnqueuePlaybackEventProvider instance = null;
  }

  public static EnqueuePlaybackEventProvider getInstance() {
    return Preconditions.checkNotNull(
        EnqueuePlaybackEventProvider.Holder.instance,
        "EnqueuePlaybackEventProvider not initialized yet.");
  }

  private final KsProducer<EventMap> ksProducer;
  private final KafkaPlaybackEventMetric kafkaPlaybackEventMetric;
  private final Registry registry;

  private static final Property<String> QUEUE_NAMESPACE =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.events.queue.namespace", String.class)
          .orElse("dm");
  private static final Property<Boolean> ENABLED_SPILLOVER =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.events.spillover.enabled", Boolean.class)
          .orElse(true);
  private final PlaybackEventTopicProvider playbackEventTopicProvider;

  @Autowired
  public EnqueuePlaybackEventProvider(
      KsProducerBuilder ksProducerBuilder,
      KafkaPlaybackEventMetric kafkaPlaybackEventMetric,
      Registry registry,
      PlaybackEventTopicProvider playbackEventTopicProvider) {
    this.kafkaPlaybackEventMetric = kafkaPlaybackEventMetric;
    this.registry = registry;
    this.playbackEventTopicProvider = playbackEventTopicProvider;
    this.ksProducer = ksProducerBuilder.build(QUEUE_NAMESPACE.get(), EventMap.class);
  }

  @PostConstruct
  public void init() {
    Preconditions.checkArgument(
        EnqueuePlaybackEventProvider.Holder.instance == null,
        "EnqueuePlaybackEventProvider has already been initialized.");
    EnqueuePlaybackEventProvider.Holder.instance = this;
  }

  public Void perform(PlaybackEvent playbackEvent) {
    PlaybackEventTopic playbackEventTopicCrossRegion =
        playbackEventTopicProvider.getPlaybackEventTopicCrossRegion(playbackEvent);
    EventMap eventMap =
        getEventMap(playbackEventTopicCrossRegion.getName(), playbackEvent.toByteArray());
    PlaybackEventTopic spilloverTopic = playbackEventTopicProvider.getSpilloverTopic(playbackEvent);
    queue(eventMap, playbackEventTopicCrossRegion, spilloverTopic);
    return null;
  }

  public boolean enqueue(ViewingHistoryHideRequest viewingHistoryHideRequest) {
    EventMap eventData =
        getEventMap(
            PlaybackEventTopic.PLAYBACK_HIDE_VIEWING_HISTORY_REQUEST.getName(),
            viewingHistoryHideRequest.toByteArray());
    queue(eventData, PlaybackEventTopic.PLAYBACK_HIDE_VIEWING_HISTORY_REQUEST, null);
    return true;
  }

  private void queue(
      EventMap eventData,
      PlaybackEventTopic topic,
      @Nullable PlaybackEventTopic possibeSpilloverTopic) {
    Map<String, String> tags = new HashMap<>();
    tags.put(ON_ATTEMPT_TAG, "true");
    final long start = registry.clock().monotonicTime();
    try {
      ksProducer
          .send(eventData)
          .whenCompleteAsync(
              (result, exception) -> {
                if (exception != null) {
                  measureFailure(topic, start, tags, exception);
                  Optional.ofNullable(possibeSpilloverTopic)
                      .ifPresent(spilloverTopic -> spillover(eventData, spilloverTopic));
                } else {
                  measureSuccess(topic, start, tags);
                }
              });
    } catch (Exception e) {
      measureFailure(topic, start, tags, e);
      Optional.ofNullable(possibeSpilloverTopic)
          .ifPresent(spilloverTopic -> spillover(eventData, spilloverTopic));
    }
  }

  private void spillover(EventMap eventMap, @Nullable PlaybackEventTopic spilloverTopic) {
    if (!ENABLED_SPILLOVER.get()) {
      return;
    }

    EventMap eventMapFallback = EventMap.build(spilloverTopic.getName());
    eventMapFallback.putAll(eventMap);

    Map<String, String> tags = new HashMap<>();
    tags.put(ON_ATTEMPT_TAG, "true");

    final long start = registry.clock().monotonicTime();

    try {
      ksProducer
          .send(eventMapFallback)
          .whenCompleteAsync(
              (result, exception) -> {
                if (exception != null) {
                  measureFailure(spilloverTopic, start, tags, exception);
                } else {
                  measureSuccess(spilloverTopic, start, tags);
                }
              });
    } catch (Exception e) {
      measureFailure(spilloverTopic, start, tags, e);
      LOG.error("Error enqueing to spillover", e);
    }
  }

  private String toReason(Throwable e) {
    if (e instanceof BufferExhaustedException) {
      return "BUFFER_EXHAUSTED";
    }

    if (e instanceof SerializationException) {
      return "SERIALIZATION";
    }
    return "EXCEPTION";
  }

  private void measureFailure(
      PlaybackEventTopic topic, long start, Map<String, String> tags, Throwable e) {
    LOG.error("Error queuing playback event", e);
    long elapsed = registry.clock().monotonicTime() - start;

    tags.put(ON_SENT_TAG, "false");
    tags.put(REASON_TAG, toReason(e));

    kafkaPlaybackEventMetric.measure(false, elapsed, topic, Map.copyOf(tags));
  }

  private void measureSuccess(PlaybackEventTopic topic, long start, Map<String, String> tags) {
    long elapsed = registry.clock().monotonicTime() - start;

    tags.put(ON_SENT_TAG, "true");

    kafkaPlaybackEventMetric.measure(true, elapsed, topic, Map.copyOf(tags));
  }

  private static EventMap getEventMap(String topic, byte[] bytes) {
    EventMap eventMap = EventMap.build(topic);
    eventMap.put(EventQueueKey.TRANSACTION_ID.name(), UUID.randomUUID().toString());
    eventMap.put(EventQueueKey.EVENT_BYTE_ARRAY.name(), bytes);
    return eventMap;
  }
}
