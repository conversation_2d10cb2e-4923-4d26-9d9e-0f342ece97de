package dna.api.service.model;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.netflix.demograph.graph.Atom;
import java.util.HashMap;
import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

/** @dotnext com.netflix.api.service.promotion.APIExtrasItem */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class ExtrasItem implements GraphData {
  public String dataSource;

  public String postId;

  public String postCategoryType;

  public String postCategoryLabel;

  public String postType;

  public String imageType;

  public String aspectRatio;

  public Integer numImages;

  public Boolean burnedInDialog;

  public Boolean shouldLoop;

  public Boolean silent;

  public Atom<List<String>> validLanguages;

  public String postText;

  public String postTitle;

  public String postSubtitle;

  public Long publishDate;

  public String postState;

  public Boolean pushNotification;

  public Boolean featuredPost;

  public Atom<List<String>> actions;

  public Video postVideo;

  public Video metadataVideo;

  public Video topNodeVideo;

  public Video playableVideo;

  public Video tagsVideo;

  public Video imageRequestVideo;

  private HashMap<String, Object> attributeToValue;

  public String getDataSource() {
    return dataSource;
  }

  public String getPostId() {
    return postId;
  }

  public String getPostCategoryType() {
    return postCategoryType;
  }

  public String getPostCategoryLabel() {
    return postCategoryLabel;
  }

  public String getPostType() {
    return postType;
  }

  public String getImageType() {
    return imageType;
  }

  public String getAspectRatio() {
    return aspectRatio;
  }

  public Integer getNumImages() {
    return numImages;
  }

  public Boolean getBurnedInDialog() {
    return burnedInDialog;
  }

  public Boolean getShouldLoop() {
    return shouldLoop;
  }

  public Boolean getSilent() {
    return silent;
  }

  public Atom<List<String>> getValidLanguages() {
    return validLanguages;
  }

  public String getPostText() {
    return postText;
  }

  public String getPostTitle() {
    return postTitle;
  }

  public String getPostSubtitle() {
    return postSubtitle;
  }

  public Long getPublishDate() {
    return publishDate;
  }

  public String getPostState() {
    return postState;
  }

  public Boolean getPushNotification() {
    return pushNotification;
  }

  public Boolean getFeaturedPost() {
    return featuredPost;
  }

  public Atom<List<String>> getActions() {
    return actions;
  }

  public ExtrasItem setDataSource(String dataSource) {
    this.dataSource = dataSource;
    return this;
  }

  public ExtrasItem setPostId(String postId) {
    this.postId = postId;
    return this;
  }

  public ExtrasItem setPostCategoryType(String postCategoryType) {
    this.postCategoryType = postCategoryType;
    return this;
  }

  public ExtrasItem setPostCategoryLabel(String postCategoryLabel) {
    this.postCategoryLabel = postCategoryLabel;
    return this;
  }

  public ExtrasItem setPostType(String postType) {
    this.postType = postType;
    return this;
  }

  public ExtrasItem setImageType(String imageType) {
    this.imageType = imageType;
    return this;
  }

  public ExtrasItem setAspectRatio(String aspectRatio) {
    this.aspectRatio = aspectRatio;
    return this;
  }

  public ExtrasItem setNumImages(Integer numImages) {
    this.numImages = numImages;
    return this;
  }

  public ExtrasItem setBurnedInDialog(Boolean burnedInDialog) {
    this.burnedInDialog = burnedInDialog;
    return this;
  }

  public ExtrasItem setShouldLoop(Boolean shouldLoop) {
    this.shouldLoop = shouldLoop;
    return this;
  }

  public ExtrasItem setSilent(Boolean silent) {
    this.silent = silent;
    return this;
  }

  public ExtrasItem setValidLanguages(Atom<List<String>> validLanguages) {
    this.validLanguages = validLanguages;
    return this;
  }

  public ExtrasItem setPostText(String postText) {
    this.postText = postText;
    return this;
  }

  public ExtrasItem setPostTitle(String postTitle) {
    this.postTitle = postTitle;
    return this;
  }

  public ExtrasItem setPostSubtitle(String postSubtitle) {
    this.postSubtitle = postSubtitle;
    return this;
  }

  public ExtrasItem setPublishDate(Long publishDate) {
    this.publishDate = publishDate;
    return this;
  }

  public ExtrasItem setPostState(String postState) {
    this.postState = postState;
    return this;
  }

  public ExtrasItem setPushNotification(Boolean pushNotification) {
    this.pushNotification = pushNotification;
    return this;
  }

  public ExtrasItem setFeaturedPost(Boolean featuredPost) {
    this.featuredPost = featuredPost;
    return this;
  }

  public ExtrasItem setActions(Atom<List<String>> actions) {
    this.actions = actions;
    return this;
  }

  @JsonAnyGetter
  public HashMap<String, Object> attributeToValue() {
    return this.attributeToValue;
  }

  public Object getAttribute(String attribute) {
    return this.attributeToValue != null ? this.attributeToValue.get(attribute) : null;
  }

  @JsonAnySetter
  public void setAttribute(String key, Object value) {
    if (attributeToValue == null) {
      attributeToValue = new HashMap<>();
    }
    this.attributeToValue.put(key, value);
  }
}
