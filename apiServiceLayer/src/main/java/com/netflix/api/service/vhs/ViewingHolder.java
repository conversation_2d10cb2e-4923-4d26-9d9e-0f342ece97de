package com.netflix.api.service.vhs;

import com.netflix.streamsaccountingservice.protogen.ActiveSessionInfo;

public class ViewingHolder {
  ActiveSessionInfo activeSessionInfo;
  int deviceTypeId;

  ViewingHolder(ActiveSessionInfo activeSessionInfo, int deviceTypeId) {
    this.activeSessionInfo = activeSessionInfo;
    this.deviceTypeId = deviceTypeId;
  }

  public static ViewingHolderBuilder builder() {
    return new ViewingHolderBuilder();
  }

  public static class ViewingHolderBuilder {

    private ActiveSessionInfo activeSessionInfo;
    private int deviceTypeId;

    ViewingHolderBuilder() {}

    public ViewingHolderBuilder activeSessionInfo(ActiveSessionInfo activeSessionInfo) {
      this.activeSessionInfo = activeSessionInfo;
      return this;
    }

    public ViewingHolderBuilder deviceTypeId(int deviceTypeId) {
      this.deviceTypeId = deviceTypeId;
      return this;
    }

    public ViewingHolder build() {
      return new ViewingHolder(this.activeSessionInfo, this.deviceTypeId);
    }

    public String toString() {
      return "ViewingHolder.ViewingHolderBuilder(activeSessionInfo="
          + this.activeSessionInfo
          + ", deviceTypeId="
          + this.deviceTypeId
          + ")";
    }
  }
}
