package com.netflix.api.service.video;

import com.google.common.base.Preconditions;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.facade.service.collection.CollectionData;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.request.builders.GPSClientSettings;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import com.netflix.api.service.batch.USTListAdapter;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.map.annotation.MapAnnotationConstants;
import com.netflix.map.datatypes.MapResponse;
import com.netflix.type.ISOCountry;
import com.netflix.type.NFCountry;
import com.netflix.videometadata.l10n.VMSL10NProviderDelegate;
import com.netflix.videometadata.type.Certification;
import com.netflix.videometadata.type.CompleteVideo;
import jakarta.annotation.Nullable;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.Observable;
import rx.Single;

/**
 * This object encapsulates the genre of a movie.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.related1.png">
 * <img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.related2.png">
 */
@EnableDeprecatedMetrics
public class APIGenreImpl implements APIGenre {
  private final Logger logger = LoggerFactory.getLogger(APIGenreImpl.class);

  private static final Comparator<APICertification> MATURITY_LEVEL_COMPARATOR =
      Comparator.comparingInt(APICertification::getMaturityLevel);

  private final String uuid;
  private final CollectionData collection;
  private final APIVideoFactory videoFactory;
  private final VMSL10NProviderDelegate vmsl10NProviderDelegate;
  private final USTListAdapter listAdapter;

  public APIGenreImpl(
      CollectionData collection,
      APIVideoFactory videoFactory,
      VMSL10NProviderDelegate vmsl10NProviderDelegate,
      USTListAdapter listAdapter) {
    this(collection, null, videoFactory, vmsl10NProviderDelegate, listAdapter);
  }

  public APIGenreImpl(
      CollectionData collection,
      @Nullable String uuid,
      APIVideoFactory videoFactory,
      VMSL10NProviderDelegate vmsl10NProviderDelegate,
      USTListAdapter listAdapter) {
    this.collection = Preconditions.checkNotNull(collection);
    this.uuid = uuid;
    this.videoFactory = videoFactory;
    this.vmsl10NProviderDelegate = vmsl10NProviderDelegate;
    this.listAdapter = listAdapter;
  }

  @Override
  @Deprecated
  public Integer getId() {
    return (int) collection.getId();
  }

  @Override
  public long getSourceId() {
    return collection.getId();
  }

  @Override
  public String getName() {
    return collection.getName();
  }

  @Override
  public String getNavigationMenuName() {
    return collection.getNavigationMenuName();
  }

  @Override
  public boolean hasNonMemberType() {
    return collection.hasNonMemberType();
  }

  @Override
  public Set<String> types() {
    return collection.getTypes();
  }

  @Override
  public String getStringAttribute(String name) {
    return convertAttribute(name, String.class, collection.getAttributes());
  }

  @Override
  public Integer getIntAttribute(String name) {
    return convertAttribute(name, Integer.class, collection.getAttributes());
  }

  @Override
  public Long getLongAttribute(String name) {
    return convertAttribute(name, Long.class, collection.getAttributes());
  }

  @Override
  public Date getDateAttribute(String name) {
    return convertAttribute(name, Date.class, collection.getAttributes());
  }

  @Override
  public Boolean getBooleanAttribute(String name) {
    return convertAttribute(name, Boolean.class, collection.getAttributes());
  }

  @Override
  public String getUUID() {
    return uuid;
  }

  public static String toGalleryOrder(APIVideoListOrder sortOrder) {
    return switch (sortOrder) {
      case ROW -> "row";
      case SUGGESTIONS -> "pvr";
      case RATINGS -> "cinematch";
      case AtoZ -> "alphabetical";
      case RELEASE_DATE -> "releaseDate";
      case ZtoA -> "reverseAlphabetical";
      default -> "natural";
    };
  }

  // Default maximum number of videos emitted unless overridden by maxResults
  private static final int DEFAULT_MAX_VIDEO_RESULTS = 75;

  // Default sorting order of videos emitted unless overridden by sortOrder
  private static final APIVideoListOrder DEFAULT_SORT_ORDER = APIVideoListOrder.SUGGESTIONS;

  public Single<MapResponse> getPersonalizedVideos(
      APIUser user,
      Integer maxResults,
      APIVideoListOrder sortOrder,
      Map<String, Object> annotations) {

    int _maxResults = maxResults == null ? DEFAULT_MAX_VIDEO_RESULTS : maxResults;
    APIVideoListOrder _sortOrder = sortOrder == null ? DEFAULT_SORT_ORDER : sortOrder;

    return _getMapGalleryVideos(user, _maxResults, _sortOrder, annotations);
  }

  private Single<MapResponse> _getMapGalleryVideos(
      APIUser user, int maxResults, APIVideoListOrder sortOrder, Map<String, Object> annotations) {

    APIRequestContext requestContext = APIRequest.getCurrentRequest().getRequestContext();
    String esn = requestContext.getESN();
    // ugh, at some point, MAP made deviceId required for a MapGetGalleryRequest, add it
    // get device id
    Integer appId = requestContext.getAppId();
    String deviceId = GPSClientSettings.getClientType(appId);

    final Map<String, Object> _annotations =
        getGalleryAnnotations(
            maxResults, sortOrder, annotations, requestContext, deviceId, esn, user);
    return listAdapter.getGallery(_annotations);
  }

  private @NotNull Map<String, Object> getGalleryAnnotations(
      int maxResults,
      APIVideoListOrder sortOrder,
      Map<String, Object> annotations,
      APIRequestContext requestContext,
      String deviceId,
      String esn,
      APIUser currentUser) {
    final Map<String, Object> _annotations = new HashMap<>();
    if (annotations != null) {
      _annotations.putAll(annotations);
    }
    _annotations.putAll(
        USTListAdapter.getAccountProfileInfoToAnnotations(
            APIUserUtil.getAccountProfileRemote(currentUser)));

    _annotations.put("genre", collection.getId());
    _annotations.put("country", NFCountry.findInstance(requestContext.getCountry().getId()));
    _annotations.put("visitor", currentUser.getCustomerId());
    _annotations.put("listContext", MapAnnotationConstants.ListContexts.genre.name());
    _annotations.put("galleryOrder", toGalleryOrder(sortOrder));
    _annotations.put("deviceId", deviceId);
    _annotations.put("requestCount", maxResults);

    if (annotations != null
        && annotations.containsKey(MapAnnotationConstants.SUPPLEMENTAL_TRACK_IDS)) {
      Object o = annotations.get(MapAnnotationConstants.SUPPLEMENTAL_TRACK_IDS);
      if (o instanceof List) {
        _annotations.put("supplementalTrackIds", o);
      }
    }
    if (esn != null) {
      _annotations.put("esn", esn);
    }
    return _annotations;
  }

  /**
   * Returns an ordered (by maturity level in ascending order) list of certification ratings of the
   * gallery of videos served to the user for this genre.
   *
   * @param user {@link com.netflix.api.service.identity.APIUser} representing the Netflix customer.
   * @return An ordered list of unique certification ratings of all the videos in this genre's
   *     gallery
   */
  @Override
  public Observable<List<APICertification>> getGalleryCertifications(final APIUser user) {
    return getGalleryCertifications()
        .map(
            source -> {
              List<APICertification> result = new LinkedList<>();
              int maxMaturityLevel = user.getMaturity().getLevel();
              for (APICertification c : source) {
                if (c.getMaturityLevel() > maxMaturityLevel) break;
                result.add(c);
              }
              return result;
            });
  }

  public Observable<List<APICertification>> getGalleryCertifications() {
    if (collection == null) {
      logger.error("Collection is null!");
      return Observable.just(List.of());
    }
    ISOCountry country = RequestContextWrapper.get().getCountry();
    if (country == null) {
      logger.error("Request context country is null!");
      return Observable.just(List.of());
    }
    var id = collection.getId();
    return videoFactory
        .getCollectionLookup()
        .getVideoIdsFromCollections(Collections.singletonList(id))
        .map(videoIdsMap -> videoIdsMap.getOrDefault(id, Set.of()))
        .map(videoIdsSet -> videoFactory.getVideoLookup().getVideos(videoIdsSet).values())
        .map(
            maybeCompleteVideos ->
                maybeCompleteVideos.stream()
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .filter(
                        completeVideo ->
                            completeVideo.isTopNode()
                                && completeVideo.isAvailableForED()
                                && !completeVideo.isSupplementalVideo())
                    .map(CompleteVideo::getCertificationList)
                    .filter(
                        certifications ->
                            certifications != null
                                && !certifications.isEmpty()
                                && certifications.getFirst() != null)
                    .map(List::getFirst)
                    .filter(certification -> certification.getMovieCert() != null)
                    .collect(
                        Collectors.toMap(
                            certification -> certification.getMovieCert().getName(),
                            Function.identity(),
                            (cert1, cert2) -> cert1)))
        .map(
            certificationsMap ->
                certificationsMap.values().stream()
                    .map(this::toAPICertification)
                    .sorted(MATURITY_LEVEL_COMPARATOR)
                    .toList())
        .toObservable();
  }

  private APICertification toAPICertification(Certification certification) {
    return new APICertificationImpl(certification, vmsl10NProviderDelegate);
  }

  @Override
  public String toString() {
    return "APIGenre{" + "id=" + collection.getId() + '}';
  }

  private static <T> T convertAttribute(
      String name, Class<T> clazz, Map<String, Object> attributes) {
    if (attributes.containsKey(name)) {
      Object o = attributes.get(name);
      if (o != null && o.getClass().isAssignableFrom(clazz)) {
        return clazz.cast(o);
      }
    }
    return null;
  }
}
