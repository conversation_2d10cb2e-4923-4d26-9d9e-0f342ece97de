package com.netflix.api.service.identity;

/**
 * A representation of a user after a Registration, Authentication, or Update action. If successful,
 * you can use the {@link #getUser()} method to retrieve the {@code APIUser} object resulting from
 * the operation, but if there were issues with the the operation {@link #getUser()} will return
 * {@code null}.
 *
 * <p>
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIUser.class.png"
 * alt="Class Diagram: APIUser">
 */
public interface APIUserHolder {

  /**
   * Get the user this holder refers to.
   *
   * @return the user from the operation or {@code null} if there was a problem with the operation
   */
  APIUser getUser();
}
