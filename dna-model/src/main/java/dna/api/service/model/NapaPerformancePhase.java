package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaPerformancePhase {
  private String name;

  private String parentPhase;

  private Long startTime;

  private Long duration;

  private Integer count;

  public String getName() {
    return name;
  }

  public String getParentPhase() {
    return parentPhase;
  }

  public Long getStartTime() {
    return startTime;
  }

  public Long getDuration() {
    return duration;
  }

  public Integer getCount() {
    return count;
  }

  public NapaPerformancePhase setName(String name) {
    this.name = name;
    return this;
  }

  public NapaPerformancePhase setParentPhase(String parentPhase) {
    this.parentPhase = parentPhase;
    return this;
  }

  public NapaPerformancePhase setStartTime(Long startTime) {
    this.startTime = startTime;
    return this;
  }

  public NapaPerformancePhase setDuration(Long duration) {
    this.duration = duration;
    return this;
  }

  public NapaPerformancePhase setCount(Integer count) {
    this.count = count;
    return this;
  }
}
