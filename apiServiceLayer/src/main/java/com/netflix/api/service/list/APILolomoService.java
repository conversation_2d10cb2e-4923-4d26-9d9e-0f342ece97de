package com.netflix.api.service.list;

import com.netflix.api.service.ListItem;
import com.netflix.api.service.identity.APIUser;
import rx.Observable;

/**
 * Service that provides lolomos and lists in a lolomo: lists that are displayed on a homepage
 * (standard or JustForKids).
 *
 * <p>Lolomos are available for homepage, kids' homepage, and top level genres.
 *
 * <p><img
 * src="https://confluence.netflix.com/download/attachments/52216234/APILolomoService.class.png"
 * alt="Class Diagram: APILolomoService">
 */
public interface APILolomoService {
  /**
   * Refresh a list according to the parameters of a {@link ListRefreshRequest}. Some lists are
   * "refresh-able", i.e., they change as a direct result of a customer action, and a fresh list
   * needs to be presented to the customer. For example, when a customer adds a video to his/her
   * playlist, playlist needs to be refreshed. Recently watched list needs periodic refreshes to
   * include up-to-date videos watched by the customer.
   *
   * <p>Note: For the sake of optimization on the server side, more information than just the ID of
   * the list is needed for this call (like the ID of its parent lolomo, and list context).
   *
   * @param request to refresh the lolomo list. You can build this with {@link
   *     #buildListRefreshRequest(String, String,
   *     com.netflix.api.service.list.APILolomoList.ListContext)}.
   * @return the fresh lolomo list
   */
  Observable<ListItem<APILolomoList<? extends APIEntity>>> refreshLolomoList(
      final ListRefreshRequest request, APIUser user);

  /**
   * Builds a base list refresh request that you can pass in to {@link
   * #refreshLolomoList(ListRefreshRequest, APIUser)}. You can then set/override additional parameters by
   * using the methods in the {@link ListRefreshRequest}.
   *
   * @param listId ID of the list being refreshed
   * @param rootLolomoId ID of the parent (root) lolomo that this list belongs to
   * @param listContext {@code ListContext} of the list being refreshed
   * @return ListRefreshRequest that can be further modified with options or passed in to {@link
   *     #refreshLolomoList(ListRefreshRequest, APIUser)}
   */
  ListRefreshRequest buildListRefreshRequest(
      String listId, String rootLolomoId, APILolomoList.ListContext listContext);
}
