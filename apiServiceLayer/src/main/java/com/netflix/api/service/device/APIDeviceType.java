package com.netflix.api.service.device;

/**
 * <img
 * src="https://confluence.netflix.com/download/attachments/52216234/deviceManagement.class.png"
 * alt="APIDeviceManagementService class diagram">
 */
public interface APIDeviceType {

  /**
   * The DeviceTypeId
   *
   * @return an integer representing the id for this object
   */
  int getId();

  String getDescription();

  /** Returns the device category id */
  int getCategoryId();

  /**
   * Content Usage Policy (CUP) device category is one of these 5 groups: "OpenPlatformCDM",
   * "OpenPlatformProtectedCDM", "Mobile", "Game Console", or "CE".
   *
   * @return the Content Usage Policy (CUP) device category
   */
  String getCUPCategory();

  String getReportingName();
}
