package com.netflix.api.startup.priming.scheduling;

import com.google.inject.Key;

public class StripeSelectorPackage implements StripeSelector {

  private final int packageDepth;

  public StripeSelectorPackage(int packageDepth) {
    this.packageDepth = packageDepth;
  }

  @Override
  public boolean accept(Key<?> key) {
    return true;
  }

  @Override
  public int select(int stripCount, Key<?> key) {
    Class<?> cls = key.getTypeLiteral().getRawType();
    return ((~(1 << (Integer.SIZE - 1)) & smear(computeHashCode(cls))) % stripCount);
  }

  /*
   * This method was written by <PERSON> with assistance from members of JCP
   * JSR-166 Expert Group and released to the public domain, as explained at
   * http://creativecommons.org/licenses/publicdomain
   *
   * As of 2010/06/11, this method is identical to the (package private) hash
   * method in OpenJDK 7's java.util.HashMap class.
   */
  // Copied from java/com/google/common/collect/Hashing.java
  private static int smear(int hashCode) {
    hashCode ^= (hashCode >>> 20) ^ (hashCode >>> 12);
    return hashCode ^ (hashCode >>> 7) ^ (hashCode >>> 4);
  }

  private int computeHashCode(Class<?> cls) {
    String fullClassName = cls.getCanonicalName();

    if (fullClassName == null) return 0;
    String[] components = fullClassName.split("\\.");

    int pkgLen = components.length - 1;

    int result = 1;
    for (int i = 0; i < packageDepth && i < pkgLen; i++) {
      result = 31 * result + (components[i] == null ? 0 : components[i].hashCode());
    }
    return result;
  }
}
