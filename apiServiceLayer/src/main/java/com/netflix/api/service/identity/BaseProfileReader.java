package com.netflix.api.service.identity;

import static java.util.concurrent.CompletableFuture.completedFuture;

import com.google.common.base.Preconditions;
import java.util.Date;
import java.util.concurrent.CompletionStage;

public abstract class BaseProfileReader implements ProfileReader {

  private final APIUserInternal user;
  private final LocalizedProfileLogic localizedProfileLogic;

  private static long getMillisSinceEpoch(Date date) {
    if (date == null) {
      return 0;
    }
    return date.getTime();
  }

  protected BaseProfileReader(APIUserInternal user, LocalizedProfileLogic kidsProfileTestLogic) {
    this.user = Preconditions.checkNotNull(user, "APIUser must be provided");
    this.localizedProfileLogic = kidsProfileTestLogic;
  }

  @Override
  public Long getCustomerId() {
    return APIUserUtil.getCustomerId(user);
  }

  @Override
  public String getCustomerGuid() {
    return user.getCustomerGUID();
  }

  @Override
  public boolean isAccountOwner() {
    return user.isPrimaryAccountOwner();
  }

  protected abstract String profileName();

  protected abstract String primaryLanguage();

  @SuppressWarnings("deprecation")
  @Override
  public CompletionStage<String> getProfileName() {
    if (user.isDefaultKidsProfile()
        && localizedProfileLogic.showLocalizedDefaultKidsProfileName(user.getAccountProfile())) {
      return localizedProfileLogic.getDefaultKidsProfileName(primaryLanguage());
    }
    if (user.isFamilyExperience()) {
      return localizedProfileLogic.getFamilyProfileName(user.getAccountProfile());
    }
    return completedFuture(profileName());
  }

  @Override
  public CompletionStage<String> getHtmlDecodedProfileName() {
    return getProfileName()
        .thenApply(name -> name.equals(profileName()) ? htmlDecodedProfileName() : name);
  }

  protected abstract String htmlDecodedProfileName();

  @Override
  public APIMaturity getMaturity() {
    return user.getMaturity();
  }

  @Override
  public long getDateCreated() {
    return getMillisSinceEpoch(user.getProfileCreationDate());
  }

  @Override
  public String getIconName() {
    return user.getIconName();
  }
}
