package com.netflix.api.service.interactive;

import java.util.List;
import java.util.Set;

public interface APIInteractiveExperienceService {

  enum APIInteractiveExperiencePlatform {
    ANDROID_SAMURAI,
    IOS_ARGO_PHONE,
    IOS_ARGO_TABLET,
    TVUI_QL,
    WEB_AKIRA
  }

  interface APIPrePlayAnnotation {
    boolean supportsTutorial();

    String getInteractiveDeviceType();
  }

  interface APIInteractiveExperienceRequest {

    APIInteractiveExperiencePlatform getPlatform();

    APIInteractiveExperienceRequest setPlatform(APIInteractiveExperiencePlatform platform);

    int getUiSpecVersion();

    APIInteractiveExperienceRequest setUiSpecVersion(int uiSpecVersion);

    Set<String> getSupportedExperiences();

    APIInteractiveExperienceRequest setSupportedExperiences(Set<String> supportedExperiences);

    String getAppSdkVersion();

    APIInteractiveExperienceRequest setAppSdkVersion(String appSdkVersion);

    String getUiVersion();

    APIInteractiveExperienceRequest setUiVersion(String uiVersion);

    String getOsVersion();

    APIInteractiveExperienceRequest setOsVersion(String osVersion);

    Integer getGmtOffsetMs();

    APIInteractiveExperienceRequest setGmtOffsetMs(Integer gmtOffsetMs);

    String getTimeZoneId();

    APIInteractiveExperienceRequest setTimeZoneId(String timeZoneId);
  }

  interface APIUIMetadataRequest {

    boolean isKidsUi();

    APIUIMetadataRequest setUseSslForAssets(boolean useSslForAssets);

    boolean isUseSslForAssets();

    APIUIMetadataRequest setPreferredLocales(List<String> preferredLocales);

    List<String> getPreferredLocales();

    List<String> getSupportedLocalesForDevice();

    APIUIMetadataRequest setUsePseudoLoc(boolean usePseudoLoc);

    boolean isUsePseudoLoc();

    APIUIMetadataRequest setImageExtensionPreferences(List<String> imageExtensionPreferences);

    List<String> getImageExtensionPreferences();

    APIUIMetadataRequest setAudioLocaleOverride(String audioLocaleOverride);

    String getAudioLocaleOverride();

    APIUIMetadataRequest setTextLocaleOverride(String textLocaleOverride);

    String getTextLocaleOverride();

    String getSchemaVersion();

    APIUIMetadataRequest setSchemaVersion(String schemaVersion);

    List<String> getMomentIds();

    APIUIMetadataRequest setMomentIds(List<String> momentIds);

    Long getPlaybackPositionMs();

    APIUIMetadataRequest setPlaybackPositionMs(Long position);

    String getIntent();

    APIUIMetadataRequest setIntent(String intent);

    APIInteractiveUserStateSnapshot getUserState();

    APIUIMetadataRequest setUserState(APIInteractiveUserStateSnapshot userState);

    Double getImageScaleFactor();

    APIUIMetadataRequest setImageScaleFactor(Double imageScaleFactor);
  }

  interface APIInteractiveUserStateSnapshot {
    byte[] getPersistentState();

    APIInteractiveUserStateSnapshot setPersistentState(byte[] persistentState);

    byte[] getSessionState();

    APIInteractiveUserStateSnapshot setSessionState(byte[] sessionState);

    byte[] getGlobalState();

    APIInteractiveUserStateSnapshot setGlobalState(byte[] globalState);
  }
}
