package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class LiveEventCriteria {
  private TimeRange timeRange;

  public TimeRange getTimeRange() {
    return timeRange;
  }

  public LiveEventCriteria setTimeRange(TimeRange timeRange) {
    this.timeRange = timeRange;
    return this;
  }
}
