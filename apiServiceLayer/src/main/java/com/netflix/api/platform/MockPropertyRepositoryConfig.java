package com.netflix.api.platform;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

public class MockPropertyRepositoryConfig {

  static boolean firstTime = true;

  public PropertyRepository init() {
    PropertyRepository mockPropertyRepository = Mockito.mock(PropertyRepository.class);

    @SuppressWarnings("unchecked")
    Property<Boolean> falseMockProperty = Mockito.mock(Property.class);
    Mockito.when(falseMockProperty.orElse(Mockito.any())).thenReturn(falseMockProperty);
    Mockito.when(falseMockProperty.get()).thenReturn(false);

    @SuppressWarnings("unchecked")
    Property<Boolean> trueMockProperty = Mockito.mock(Property.class);
    Mockito.when(trueMockProperty.orElse(Mockito.any())).thenReturn(trueMockProperty);
    Mockito.when(trueMockProperty.get()).thenReturn(true);

    @SuppressWarnings("unchecked")
    Property<String> stringMockProperty = Mockito.mock(Property.class);
    Mockito.when(stringMockProperty.orElse(Mockito.any())).thenReturn(stringMockProperty);
    Mockito.when(stringMockProperty.get()).thenReturn(null);

    @SuppressWarnings("unchecked")
    Property<Integer> integerMockProperty = Mockito.mock(Property.class);
    Mockito.when(integerMockProperty.orElse(Mockito.any())).thenReturn(integerMockProperty);
    Mockito.when(integerMockProperty.get()).thenReturn(250);

    @SuppressWarnings("unchecked")
    Property<Long> longMockProperty = Mockito.mock(Property.class);
    Mockito.when(longMockProperty.orElse(Mockito.any())).thenReturn(longMockProperty);
    Mockito.when(longMockProperty.get()).thenReturn(0l);

    @SuppressWarnings("unchecked")
    Property<Double> doubleMockProperty = Mockito.mock(Property.class);
    Mockito.when(doubleMockProperty.orElse(Mockito.any())).thenReturn(doubleMockProperty);
    Mockito.when(doubleMockProperty.get()).thenReturn(20.0);

    @SuppressWarnings("unchecked")
    Property<Map<String, String>> mapMockProperty = Mockito.mock(Property.class);
    Mockito.when(mapMockProperty.orElse(Mockito.any())).thenReturn(mapMockProperty);
    Mockito.when(mapMockProperty.get()).thenReturn(Collections.emptyMap());

    Mockito.when(mockPropertyRepository.get("api.throttle.dump", Boolean.class))
        .thenReturn(falseMockProperty);
    Mockito.when(mockPropertyRepository.get("api.throttle.blocked", Boolean.class))
        .thenReturn(falseMockProperty);
    Mockito.when(mockPropertyRepository.get("api.throttle.dump", Boolean.class))
        .thenReturn(falseMockProperty);
    Mockito.when(mockPropertyRepository.get("api.request.throttle.size", Integer.class))
        .thenReturn(integerMockProperty);
    Mockito.when(mockPropertyRepository.get("api.request.throttle.target.percentage", Double.class))
        .thenReturn(doubleMockProperty);
    Mockito.when(
            mockPropertyRepository.get("api.request.throttle.background.percentage", Double.class))
        .thenReturn(doubleMockProperty);

    Mockito.when(mockPropertyRepository.get("api.throttle.background.enable", Boolean.class))
        .thenReturn(trueMockProperty);

    @SuppressWarnings("unchecked")
    Property<List<String>> listMockProperty = Mockito.mock(Property.class);
    Mockito.when(listMockProperty.orElse(Mockito.any())).thenReturn(listMockProperty);
    Mockito.when(listMockProperty.get()).thenReturn(List.of());

    @SuppressWarnings("unchecked")
    Property<Integer> statusMockProperty = Mockito.mock(Property.class);
    Mockito.when(statusMockProperty.orElse(Mockito.any())).thenReturn(statusMockProperty);
    Mockito.when(statusMockProperty.get()).thenReturn(429);
    Mockito.when(mockPropertyRepository.get("api.throttle.background.status", Integer.class))
        .thenReturn(statusMockProperty);

    Mockito.when(mockPropertyRepository.get("api.throttle.allowJson", Boolean.class))
        .thenReturn(trueMockProperty);

    @SuppressWarnings("unchecked")
    Property<Integer> synthDeviceMockProperty = Mockito.mock(Property.class);
    Mockito.when(synthDeviceMockProperty.orElse(Mockito.any())).thenReturn(synthDeviceMockProperty);
    Mockito.when(synthDeviceMockProperty.get()).thenReturn(921);
    Mockito.when(mockPropertyRepository.get("com.netflix.api.synthetic.device.id", Integer.class))
        .thenReturn(synthDeviceMockProperty);

    Mockito.when(mockPropertyRepository.get("platform.enableLockFreeRequestContext", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(
            mockPropertyRepository.get("com.netflix.api.endpointscript.timeoutms", Integer.class))
        .thenReturn(integerMockProperty);

    @SuppressWarnings("unchecked")
    Property<Double> bkMockProperty = Mockito.mock(Property.class);
    Mockito.when(bkMockProperty.orElse(Mockito.any())).thenReturn(bkMockProperty);
    Mockito.when(bkMockProperty.get()).thenReturn(60.0);
    Mockito.when(
            mockPropertyRepository.get("api.request.throttle.background.percentage", Double.class))
        .thenReturn(bkMockProperty);

    Mockito.when(mockPropertyRepository.get("api.clientContext.unknownAsBackground", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(mockPropertyRepository.get("eas.api.passportESNPrecedence", Boolean.class))
        .thenReturn(trueMockProperty);

    @SuppressWarnings("unchecked")
    Property<String> hardcodedMockProperty = Mockito.mock(Property.class);
    Mockito.when(hardcodedMockProperty.orElse(Mockito.any())).thenReturn(hardcodedMockProperty);
    Mockito.when(hardcodedMockProperty.get()).thenReturn("hardcoded");

    Mockito.when(mockPropertyRepository.get("api.appIds.mappings", String.class))
        .thenReturn(stringMockProperty);

    Mockito.when(mockPropertyRepository.get("eapi.appIds.mappingType", String.class))
        .thenReturn(hardcodedMockProperty);

    Mockito.when(
            mockPropertyRepository.get(
                "eas.api.RequestScopedIdentityManager.setPassportInRequestContext", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.get("stats.enableNQ", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(mockPropertyRepository.get("stats.deviceRetirementsSampleRate", Integer.class))
        .thenReturn(integerMockProperty);

    Mockito.when(
            mockPropertyRepository.get("api.endpoint.async.command.timeout.default", Integer.class))
        .thenReturn(integerMockProperty);

    Mockito.when(mockPropertyRepository.get("api.stats-filter.enabled", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.get("api.stats-filter.allow.empty.path", Boolean.class))
        .thenReturn(trueMockProperty);
    Mockito.when(mockPropertyRepository.getList("api.stats-filter.uri.denylist", String.class))
        .thenReturn(listMockProperty);

    Mockito.when(mockPropertyRepository.get("api.clientContext.unknownAsBackground", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(mockPropertyRepository.get("api.nfesn.microcontextEnabled", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.get("eas.api.passportESNPrecedence", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.get("api.urlutil.ignoreNoGroup", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(
            mockPropertyRepository.get("com.netflix.api.interrupt.onerror.enabled", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.get("platform.enableLockFreeRequestContext", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.get("dna.api.reduceFalcorLogging", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.get("dna.api.logFalcorFanout", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(mockPropertyRepository.get("api.enableMappedProfileAvatar", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(mockPropertyRepository.get("api.dna.terminatePathOnVideoNotFound", Boolean.class))
        .thenReturn(falseMockProperty);
    Mockito.when(
            mockPropertyRepository.get("api.clientSupportsCollectibles.enabled", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(
            mockPropertyRepository.get("api.clientAppCanHandleMultiLanguageCatalog", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.get("api.enableInteractiveVideos", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(
            mockPropertyRepository.get(
                "api.supportedLocalizationFeatures.kidsProfile.defaultTrue.enabled", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.get("api.batch.data.source.metric.enabled", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(mockPropertyRepository.get("dna.api.reduceFalcorLogging", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.get("dna.api.logFalcorFanout", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(mockPropertyRepository.get("dna.api.falcor.failure.mantis.enabled", Boolean.class))
        .thenReturn(falseMockProperty);

    @SuppressWarnings("unchecked")
    Property<Long> long1MockProperty = Mockito.mock(Property.class);
    Mockito.when(long1MockProperty.orElse(Mockito.any())).thenReturn(long1MockProperty);
    Mockito.when(long1MockProperty.get()).thenReturn(60000l);
    Mockito.when(
            mockPropertyRepository.get("com.netflix.api.servlet.enableasync.timeoutms", Long.class))
        .thenReturn(long1MockProperty);

    Mockito.when(mockPropertyRepository.get("groupDefender.allowAll", Boolean.class))
        .thenReturn(falseMockProperty);
    Mockito.when(mockPropertyRepository.get("groupDefender.allowHTML5", Boolean.class))
        .thenReturn(falseMockProperty);
    Mockito.when(mockPropertyRepository.get("groupDefender.allowAndroidNoDevice", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(mockPropertyRepository.get("api.cms.enableDebug", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(
            mockPropertyRepository.get(
                "com.netflix.api.videos.with.special.messages", Integer.class))
        .thenReturn(integerMockProperty);

    Mockito.when(mockPropertyRepository.get("api.canwatchnow.enable", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(
            mockPropertyRepository.getList("request.context.client.capabilities", String.class))
        .thenReturn(listMockProperty);

    Mockito.when(mockPropertyRepository.get("api.request.expiry.enabled", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(mockPropertyRepository.get("api.endpoint.command.queue.unbounded", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(
            mockPropertyRepository.get(
                "com.netflix.api.dependencies.ab.useLegacyMembershipClientWrapper", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.get("com.netflix.api.checkCountryStatus", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(
            mockPropertyRepository.get("com.netflix.api.getoStatus.publishGeoData", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(
            mockPropertyRepository.get(
                "api.endpoint.async.command.timeout.executor", Integer.class))
        .thenReturn(integerMockProperty);

    Mockito.when(
            mockPropertyRepository.get(
                "com.netflix.api.identityService.protoRestriction", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(
            mockPropertyRepository.getList(
                "com.netflix.api.identityService.protoRestrictionAllowlist", String.class))
        .thenReturn(listMockProperty);

    Mockito.when(mockPropertyRepository.get("api.identity.winpin.enable", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(
            mockPropertyRepository.get("api.endpoint.threadPool.default.coreSize", Integer.class))
        .thenReturn(integerMockProperty);

    Mockito.when(
            mockPropertyRepository.get(
                "api.endpoint.threadPool.default.keepAliveTimeMinutes", Integer.class))
        .thenReturn(integerMockProperty);

    Mockito.when(
            mockPropertyRepository.get(
                "api.endpoint.threadPool.default.maxQueueSize", Integer.class))
        .thenReturn(integerMockProperty);

    Mockito.when(mockPropertyRepository.get("api.ulm.image.url.template", String.class))
        .thenReturn(stringMockProperty);

    Mockito.when(mockPropertyRepository.get("api.ulm.image.url.https.template", String.class))
        .thenReturn(stringMockProperty);

    Mockito.when(mockPropertyRepository.get("api.ulm.enabled", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.getList("api.ulm.logic2.country.blacklist", String.class))
        .thenReturn(listMockProperty);

    Mockito.when(
            mockPropertyRepository.getMap(
                "api.ulm.locale.directory.mapping", String.class, String.class))
        .thenReturn(mapMockProperty);

    Mockito.when(
            mockPropertyRepository.get(
                "api.endpoint.threadPool.default.statisticalWindow", Integer.class))
        .thenReturn(integerMockProperty);

    Mockito.when(mockPropertyRepository.get("api.endpoint.counter.defaultBuckets", Integer.class))
        .thenReturn(integerMockProperty);

    Mockito.when(
            mockPropertyRepository.get(
                "api.endpoint.async.command.failrequestonexception", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(
            mockPropertyRepository.get("api.endpoint.async.command.cancel.attempt", Integer.class))
        .thenReturn(integerMockProperty);

    Mockito.when(mockPropertyRepository.get("api.identity.enable-ignore-pattern", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(
            mockPropertyRepository.get("microcontext.adapter.user-sync.enabled", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(
            mockPropertyRepository.get(
                "IdentityFilter.enableForceSetPassportOnResponseHeader", Boolean.class))
        .thenReturn(trueMockProperty);

    Mockito.when(mockPropertyRepository.get("IdentityFilter.enablePassportScopes", Boolean.class))
        .thenReturn(falseMockProperty);

    Mockito.when(
            mockPropertyRepository.get("IdentityFilter.setPassportInRequestContext", Boolean.class))
        .thenReturn(trueMockProperty);

    if (firstTime == true) {
      firstTime = false;
      try (MockedStatic<PropertyRepositoryHolder> mocked =
          Mockito.mockStatic(PropertyRepositoryHolder.class, Mockito.CALLS_REAL_METHODS)) {
        mocked
            .when(() -> PropertyRepositoryHolder.getPropertyRepository())
            .thenReturn(mockPropertyRepository);
        new PropertyRepositoryHolder(mockPropertyRepository);
      }
    }

    return mockPropertyRepository;
  }
}
