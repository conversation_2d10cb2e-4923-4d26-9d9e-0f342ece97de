package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchVideoCarouselSection extends SearchPageSection {
  private SearchVideoCarousel carousel;

  public SearchVideoCarousel getCarousel() {
    return carousel;
  }

  public SearchVideoCarouselSection setCarousel(SearchVideoCarousel carousel) {
    this.carousel = carousel;
    return this;
  }
}
