package com.netflix.api.service.titlegroup;

import com.google.inject.Provider;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
public class APITitleGroupServiceImpl implements APITitleGroupService {

  private final Provider<APITitleGroupDataSource> _dataSourceProvider;

  @Autowired
  public APITitleGroupServiceImpl(APITitleGroupDataSourceProvider dataSource) {
    this._dataSourceProvider = dataSource;
  }

  @Override
  public Observable<List<APITitleGroupSet>> findGroupSets(
      Collection<APITitleGroupSetMember> members) {
    if (members == null || members.isEmpty()) return Observable.empty();

    return _dataSourceProvider.get().findGroupSets(members);
  }

  @Override
  public Observable<List<APITitleGroupSet>> getGroupSets(Collection<APITitleGroup> groups) {
    if (groups == null || groups.isEmpty()) return Observable.empty();

    return _dataSourceProvider.get().getGroupSets(groups);
  }

  public Observable<Set<APITitleGroupSetMember>> findMembersFromIds(Collection<Integer> videoIds) {
    if (videoIds == null) return Observable.empty();
    return _dataSourceProvider.get().findMembersFromIds((videoIds));
  }

  @Override
  public Observable<List<APITitleGroup>> findGroups(Collection<Integer> groups) {
    if (groups == null || groups.isEmpty()) return Observable.empty();
    return _dataSourceProvider.get().findGroups(groups);
  }
}
