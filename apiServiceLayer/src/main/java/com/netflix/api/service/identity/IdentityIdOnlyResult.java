package com.netflix.api.service.identity;

import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.membership.MembershipStatus;
import java.util.Optional;

public class IdentityIdOnlyResult extends IdentityResult<Long> {

  private final Long customerId;
  private final Long accountOwnerId;

  IdentityIdOnlyResult(long customerId, long accountOwnerId) {
    this.customerId = customerId;
    this.accountOwnerId = accountOwnerId;
  }

  @Override
  public Long getData() {
    return customerId;
  }

  @Override
  public boolean isSuccess() {
    return true;
  }

  @Override
  public Long getCustomerId() {
    return customerId;
  }

  @Override
  public Long getAccountOwnerId() {
    return accountOwnerId;
  }

  @Override
  public String getSignupCountry() {
    return null;
  }

  @Override
  public String getRegistrationCountry() {
    return null;
  }

  @Override
  public String getESN() {
    return null;
  }

  @Override
  public Optional<MembershipStatus> getMembershipStatus() {
    return Optional.empty();
  }

  @Override
  public String getCustomerGuid() {
    return null;
  }

  @Override
  public IdentitySource getDeviceInformationSource() {
    return IdentitySource.NONE;
  }

  @Override
  public IdentitySource getUserInformationSource() {
    return IdentitySource.NONE;
  }

  @Override
  public Integer getDeviceType() {
    return null;
  }
}
