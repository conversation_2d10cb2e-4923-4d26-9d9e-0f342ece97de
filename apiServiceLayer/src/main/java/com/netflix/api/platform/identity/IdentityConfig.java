package com.netflix.api.platform.identity;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import java.util.regex.Pattern;

/**
 * Defines config related to Identity and DCR cookies.
 *
 * <AUTHOR>
 */
public class IdentityConfig {
  private static final Property<Boolean> ENABLE_IGNORE =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.identity.enable-ignore-pattern", Boolean.class)
          .orElse(false);
  private static final Pattern IGNORE_PATTERN = Pattern.compile("/apps/[^/]+/upgrade_policy");

  private static final IdentityConfig INSTANCE = new IdentityConfig();

  private IdentityConfig() {}

  public static IdentityConfig instance() {
    return INSTANCE;
  }

  public boolean shouldProcess(String path) {
    final Pattern ignorePattern = getIgnorePattern();
    // API-6169 allow cookies to be process if no path is present
    return path == null || ignorePattern == null || !ignorePattern.matcher(path).matches();
  }

  private static Pattern getIgnorePattern() {
    if (ENABLE_IGNORE.get()) {
      return IGNORE_PATTERN;
    } else {
      return null;
    }
  }
}
