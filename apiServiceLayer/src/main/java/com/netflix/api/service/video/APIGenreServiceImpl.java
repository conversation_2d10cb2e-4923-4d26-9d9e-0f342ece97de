package com.netflix.api.service.video;

import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.facade.service.collection.CollectionData;
import com.netflix.api.facade.service.collection.CollectionLookup;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Single;

@Component
@EnableDeprecatedMetrics
public class APIGenreServiceImpl implements APIGenreService {

  private final APIGenreFactory genreFactory;
  private final CollectionLookup collectionLookup;

  @Autowired
  public APIGenreServiceImpl(APIGenreFactory genreFactory, CollectionLookup collectionLookup) {
    this.genreFactory = genreFactory;
    this.collectionLookup = collectionLookup;
  }

  @Override
  public Single<Map<Long, APIGenre>> getGenreMap(Collection<Long> ids) {
    if (ids == null || ids.isEmpty()) {
      return Single.just(Collections.emptyMap());
    }
    return collectionLookup
        .getCollections(ids)
        .map(
            genreMap -> {
              Map<Long, APIGenre> map = HashMap.newHashMap(ids.size());
              for (Entry<Long, Optional<CollectionData>> entry : genreMap.entrySet()) {
                entry
                    .getValue()
                    .ifPresent(genre -> map.put(entry.getKey(), genreFactory.getInstance(genre)));
              }
              return map;
            });
  }
}
