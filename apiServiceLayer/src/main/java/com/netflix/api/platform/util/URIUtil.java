package com.netflix.api.platform.util;

import com.netflix.archaius.api.Property;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class URIUtil {
  private URIUtil() {}

  private static final Property<Boolean> IGNORE_NO_GROUP =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.urlutil.ignoreNoGroup", Boolean.class)
          .orElse(true);
  private static final Logger logger = LoggerFactory.getLogger(URIUtil.class);

  public static String endpointGroup(String uri) {
    if (uri == null || uri.isEmpty() || uri.equals("/")) {
      return "none";
    }

    int start = 0;
    if (uri.startsWith("/")) {
      start = 1;
    }

    int end = uri.indexOf('/', start);
    if (end == -1) {
      if (IGNORE_NO_GROUP.get()) {
        logger.debug("Ignore group for path {}", uri);
        // no group segment so ignore this metric
        return "ignored";
      } else {
        // only one path segment
        return uri.substring(start);
      }
    } else if (end == 1) {
      // path of "//"
      return "none";
    }

    return uri.substring(start, end);
  }
}
