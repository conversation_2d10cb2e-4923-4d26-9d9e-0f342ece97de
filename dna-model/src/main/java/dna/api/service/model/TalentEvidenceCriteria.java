package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class TalentEvidenceCriteria {
  /**
   * Value that indicates to evidence systems which page/context the UI is requesting this for. For
   * example, ODP is used to indicate that this value will be displayed on a details page (ODP =
   * original display page).
   *
   * <p>Consult with Evidence Engineering (#evengers) for more info.
   */
  private UIContext uiContext;

  private List<TalentEvidenceType> evidenceTypes;

  public UIContext getUiContext() {
    return uiContext;
  }

  public List<TalentEvidenceType> getEvidenceTypes() {
    return evidenceTypes;
  }

  public TalentEvidenceCriteria setUiContext(UIContext uiContext) {
    this.uiContext = uiContext;
    return this;
  }

  public TalentEvidenceCriteria setEvidenceTypes(List<TalentEvidenceType> evidenceTypes) {
    this.evidenceTypes = evidenceTypes;
    return this;
  }
}
