package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.video.APIMostWatchedData. */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class VideoMostWatchedData {
  private Integer collectionId;

  private Integer rank;

  private String last7DaysStartDate;

  private String last7DaysEndDate;

  private String publishDate;

  public Integer getCollectionId() {
    return collectionId;
  }

  public Integer getRank() {
    return rank;
  }

  public String getLast7DaysStartDate() {
    return last7DaysStartDate;
  }

  public String getLast7DaysEndDate() {
    return last7DaysEndDate;
  }

  public String getPublishDate() {
    return publishDate;
  }

  public VideoMostWatchedData setCollectionId(Integer collectionId) {
    this.collectionId = collectionId;
    return this;
  }

  public VideoMostWatchedData setRank(Integer rank) {
    this.rank = rank;
    return this;
  }

  public VideoMostWatchedData setLast7DaysStartDate(String last7DaysStartDate) {
    this.last7DaysStartDate = last7DaysStartDate;
    return this;
  }

  public VideoMostWatchedData setLast7DaysEndDate(String last7DaysEndDate) {
    this.last7DaysEndDate = last7DaysEndDate;
    return this;
  }

  public VideoMostWatchedData setPublishDate(String publishDate) {
    this.publishDate = publishDate;
    return this;
  }
}
