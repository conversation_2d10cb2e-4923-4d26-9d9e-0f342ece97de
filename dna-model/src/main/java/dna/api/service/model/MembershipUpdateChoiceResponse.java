package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class MembershipUpdateChoiceResponse {
  private Boolean success;

  private Boolean fallback;

  /** True if plan change happens at renewal */
  private Boolean isDeferredPlanChange;

  public Boolean getSuccess() {
    return success;
  }

  public Boolean getFallback() {
    return fallback;
  }

  public Boolean getIsDeferredPlanChange() {
    return isDeferredPlanChange;
  }

  public MembershipUpdateChoiceResponse setSuccess(Boolean success) {
    this.success = success;
    return this;
  }

  public MembershipUpdateChoiceResponse setFallback(Boolean fallback) {
    this.fallback = fallback;
    return this;
  }

  public MembershipUpdateChoiceResponse setIsDeferredPlanChange(Boolean isDeferredPlanChange) {
    this.isDeferredPlanChange = isDeferredPlanChange;
    return this;
  }
}
