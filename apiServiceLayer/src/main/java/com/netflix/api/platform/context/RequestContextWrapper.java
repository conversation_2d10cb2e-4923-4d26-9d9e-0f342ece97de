package com.netflix.api.platform.context;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.i18n.NFLocale;
import com.netflix.lang.ThreadVariable;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.type.DeviceType;
import com.netflix.type.ISOCountry;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RequestContextWrapper {

  private static final Logger logger = LoggerFactory.getLogger(RequestContextWrapper.class);

  private static final Property<Boolean> ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("platform.enableLockFreeRequestContext", Boolean.class)
          .orElse(true);

  private static final ThreadVariable<AtomicReference<ImmutableRequestContext>> current =
      new ThreadVariable<AtomicReference<ImmutableRequestContext>>() {
        @Override
        protected AtomicReference<ImmutableRequestContext> initialValue() {
          return new AtomicReference<>();
        }
      };

  public static class Builder {
    String requestId;
    ISOCountry country;
    String localeList;
    String deviceId;
    DeviceType deviceType;

    public static Builder newInstance() {
      return new Builder();
    }

    public static Builder of(ImmutableRequestContext current) {
      if (current == null) {
        return newInstance();
      } else {
        return new Builder(current);
      }
    }

    public Builder() {}

    Builder(ImmutableRequestContext current) {
      this.requestId = current.getRequestId();
      this.country = current.getCountry();
      this.localeList = current.getLocaleList();
      this.deviceType = current.getDeviceType();
      this.deviceId = current.getDeviceId();
    }

    public Builder country(ISOCountry country) {
      this.country = country;
      return this;
    }

    public Builder localeList(String localeList) {
      this.localeList = localeList;
      return this;
    }

    public Builder requestId(String requestId) {
      this.requestId = requestId;
      return this;
    }

    public Builder deviceId(String deviceId) {
      this.deviceId = deviceId;
      return this;
    }

    public Builder deviceType(DeviceType deviceType) {
      this.deviceType = deviceType;
      return this;
    }
  }

  public static Optional<ImmutableRequestContext> getOptional() {
    return Optional.ofNullable(current.get().get());
  }

  public static ImmutableRequestContext get() {
    if (ENABLED.get()) {
      return getOptional()
          .orElseGet(
              () -> {
                logger.warn(
                    "uninitialized access thread {} uuid {}",
                    Thread.currentThread().getName(),
                    CurrentRequestContext.get().getRequestId(),
                    new Exception());
                return DefaultRequestContext.getInstance();
              });
    }
    return DefaultRequestContext.getInstance();
  }

  private static void setCurrent(ImmutableRequestContext update) {
    ImmutableRequestContext old = current.get().get();
    if (Objects.equals(update, old)) {
      logger.debug("Ignoring requestcontext update, values are equal {}", update);
      return;
    }
    logger.debug("setting current LockFreeImmutableRequestContext from {} to {}", old, update);
    if (!current.get().compareAndSet(old, update)) {
      ImmutableRequestContext middle = current.get().get();
      // ignore if the result was equivalent to the one that got dropped
      if (!Objects.equals(update, middle)) {
        logger.error(
            "requestcontext reference concurrent modification while updating \nold {} \nnew {} \nmid {}",
            old,
            update,
            middle,
            new Exception());
      }
    }
  }

  public static ImmutableRequestContext createAndSet(Builder builder) {
    setCurrent(new LockFreeImmutableRequestContext(builder));
    return get();
  }

  public static List<NFLocale> convertLocales(String localeList) {
    List<NFLocale> references = new ArrayList<>();
    if (localeList == null) return references;
    String localesString = localeList.trim();
    if (localesString.isEmpty()) return references;
    String[] localesStringArray = localesString.split(",");
    for (String localeString : localesStringArray) {
      references.add(NFLocale.createFromString(localeString));
    }
    return references;
  }
}
