package com.netflix.api.service.storage;

import com.netflix.api.util.ObservableFactory;
import com.netflix.api.util.SingleFactory;
import com.netflix.evcache.EVCache;
import java.util.Map;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Single;
import rx.schedulers.Schedulers;

@Component
public class APIStorageServiceImpl implements APIStorageService {
  private final EVCache evCacheTVUIStartup;
  private final EVCache evCacheDETPropertyLookup;
  private final SingleFactory singleFactory;
  private final ObservableFactory observableFactory;

  @Autowired
  public APIStorageServiceImpl(
      final EVCache.Builder evCacheBuilder,
      final SingleFactory singleFactory,
      final ObservableFactory observableFactory) {
    this.singleFactory = singleFactory;
    evCacheTVUIStartup =
        evCacheBuilder
            .setAppName("EVCACHE_TVUI_STARTUP")
            .enableRetry()
            .enableExceptionPropagation()
            .build();
    evCacheDETPropertyLookup =
        evCacheBuilder
            .setAppName("EVCACHE_NPP_RUNTIMEDATA")
            .enableRetry()
            .enableExceptionPropagation()
            .build();
    this.observableFactory = observableFactory;
  }

  @Override
  public Single<String> getTVUIStartupData(String key) {
    return singleFactory.fromAsync(
        evCacheTVUIStartup.get(key, Schedulers.io()), getClass().getName());
  }

  @Override
  public Single<Map<String, String>> getTVUIStartupData(Set<String> keys) {
    return observableFactory.singleFrom(() -> evCacheTVUIStartup.getBulk(keys)); // propagate error
  }

  @Override
  public Single<String> getDETPartnerData(String key) {
    return singleFactory.fromAsync(
        evCacheDETPropertyLookup.get(key, Schedulers.io()), getClass().getName());
  }
}
