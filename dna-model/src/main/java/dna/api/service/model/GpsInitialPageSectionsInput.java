package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class GpsInitialPageSectionsInput {
  /** Desired number of initial sections. */
  private Integer sectionsSize;

  public Integer getSectionsSize() {
    return sectionsSize;
  }

  public GpsInitialPageSectionsInput setSectionsSize(Integer sectionsSize) {
    this.sectionsSize = sectionsSize;
    return this;
  }
}
