package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NextLinear {
  private Long secsUntilStart;

  private LinearScheduleItem nextVideo;

  private LinearResponseMetadata metadata;

  public Long getSecsUntilStart() {
    return secsUntilStart;
  }

  public LinearScheduleItem getNextVideo() {
    return nextVideo;
  }

  public LinearResponseMetadata getMetadata() {
    return metadata;
  }

  public NextLinear setSecsUntilStart(Long secsUntilStart) {
    this.secsUntilStart = secsUntilStart;
    return this;
  }

  public NextLinear setNextVideo(LinearScheduleItem nextVideo) {
    this.nextVideo = nextVideo;
    return this;
  }

  public NextLinear setMetadata(LinearResponseMetadata metadata) {
    this.metadata = metadata;
    return this;
  }
}
