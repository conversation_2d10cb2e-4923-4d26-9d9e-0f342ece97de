package com.netflix.api.service.identity;

import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.service.identity.APIUser.Experience;
import com.netflix.api.service.identity.exceptions.CreateProfileException;
import com.netflix.api.service.identity.exceptions.DuplicateProfileIconException;
import com.netflix.api.service.identity.exceptions.DuplicateProfileNameException;
import com.netflix.archaius.api.Property;
import com.netflix.custeng.subscriber.model.ExperienceType;
import com.netflix.servo.DefaultMonitorRegistry;
import com.netflix.servo.monitor.Counter;
import com.netflix.servo.monitor.Monitors;
import com.netflix.subscriberservice.common.MaturityLevel;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.subscriberservice.protogen.AddProfileRequest;
import com.netflix.subscriberservice.protogen.AddProfileResponse;
import com.netflix.subscriberservice.protogen.ProfileAttributes;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class APIUserCreateListBehavior extends APIUserManagementBehavior {
  private static final Logger logger = LoggerFactory.getLogger(APIUserCreateListBehavior.class);

  private static final Counter COUNTER_CREATE_PROFILE_VALIDATION_ERROR =
      Monitors.newCounter("COUNTER-USERPROFILES.CREATE_PROFILE_VALIDATION_ERROR");
  private static final Counter COUNTER_CREATE_PROFILE_ERROR =
      Monitors.newCounter("COUNTER-USERPROFILES.COUNTER_CREATE_PROFILE_ERROR");

  static {
    DefaultMonitorRegistry.getInstance().register(COUNTER_CREATE_PROFILE_VALIDATION_ERROR);
    DefaultMonitorRegistry.getInstance().register(COUNTER_CREATE_PROFILE_ERROR);
  }

  private static final Property<Boolean> FIRST_USE_DEFAULT =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.service.identity.registration.firstUseDefault", Boolean.class)
          .orElse(true);

  private final APIUser accountOwner;
  private final CurrentUser currentUser;
  private final UserCreator userCreator;
  private final CacheInvalidator cacheInvalidator;

  APIUserCreateListBehavior(
      APIUser accountOwner, APIUserFactory userFactory, APIProfileErrors.Localizer localizer) {
    super(userFactory, localizer);
    if (accountOwner == null) {
      throw new APIServiceRuntimeException("Can not create a user with a NULL Owner!");
    }
    this.accountOwner = accountOwner;
    this.currentUser = new ContextCurrentUser();
    this.cacheInvalidator = new ProductionCacheInvalidator();
    this.userCreator = new SubscriberUserCreator();
  }

  @Override
  public APIUser loadItem(IdentityResult result) {
    return userFactory.createUser(result, true);
  }

  @Override
  public List<IdentityResult> getReferences() {
    List<IdentityResult> newCustomerIdList = new ArrayList<>();

    validateProfileOnCreate();
    if (firstUse == null && FIRST_USE_DEFAULT.get()) {
      firstUse = Boolean.TRUE;
    }
    String languageInheritingProfileCreator =
        (language != null) ? language : currentUser.get().getLanguage();
    try {
      IdentityResult newCustomer =
          userCreator.createUser(iconName, firstUse, languageInheritingProfileCreator);
      if (newCustomer != null) {
        cacheInvalidator.invalidateForUser(accountOwner);
        contextManager.refreshUserOnApiRequest();
        newCustomerIdList.add(newCustomer);
      } else {
        COUNTER_CREATE_PROFILE_ERROR.increment();
        String errorMsg = getUnknownCreateProfileErrorMessage();
        throw new CreateProfileException(errorMsg);
      }
    } catch (Exception e) {
      logger.warn("error creating profile: ", e);
      COUNTER_CREATE_PROFILE_ERROR.increment();
      if (isDuplicateNameException(e)) {
        String errorMsg = getDuplicateNameErrorMessage();
        throw new DuplicateProfileNameException(errorMsg);
      } else if (isDuplicateIconException(e)) {
        String errorMsg = getDuplicatedIconErrorMessage();
        throw new DuplicateProfileIconException(errorMsg);
      } else {
        String errorMsg = getUnknownCreateProfileErrorMessage();
        throw new CreateProfileException(errorMsg, e);
      }
    }
    return newCustomerIdList;
  }

  private String getUnknownCreateProfileErrorMessage() {
    return localizer.localize(
        APIProfileErrors.UNKNOWN_PROFILE_CREATE_FAILURE_MESSAGE_KEY,
        APIProfileErrors.DEFAULT_UNKNOWN_CREATE_ERROR_MESSAGE);
  }

  private void validateProfileOnCreate() {
    try {
      validateName();
    } catch (APIServiceRuntimeException e) {
      COUNTER_CREATE_PROFILE_VALIDATION_ERROR.increment();
      throw e;
    }
  }

  interface UserCreator {
    IdentityResult createUser(
        String iconName, Boolean firstUse, String languageInheritingProfileCreator);
  }

  class SubscriberUserCreator implements UserCreator {

    @Override
    public IdentityResult createUser(
        String iconName, final Boolean firstUse, String languageInheritingProfileCreator) {
      AddProfileRequest.Builder builder =
          AddProfileRequest.newBuilder()
              .setAccountId(APIUserUtil.getAccountOwnerId(currentUser.get()))
              .setCurrentProfileId(APIUserUtil.getCustomerId(currentUser.get()));
      ProfileAttributes.Builder profileAttributesBuilder = ProfileAttributes.newBuilder();
      if (profileName != null) {
        profileAttributesBuilder.setProfileName(profileName);
      }
      if (experience != null) {
        // previous implementation did not set a value for watch_together, preserve that behavior
        if (experience == Experience.jfk) {
          profileAttributesBuilder.setExperienceType(ExperienceType.just_for_kids.name());
        } else if (experience == Experience.standard) {
          profileAttributesBuilder.setExperienceType(ExperienceType.regular.name());
        } else if (experience == Experience.family) {
          profileAttributesBuilder.setExperienceType(ExperienceType.family.name());
        } else if (experience == Experience.watch_together) {
          profileAttributesBuilder.setExperienceType(ExperienceType.watch_together.name());
        }
      }
      if (firstUse != null) {
        profileAttributesBuilder.setIsProfileFirstUse(firstUse);
      }
      // if maturityLevel present use that if absent check for rawMaturity
      if (maturityLevel != null) {
        profileAttributesBuilder.setMaxMaturityLevel(MaturityLevel.getMaturityValue(maturityLevel));
      } else if (rawMaturityValue != null) {
        profileAttributesBuilder.setMaxMaturityLevel(rawMaturityValue);
      }

      if (iconName != null) {
        profileAttributesBuilder.setAvatarImage(iconName);
      }
      if (languageInheritingProfileCreator != null) {
        profileAttributesBuilder.setPrimaryLanguage(languageInheritingProfileCreator);
      }
      // age is not used in sub2
      builder.setProfileAttributes(profileAttributesBuilder.build());

      AddProfileResponse response =
          userFactory.getSubscriberManagementService().addProfile(builder.build());
      AccountProfileRemote accountProfileRemote = response.getAccountProfile();

      // identityresult is only used to create a user thus no need to give cookie information
      return new IdentityIdOnlyResult(
          accountProfileRemote.getBoxedProfileId(), accountProfileRemote.getBoxedAcctId());
    }
  }

  interface CurrentUser {
    APIUser get();
  }

  static class ContextCurrentUser implements CurrentUser {

    @Override
    public APIUser get() {
      return APIRequest.getCurrentRequest().getUser();
    }
  }
}
