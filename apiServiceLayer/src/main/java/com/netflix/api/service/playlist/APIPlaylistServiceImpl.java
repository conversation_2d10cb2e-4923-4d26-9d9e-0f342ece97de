package com.netflix.api.service.playlist;

import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.context.ImmutableRequestContext;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.video.APIVideo;
import com.netflix.archaius.api.Property;
import com.netflix.mylist.common.MylistConstants.Columns;
import com.netflix.mylist.protogen.MylistBlockRequest;
import com.netflix.mylist.protogen.MylistServiceGrpc.MylistServiceStub;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.type.proto.Videos;
import com.netflix.type.protogen.BasicTypes;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
public class APIPlaylistServiceImpl implements APIPlaylistService {

  private static final Property<Boolean> BLOCKED_SOURCE_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.playlist.blockedSource.enabled", Boolean.class)
          .orElse(true);

  private final MylistServiceStub myListService;

  @Autowired
  public APIPlaylistServiceImpl(@GrpcSpringClient("mylist") MylistServiceStub myListService) {
    this.myListService = myListService;
  }

  @Override
  public Observable<Void> removeFromContinueWatching(
      Collection<APIVideo> videos, @Nullable String uiVersion, Integer trackId) {
    return removeFromContinueWatchingById(
        videos.stream().map(APIVideo::getId).toList(), uiVersion, trackId);
  }

  private Observable<Void> removeFromContinueWatchingById(
      Collection<Integer> videos, @Nullable String uiVersion, @Nullable Integer trackId) {
    Map<String, String> metadata = new HashMap<>();
    ImmutableRequestContext requestContext = RequestContextWrapper.get();
    metadata.put(Columns.ESN, APIRequest.getCurrentRequest().getRequestContext().getESN());
    metadata.put(Columns.DEVICE_TYPE_ID, requestContext.getDeviceId());
    metadata.put(Columns.UI_VERSION, uiVersion);
    if (BLOCKED_SOURCE_ENABLED.get()) {
      metadata.put(Columns.BLOCKED_SOURCE, trackId == null ? "-1" : trackId.toString());
    }
    MylistBlockRequest.Builder builder =
        MylistBlockRequest.newBuilder()
            .setVisitor(BasicTypes.Visitor.newBuilder().setId(CurrentVisitor.get().getId()).build())
            .putAllMetaData(metadata)
            .setIsoCountry(
                BasicTypes.ISOCountry.newBuilder()
                    .setId(requestContext.getCountry().getId())
                    .build());
    videos.forEach(id -> builder.addBlockedVideo(Videos.toProtobuf(id)));
    return RxObservable.defer(myListService::blockVideo, builder.build()).map(res -> null);
  }
}
