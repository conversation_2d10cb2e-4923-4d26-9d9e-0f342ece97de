package com.netflix.api.service.searchnew;

import rx.Observable;

/**
 * Search service - exposes methods to get search results of different types by query, and search
 * results by reference.
 */
public interface APISearchService {
  /**
   * Get search results for APISearchRequest {@link APISearchRequest}
   *
   * @param request - a non-null request. request can be for a term; for this, you must build search
   *     request from {@link APISearchRequest#newSearchRequestByQuery(String,
   *     APISearchConstants.SearchListType)}. request can be for an entity idl for this, you must
   *     build search request from {@link APISearchRequest#newSearchRequestByEntityId(String)}.
   * @return Observable of a single {@link APISearchList} that has the items
   */
  Observable<APISearchList> getSearchResults(final APISearchRequest request);

  /**
   * Look up an already created search list by reference {@link APISearchList#getReferenceId()}
   *
   * @param referenceId - a non-null reference id.
   * @param from - restrict the result set to start from an index (must be 0 or greater)
   * @param count - restrict the result set to a specific number of items (must be 0 or greater)
   * @return Observable of a single {@link APISearchList} that has the items
   */
  Observable<APISearchList> getListByReference(
      final String referenceId, final int from, final int count);
}
