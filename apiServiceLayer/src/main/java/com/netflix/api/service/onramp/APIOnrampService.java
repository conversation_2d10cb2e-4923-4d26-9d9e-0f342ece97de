package com.netflix.api.service.onramp;

import java.util.List;
import java.util.Map;
import rx.Observable;
import rx.Single;

/** Provides functionality related to customer's on-ramp experience */
public interface APIOnrampService {

  /**
   * Check the on-ramp experience eligibility of the customer
   *
   * @param request parameters of the check
   * @return {@link Observable} of MAP response annotations
   */
  Observable<Map<String, Object>> getOnrampEligibilityObservable(APIOnrampRequest request);

  /**
   * Record the customer on-ramp experience impression
   *
   * @param request parameters of the on-ramp experience
   * @return {@link Observable} of MAP response annotations
   */
  Observable<Map<String, Object>> recordOnrampImpressionObservable(APIOnrampRequest request);

  /**
   * Convenience factory method that creates a new {@link APIOnrampRequest} object
   *
   * @return a new {@link APIOnrampRequest} object created
   */
  APIOnrampRequest createRequest();

  /**
   * Returns the onramp video IDs.
   *
   * @return an {@link Observable} that emits the list of onramp videos for this profile, or an
   *     empty list in case of error
   */
  Observable<List<Long>> getOnrampVideoIds();

  /**
   * Returns the onramp collection IDs.
   *
   * @return an {@link Observable} that emits the list of onramp collections for this profile, or an
   *     empty list in case of error
   */
  Single<List<Long>> getOnrampCollectionIds();
}
