package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.vps.APIVoiceIntent */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class VoiceIntent {
  private VoiceIntentType intentType;

  private String id;

  private String value;

  public VoiceIntentType getIntentType() {
    return intentType;
  }

  public String getId() {
    return id;
  }

  public String getValue() {
    return value;
  }

  public VoiceIntent setIntentType(VoiceIntentType intentType) {
    this.intentType = intentType;
    return this;
  }

  public VoiceIntent setId(String id) {
    this.id = id;
    return this;
  }

  public VoiceIntent setValue(String value) {
    this.value = value;
    return this;
  }
}
