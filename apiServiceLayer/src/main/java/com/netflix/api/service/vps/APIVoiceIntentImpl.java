package com.netflix.api.service.vps;

import com.google.common.base.Preconditions;
import com.netflix.vps.protogen.Intent;

public class APIVoiceIntentImpl implements APIVoiceIntent {

  private APIVoiceIntentType voiceIntentType;
  private String id;
  private String value;

  public APIVoiceIntentImpl(APIVoiceIntentType voiceIntentType, String id, String value) {
    this.voiceIntentType = voiceIntentType;
    this.id = id;
    this.value = value;
  }

  public APIVoiceIntentImpl(Intent voiceIntent) {
    Preconditions.checkNotNull(voiceIntent);
    this.voiceIntentType = APIVoiceIntentType.valueOf(voiceIntent.getType().toString());
    this.id = voiceIntent.getId();
    this.value = voiceIntent.getValue();
  }

  public APIVoiceIntentType getVoiceIntentType() {
    return voiceIntentType;
  }

  public String getId() {
    return id;
  }

  public String getValue() {
    return value;
  }

  public void setVoiceIntentType(APIVoiceIntentType intentType) {
    this.voiceIntentType = intentType;
  }

  public void setId(String id) {
    this.id = id;
  }

  public void setValue(String value) {
    this.value = value;
  }
}
