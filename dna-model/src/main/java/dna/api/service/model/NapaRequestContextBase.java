package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaRequestContextBase {
  /** The app version, e.g. 12.26.1 */
  private String appVersion;

  private NapaClientCapabilities clientCapabilities;

  private Integer debugLevel;

  private SearchDeviceCategory deviceCategory;

  private String partner;

  private NapaRequestContextOverrides overrides;

  private Integer performanceLevel;

  private NapaVoiceContext voiceContext;

  private NapaNonMemberInfo nonMemberInfo;

  private NapaInstalledGamesInfo installedGames;

  public String getAppVersion() {
    return appVersion;
  }

  public NapaClientCapabilities getClientCapabilities() {
    return clientCapabilities;
  }

  public Integer getDebugLevel() {
    return debugLevel;
  }

  public SearchDeviceCategory getDeviceCategory() {
    return deviceCategory;
  }

  public String getPartner() {
    return partner;
  }

  public NapaRequestContextOverrides getOverrides() {
    return overrides;
  }

  public Integer getPerformanceLevel() {
    return performanceLevel;
  }

  public NapaVoiceContext getVoiceContext() {
    return voiceContext;
  }

  public NapaNonMemberInfo getNonMemberInfo() {
    return nonMemberInfo;
  }

  public NapaInstalledGamesInfo getInstalledGames() {
    return installedGames;
  }

  public NapaRequestContextBase setAppVersion(String appVersion) {
    this.appVersion = appVersion;
    return this;
  }

  public NapaRequestContextBase setClientCapabilities(NapaClientCapabilities clientCapabilities) {
    this.clientCapabilities = clientCapabilities;
    return this;
  }

  public NapaRequestContextBase setDebugLevel(Integer debugLevel) {
    this.debugLevel = debugLevel;
    return this;
  }

  public NapaRequestContextBase setDeviceCategory(SearchDeviceCategory deviceCategory) {
    this.deviceCategory = deviceCategory;
    return this;
  }

  public NapaRequestContextBase setPartner(String partner) {
    this.partner = partner;
    return this;
  }

  public NapaRequestContextBase setOverrides(NapaRequestContextOverrides overrides) {
    this.overrides = overrides;
    return this;
  }

  public NapaRequestContextBase setPerformanceLevel(Integer performanceLevel) {
    this.performanceLevel = performanceLevel;
    return this;
  }

  public NapaRequestContextBase setVoiceContext(NapaVoiceContext voiceContext) {
    this.voiceContext = voiceContext;
    return this;
  }

  public NapaRequestContextBase setNonMemberInfo(NapaNonMemberInfo nonMemberInfo) {
    this.nonMemberInfo = nonMemberInfo;
    return this;
  }

  public NapaRequestContextBase setInstalledGames(NapaInstalledGamesInfo installedGames) {
    this.installedGames = installedGames;
    return this;
  }
}
