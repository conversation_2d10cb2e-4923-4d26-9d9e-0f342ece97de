package dna.api.service.model;

import static dna.api.service.model.EnumChecker.noMissingValues;

import com.netflix.api.service.identity.APIAuthTokenScope;
import org.junit.jupiter.api.Test;

class AuthTokenEnumTest {

  @Test
  void authTokenScope() {
    // make sure that we have all the enum values that authTokenScope does, otherwise add to
    // auth.graphql and run ./gradlew generateFalcor
    noMissingValues(
        AuthTokenScope.values(),
        APIAuthTokenScope.class,
        "dna-router/src/main/resources/auth.graphql");
  }
}
