package com.netflix.api.service.ums;

import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.ums.APIMessage.APIMessageState;
import com.netflix.api.service.ums.APIMessage.APISocialMessageType;

/**
 * FIXME FIXME FIXME.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIUMS.class.png"
 * alt="Class Diagram: APIUniversalMessagingService">
 */
public interface APIFilterNotificationMessagesCountRequest {

  /**
   * FIXME FIXME FIXME.
   *
   * @param readState FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationMessagesCountRequest setReadState(APIMessageState readState);

  /**
   * FIXME FIXME FIXME.
   *
   * @param socialMessageType FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationMessagesCountRequest setSocialMessageType(
      APISocialMessageType socialMessageType);

  /**
   * FIXME FIXME FIXME.
   *
   * @return
   */
  APIUser getUser();
}
