package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SupplementalMuxUrlRequest {
  private List<String> encodingProfiles;

  private Boolean secureImageUrl;

  private Boolean userInitiated;

  public List<String> getEncodingProfiles() {
    return encodingProfiles;
  }

  public Boolean getSecureImageUrl() {
    return secureImageUrl;
  }

  public Boolean getUserInitiated() {
    return userInitiated;
  }

  public SupplementalMuxUrlRequest setEncodingProfiles(List<String> encodingProfiles) {
    this.encodingProfiles = encodingProfiles;
    return this;
  }

  public SupplementalMuxUrlRequest setSecureImageUrl(Boolean secureImageUrl) {
    this.secureImageUrl = secureImageUrl;
    return this;
  }

  public SupplementalMuxUrlRequest setUserInitiated(Boolean userInitiated) {
    this.userInitiated = userInitiated;
    return this;
  }
}
