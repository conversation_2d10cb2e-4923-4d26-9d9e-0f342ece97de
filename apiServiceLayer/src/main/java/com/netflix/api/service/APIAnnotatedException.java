package com.netflix.api.service;

import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import java.util.HashMap;
import java.util.Map;

/** An Exception which contains extra meta-data in the form of name value pair annotations */
public class APIAnnotatedException extends APIServiceRuntimeException {

  private static final long serialVersionUID = 893999225124329927L;

  private final Map<String, String> annotations;

  /**
   * Constructs a new exception with the specified detail message. Annotations will be initialized
   * to empty.
   *
   * @param message the detail message. The detail message is saved for later retrieval by the
   *     {@link #getMessage()} method.
   */
  public APIAnnotatedException(String message) {
    this(message, null, null);
  }

  /**
   * Constructs a new exception with the specified detail message and cause. Annotations will be
   * initialized to empty.
   *
   * @param message the detail message. The detail message is saved for later retrieval by the
   *     {@link #getMessage()} method.
   * @param cause the cause (which is saved for later retrieval by the {@link #getCause()} method).
   *     (A <tt>null</tt> value is permitted, and indicates that the cause is nonexistent or
   *     unknown.)
   */
  public APIAnnotatedException(String message, Throwable cause) {
    super(message, cause);
    annotations = Map.of();
  }

  /**
   * Constructs a new exception with the specified detail message, cause and annotations.
   *
   * @param message the detail message. The detail message is saved for later retrieval by the
   *     {@link #getMessage()} method.
   * @param cause the cause (which is saved for later retrieval by the {@link #getCause()} method).
   *     (A <tt>null</tt> value is permitted, and indicates that the cause is nonexistent or
   *     unknown.)
   * @param objAnnotations the annotations which is saved for later retrieval by {@link
   *     #getAnnotation(String)} or {@link #getAnnotations()}. A <tt>null</tt> is permitted.
   */
  public APIAnnotatedException(
      String message, Throwable cause, Map<String, Object> objAnnotations) {
    super(message, cause);

    this.annotations = convert(objAnnotations);
  }

  /**
   * Constructs a new exception with the specified detail message and annotations.
   *
   * @param message the detail message. The detail message is saved for later retrieval by the
   *     {@link #getMessage()} method.
   * @param objAnnotations the annotations which is saved for later retrieval by {@link
   *     #getAnnotation(String)} or {@link #getAnnotations()}. A <tt>null</tt> is permitted.
   */
  public APIAnnotatedException(String message, Map<String, Object> objAnnotations) {
    super(message);

    this.annotations = convert(objAnnotations);
  }

  private static Map<String, String> convert(Map<String, Object> annotations) {
    if (annotations == null) return Map.of();

    Map<String, String> map = HashMap.newHashMap(annotations.size());
    for (var entry : annotations.entrySet()) {
      var key = entry.getKey();
      var obj = entry.getValue();

      map.put(key, obj.toString());
    }

    return Map.copyOf(map);
  }

  /**
   * Returns all the annotations set via the constructor
   *
   * @return the annotations for this exception
   */
  public Map<String, String> getAnnotations() {
    return annotations;
  }

  /**
   * Returns a single annotation
   *
   * @param name the key to lookup in the annotations dictionary
   * @return the value for they key. Can return <tt>null</tt> if no value is present.
   */
  public String getAnnotation(String name) {
    return annotations.get(Preconditions.checkNotNull(name));
  }

  @Override
  public String getMessage() {
    StringBuilder sb = new StringBuilder(super.getMessage());
    if (annotations != null && !annotations.isEmpty()) {
      sb.append(": ");
      Joiner.on(',').withKeyValueSeparator("=").appendTo(sb, annotations);
    }
    return sb.toString();
  }
}
