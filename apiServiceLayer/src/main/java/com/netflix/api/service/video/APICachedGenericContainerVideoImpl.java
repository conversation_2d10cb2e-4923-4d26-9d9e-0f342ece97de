package com.netflix.api.service.video;

import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.vms.provider.VideoFormatDescriptorProvider;

public class APICachedGenericContainerVideoImpl extends APICachedVideoImpl {

  protected APICachedGenericContainerVideoImpl(
      CompleteVideo video,
      APIVideoFactory videoFactoryInterface,
      USTVideoMetadataAdapter videoMetadataAdapter,
      VideoFormatDescriptorProvider videoFormatDescriptorProvider) {
    super(video, videoFactoryInterface, videoMetadataAdapter, videoFormatDescriptorProvider);
  }
}
