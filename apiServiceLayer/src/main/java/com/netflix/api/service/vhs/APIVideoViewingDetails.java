package com.netflix.api.service.vhs;

public interface APIVideoViewingDetails {

  /**
   * How long the viewing lasted in second
   *
   * @return viewing duration
   */
  int getWatchedDurationInSeconds();

  /**
   * The country for the viewing
   *
   * @return the country as an API model object
   */
  String getCountry();

  /**
   * Get the ESN (Electronic Serial Number) of the device on which the video was viewed. API.Next
   * resolves this ESN from the following sources (in the order of precedence):
   *
   * <ol>
   *   <li>request parameter "<code>esn</code>" (e.g. <code>esn=NFPS3-001-<i>blah</i></code>)
   *   <li>request header "<code>X-Netflix.esn</code>"
   *   <li>device or double bound cookies (<code>NetflixId</code> and <code>SecureNetflixId</code>
   *       ones)
   * </ol>
   *
   * @return the ESN of the device, as a String
   */
  String getEsn();

  /**
   * The device type for this viewing
   *
   * @return the device type
   */
  int getDeviceTypeId();

  /**
   * The timestamp in which the viewing started
   *
   * @return the timestamp
   */
  long getStartTs();
}
