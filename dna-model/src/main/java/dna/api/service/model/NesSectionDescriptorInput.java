package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NesSectionDescriptorInput {
  private NesSectionType sectionType;

  private NesSectionDescriptorQualifierUnion qualifier;

  public NesSectionType getSectionType() {
    return sectionType;
  }

  public NesSectionDescriptorQualifierUnion getQualifier() {
    return qualifier;
  }

  public NesSectionDescriptorInput setSectionType(NesSectionType sectionType) {
    this.sectionType = sectionType;
    return this;
  }

  public NesSectionDescriptorInput setQualifier(NesSectionDescriptorQualifierUnion qualifier) {
    this.qualifier = qualifier;
    return this;
  }
}
