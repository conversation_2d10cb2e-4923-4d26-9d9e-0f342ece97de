package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class MostLiked {
  private Long ttl;

  private Boolean isMostLiked;

  private Float percentThumbsUp;

  public Long getTtl() {
    return ttl;
  }

  public Boolean getIsMostLiked() {
    return isMostLiked;
  }

  public Float getPercentThumbsUp() {
    return percentThumbsUp;
  }

  public MostLiked setTtl(Long ttl) {
    this.ttl = ttl;
    return this;
  }

  public MostLiked setIsMostLiked(Boolean isMostLiked) {
    this.isMostLiked = isMostLiked;
    return this;
  }

  public MostLiked setPercentThumbsUp(Float percentThumbsUp) {
    this.percentThumbsUp = percentThumbsUp;
    return this;
  }
}
