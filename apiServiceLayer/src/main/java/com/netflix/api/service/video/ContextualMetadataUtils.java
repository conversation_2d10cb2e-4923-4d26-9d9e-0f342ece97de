package com.netflix.api.service.video;

import static com.netflix.api.service.APIClientCapabilitiesInternal.DEVICE_CAPABILITIES;
import static com.netflix.api.service.APIClientCapabilitiesInternal.FEATURE_CAPABILITIES;

import com.google.common.annotations.VisibleForTesting;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIClientCapabilitiesInternal;
import com.netflix.api.service.APIRequest;
import com.netflix.archaius.api.Property;
import com.netflix.lang.RequestVariable;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import ust.video.personalized.v1.PersonalizedVideoContext;

public class ContextualMetadataUtils {

  private ContextualMetadataUtils() {}

  private static final RequestVariable<AtomicReference<Map<String, String>>>
      requestScopedCapabilities =
          new RequestVariable<>() {
            @Override
            public AtomicReference<Map<String, String>> initialValue() {
              return new AtomicReference<>();
            }
          };

  // this can be removed once we confirm there are no issues
  private static final Property<Boolean> CLIENT_SUPPORTS_COLLECTIBLES_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.clientSupportsCollectibles.enabled", Boolean.class)
          .orElse(false);

  /** Replacement for APICachedVideoImpl.getDeviceCapabilitiesForNSRE() */
  public static Set<String> getDeviceCapabilities() {
    // FIXME add caching?
    // no device capabilities for platforms that don't support feature - we no longer opt-in on
    // their behalf, DNA-256
    return _deviceCapabilities()
        .map(strings -> (Set<String>) new HashSet<>(strings))
        .orElse(Collections.emptySet());
  }

  public static String getDeviceCapabilitiesAsString() {
    return _deviceCapabilities().map(strings -> String.join(",", strings)).orElse(null);
  }

  private static Optional<Collection<String>> _deviceCapabilities() {
    return APIClientCapabilitiesInternal.getCapability(
        APIRequest.getCurrentRequest(), DEVICE_CAPABILITIES);
  }

  public static PersonalizedVideoContext.Builder populateCapabilities(
      PersonalizedVideoContext.Builder builder) {
    APIClientCapabilitiesInternal.get(APIRequest.getCurrentRequest())
        .ifPresent(
            apiClientCapabilitiesInternal -> {
              Map<String, Collection<String>> capabilities = apiClientCapabilitiesInternal.asMap();
              if (capabilities
                  .getOrDefault(
                      APIClientCapabilitiesInternal.INTERACTIVE_ORIGINALS, Collections.emptySet())
                  .contains(APIClientCapabilitiesInternal.COLLECTABLES)) {
                if (CLIENT_SUPPORTS_COLLECTIBLES_ENABLED.get()) {
                  builder.setClientSupportsCollectibles(true);
                }
              }

              Collection<String> features = capabilities.get(FEATURE_CAPABILITIES);
              if (features != null) {
                builder.addAllFeatureCapabilities(features);
              }
            });
    _deviceCapabilities().ifPresent(builder::addAllDeviceCapabilities);
    return builder;
  }

  public static Map<String, String> getClientCapabilities() {
    Map<String, String> cachedCapabilities = requestScopedCapabilities.get().get();
    if (cachedCapabilities != null) {
      return cachedCapabilities;
    }

    final Map<String, String> capabilities = new HashMap<>();
    APIClientCapabilitiesInternal.get(APIRequest.getCurrentRequest())
        .ifPresent(
            apiClientCapabilitiesInternal -> {
              if (apiClientCapabilitiesInternal.currentInteractiveTitlesPresent()) {
                capabilities.put(
                    APIClientCapabilitiesInternal.INTERACTIVE_VIDEOS_FEATURE,
                    apiClientCapabilitiesInternal.getCurrentInteractiveTitlesAsString());
              }

              if (apiClientCapabilitiesInternal
                  .asMap()
                  .getOrDefault(
                      APIClientCapabilitiesInternal.INTERACTIVE_ORIGINALS, Collections.emptySet())
                  .contains(APIClientCapabilitiesInternal.COLLECTABLES)) {
                capabilities.put("clientSupportsCollectibles", "true");
              }

              capabilities.putAll(passedThroughFeatureCapabilities(apiClientCapabilitiesInternal));
            });

    String titleCapability = ContextualMetadataUtils.getDeviceCapabilitiesAsString();
    if (titleCapability != null) {
      capabilities.put("titleCapabilities", titleCapability);
    }
    requestScopedCapabilities.get().set(capabilities);
    return capabilities;
  }

  public static Set<String> getFeatureCapabilities() {
    final Map<String, String> capabilities = new HashMap<>();
    APIClientCapabilitiesInternal.get(APIRequest.getCurrentRequest())
        .ifPresent(
            apiClientCapabilitiesInternal ->
                capabilities.putAll(
                    passedThroughFeatureCapabilities(apiClientCapabilitiesInternal)));
    return capabilities.keySet();
  }

  // https://jira.netflix.net/browse/DNA-2598
  @VisibleForTesting
  public static Map<String, String> passedThroughFeatureCapabilities(
      APIClientCapabilitiesInternal apiClientCapabilitiesInternal) {
    return apiClientCapabilitiesInternal
        .asMap()
        .getOrDefault(FEATURE_CAPABILITIES, Collections.emptySet())
        .stream()
        .collect(Collectors.toMap(Function.identity(), capability -> "true"));
  }
}
