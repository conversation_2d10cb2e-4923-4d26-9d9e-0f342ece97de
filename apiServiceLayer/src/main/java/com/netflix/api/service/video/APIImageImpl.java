package com.netflix.api.service.video;

import static org.apache.commons.lang.StringUtils.substringBefore;

import com.netflix.servo.monitor.DynamicCounter;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

public class API<PERSON>mageImpl implements APIImage {

  private final int width;
  private final int height;
  private final String url;
  private final String secureUrl;
  private final ImageExtension extension;
  private final String imageTypeIdentifier;
  private final String sourceFileId;

  protected APIImageImpl(
      String url,
      int width,
      int height,
      String extension,
      String imageTypeIdentifier,
      String sourceFileId) {
    this(url, null, width, height, extension, imageTypeIdentifier, sourceFileId);
  }

  public APIImageImpl(com.netflix.images.protogen.Image image, String key, boolean secure) {
    this(
        secure ? image.getSecureUrl() : image.getInsecureUrl(),
        image.getSecureUrl(),
        image.getWidth(),
        image.getHeight(),
        null,
        key,
        image.getBoxedSourceFileID());
    DynamicCounter.increment("api.image.constructor", "secure", Boolean.toString(secure));
  }

  /**
   * Construct an APIImage for a url, height, width and an extension.
   *
   * @param url location of the image
   * @param width width of the image in pixels
   * @param height height of the image in pixels
   * @param extension file extension of the image (defaults to jpg if null is passed)
   * @param imageTypeIdentifier Uniquely identifies the image type
   * @param sourceFileId the source file id from nil
   */
  public APIImageImpl(
      final String url,
      final String secureUrl,
      final int width,
      final int height,
      final String extension,
      String imageTypeIdentifier,
      String sourceFileId) {
    this.url = url;
    this.secureUrl = secureUrl;
    this.width = width;
    this.height = height;
    this.extension = fromString(extension, url);
    this.imageTypeIdentifier = imageTypeIdentifier;
    this.sourceFileId = sourceFileId;
  }

  static ImageExtension fromString(String fileExtension, String url) {
    if (fileExtension != null) {
      for (ImageExtension ie : ImageExtension.values()) {
        if (fileExtension.endsWith(ie.name())) return ie;
      }
    }

    return getImageExtension(url);
  }

  private static ImageExtension getImageExtension(String url) {
    if (url != null && url.trim().length() > 4) {
      for (ImageExtension ie : ImageExtension.values()) {
        if (substringBefore(url, "?").endsWith(ie.name())) {
          return ie;
        }
      }
    }
    return ImageExtension.jpg;
  }

  @Override
  public String getUrl() {
    return url;
  }

  @Override
  public int getWidth() {
    return width;
  }

  @Override
  public int getHeight() {
    return height;
  }

  @Override
  public ImageExtension getExtension() {
    return extension;
  }

  @Override
  public String getSourceFileId() {
    return sourceFileId;
  }

  @Override
  public String getUrl(Boolean secure) {
    if (Objects.equals(Boolean.TRUE, secure)) {
      return secureUrl != null ? secureUrl : url.startsWith("https") ? url : null;
    }
    return url;
  }

  @Override
  public Map<String, String> getAttributes() {
    return null;
  }

  @Override
  public Map<String, Collection<String>> getMultiValueAttributes() {
    return null;
  }

  @Override
  public String getImageTypeIdentifier() {
    return imageTypeIdentifier;
  }

  @Override
  public boolean isRightToLeft() {
    return false;
  }

  @Override
  public boolean isSmoky() {
    return false;
  }

  @Override
  public String toString() {
    return "APIImage{"
        + "width="
        + getWidth()
        + ", height="
        + getHeight()
        + ", url='"
        + getUrl()
        + '\''
        + ", secureUrl='"
        + getUrl(true)
        + '\''
        + ", extension="
        + extension
        + ", imageTypeIdentifier='"
        + imageTypeIdentifier
        + '\''
        + ", sourceFileId='"
        + sourceFileId
        + '\''
        + '}';
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIImageImpl)) {
      return false;
    }
    APIImageImpl apiImage = (APIImageImpl) o;
    return width == apiImage.width
        && height == apiImage.height
        && Objects.equals(url, apiImage.url)
        && Objects.equals(secureUrl, apiImage.secureUrl)
        && extension == apiImage.extension
        && Objects.equals(imageTypeIdentifier, apiImage.imageTypeIdentifier);
  }

  @Override
  public int hashCode() {
    return Objects.hash(
        width, height, url, secureUrl, extension, imageTypeIdentifier, sourceFileId);
  }
}
