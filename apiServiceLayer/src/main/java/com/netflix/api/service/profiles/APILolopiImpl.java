package com.netflix.api.service.profiles;

import com.netflix.map.datatypes.MapAnnotations;
import com.netflix.map.datatypes.MapItem;
import com.netflix.map.datatypes.MapList;
import com.netflix.map.datatypes.MapResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class APILolopiImpl implements APILolopi {

  private final List<APILolopiList> items;
  private final Map<String, Object> annotations;

  public APILolopiImpl(MapResponse response) {
    this.annotations =
        Optional.ofNullable(response)
            .flatMap(r -> Optional.ofNullable(r.getAnnotations()))
            .map(MapAnnotations::getAnnotationsAsMap)
            .orElse(Map.of());
    List<MapList<?>> mapLists =
        Optional.ofNullable(response).map(MapResponse::getAllLists).orElse(List.of());
    List<APILolopiList> lists = new ArrayList<>(mapLists.size());
    for (MapList<?> list : mapLists) {
      MapList<MapItem<?>> mapList = (MapList<MapItem<?>>) list;
      lists.add(new APILolopiListImpl(mapList));
    }
    this.items = lists;
  }

  @Override
  public Map<String, Object> getAnnotations() {
    return annotations;
  }

  @Override
  public Object getAnnotationValue(String key) {
    return annotations.get(key);
  }

  @Override
  public List<APILolopiList> getItems() {
    return items;
  }
}
