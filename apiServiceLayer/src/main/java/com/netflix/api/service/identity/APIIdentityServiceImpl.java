package com.netflix.api.service.identity;

import com.google.common.annotations.VisibleForTesting;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.annotations.ExcludeFromCoverage;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.identity.CurrentIdentityResult;
import com.netflix.api.platform.identity.IdentityPassportResult;
import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.api.platform.util.CounterNames;
import com.netflix.api.sec.EndpointGroupDefender;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.service.identity.APIAuthorizationException.AuthorizationExceptionCause;
import com.netflix.api.service.identity.APIRegistrationException.Reason;
import com.netflix.config.DynamicStringSetProperty;
import com.netflix.config.FastProperty.BooleanProperty;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.peas.auth.protogen.TokenScope;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Registry;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.subscriberservice.protogen.AccountAttributes;
import com.netflix.userauthservice2.protogen.CreateAutoLoginTokenReply;
import com.netflix.userauthservice2.protogen.CreateAutoLoginTokenRequest;
import com.netflix.userauthservice2.protogen.UserAuthenticationServiceGrpc.UserAuthenticationServiceStub;
import jakarta.annotation.Nonnull;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
@SuppressWarnings("rawtypes")
public class APIIdentityServiceImpl implements APIIdentityService {

  private static final Logger logger = LoggerFactory.getLogger(APIIdentityServiceImpl.class);

  private static final BooleanProperty parentalControlOnLegacyDevice =
      new BooleanProperty("api.legacy.pin.parentalcontrol.feature.on", false);

  private static final BooleanProperty ENABLE_PROTO_RESTRICTION =
      new BooleanProperty("com.netflix.api.identityService.protoRestriction", true);
  private static final DynamicStringSetProperty PROTO_ALLOWLIST =
      new DynamicStringSetProperty(
          "com.netflix.api.identityService.protoRestrictionAllowlist", Set.of("/shakti", "/users"));
  private static final BooleanProperty ENABLE_WIN_PIN =
      new BooleanProperty("api.identity.winpin.enable", true);

  private final Gateway gateway;
  private final APIUserFactory userFactory;
  private final APIUserHolderFactory userHolderFactory;
  private final UserAuthenticationServiceStub userAuthService2; // UAS2 (new)
  private final Counter credentialAccountUpdate;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public APIIdentityServiceImpl(
      APIUserFactory userFactory,
      APIUserHolderFactory userHolderFactory,
      Registry registry,
      @GrpcSpringClient("userauthservice2") UserAuthenticationServiceStub userAuthService2) {
    this.gateway = new GatewayProd();
    this.userFactory = userFactory;
    this.userHolderFactory = userHolderFactory;
    this.userAuthService2 = userAuthService2;
    credentialAccountUpdate = registry.counter(CounterNames.AUTH_PREFIX + "account-update");
  }

  @VisibleForTesting
  APIIdentityServiceImpl(
      Gateway gateway,
      APIUserFactory userFactory,
      APIUserHolderFactory userHolderFactory,
      Registry registry,
      UserAuthenticationServiceStub userAuthService2) {
    this.gateway = gateway;
    this.userFactory = userFactory;
    this.userHolderFactory = userHolderFactory;
    this.userAuthService2 = userAuthService2;
    credentialAccountUpdate = registry.counter(CounterNames.AUTH_PREFIX + "account-update");
  }

  @Override
  public APIUser authenticateRequest(
      HttpServletRequest request,
      HttpServletResponse response,
      boolean needSecureCookies,
      boolean userAuthRequired)
      throws APIAuthorizationException {
    if (isPassportAuthenticatedRequest()) {
      DynamicCounter.increment("api.identityService.authenticateRequest", "result", "passport");
      return getPassportAuthenticatedUser(request, needSecureCookies, userAuthRequired);
    }
    DynamicCounter.increment("api.identityService.authenticateRequest", "result", "fail");
    throw new APIAuthorizationException(
        APIAuthorizationException.AuthorizationExceptionCause.NO_USER_CREDENTIALS, "No Passport");
  }

  private boolean isPassportAuthenticatedRequest() {
    IdentityResult identityResult = CurrentIdentityResult.get();
    return identityResult instanceof IdentityPassportResult;
  }

  private APIUser getPassportAuthenticatedUser(
      HttpServletRequest request, boolean secureOnly, boolean userAuthRequired)
      throws APIAuthorizationException {
    if (!isPassportAuthenticatedRequest()) {
      logger.error("Missing IdentityPassportResult");
      return null;
    }
    IdentityPassportResult identityResult = (IdentityPassportResult) CurrentIdentityResult.get();

    if (!identityResult.isSuccess()) {
      DynamicCounter.increment("COUNTER-IdentityService-forceLogin.passport");
      throw new APIAuthorizationException(
          AuthorizationExceptionCause.INVALID_TOKEN, "force login from upstream passport");
    }

    // authorization checks - ensure that the credentials were secure if required
    String path = request.getPathInfo();
    IdentityResult.IdentitySource source = identityResult.getUserInformationSource();

    if (ENABLE_PROTO_RESTRICTION.get() && notAllowed(path)) {
      // check the security level of the incoming credentials. Insecure cookies are rejected.
      boolean secureChannel =
          (source.equals(IdentityResult.IdentitySource.COOKIES)
              || source.equals(IdentityResult.IdentitySource.MSL)
              || source.equals(IdentityResult.IdentitySource.CREDENTIALS)
              || source.equals(IdentityResult.IdentitySource.ASSERTION)
              || source.equals(IdentityResult.IdentitySource.AUTO_LOGIN_TOKEN)
              // partner cookies paths are allowed in IdentityFilter
              || source.equals(IdentityResult.IdentitySource.PARTNER_COOKIE)
              || source.equals(IdentityResult.IdentitySource.PARTNER_TOKEN));

      if (secureOnly && userAuthRequired && !secureChannel) {
        DynamicCounter.increment("COUNTER-IdentityService-protoRejectError.passport", "path", path);
        throw new APIAuthorizationException(
            AuthorizationExceptionCause.NO_USER_CREDENTIALS,
            "proto http does not match endpoint userAuthType requirement of https for " + path);
      }
    }
    if (identityResult.getCustomerId() == null) {
      logger.debug("Missing user:  device bound token");
      return null;
    }
    return gateway.newAPIUser(identityResult.getCustomerId(), identityResult.getAccountOwnerId());
  }

  @Override
  public Observable<Boolean> accountHasPwd(APIUser user) {
    if (user == null) {
      return Observable.error(new IllegalArgumentException("Must provide user"));
    }
    return gateway.accountHasPwd(APIUserUtil.getAccountOwnerId(user));
  }

  private static boolean notAllowed(String path) {
    if (path != null) {
      Set<String> allowedpaths = PROTO_ALLOWLIST.get();
      for (String allowed : allowedpaths) {
        if (path.startsWith(allowed)) {
          return false;
        }
      }
    }
    return true;
  }

  @Override
  @Deprecated
  public Observable<String> createAutoLoginToken() {
    APIRequest request = APIRequest.getCurrentRequest();
    APIUser user = request.getUser();
    if (user == null) {
      return Observable.error(
          new APIAuthorizationException(
              AuthorizationExceptionCause.NO_USER_CREDENTIALS, "user not logged in"));
    }
    if (EndpointGroupDefender.getInstance().allowAutologin(request)) {
      MantisPublishContext.getCurrent().add("autologinGrpc", "true");
      return gateway.createAutoLoginTokenGrpc(user);
    } else {
      return Observable.error(
          new IllegalStateException("The esn for this request is not allowed for this action"));
    }
  }

  @Override
  public Observable<String> createAutoLoginToken(Set<APIAuthTokenScope> tokenScopes) {
    APIRequest request = APIRequest.getCurrentRequest();
    IdentityResult identityResult = CurrentIdentityResult.get();
    if (identityResult == null || identityResult.getCustomerId() == null) {
      return Observable.error(
          new APIAuthorizationException(
              AuthorizationExceptionCause.NO_USER_CREDENTIALS, "user not logged in"));
    }
    PassportIdentity passportIdentity = CurrentIdentityResult.getPassportIdentity();
    if (passportIdentity == null) {
      return Observable.error(
          new APIAuthorizationException(
              AuthorizationExceptionCause.NO_USER_CREDENTIALS,
              "current identity is not passport based"));
    }
    if (tokenScopes == null || tokenScopes.isEmpty()) {
      return Observable.error(
          new APIAuthorizationException(
              AuthorizationExceptionCause.TOKEN_SCOPE_NOT_SUPPLIED,
              "Token scope is not supplied while creating an Auto Login Token"));
    }
    if (EndpointGroupDefender.getInstance().allowAutologin(request)) {
      MantisPublishContext.getCurrent().add("autologinGrpc", "true");
      MantisPublishContext.getCurrent()
          .add(
              "autologinScopes",
              tokenScopes.stream().map(Enum::name).sorted().collect(Collectors.joining(",")));
      return gateway.createAutoLoginTokenGrpc(passportIdentity, tokenScopes);
    } else {
      return Observable.error(
          new IllegalStateException("The esn for this request is not allowed for this action"));
    }
  }

  @Override
  public UpdateBuilder getUpdateBuilder() throws APIAuthorizationException {
    return new SubscriberUpdateBuilder();
  }

  abstract class AbstractUpdateBuilder implements UpdateBuilder {

    volatile boolean built = false;
    final Long accountOwnerId;
    final Long customerId;
    String touStatus;
    private final IdentityResult identityResult;

    AbstractUpdateBuilder() throws APIAuthorizationException {
      // IdentityFilter already extracted identity from passport; use it
      identityResult = CurrentIdentityResult.get();
      accountOwnerId = identityResult.getAccountOwnerId();
      Long cid = identityResult.getCustomerId();
      if (logger.isInfoEnabled()) {
        logger.info("accountOwnerId={}", accountOwnerId);
        logger.info("customerId={}", cid);
      }

      if (accountOwnerId == null) {
        throw new APIAuthorizationException(
            AuthorizationExceptionCause.AUTHENTICATION_FAILURE, "invalid or missing passport");
      }

      customerId = cid != null ? cid : accountOwnerId;
    }

    void checkAlreadyBuilt() {
      if (built) {
        throw new IllegalStateException("The user registration has already been done");
      }
    }

    @Override
    public synchronized UpdateBuilder setEmail(String email) {
      checkAlreadyBuilt();
      return this;
    }

    @Override
    @Deprecated
    public synchronized UpdateBuilder setNewPassword(String newPassword) {
      checkAlreadyBuilt();
      return this;
    }

    @Override
    @Deprecated
    public synchronized UpdateBuilder setCurrentPassword(String currentPassword) {
      checkAlreadyBuilt();
      return this;
    }

    @Override
    public UpdateBuilder setPin(String pin) {
      _setPin(pin);
      return this;
    }

    abstract void _setPin(String pin);

    @Override
    public UpdateBuilder setPinEnabled(boolean enabled) {
      _setPinEnabled(enabled);
      return this;
    }

    abstract void _setPinEnabled(boolean enabled);

    @Override
    public UpdateBuilder setAdultVerified(boolean verified) {
      _setAdultVerified(verified);
      return this;
    }

    abstract void _setAdultVerified(boolean verified);

    @Override
    public UpdateBuilder setSignOutOfAllDevices(boolean signOutOfAllDevices) {
      return this;
    }

    @Override
    public APIUserHolder build() throws APIRegistrationException {
      return buildInternal();
    }

    private synchronized APIUserHolder buildInternal() throws APIRegistrationException {
      checkAlreadyBuilt();

      try {
        if (touStatus != null) {
          _updateTOU(touStatus);
        }

        _updateAccount();

        built = true;
        credentialAccountUpdate.increment();
        return userHolderFactory.create(gateway.newAPIUser(identityResult, true));
      } catch (Exception e) {
        throw new APIRegistrationException("Internal error", Reason.INTERNAL_ERROR, e);
      }
    }

    abstract void _updateTOU(String touStatus);

    abstract void _updateAccount();
  }

  class SubscriberUpdateBuilder extends AbstractUpdateBuilder {

    private AccountAttributes.Builder accountAttributesBuilder;
    private Boolean isAgeVerified;

    SubscriberUpdateBuilder() throws APIAuthorizationException {
      super();
    }

    private AccountAttributes.Builder getAccountAttributesBuilder() {
      checkAlreadyBuilt();
      if (accountAttributesBuilder == null) {
        accountAttributesBuilder = AccountAttributes.newBuilder();
      }
      return accountAttributesBuilder;
    }

    @Override
    void _setPin(String pin) {
      if (pin != null) {
        getAccountAttributesBuilder().setYouthMaturityPin(pin);
      }
    }

    @Override
    void _setPinEnabled(boolean enabled) {
      getAccountAttributesBuilder().setYouthMaturityPinEnabled(enabled);
    }

    @Override
    void _setAdultVerified(boolean verified) {
      isAgeVerified = verified;
    }

    @Override
    void _updateTOU(@Nonnull String touStatus) {
      userFactory.getAccountProfileHandler().updateTOU(accountOwnerId, touStatus, "next");
    }

    @Override
    void _updateAccount() {
      if (accountAttributesBuilder != null) {
        userFactory
            .getAccountProfileHandler()
            .updateAccount(accountOwnerId, accountAttributesBuilder.build());
      }
      if (isAgeVerified != null) {
        userFactory.getAccountProfileHandler().updateAgeVerified(accountOwnerId, isAgeVerified);
      }
    }
  }

  interface Gateway {

    APIUserInternal newAPIUser(long customerId, Long accountOwnerId);

    Observable<Boolean> accountHasPwd(Long accountOwnerId);

    APIUser newAPIUser(IdentityResult identityResult, boolean invalidateCache);

    Observable<String> createAutoLoginTokenGrpc(APIUser user);

    Observable<String> createAutoLoginTokenGrpc(
        PassportIdentity identity, Set<APIAuthTokenScope> tokenScopes);
  }

  @ExcludeFromCoverage
  private class GatewayProd implements Gateway {

    @Override
    public Observable<String> createAutoLoginTokenGrpc(APIUser user) {
      final CreateAutoLoginTokenRequest tokenRequest =
          CreateAutoLoginTokenRequest.newBuilder()
              .setPassport(CurrentIdentityResult.getPassportIdentity().getPassportAsString())
              .addAllScopes(Set.of(TokenScope.DEFAULT))
              .build();
      return RxObservable.call(userAuthService2::createAutoLoginToken, tokenRequest)
          .map(CreateAutoLoginTokenReply::getAutoLoginToken);
    }

    @Override
    public Observable<String> createAutoLoginTokenGrpc(
        PassportIdentity identity, Set<APIAuthTokenScope> tokenScopes) {
      Set<TokenScope> mappedTokenScopes = mapTokenScopes(tokenScopes);
      final CreateAutoLoginTokenRequest tokenRequest =
          CreateAutoLoginTokenRequest.newBuilder()
              .setPassport(identity.getPassportAsString())
              .addAllScopes(mappedTokenScopes)
              .build();
      return RxObservable.call(userAuthService2::createAutoLoginToken, tokenRequest)
          .map(CreateAutoLoginTokenReply::getAutoLoginToken);
    }

    @Override
    public APIUserInternal newAPIUser(long customerId, Long accountOwnerId) {
      return userFactory.createUser(customerId, accountOwnerId);
    }

    @Override
    public APIUser newAPIUser(IdentityResult identityResult, boolean invalidateCache) {
      return userFactory.createUser(identityResult, invalidateCache);
    }

    @Override
    public Observable<Boolean> accountHasPwd(Long accountOwnerId) {
      return userFactory.getAccountProfileHandler().getAccountHasPassword(accountOwnerId);
    }
  }

  @Override
  public Observable<Boolean> isProfileLockPinValid(
      final String userEnteredPin, final APIUser user) {
    return ((APIUserInternal) user)
        .getAccountProfileAsObservable()
        .map(account -> Objects.equals(account.getBoxedProfileAccessPin(), userEnteredPin));
  }

  @Override
  @Deprecated
  public Observable<Boolean> isUserPinValid(final String userEnteredPin, final APIUser user) {
    // temporarily change the behavior for win endpoints to only validate the pin and not consult
    // age verification
    if (parentalControlOnLegacyDevice.get()) {
      return user.isProfileLocked()
          .flatMap(
              locked -> {
                if (locked != null && locked) {
                  return isProfileLockPinValid(userEnteredPin, user);
                } else {
                  return isR21PinValid(userEnteredPin, user);
                }
              });
    } else {
      return isR21PinValid(userEnteredPin, user);
    }
  }

  @Override
  @Deprecated
  public Observable<Boolean> isUserPinValid(
      final String userEnteredPin,
      final APIUser user,
      final boolean isClientAgeVerificationCapable) {
    if (parentalControlOnLegacyDevice.get()) {
      return user.isProfileLocked()
          .flatMap(
              locked -> {
                if (locked != null && locked) {
                  return isProfileLockPinValid(userEnteredPin, user);
                } else {
                  return isR21PinValid(userEnteredPin, user, isClientAgeVerificationCapable);
                }
              });
    } else {
      return isR21PinValid(userEnteredPin, user, isClientAgeVerificationCapable);
    }
  }

  // Mimics old isUserPinValid() method behavior, so isUserPinValid() can be retrofitted with
  @Override
  public Observable<Boolean> isR21PinValid(final String userEnteredPin, final APIUser user) {
    // temporarily change the behavior for win endpoints to only validate the pin and not consult
    // age verification
    String path = APIRequest.getCurrentRequest().getRequestContext().getEndpointPath();
    boolean defaultClientAgeVerificationCapable =
        ENABLE_WIN_PIN.get() && path != null && path.startsWith("/win");
    return isR21PinValid(userEnteredPin, user, defaultClientAgeVerificationCapable);
  }

  @Override
  public Observable<Boolean> isR21PinValid(
      final String userEnteredPin,
      final APIUser user,
      final boolean isClientAgeVerificationCapable) {

    return ((APIUserInternal) user)
        .getAccountProfileAsObservable()
        .concatMapEager(
            account ->
                Observable.defer(
                    () ->
                        Observable.just(
                            (isClientAgeVerificationCapable
                                    || account.getOptionalIsAgeVerified().orElse(false))
                                && Objects.equals(account.getBoxedYouthPin(), userEnteredPin))));
  }

  @VisibleForTesting
  static Set<TokenScope> mapTokenScopes(Set<APIAuthTokenScope> tokenScopes) {
    List<APIAuthTokenScope> unrecognizedScopes =
        tokenScopes.stream()
            .filter(apiAuthTokenScope -> TokenScope.forNumber(apiAuthTokenScope.getValue()) == null)
            .toList();
    if (!unrecognizedScopes.isEmpty()) {
      throw new APIServiceRuntimeException(
          String.format(
              "Unable to map scopes (%s). This is unexpected. This may happen for a new scope which isn't supported yet.",
              unrecognizedScopes.stream().map(Enum::name).collect(Collectors.joining(","))));
    }
    return tokenScopes.stream()
        .map(APIAuthTokenScope::getValue)
        .map(TokenScope::forNumber)
        .collect(Collectors.toSet());
  }
}
