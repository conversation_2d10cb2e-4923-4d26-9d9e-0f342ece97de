package com.netflix.api.service.vps;

import java.util.ArrayList;
import java.util.List;

public class APIVoiceRequestPayloadImpl implements APIVoiceRequestPayload {

  private final List<APIVoiceResource> resources;
  private final List<APIVoiceIntent> intents;

  public APIVoiceRequestPayloadImpl(
      final List<APIVoiceResource> resources, final List<APIVoiceIntent> intents) {
    this.resources = (resources != null) ? resources : new ArrayList<>();
    this.intents = (intents != null) ? intents : new ArrayList<>();
  }

  public List<APIVoiceResource> getResources() {
    return resources;
  }

  public List<APIVoiceIntent> getIntents() {
    return intents;
  }

  public void addIntent(APIVoiceIntent intent) {
    intents.add(intent);
  }

  public void addResource(APIVoiceResource resource) {
    resources.add(resource);
  }
}
