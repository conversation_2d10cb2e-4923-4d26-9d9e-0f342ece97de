
# proto2api for GraphQL

In order to code gen GraphQL SDL (*.graphql) files from a protobuf schema:

1. Define the configs in ./proto2api.gradle
1. Output objects and enums should be defined using `graphQLType`.
1. Input objects should be defined as `graphQLInputType`.
1. Define the types in order of hierarchy. If a proto message contains and references an enum, define the enum first.
1. Output all the types and configs generated from a single proto generation task in a unique *.graphql file. The plugin will overwrite the existing file, but retain any existing *directives* only.
1. **Note:** The plugin can create ObjectTypeDefinitions from service definitions in proto as well, so you can define routes this way.
1. Find any input/output object types that you don't want to generate dna-model types for (you'd rather output protos instead).
    * Mark these with `@implemented_by(klass: "FQDN_OF_PROTOGEN_CLASS")`
    * dna-model types won't be generated, and input and output types in *Routes files will reference the `klass` instead.
1. Once the service has been defined in your unique *.graphql file by running the proto2api.gradle task, define the route in the `Api` type in ./src/main/resources/api.graphql.
1. Run `./gradlew generateFalcor`
1. Implement a protobuf deserializer for any `implemented_by` types that are protogen classes. See `dna.api.deserializers.ExamplePingResponseDeserializer` for an example. Add these to JsonUtil#desersializers. This will be used by the DNA.java client, as well as demograph for protobuf <--> json (de)serialization.

# TODOs
Following are some additional tasks that would make code gen better.

1. Enable ObjectTypeExtensionDefinitions in the ast generator (dna-graphql-schema).

# Troubleshooting

1. If you see the following failure/stack trace:
```
java.lang.RuntimeException: 
  com.fasterxml.jackson.databind.JsonMappingException: 
  com.fasterxml.jackson.databind.JsonMappingException: 
  com.fasterxml.jackson.databind.exc.InvalidDefinitionException: 
  Cannot find a (Map) Key deserializer for type [simple type, class com.google.protobuf.Descriptors$FieldDescriptor]
```
Set a breakpoint in 
`com.fasterxml.jackson.databind.deser.BeanDeserializerFactory#97` and line 1979 should return your custom bean deserializer. See if it's not doing that.
