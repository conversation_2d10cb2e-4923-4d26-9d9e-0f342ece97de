package com.netflix.api.platform.avatar;

import static java.util.Map.entry;

import java.util.Map;

public class LegacyIconNameMapper {
  private LegacyIconNameMapper() {}

  private static final Map<String, String> ICON_MAP =
      Map.ofEntries(
          entry("icon1", "AVATAR|4b861060-3369-11e8-aada-0a7af94fe082|en|US|80148069"),
          entry("icon2", "AVATAR|143612c0-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon3", "AVATAR|14374b41-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon4", "AVATAR|14372431-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon5", "AVATAR|1436d611-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon6", "AVATAR|143660e2-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon7", "AVATAR|143612c1-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon8", "AVATAR|14377251-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon9", "AVATAR|1436fd20-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon10", "AVATAR|1436af00-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon11", "AVATAR|143660e1-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon12", "AVATAR|14379960-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon13", "AVATAR|1436d610-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon14", "AVATAR|143687f1-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon15", "AVATAR|143639d1-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon16", "AVATAR|14377250-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon17", "AVATAR|14372430-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon18", "AVATAR|1436af01-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon19", "AVATAR|611b84c0-3768-11e8-aada-0a7af94fe082|en|US|80148069"),
          entry("icon20", "AVATAR|1436fd22-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon21", "AVATAR|14374b42-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon22", "AVATAR|1436d612-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon23", "AVATAR|143687f2-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon24", "AVATAR|143660e0-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon25", "AVATAR|14379961-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon26", "AVATAR|1436fd21-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon27", "AVATAR|143687f0-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon28", "AVATAR|143639d0-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon29", "AVATAR|1435ebb0-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon36", "AVATAR|14374b40-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon37", "AVATAR|142d1210-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon38", "AVATAR|14377252-36c7-11e8-b308-0e1f42e0af2a|en|US|80148069"),
          entry("icon51", "AVATAR|864d75f0-3768-11e8-a734-126acf8e1a60|en|US|80148069"));

  public static String from(String legacy) {
    return ICON_MAP.getOrDefault(legacy, legacy);
  }
}
