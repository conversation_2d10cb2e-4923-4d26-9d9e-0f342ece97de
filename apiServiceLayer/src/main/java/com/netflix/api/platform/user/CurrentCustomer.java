package com.netflix.api.platform.user;

import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.lang.ThreadVariable;

/**
 * Contains thread-local information about the current customer.
 *
 * <AUTHOR>
 */
@Deprecated
public class CurrentCustomer {

  private static final ThreadVariable<IdentityResult> IDENTITY = new ThreadVariable<>();

  private static final ThreadVariable<Long> CUSTOMER_ID = new ThreadVariable<>();

  /** Called for DCR requests. */
  public static void setIdentity(IdentityResult i) {
    IDENTITY.set(i);
    CUSTOMER_ID.set(i.getCustomerId());
  }

  /** Called for OAuth requests. */
  public static void setCustomerId(Long customerId) {
    CUSTOMER_ID.set(customerId);
  }

  public static CurrentCustomer instance() {
    return new CurrentCustomer();
  }

  private CurrentCustomer() {}

  public Long getCustomerId() {
    return CUSTOMER_ID.get();
  }
}
