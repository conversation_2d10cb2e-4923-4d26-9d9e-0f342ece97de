package com.netflix.api.service.account;

import com.netflix.memberprice.protogen.PriceConsentInfo;

public class APIPriceConsentInfoImpl implements APIPriceConsentInfo {
  private final PriceConsentInfo priceConsentInfo;

  APIPriceConsentInfoImpl(PriceConsentInfo priceConsentInfo) {
    this.priceConsentInfo = priceConsentInfo;
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.plan_id"
   */
  @Override
  public Long getPlanId() {
    return this.priceConsentInfo.getPlanId();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.price_tier"
   */
  @Override
  public String getPriceTier() {
    return this.priceConsentInfo.getPriceTier();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.currency"
   */
  @Override
  public String getCurrency() {
    return this.priceConsentInfo.getCurrency();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.price_formatted"
   */
  @Override
  public String getPriceFormatted() {
    return this.priceConsentInfo.getPriceFormatted();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.consented_date"
   */
  @Override
  public Long getConsentedDate() {
    return this.priceConsentInfo.getBoxedConsentedDate();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.notification_start_date"
   */
  @Override
  public Long getNotificationStartDate() {
    return this.priceConsentInfo.getBoxedNotificationStartDate();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.extended_gf_date"
   */
  @Override
  public Long getExtendedGfDate() {
    return this.priceConsentInfo.getBoxedExtendedGfDate();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.enforce_price_change_until_date"
   */
  @Override
  public Long getEnforcePriceChangeUntilDate() {
    return this.priceConsentInfo.getBoxedEnforcePriceChangeUntilDate();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.auto_consent_after_date"
   */
  @Override
  public Long getAutoConsentAfterDate() {
    return this.priceConsentInfo.getBoxedAutoConsentAfterDate();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.auto_consent_notification_date"
   */
  @Override
  public Long getAutoConsentNotificationDate() {
    return this.priceConsentInfo.getBoxedAutoConsentNotificationDate();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "pricechange.proto#PriceConsentInfo.status"
   */
  @Override
  public APIConsentStatus getStatus() {
    PriceConsentInfo.ConsentStatus consentStatus = this.priceConsentInfo.getStatus();
    if (consentStatus == null) {
      return null;
    }
    if (consentStatus == PriceConsentInfo.ConsentStatus.UNRECOGNIZED) {
      return APIConsentStatus.UNRECOGNIZED;
    } else {
      try {
        return Enum.valueOf(APIConsentStatus.class, consentStatus.name());
      } catch (IllegalArgumentException e) {
        return APIConsentStatus.UNRECOGNIZED;
      }
    }
  }
}
