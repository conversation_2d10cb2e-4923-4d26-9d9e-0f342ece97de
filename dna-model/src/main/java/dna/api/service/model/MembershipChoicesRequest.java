package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class MembershipChoicesRequest {
  private String context;

  private String locale;

  private String nglVersion;

  private String getPromoData;

  private String filterType;

  private Boolean shouldOverrideForCS;

  /**
   * Context passed from clients to understand narrow use case of why product choices was requested
   */
  private String loggingContext;

  private MembershipStreamQuality qualityFilter;

  public String getContext() {
    return context;
  }

  public String getLocale() {
    return locale;
  }

  public String getNglVersion() {
    return nglVersion;
  }

  public String getGetPromoData() {
    return getPromoData;
  }

  public String getFilterType() {
    return filterType;
  }

  public Boolean getShouldOverrideForCS() {
    return shouldOverrideForCS;
  }

  public String getLoggingContext() {
    return loggingContext;
  }

  public MembershipStreamQuality getQualityFilter() {
    return qualityFilter;
  }

  public MembershipChoicesRequest setContext(String context) {
    this.context = context;
    return this;
  }

  public MembershipChoicesRequest setLocale(String locale) {
    this.locale = locale;
    return this;
  }

  public MembershipChoicesRequest setNglVersion(String nglVersion) {
    this.nglVersion = nglVersion;
    return this;
  }

  public MembershipChoicesRequest setGetPromoData(String getPromoData) {
    this.getPromoData = getPromoData;
    return this;
  }

  public MembershipChoicesRequest setFilterType(String filterType) {
    this.filterType = filterType;
    return this;
  }

  public MembershipChoicesRequest setShouldOverrideForCS(Boolean shouldOverrideForCS) {
    this.shouldOverrideForCS = shouldOverrideForCS;
    return this;
  }

  public MembershipChoicesRequest setLoggingContext(String loggingContext) {
    this.loggingContext = loggingContext;
    return this;
  }

  public MembershipChoicesRequest setQualityFilter(MembershipStreamQuality qualityFilter) {
    this.qualityFilter = qualityFilter;
    return this;
  }
}
