package com.netflix.api.platform.http;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.WriteListener;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.Writer;
import java.util.concurrent.atomic.LongAdder;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Wrapper to allow capturing values that were set during the request/response loop. */
public class APIHttpServletResponse extends HttpServletResponseWrapper {
  private final LongAdder capturedCountUpdater = new LongAdder();
  private static final Property<Boolean> REQUEST_CONTENT_LENGTH_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.contentLengthFilter.enabled", Boolean.class)
          .orElse(true);
  private static final Property<Boolean> PROPAGATE_OUTPUTSTREAM_CLOSE =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.httpservletresponse.propagate.outputstream.close", Boolean.class)
          .orElse(false);
  private static final Property<Boolean> PROPAGATE_OUTPUTSTREAM_FLUSH =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.httpservletresponse.propagate.outputstream.flush", Boolean.class)
          .orElse(false);
  private static final Property<Boolean> PROPAGATE_WRITER_CLOSE =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.httpservletresponse.propagate.writer.close", Boolean.class)
          .orElse(false);
  private static final Property<Boolean> PROPAGATE_WRITER_FLUSH =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.httpservletresponse.propagate.writer.flush", Boolean.class)
          .orElse(false);
  private static final Logger logger = LoggerFactory.getLogger(APIHttpServletResponse.class);

  public APIHttpServletResponse(ServletResponse actual) {
    super((HttpServletResponse) actual);
  }

  /**
   * @return the number of bytes written to the response
   *     <p><i>Note: this will only a value if the capturedContentLength is set for this
   *     instantiation, otherwise this will always return 0</i>.
   */
  public long getContentLength() {
    return capturedCountUpdater.longValue();
  }

  @Override
  public ServletOutputStream getOutputStream() throws IOException {
    return new APIServletOutputStream(getResponse().getOutputStream());
  }

  private final class APIServletOutputStream extends ServletOutputStream {
    private final ServletOutputStream actualStream;

    private APIServletOutputStream(ServletOutputStream actualStream) {
      this.actualStream = actualStream;
    }

    @Override
    public void write(int b) throws IOException {
      if (REQUEST_CONTENT_LENGTH_ENABLED.get()) {
        capturedCountUpdater.increment();
      }
      actualStream.write(b);
    }

    @Override
    public void write(@NotNull byte[] b) throws IOException {
      if (REQUEST_CONTENT_LENGTH_ENABLED.get()) {
        capturedCountUpdater.add(b.length);
      }
      actualStream.write(b);
    }

    @Override
    public void write(@NotNull byte[] b, int off, int len) throws IOException {
      if (REQUEST_CONTENT_LENGTH_ENABLED.get()) {
        capturedCountUpdater.add(len);
      }
      actualStream.write(b, off, len);
    }

    @Override
    public void flush() throws IOException {
      if (logger.isDebugEnabled()) {
        logger.debug("Calling flush", new Exception());
      }
      if (PROPAGATE_OUTPUTSTREAM_FLUSH.get()) {
        actualStream.flush();
      }
    }

    @Override
    public void close() throws IOException {
      if (logger.isDebugEnabled()) {
        logger.debug("Calling close", new Exception());
      }
      if (PROPAGATE_OUTPUTSTREAM_CLOSE.get()) {
        actualStream.close();
      }
    }

    @Override
    public boolean isReady() {
      return actualStream.isReady();
    }

    @Override
    public void setWriteListener(WriteListener writeListener) {
      actualStream.setWriteListener(writeListener);
    }
  }

  @Override
  public PrintWriter getWriter() throws IOException {
    return new PrintWriter(new APIWriter(getResponse().getWriter()));
  }

  private final class APIWriter extends Writer {
    private final PrintWriter actualWriter;

    private APIWriter(PrintWriter actualWriter) {
      this.actualWriter = actualWriter;
    }

    @Override
    public void write(@NotNull char[] cbuf, int off, int len) {
      if (REQUEST_CONTENT_LENGTH_ENABLED.get()) {
        capturedCountUpdater.add(len);
      }
      actualWriter.write(cbuf, off, len);
    }

    @Override
    public void flush() {
      if (logger.isDebugEnabled()) {
        logger.debug("Calling flush", new Exception());
      }
      if (PROPAGATE_WRITER_FLUSH.get()) {
        actualWriter.flush();
      }
    }

    @Override
    public void close() {
      if (logger.isDebugEnabled()) {
        logger.debug("Calling close", new Exception());
      }
      if (PROPAGATE_WRITER_CLOSE.get()) {
        actualWriter.close();
      }
    }
  }
}
