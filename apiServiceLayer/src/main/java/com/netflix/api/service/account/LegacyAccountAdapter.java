package com.netflix.api.service.account;

import static com.netflix.memberprice.adapter.MemberpriceRequestAdapter.getUpdateProductChoiceMapRequest;
import static com.netflix.memberprice.adapter.MemberpriceRequestAdapter.setRequestParams;

import com.google.common.primitives.Longs;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.archaius.api.Property;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.memberprice.adapter.MemberpriceResponseAdapter;
import com.netflix.memberprice.adapter.ProductChoiceFilters;
import com.netflix.memberprice.protogen.GetProductChoicesRequest;
import com.netflix.memberprice.protogen.GetProductChoicesResponse;
import com.netflix.memberprice.protogen.MemberChoiceServiceGrpc.MemberChoiceServiceStub;
import com.netflix.partnersub.consors.protogen.PromotionEligibilityRequest;
import com.netflix.partnersub.consors.protogen.PromotionEligibilityResponse;
import com.netflix.partnersub.consors.protogen.StbServiceGrpc.StbServiceStub;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import java.util.HashMap;
import java.util.Map;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
public class LegacyAccountAdapter {

  private static final String USER_ID_PLACEHOLDER_REGEXP = "\\$USER_ID";
  private static final String ACCOUNT_OWNER_ID_PLACEHOLDER_REGEXP = "\\$ACCOUNT_OWNER_ID";
  private static final Property<Boolean> performUserIdMapping =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.accountManagement.performUserIdMapping", Boolean.class)
          .orElse(true); // turn off only if there's an issue; this is low-risk
  private static final Property<Boolean> ENABLE_MANTIS =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.legacyaccountadapter.mantis.enable", Boolean.class)
          .orElse(true);

  private final MemberChoiceServiceStub memberChoiceServiceStub;
  private final StbServiceStub stbServiceStub;

  @Autowired
  public LegacyAccountAdapter(
      @GrpcSpringClient("memberprice") MemberChoiceServiceStub memberChoiceServiceStub,
      @GrpcSpringClient("consors") StbServiceStub stbServiceStub) {
    this.memberChoiceServiceStub = memberChoiceServiceStub;
    this.stbServiceStub = stbServiceStub;
  }

  private static String replacePii(String value) {
    if (performUserIdMapping.get() && value != null) {
      APIUser user = APIRequest.getCurrentRequest().getUser();
      if (user != null) {
        var customerId = String.valueOf(APIUserUtil.getCustomerId(user));
        var ownerId = String.valueOf(APIUserUtil.getAccountOwnerId(user));
        value = value.replace(USER_ID_PLACEHOLDER_REGEXP, customerId);
        value = value.replace(ACCOUNT_OWNER_ID_PLACEHOLDER_REGEXP, ownerId);
      }
    }
    return value;
  }

  public Observable<Map<String, Object>> getProductChoiceMap(
      long customerId, Map<String, String> requestMap) {
    GetProductChoicesRequest.Builder request =
        GetProductChoicesRequest.newBuilder().setCustomerId(customerId);
    if (requestMap != null) {
      request.putAllRequestMap(setRequestParams(requestMap));
    }
    request.putRequestMap("version", ProductChoiceFilters.API_VERSION_REQUEST_MAP);
    return RxObservable.defer(memberChoiceServiceStub::getChoices, request.build())
        .onErrorReturn(e -> GetProductChoicesResponse.getDefaultInstance())
        .map(MemberpriceResponseAdapter::getResponseMap);
  }

  public Observable<Map<String, Object>> updateProductChoiceMap(
      long customerId, Map<String, String> updateMap) {
    return RxObservable.defer(
            memberChoiceServiceStub::updateChoice,
            getUpdateProductChoiceMapRequest(customerId, updateMap))
        .map(MemberpriceResponseAdapter::getMapFromUpdateChoiceResponse);
  }

  public Observable<Map<String, Object>> promoEligibility(Map<String, Object> args) {
    final PromotionEligibilityRequest.Builder builder =
        PromotionEligibilityRequest.newBuilder().setEsn((String) args.get("device_esn"));
    final Object customerId = args.get("customer_id");
    if (customerId instanceof String cid) {
      Long parsed = Longs.tryParse(replacePii(cid));
      if (parsed != null) {
        builder.setCustomerId(parsed);
      }
    }
    final Object partnerToken = args.get("partner_token");
    if (partnerToken instanceof Boolean b) {
      builder.setPartnerToken(Boolean.toString(b));
    } else if (partnerToken instanceof String token) {
      builder.setPartnerToken(token);
    }
    PromotionEligibilityRequest request = builder.build();
    if (ENABLE_MANTIS.get()) {
      MantisPublishContext.getCurrent().add("promoEligibilityRequest", request.toString());
    }
    return RxObservable.defer(stbServiceStub::promotionEligibility, request)
        .map(LegacyAccountAdapter::convertPromoResponse);
  }

  @NotNull
  private static HashMap<String, Object> convertPromoResponse(
      PromotionEligibilityResponse response) {
    final HashMap<String, Object> map = HashMap.newHashMap(3);
    response.getOptionalIsEligible().ifPresent(b -> map.put("eligible", b));
    response.getOptionalErrorCode().ifPresent(s -> map.put("errorCode", s));
    response.getOptionalIsNioEligible().ifPresent(b -> map.put("nioEligible", b));
    if (ENABLE_MANTIS.get()) {
      MantisPublishContext.getCurrent().add("promoEligibilityResponse", response.toString());
      MantisPublishContext.getCurrent().add("promoEligibilityResponseMap", map.toString());
    }
    return map;
  }
}
