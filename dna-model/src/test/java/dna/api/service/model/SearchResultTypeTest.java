package dna.api.service.model;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import com.netflix.search.service.SearchResultType;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

class SearchResultTypeTest {

  @ParameterizedTest
  @EnumSource(SearchResultType.class)
  void enumsShouldAllExistInDna(SearchResultType value) {
    assertDoesNotThrow(() -> SearchResultType.valueOf(value.name()));
  }
}
