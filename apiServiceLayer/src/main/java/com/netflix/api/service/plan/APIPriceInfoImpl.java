package com.netflix.api.service.plan;

import com.netflix.api.platform.deprecated.DeprecatedMethodTracker;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIPriceInfo;
import java.util.Collections;
import java.util.Date;

public class APIPriceInfoImpl implements APIPriceInfo {

  private final APIAccountPlanInfo info;

  public static APIPriceInfo defaultInstance() {
    return new APIPriceInfoImpl(new APIAccountPlanInfo(null, null));
  }

  public APIPriceInfoImpl(APIAccountPlanInfo info) {
    this.info = info;
  }

  @Override
  public String getFormattedPrice() {
    return info.getFormattedCurrentPrice();
  }

  @Override
  @Deprecated
  public Date getExpiryDate() {
    DeprecatedMethodTracker.incrementDeprecatedMetrics(
        APIPriceInfoImpl.class.getName(),
        "getExpiryDate",
        Collections.emptyList(),
        APIRequest.getCurrentRequest().getRequestContext().getEndpointPath());
    return null;
  }
}
