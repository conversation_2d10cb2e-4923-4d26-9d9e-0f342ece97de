package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class Game implements GraphData {
  public Integer androidMinMemoryGb;

  public Integer androidMinSdkVersion;

  public Integer androidNumProcessors;

  public String androidPackageName;

  public Integer androidPackageSizeInMb;

  public String androidPlayStoreUrl;

  public String controllerInfo;

  public String copyrights;

  public String developer;

  public String deviceCapabilityRequirement;

  public String deviceCompatibility;

  public Boolean hasChat;

  public Boolean hasControllerSupport;

  public Long iosAppTunesId;

  public String iosAppStoreId;

  public String iosBundleId;

  public Integer iosBundleSizeInMb;

  public String iosMinVersionStr;

  public Boolean isMerchable;

  public Boolean isPlayable;

  public Boolean isPreReleasePinProtected;

  public Integer maximumPlayers;

  public Integer minimumPlayers;

  public Atom<List<String>> modes;

  public Atom<List<String>> allModes;

  public String numberOfPlayers;

  public String orientation;

  public Atom<List<String>> supportedLanguages;

  public Boolean supportsOfflineMode;

  public String version;

  public String versionNotes;

  public String urlScheme;

  public String connectivityRequirement;

  public Boolean isCloud;

  public GameType gameType;

  public Map<String, Atom<List<Tag>>> tagList;

  public SubGame subGame;

  public Genre primaryGenre;

  public Integer getAndroidMinMemoryGb() {
    return androidMinMemoryGb;
  }

  public Integer getAndroidMinSdkVersion() {
    return androidMinSdkVersion;
  }

  public Integer getAndroidNumProcessors() {
    return androidNumProcessors;
  }

  public String getAndroidPackageName() {
    return androidPackageName;
  }

  public Integer getAndroidPackageSizeInMb() {
    return androidPackageSizeInMb;
  }

  public String getAndroidPlayStoreUrl() {
    return androidPlayStoreUrl;
  }

  public String getControllerInfo() {
    return controllerInfo;
  }

  public String getCopyrights() {
    return copyrights;
  }

  public String getDeveloper() {
    return developer;
  }

  public String getDeviceCapabilityRequirement() {
    return deviceCapabilityRequirement;
  }

  public String getDeviceCompatibility() {
    return deviceCompatibility;
  }

  public Boolean getHasChat() {
    return hasChat;
  }

  public Boolean getHasControllerSupport() {
    return hasControllerSupport;
  }

  public Long getIosAppTunesId() {
    return iosAppTunesId;
  }

  public String getIosAppStoreId() {
    return iosAppStoreId;
  }

  public String getIosBundleId() {
    return iosBundleId;
  }

  public Integer getIosBundleSizeInMb() {
    return iosBundleSizeInMb;
  }

  public String getIosMinVersionStr() {
    return iosMinVersionStr;
  }

  public Boolean getIsMerchable() {
    return isMerchable;
  }

  public Boolean getIsPlayable() {
    return isPlayable;
  }

  public Boolean getIsPreReleasePinProtected() {
    return isPreReleasePinProtected;
  }

  public Integer getMaximumPlayers() {
    return maximumPlayers;
  }

  public Integer getMinimumPlayers() {
    return minimumPlayers;
  }

  public Atom<List<String>> getModes() {
    return modes;
  }

  public Atom<List<String>> getAllModes() {
    return allModes;
  }

  public String getNumberOfPlayers() {
    return numberOfPlayers;
  }

  public String getOrientation() {
    return orientation;
  }

  public Atom<List<String>> getSupportedLanguages() {
    return supportedLanguages;
  }

  public Boolean getSupportsOfflineMode() {
    return supportsOfflineMode;
  }

  public String getVersion() {
    return version;
  }

  public String getVersionNotes() {
    return versionNotes;
  }

  public String getUrlScheme() {
    return urlScheme;
  }

  public String getConnectivityRequirement() {
    return connectivityRequirement;
  }

  public Boolean getIsCloud() {
    return isCloud;
  }

  public GameType getGameType() {
    return gameType;
  }

  public Game setAndroidMinMemoryGb(Integer androidMinMemoryGb) {
    this.androidMinMemoryGb = androidMinMemoryGb;
    return this;
  }

  public Game setAndroidMinSdkVersion(Integer androidMinSdkVersion) {
    this.androidMinSdkVersion = androidMinSdkVersion;
    return this;
  }

  public Game setAndroidNumProcessors(Integer androidNumProcessors) {
    this.androidNumProcessors = androidNumProcessors;
    return this;
  }

  public Game setAndroidPackageName(String androidPackageName) {
    this.androidPackageName = androidPackageName;
    return this;
  }

  public Game setAndroidPackageSizeInMb(Integer androidPackageSizeInMb) {
    this.androidPackageSizeInMb = androidPackageSizeInMb;
    return this;
  }

  public Game setAndroidPlayStoreUrl(String androidPlayStoreUrl) {
    this.androidPlayStoreUrl = androidPlayStoreUrl;
    return this;
  }

  public Game setControllerInfo(String controllerInfo) {
    this.controllerInfo = controllerInfo;
    return this;
  }

  public Game setCopyrights(String copyrights) {
    this.copyrights = copyrights;
    return this;
  }

  public Game setDeveloper(String developer) {
    this.developer = developer;
    return this;
  }

  public Game setDeviceCapabilityRequirement(String deviceCapabilityRequirement) {
    this.deviceCapabilityRequirement = deviceCapabilityRequirement;
    return this;
  }

  public Game setDeviceCompatibility(String deviceCompatibility) {
    this.deviceCompatibility = deviceCompatibility;
    return this;
  }

  public Game setHasChat(Boolean hasChat) {
    this.hasChat = hasChat;
    return this;
  }

  public Game setHasControllerSupport(Boolean hasControllerSupport) {
    this.hasControllerSupport = hasControllerSupport;
    return this;
  }

  public Game setIosAppTunesId(Long iosAppTunesId) {
    this.iosAppTunesId = iosAppTunesId;
    return this;
  }

  public Game setIosAppStoreId(String iosAppStoreId) {
    this.iosAppStoreId = iosAppStoreId;
    return this;
  }

  public Game setIosBundleId(String iosBundleId) {
    this.iosBundleId = iosBundleId;
    return this;
  }

  public Game setIosBundleSizeInMb(Integer iosBundleSizeInMb) {
    this.iosBundleSizeInMb = iosBundleSizeInMb;
    return this;
  }

  public Game setIosMinVersionStr(String iosMinVersionStr) {
    this.iosMinVersionStr = iosMinVersionStr;
    return this;
  }

  public Game setIsMerchable(Boolean isMerchable) {
    this.isMerchable = isMerchable;
    return this;
  }

  public Game setIsPlayable(Boolean isPlayable) {
    this.isPlayable = isPlayable;
    return this;
  }

  public Game setIsPreReleasePinProtected(Boolean isPreReleasePinProtected) {
    this.isPreReleasePinProtected = isPreReleasePinProtected;
    return this;
  }

  public Game setMaximumPlayers(Integer maximumPlayers) {
    this.maximumPlayers = maximumPlayers;
    return this;
  }

  public Game setMinimumPlayers(Integer minimumPlayers) {
    this.minimumPlayers = minimumPlayers;
    return this;
  }

  public Game setModes(Atom<List<String>> modes) {
    this.modes = modes;
    return this;
  }

  public Game setAllModes(Atom<List<String>> allModes) {
    this.allModes = allModes;
    return this;
  }

  public Game setNumberOfPlayers(String numberOfPlayers) {
    this.numberOfPlayers = numberOfPlayers;
    return this;
  }

  public Game setOrientation(String orientation) {
    this.orientation = orientation;
    return this;
  }

  public Game setSupportedLanguages(Atom<List<String>> supportedLanguages) {
    this.supportedLanguages = supportedLanguages;
    return this;
  }

  public Game setSupportsOfflineMode(Boolean supportsOfflineMode) {
    this.supportsOfflineMode = supportsOfflineMode;
    return this;
  }

  public Game setVersion(String version) {
    this.version = version;
    return this;
  }

  public Game setVersionNotes(String versionNotes) {
    this.versionNotes = versionNotes;
    return this;
  }

  public Game setUrlScheme(String urlScheme) {
    this.urlScheme = urlScheme;
    return this;
  }

  public Game setConnectivityRequirement(String connectivityRequirement) {
    this.connectivityRequirement = connectivityRequirement;
    return this;
  }

  public Game setIsCloud(Boolean isCloud) {
    this.isCloud = isCloud;
    return this;
  }

  public Game setGameType(GameType gameType) {
    this.gameType = gameType;
    return this;
  }
}
