package dna.api.converters;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.netflix.nes.section.descriptor.protogen.GenreSectionDescriptor;
import com.netflix.nes.section.descriptor.protogen.SectionDescriptor;
import dna.api.converters.nes.NesSectionDescriptorInputConverter;
import dna.api.service.model.NesGenreInput;
import dna.api.service.model.NesSectionDescriptorInput;
import dna.api.service.model.NesSectionDescriptorQualifierUnion;
import dna.api.service.model.NesSectionType;
import java.util.EnumSet;
import org.junit.jupiter.api.Test;

class NesSectionDescriptorInputConverterTest {

  @Test
  void convertSectionDescriptorInputNull() {
    final SectionDescriptor SectionDescriptor =
        NesSectionDescriptorInputConverter.convertSectionDescriptor(null);
    assertNotNull(SectionDescriptor);
  }

  @Test
  void convertSectionDescriptorInputEmpty() {
    NesSectionDescriptorInput nes = new NesSectionDescriptorInput();
    final SectionDescriptor SectionDescriptor =
        NesSectionDescriptorInputConverter.convertSectionDescriptor(nes);
    assertNotNull(SectionDescriptor);
  }

  @Test
  void convertSectionDescriptorInput() {
    NesSectionDescriptorInput nes =
        new NesSectionDescriptorInput()
            .setQualifier(
                new NesSectionDescriptorQualifierUnion().setGenre(new NesGenreInput().setId(123L)))
            .setSectionType(NesSectionType.Genre);
    final SectionDescriptor sectionDescriptor =
        NesSectionDescriptorInputConverter.convertSectionDescriptor(nes);
    assertNotNull(sectionDescriptor);
    assertTrue(sectionDescriptor.hasGenreSectionDescriptor());
    final GenreSectionDescriptor genreSectionDescriptor =
        sectionDescriptor.getGenreSectionDescriptor();
    assertEquals(123L, genreSectionDescriptor.getGenre().getId());
  }

  @Test
  void implementsAllSectionTypes() {
    for (final NesSectionType SectionType : EnumSet.allOf(NesSectionType.class)) {
      assertTrue(
          NesSectionDescriptorInputConverter.SECTION_DESCRIPTOR_BUILDERS.containsKey(SectionType));
    }
  }
}
