package com.netflix.api.service.device;

import java.util.Collection;
import java.util.Map;
import rx.Observable;

/**
 * A service that handles device interactions such as activating/deactivating devices or accessing
 * information
 *
 * <p><img
 * src="https://confluence.netflix.com/download/attachments/52216234/deviceManagement.class.png"
 * alt="APIDeviceManagementService class diagram">
 */
public interface APIDeviceManagementService {

  /**
   * Asynchronously returns a {@code Map} whose keys are the requested property names mapped to
   * their corresponding values.
   *
   * @param propertyNames requested property names
   * @param nrdAppVersion Value of the query param 'nrdapp_version' from a device appboot request.
   * @return a {@code Map} whose keys are the requested device property names mapped to their
   *     corresponding values.
   */
  Observable<Map<String, String>> getCurrentDeviceProperties(
      Collection<String> propertyNames, String nrdAppVersion);
}
