package com.netflix.api.service.vps;

/**
 * Interface representing request header P<PERSON><PERSON><PERSON> {@link APIVoiceService} can be used to create a
 * request header
 */
public interface APIVoiceRequestHeader {
  APIVoiceCommandType getCommandDirective();

  String getPayloadVersion();

  void setCommandDirective(final APIVoiceCommandType commandDirective);

  void setPayloadVersion(final String payloadVersion);
}
