package com.netflix.api.service.plan;

import com.netflix.api.service.account.APIPlanDeviceType;
import java.util.Set;

public interface PlanData {

  long getPlanId();

  boolean isSDOnly();

  boolean isHDEnabled();

  boolean isUHDEnabled();

  boolean isMobileOnly();

  int getMaxStreams();

  int getMaxDownloadDevices();

  @Deprecated
  boolean isFreemium();

  Set<APIPlanDeviceType> getAllowedDeviceTypes();
}
