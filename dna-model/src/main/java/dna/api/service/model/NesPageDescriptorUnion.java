package dna.api.service.model;

import com.netflix.nes.page.descriptor.protogen.AssistiveAudioPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.ClipsPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.ComingSoonPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.DownloadsPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.GenrePageDescriptor;
import com.netflix.nes.page.descriptor.protogen.HomePageDescriptor;
import com.netflix.nes.page.descriptor.protogen.MobileCollectionsPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.NewReleasesPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.PartnerPreviewPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.PlaylistPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.PreappPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.RecentlyAddedPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.RecentlyWatchedPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.TurboNavigationPageDescriptor;
import com.netflix.nes.page.descriptor.protogen.VideoMessagingPageDescriptor;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NesPageDescriptorUnion {
  private AssistiveAudioPageDescriptor assistiveAudioPageDescriptor;

  private ClipsPageDescriptor clipsPageDescriptor;

  private ComingSoonPageDescriptor comingSoonPageDescriptor;

  private DownloadsPageDescriptor downloadsPageDescriptor;

  private GenrePageDescriptor genrePageDescriptor;

  private HomePageDescriptor homePageDescriptor;

  private MobileCollectionsPageDescriptor mobileCollectionsPageDescriptor;

  private NewReleasesPageDescriptor newReleasesPageDescriptor;

  private PartnerPreviewPageDescriptor partnerPreviewPageDescriptor;

  private PlaylistPageDescriptor playlistPageDescriptor;

  private PreappPageDescriptor preappPageDescriptor;

  private RecentlyAddedPageDescriptor recentlyAddedPageDescriptor;

  private RecentlyWatchedPageDescriptor recentlyWatchedPageDescriptor;

  private TurboNavigationPageDescriptor turboNavigationPageDescriptor;

  private VideoMessagingPageDescriptor videoMessagingPageDescriptor;

  public AssistiveAudioPageDescriptor getAssistiveAudioPageDescriptor() {
    return assistiveAudioPageDescriptor;
  }

  public ClipsPageDescriptor getClipsPageDescriptor() {
    return clipsPageDescriptor;
  }

  public ComingSoonPageDescriptor getComingSoonPageDescriptor() {
    return comingSoonPageDescriptor;
  }

  public DownloadsPageDescriptor getDownloadsPageDescriptor() {
    return downloadsPageDescriptor;
  }

  public GenrePageDescriptor getGenrePageDescriptor() {
    return genrePageDescriptor;
  }

  public HomePageDescriptor getHomePageDescriptor() {
    return homePageDescriptor;
  }

  public MobileCollectionsPageDescriptor getMobileCollectionsPageDescriptor() {
    return mobileCollectionsPageDescriptor;
  }

  public NewReleasesPageDescriptor getNewReleasesPageDescriptor() {
    return newReleasesPageDescriptor;
  }

  public PartnerPreviewPageDescriptor getPartnerPreviewPageDescriptor() {
    return partnerPreviewPageDescriptor;
  }

  public PlaylistPageDescriptor getPlaylistPageDescriptor() {
    return playlistPageDescriptor;
  }

  public PreappPageDescriptor getPreappPageDescriptor() {
    return preappPageDescriptor;
  }

  public RecentlyAddedPageDescriptor getRecentlyAddedPageDescriptor() {
    return recentlyAddedPageDescriptor;
  }

  public RecentlyWatchedPageDescriptor getRecentlyWatchedPageDescriptor() {
    return recentlyWatchedPageDescriptor;
  }

  public TurboNavigationPageDescriptor getTurboNavigationPageDescriptor() {
    return turboNavigationPageDescriptor;
  }

  public VideoMessagingPageDescriptor getVideoMessagingPageDescriptor() {
    return videoMessagingPageDescriptor;
  }

  public NesPageDescriptorUnion setAssistiveAudioPageDescriptor(
      AssistiveAudioPageDescriptor assistiveAudioPageDescriptor) {
    this.assistiveAudioPageDescriptor = assistiveAudioPageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setClipsPageDescriptor(ClipsPageDescriptor clipsPageDescriptor) {
    this.clipsPageDescriptor = clipsPageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setComingSoonPageDescriptor(
      ComingSoonPageDescriptor comingSoonPageDescriptor) {
    this.comingSoonPageDescriptor = comingSoonPageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setDownloadsPageDescriptor(
      DownloadsPageDescriptor downloadsPageDescriptor) {
    this.downloadsPageDescriptor = downloadsPageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setGenrePageDescriptor(GenrePageDescriptor genrePageDescriptor) {
    this.genrePageDescriptor = genrePageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setHomePageDescriptor(HomePageDescriptor homePageDescriptor) {
    this.homePageDescriptor = homePageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setMobileCollectionsPageDescriptor(
      MobileCollectionsPageDescriptor mobileCollectionsPageDescriptor) {
    this.mobileCollectionsPageDescriptor = mobileCollectionsPageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setNewReleasesPageDescriptor(
      NewReleasesPageDescriptor newReleasesPageDescriptor) {
    this.newReleasesPageDescriptor = newReleasesPageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setPartnerPreviewPageDescriptor(
      PartnerPreviewPageDescriptor partnerPreviewPageDescriptor) {
    this.partnerPreviewPageDescriptor = partnerPreviewPageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setPlaylistPageDescriptor(
      PlaylistPageDescriptor playlistPageDescriptor) {
    this.playlistPageDescriptor = playlistPageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setPreappPageDescriptor(PreappPageDescriptor preappPageDescriptor) {
    this.preappPageDescriptor = preappPageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setRecentlyAddedPageDescriptor(
      RecentlyAddedPageDescriptor recentlyAddedPageDescriptor) {
    this.recentlyAddedPageDescriptor = recentlyAddedPageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setRecentlyWatchedPageDescriptor(
      RecentlyWatchedPageDescriptor recentlyWatchedPageDescriptor) {
    this.recentlyWatchedPageDescriptor = recentlyWatchedPageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setTurboNavigationPageDescriptor(
      TurboNavigationPageDescriptor turboNavigationPageDescriptor) {
    this.turboNavigationPageDescriptor = turboNavigationPageDescriptor;
    return this;
  }

  public NesPageDescriptorUnion setVideoMessagingPageDescriptor(
      VideoMessagingPageDescriptor videoMessagingPageDescriptor) {
    this.videoMessagingPageDescriptor = videoMessagingPageDescriptor;
    return this;
  }
}
