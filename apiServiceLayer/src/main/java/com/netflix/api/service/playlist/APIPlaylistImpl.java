package com.netflix.api.service.playlist;

import jakarta.annotation.Nonnull;
import java.util.List;
import java.util.Objects;

public class APIPlaylistImpl implements APIPlaylist {
  private final List<APIPlaylistItem> videos;

  public APIPlaylistImpl(@Nonnull List<APIPlaylistItem> playlistItems) {
    this.videos = Objects.requireNonNull(playlistItems, "List of videos must not be null");
  }

  @Override
  public List<APIPlaylistItem> getPlaylistItems() {
    return videos;
  }
}
