package com.netflix.api.service;

import com.netflix.type.ISOCountry;
import java.util.Map;
import rx.Observable;

/**
 * Wrapper around ISOCountry. FIXME FIXME FIXME rewrite to describe the function of the interface
 * from the perspective of those who will be using it, as opposed to the justification for the
 * interface from the perspective of those who implemented it.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIUser.class.png">
 */
public interface APICountry {

  /**
   * Two letter country code - e.g., {@code US}, {@code CA}
   *
   * @return Two letter country code - e.g., {@code US}, {@code CA}. Not null.
   */
  String getId();

  /**
   * Status of Netflix support in this country. The string value returned is the {@code name()} of
   * one of the {@code APICountry.CountryStatus} enum names. This is a string to allow loose
   * coupling between API and underlying dependencies. Instead of verifying aga, they can do:
   *
   * <p>
   *
   * <pre><code>
   * if(getStatus().equals(APICountry.CountryStatus.ALLOW.name())) {
   *     // do something
   * }
   * </code></pre>
   *
   * @deprecated after global launch we only serve requests from allowed countries, zuul will block
   *     requests from countries that are not allowed
   * @return status of Netflix support in this country - as one of {@code
   *     APICountry.CountryStatus.name()}
   */
  @Deprecated
  String getStatus();

  /**
   * Gets the default {@link APILocale} for this country. If no country-specific language has been
   * specified, the global default language is used to construct the locale.
   *
   * @return the default {@link APILocale} for this country
   */
  APILocale getDefaultLocale();

  /**
   * Gets the display name for this country based on the locale parameter
   *
   * @return a string representation of this country, for instance {@code APICountry} of {@code
   *     en-US} and locale of {@code en} this will be "United States" while the locale of {@code
   *     fr-FR} would be "Etats-Unis"
   */
  String getDisplayName(String locale);

  /**
   * Returns a {@link Observable} of map whose keys are social service names (e.g., "FACEBOOK") and
   * values are URLs for the Netflix brand (e.g., "https://twitter.com/NetflixAsia") corresponding
   * to this country (and potentially request context locale).
   *
   * @return an {@link Observable} of map of service names to URLs.
   */
  Observable<Map<String, String>> getBrandLinks();

  ISOCountry getISOCountry();
}
