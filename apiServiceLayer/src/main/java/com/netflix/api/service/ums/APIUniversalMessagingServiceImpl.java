package com.netflix.api.service.ums;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.util.Timestamps;
import com.netflix.api.grpc.GrpcCallHelpers.RxSingle;
import com.netflix.api.platform.util.CounterNames;
import com.netflix.api.service.APIObservableBehaviorImpl;
import com.netflix.api.service.APIObservableBuilderImpl;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.cmp.common.MessageCategory;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Registry;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.ums.common.alerts.request.AddUmsAlertFeedbackRequest;
import com.netflix.ums.common.model.UmsResponseCode;
import com.netflix.ums.common.requestresponse.FilterNotificationsByIdsResponse;
import com.netflix.ums.common.requestresponse.MarkMessagesReadRequest;
import com.netflix.zipurl.protogen.GetUrlRequest;
import com.netflix.zipurl.protogen.UrlResponse;
import com.netflix.zipurl.protogen.ZipurlServiceGrpc.ZipurlServiceStub;
import java.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Single;
import rx.Subscriber;

@Component
public class APIUniversalMessagingServiceImpl implements APIUniversalMessagingService {
  private static final String PATH_TAG_NAME = "path";
  private static final String NULL_ESN_NAME_COUNTER_NAME = CounterNames.API_PREFIX + "ums-null-esn";

  private final Id nullEsnMetric;
  private final Registry registry;
  private final ZipurlServiceStub zipurlServiceStub;
  private final APIAsyncAddAlertFeedbackTelaImpl apiAsyncAddAlertFeedbackTelaImpl;
  private final APIAsyncGetCustomerAlertsTelaImpl apiAsyncGetCustomerAlertsTelaImpl;
  private final APIAsyncGetSatisfactionSurveysTelaImpl apiAsyncGetSatisfactionSurveysTelaImpl;
  private final APIAsyncMarkMessagesReadTelaImpl apiAsyncMarkMessagesReadTelaImpl;
  private final APIAsyncFilterNotificationsTelaFactory apiAsyncFilterNotificationsTelaFactory;
  public static final ObjectMapper UMS_MAPPER = initializeObjectMapper();

  private static ObjectMapper initializeObjectMapper() {
    JsonFactory jsonFactory = new JsonFactory();
    ObjectMapper objectMapper = new ObjectMapper(jsonFactory);
    objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    return objectMapper;
  }

  @Autowired
  public APIUniversalMessagingServiceImpl(
      final Registry registry,
      @GrpcSpringClient("zipurl") ZipurlServiceStub zipurlServiceStub,
      final APIAsyncAddAlertFeedbackTelaImpl apiAsyncAddAlertFeedbackTelaImpl,
      final APIAsyncGetCustomerAlertsTelaImpl apiAsyncGetCustomerAlertsTelaImpl,
      final APIAsyncGetSatisfactionSurveysTelaImpl apiAsyncGetSatisfactionSurveysTelaImpl,
      final APIAsyncMarkMessagesReadTelaImpl apiAsyncMarkMessagesReadTelaImpl,
      final APIAsyncFilterNotificationsTelaFactory apiAsyncFilterNotificationsTelaFactory) {
    this.zipurlServiceStub = zipurlServiceStub;
    this.registry = registry;
    this.apiAsyncAddAlertFeedbackTelaImpl = apiAsyncAddAlertFeedbackTelaImpl;
    this.apiAsyncGetCustomerAlertsTelaImpl = apiAsyncGetCustomerAlertsTelaImpl;
    this.apiAsyncGetSatisfactionSurveysTelaImpl = apiAsyncGetSatisfactionSurveysTelaImpl;
    this.apiAsyncMarkMessagesReadTelaImpl = apiAsyncMarkMessagesReadTelaImpl;
    this.apiAsyncFilterNotificationsTelaFactory = apiAsyncFilterNotificationsTelaFactory;

    nullEsnMetric = registry.createId(NULL_ESN_NAME_COUNTER_NAME);
  }

  @Override
  public Single<APIUrlResponse> lookupUrlByPattern(String shortUrlPattern) {
    return RxSingle.defer(
            zipurlServiceStub::getUrl,
            GetUrlRequest.newBuilder().setShortUrlPattern(shortUrlPattern).build())
        .onErrorReturn(
            ignore ->
                UrlResponse.newBuilder()
                    .setLongUrl("https://www.netflix.com/browse")
                    .setShortUrl("fallback")
                    .setExpiryTs(Timestamps.fromMillis(0L))
                    .build())
        .map(
            response ->
                new APIUrlResponseImpl(
                    response.getLongUrl(),
                    response.getShortUrl(),
                    Timestamps.toMillis(response.getExpiryTs())));
  }

  @Override
  public UniversalMessagingServiceMarkReadBuilder markMessagesRead(APIUser user) {
    return new UniversalMessagingServiceMarkReadBuilderImpl(user);
  }

  @Override
  public APICustomerAlertsRequest buildCustomerAlertsRequest(APIUser user) {
    return new APICustomerAlertsRequestImpl(user);
  }

  @Override
  public APIAddUmsAlertFeedbackRequest buildAddUmsAlertFeedbackRequest(APIUser user) {
    return new APIAddUmsAlertFeedbackRequestImpl(user);
  }

  @Override
  public Observable<Map<String, Object>> addAlertFeedback(
      APIAddUmsAlertFeedbackRequest addUmsAlertFeedbackRequest) {
    if (addUmsAlertFeedbackRequest == null) {
      return Observable.error(new IllegalArgumentException("Request cannot be null"));
    }

    AddUmsAlertFeedbackRequest request =
        ((APIAddUmsAlertFeedbackRequestImpl) addUmsAlertFeedbackRequest).build();

    return apiAsyncAddAlertFeedbackTelaImpl.addAlertFeedback(request);
  }

  @Override
  public Observable<Map<String, Object>> getCustomerAlerts(final APICustomerAlertsRequest request) {
    if (request == null) {
      return Observable.error(new IllegalArgumentException("Request cannot be null"));
    }

    var req = ((APICustomerAlertsRequestImpl) request).build();
    return apiAsyncGetCustomerAlertsTelaImpl.getAlerts(req).flatMap(Observable::from);
  }

  @Override
  public APIFilterNotifications filterNotifications(APIFilterNotificationMessagesRequest request) {
    APIFilterNotificationMessagesRequestImpl castReq =
        (APIFilterNotificationMessagesRequestImpl) request;

    return apiAsyncFilterNotificationsTelaFactory.create(castReq);
  }

  @Override
  public APIFilterNotificationMessagesRequest buildFilterNotificationMessagesRequest(APIUser user) {
    return new APIFilterNotificationMessagesRequestImpl(user);
  }

  @Override
  public APISatisfactionSurveysRequest buildSatisfactionSurveysRequest(APIUser user) {
    return new APISatisfactionSurveysRequestImpl(user);
  }

  @Override
  public Observable<APISatisfactionSurveysResponse> getSatisfactionSurveys(
      APISatisfactionSurveysRequest request) {

    return apiAsyncGetSatisfactionSurveysTelaImpl.getSurveys(request);
  }

  public Observable<APIFilterNotificationsByIdsResponse> filterNotificationsByIds() {
    // short circuit this call, it is not used anymore. This supports legacy devices.
    FilterNotificationsByIdsResponse emptyResponse = new FilterNotificationsByIdsResponse();
    emptyResponse.setMsgs(new ArrayList<>()); // empty list
    emptyResponse.setETag("d751713988987e9331980363e24189ce"); // eTag for empty list
    emptyResponse.setResponseCode(UmsResponseCode.SUCCESS);
    return Observable.just(new APIFilterNotificationsByIdsResponseImpl(emptyResponse));
  }

  private class UniversalMessagingServiceMarkReadBehavior extends APIObservableBehaviorImpl<Long> {
    private final APIUser user;
    private String esn;
    private long timestamp = -1L;
    private APIMessage.APIMessageCategory category = APIMessage.APIMessageCategory.INVALID;
    private List<String> messageIds = null;

    public UniversalMessagingServiceMarkReadBehavior(final APIUser user) {
      this.user = user;
    }

    public void withTimestamp(long timestamp) {
      this.timestamp = timestamp;
    }

    public void withMessageCategory(APIMessage.APIMessageCategory category) {
      this.category = category;
    }

    public void withEsn(String esn) {
      this.esn = esn;
    }

    public void forIdList(final List<String> ids) {
      this.messageIds = ids;
    }

    @Override
    protected void internalSubscribe(Subscriber<? super Long> subscriber) {
      try {
        MarkMessagesReadRequest request = buildMarkMessagesReadRequest();

        apiAsyncMarkMessagesReadTelaImpl.markMessagesRead(request).subscribe(subscriber);

      } catch (Exception ex) {
        subscriber.onError(ex);
      }
    }

    private MarkMessagesReadRequest buildMarkMessagesReadRequest() {
      if (timestamp > -1 && messageIds != null) {
        throw new RuntimeException("Cannot use both timestamp and message id list");
      }

      MarkMessagesReadRequest.Builder builder =
          MarkMessagesReadRequest.getBuilder()
              .withCustomerId(APIUserUtil.getCustomerId(user))
              .withMessageCategory(null)
              .withIdList(messageIds);

      if (esn == null) {
        esn = APIRequest.getCurrentRequest().getRequestContext().getESN();
      }

      if (esn == null) {
        try {
          registry
              .counter(
                  nullEsnMetric.withTag(
                      PATH_TAG_NAME,
                      APIRequest.getCurrentRequest().getServletRequest().getPathInfo()))
              .increment();
        } catch (Exception ignored) {
          // Ignored
        }
      } else {
        builder.withEsn(esn);
      }

      if (category != null) {
        builder.withMessageCategory(MessageCategory.valueOf(category.name()));
      }

      if (timestamp > -1) {
        builder.withReferenceTS(timestamp);
      }

      return builder.build();
    }
  }

  public class UniversalMessagingServiceMarkReadBuilderImpl
      extends APIObservableBuilderImpl<Long, UniversalMessagingServiceMarkReadBehavior>
      implements UniversalMessagingServiceMarkReadBuilder {
    public UniversalMessagingServiceMarkReadBuilderImpl(final APIUser user) {
      super(new UniversalMessagingServiceMarkReadBehavior(user));
    }

    @Override
    public UniversalMessagingServiceMarkReadBuilder withTimestamp(long timestamp) {
      getBehavior().withTimestamp(timestamp);
      return this;
    }

    @Override
    public UniversalMessagingServiceMarkReadBuilder withCategory(
        APIMessage.APIMessageCategory category) {
      getBehavior().withMessageCategory(category);
      return this;
    }

    @Deprecated
    @Override
    public UniversalMessagingServiceMarkReadBuilder withMessageCategory(
        APIMessage.APIMessageCategory category) {
      return withCategory(category);
    }

    @Override
    public UniversalMessagingServiceMarkReadBuilder forIdList(final List<String> ids) {
      getBehavior().forIdList(ids);
      return this;
    }

    @Override
    public UniversalMessagingServiceMarkReadBuilder withESN(String esn) {
      getBehavior().withEsn(esn);
      return this;
    }
  }
}
