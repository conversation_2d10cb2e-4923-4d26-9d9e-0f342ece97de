package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class AllocateRequestContext {
  private String currentClientType;

  private String currentUIArea;

  private String currentUIAction;

  private String currentUIVariant;

  private String appVersion;

  private String deviceESN;

  private String country;

  private String asn;

  public String getCurrentClientType() {
    return currentClientType;
  }

  public String getCurrentUIArea() {
    return currentUIArea;
  }

  public String getCurrentUIAction() {
    return currentUIAction;
  }

  public String getCurrentUIVariant() {
    return currentUIVariant;
  }

  public String getAppVersion() {
    return appVersion;
  }

  public String getDeviceESN() {
    return deviceESN;
  }

  public String getCountry() {
    return country;
  }

  public String getAsn() {
    return asn;
  }

  public AllocateRequestContext setCurrentClientType(String currentClientType) {
    this.currentClientType = currentClientType;
    return this;
  }

  public AllocateRequestContext setCurrentUIArea(String currentUIArea) {
    this.currentUIArea = currentUIArea;
    return this;
  }

  public AllocateRequestContext setCurrentUIAction(String currentUIAction) {
    this.currentUIAction = currentUIAction;
    return this;
  }

  public AllocateRequestContext setCurrentUIVariant(String currentUIVariant) {
    this.currentUIVariant = currentUIVariant;
    return this;
  }

  public AllocateRequestContext setAppVersion(String appVersion) {
    this.appVersion = appVersion;
    return this;
  }

  public AllocateRequestContext setDeviceESN(String deviceESN) {
    this.deviceESN = deviceESN;
    return this;
  }

  public AllocateRequestContext setCountry(String country) {
    this.country = country;
    return this;
  }

  public AllocateRequestContext setAsn(String asn) {
    this.asn = asn;
    return this;
  }
}
