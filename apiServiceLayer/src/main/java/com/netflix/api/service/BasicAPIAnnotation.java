package com.netflix.api.service;

import jakarta.annotation.Nonnull;

public record BasicAPIAnnotation<N, V>(N name, V value) implements APIAnnotation<N, V> {

  @Override
  @SuppressWarnings("unchecked")
  public Class<V> getType() {
    return (Class<V>) value.getClass();
  }

  @Override
  public @Nonnull String toString() {
    return "BasicAPIAnnotation{" + "name=" + name + ", value=" + value + '}';
  }
}
