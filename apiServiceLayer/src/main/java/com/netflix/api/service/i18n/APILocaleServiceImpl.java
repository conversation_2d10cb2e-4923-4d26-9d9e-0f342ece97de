package com.netflix.api.service.i18n;

import static com.google.common.collect.Sets.difference;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.grpc.GrpcCallHelpers.Future;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.APICountry;
import com.netflix.api.service.APILocale;
import com.netflix.api.service.APILocaleImpl;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.util.ServiceUtils;
import com.netflix.archaius.api.Property;
import com.netflix.dloc.protogen.DlocServiceGrpc.DlocServiceStub;
import com.netflix.dloc.protogen.GetProductPreferredLanguagesRequest;
import com.netflix.dloc.protogen.GetProductSupportedLanguagesRequest;
import com.netflix.dloc.protogen.GetSecondaryLanguagesRequest;
import com.netflix.dloc.protogen.LocaleList;
import com.netflix.dloc.protogen.ResolveLocaleRequest;
import com.netflix.i18n.NFLocale;
import com.netflix.launch.common.Country;
import com.netflix.launch.common.LaunchConfiguration;
import com.netflix.launch.common.NamespaceLaunchConfiguration;
import com.netflix.launch.common.ObjectMapperFactory;
import com.netflix.launch.decoder.AbstractDecoder;
import com.netflix.launch.decoder.Decoder;
import com.netflix.launch.decoder.DecodingException;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.type.ISOCountry;
import com.netflix.type.NFCountry;
import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
public class APILocaleServiceImpl implements APILocaleService {
  private static final Logger logger = LoggerFactory.getLogger(APILocaleServiceImpl.class);

  private static final String PRODUCT_TYPE_CATALOG = "catalog";

  private static final String OCTOBER_SKY_PRODUCT_PREFERRED_LANGUAGES_NAMESPACE =
      "cross-platform-ui";
  private static final String OCTOBER_SKY_PRODUCT_PREFERRED_LANGUAGES_KEY_PREFIX =
      "productPreferredLanguages";

  private static final String OCTOBER_SKY_PRODUCT_PREFERRED_LANGUAGES_KEY =
      OCTOBER_SKY_PRODUCT_PREFERRED_LANGUAGES_KEY_PREFIX + "_" + PRODUCT_TYPE_CATALOG;

  // decodes json string array into List<NFLocale>
  private static final Decoder<List<NFLocale>> OCTOBER_SKY_DECODER =
      new AbstractDecoder<>() {

        private final ObjectMapper om = ObjectMapperFactory.get();

        @Override
        protected List<NFLocale> decodeInternal(String value) throws DecodingException {
          try {
            List<String> readValue = om.readValue(value, new TypeReference<>() {});
            return readValue.stream().map(NFLocale::findInstance).toList();
          } catch (Exception ex) {
            throw new DecodingException(ex);
          }
        }
      };

  private final LaunchConfiguration launchConfiguration;
  private final LocaleAdapter localeAdapter;
  private final DlocServiceStub dlocServiceStub;
  private final Gateway gateway;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public APILocaleServiceImpl(
      com.netflix.archaius.api.PropertyRepository pr,
      LaunchConfiguration launchConfiguration,
      LocaleAdapter localeAdapter,
      @GrpcSpringClient("dloc") DlocServiceStub dlocServiceStub) {
    this.launchConfiguration = launchConfiguration;
    this.localeAdapter = localeAdapter;
    this.dlocServiceStub = dlocServiceStub;
    this.gateway = new GatewayProd();

    ulmImageUrlTemplate =
        pr.get("api.ulm.image.url.template", String.class)
            .orElse(
                "http://cdn-0.nflximg.com/ffe/profiles/unsupportedLanguage/%s/%s/unsupported.png");
    ulmImageUrlHttpsTemplate =
        pr.get("api.ulm.image.url.https.template", String.class)
            .orElse(
                "https://assets.nflxext.com/ffe/profiles/unsupportedLanguage/%s/%s/unsupported.png");
    ulmEnabled = pr.get("api.ulm.enabled", Boolean.class).orElse(true);
    ulmLogic2CountryBlacklist =
        pr.getList("api.ulm.logic2.country.blacklist", String.class).orElse(List.of("IL"));
    ulmLanguageCountryToLocaleDirectoryMapping =
        pr.getMap("api.ulm.locale.directory.mapping", String.class, String.class)
            .orElse(DEFAULT_MAP);
  }

  public CompletionStage<List<String>> resolveLocales(APILocaleOptions options) {
    return ServiceUtils.getAccountProfileRemoteForCurrentUser()
        .thenApply(
            profile -> {
              ResolveLocaleRequest.Builder builder = buildLocaleResolveRequest(options);
              if (profile != null) {
                builder.setProfile(profile);
              }
              return builder.build();
            })
        .thenCompose(
            resolveLocaleRequest ->
                Future.call(dlocServiceStub::resolveLocale, resolveLocaleRequest))
        .thenApply(LocaleList::getLocaleList);
  }

  private static ResolveLocaleRequest.Builder buildLocaleResolveRequest(APILocaleOptions options) {
    ResolveLocaleRequest.Builder builder = ResolveLocaleRequest.newBuilder();
    if (options.getDevicePreferredLanguages() != null) {
      builder.addAllDevicePreferredLanguages(options.getDevicePreferredLanguages());
    }
    if (options.getDeviceSupportedFontGroups() != null) {
      builder.addAllDeviceSupportedFontGroups(options.getDeviceSupportedFontGroups());
    }
    if (options.getDeviceSupportedLanguages() != null) {
      builder.addAllDeviceSupportedLanguages(options.getDeviceSupportedLanguages());
    }
    if (options.getSupportedUILanguages() != null) {
      builder.addAllSupportedUILanguages(options.getSupportedUILanguages());
    }
    ISOCountry c = RequestContextWrapper.get().getCountry();
    ISOCountry country = c == null ? NFCountry.US : c;
    builder.setCountry(country.getId());
    if (options.getPrivateUseSubtag() != null) {
      builder.setPrivateUseSubtag(options.getPrivateUseSubtag());
    }
    if (options.getUseNRMLanguagePreferences() != null) {
      builder.setUseNRMLanguagePreferences(options.getUseNRMLanguagePreferences());
    }
    if (options.getPseudoLocalizationLanguages() != null) {
      builder.putAllPseudoLocalizationLanguages(options.getPseudoLocalizationLanguages());
    }
    if (options.getUserPreferredLanguages() != null) {
      builder.addAllUserPreferredLanguages(options.getUserPreferredLanguages());
    }
    return builder;
  }

  @Override
  @Deprecated
  public Observable<List<APILocale>> getMatchingLanguageTags(
      @Nullable final List<String> devicePreferredLanguages,
      @Nullable final List<String> deviceSupportedLanguages,
      boolean useNonRegisteredMemberLanguagePreferences) {

    return Observable.fromCallable(
        () -> {
          try {
            return localeAdapter
                .getMatchingLocales(
                    devicePreferredLanguages,
                    deviceSupportedLanguages,
                    useNonRegisteredMemberLanguagePreferences)
                .stream()
                .map(APILocaleServiceImpl::toAPILocale)
                .toList();
          } catch (Exception e) {
            throw new APIServiceRuntimeException(
                "Error getting matching language tags for user", e);
          }
        });
  }

  private static APILocale toAPILocale(NFLocale nfLocale) {
    return nfLocale == null ? null : new APILocaleImpl(nfLocale);
  }

  @Override
  public Observable<List<APILocale>> getSecondaryLanguages() {
    return RxObservable.defer(
            dlocServiceStub::getSecondaryLanguages,
            GetSecondaryLanguagesRequest.newBuilder().build())
        .map(
            reply ->
                reply.getLocaleList().stream()
                    .map(NFLocale::findInstance)
                    .map(APILocaleServiceImpl::toAPILocale)
                    .toList());
  }

  @Override
  @Deprecated
  public Observable<List<APILocale>> getProductPreferredLanguageTags(
      final List<APILocale> matchingLanguageTags, String productType) {

    // validate inputs
    Preconditions.checkNotNull(matchingLanguageTags);
    Preconditions.checkNotNull(productType);

    return RxObservable.defer(
            dlocServiceStub::getProductPreferredLanguages,
            GetProductPreferredLanguagesRequest.newBuilder()
                .setProductType(buildProductType(productType))
                .setLanguages(
                    LocaleList.newBuilder()
                        .addAllLocale(matchingLanguageTags.stream().map(APILocale::getId).toList())
                        .build())
                .build())
        .map(
            reply ->
                reply.getLocaleList().stream()
                    .map(NFLocale::findInstance)
                    .map(APILocaleServiceImpl::toAPILocale)
                    .toList());
  }

  @Override
  public Observable<List<APILocale>> getProductSupportedLanguages(String productType) {
    // validate inputs
    Preconditions.checkNotNull(productType);
    return RxObservable.defer(
            dlocServiceStub::getProductSupportedLanguages,
            GetProductSupportedLanguagesRequest.newBuilder()
                .setProductType(buildProductType(productType))
                .setCountry(CurrentRequestContext.get().getCountry().getId())
                .build())
        .map(
            reply ->
                reply.getLocaleList().stream()
                    .map(NFLocale::findInstance)
                    .map(APILocaleServiceImpl::toAPILocale)
                    .toList());
  }

  private com.netflix.dloc.protogen.ProductType buildProductType(String productType) {
    if (PRODUCT_TYPE_CATALOG.equals(productType)) {
      return com.netflix.dloc.protogen.ProductType.CATALOG;
    }
    logger.info("unrecognized product type: {}", productType);
    return com.netflix.dloc.protogen.ProductType.UNKNOWN;
  }

  @Override
  @Deprecated
  public Observable<List<APILocale>> getNonMemberPreferredLanguageTags() {
    return _getNonMemberPreferredLanguageTags(
        APIRequest.getCurrentRequest().getRequestContext().getCountry());
  }

  @VisibleForTesting
  Observable<List<APILocale>> _getNonMemberPreferredLanguageTags(APICountry country) {
    return Observable.just(getSupportedLanguageTags(country))
        .onErrorResumeNext(
            e ->
                Observable.error(
                    new APIServiceRuntimeException(
                        "Error getting non-member preferred language tags for country "
                            + country.getId(),
                        e)));
  }

  private List<APILocale> getSupportedLanguageTags(APICountry country) {
    List<NFLocale> supportedLanguages = getSupportedLanguageTagsFromOctoberSky(country);
    if (supportedLanguages == null) {
      supportedLanguages = Collections.singletonList(NFLocale.ENGLISH);
    }
    List<NFLocale> filteredList =
        NFLocale.filterLanguageTags(
            supportedLanguages, NFLocale.getAvailableLanguagePreferenceLocales());
    List<APILocale> list = new ArrayList<>(filteredList.size());
    for (NFLocale nfLocale : filteredList) {
      list.add(toAPILocale(nfLocale));
    }
    return list;
  }

  private interface Gateway {

    List<NFLocale> getSupportedLocalesForCurrentCountry();
  }

  private static class GatewayProd implements Gateway {

    @Override
    public List<NFLocale> getSupportedLocalesForCurrentCountry() {
      try {
        return NFLocale.getSupportedLocales(RequestContextWrapper.get().getCountry());
      } catch (Exception e) {
        return List.of();
      }
    }
  }

  private List<NFLocale> getSupportedLanguageTagsFromOctoberSky(APICountry country) {
    String namespace = OCTOBER_SKY_PRODUCT_PREFERRED_LANGUAGES_NAMESPACE;
    List<NFLocale> defaultValue = gateway.getSupportedLocalesForCurrentCountry();
    NamespaceLaunchConfiguration namespaceLaunchConfig;
    try {
      namespaceLaunchConfig = launchConfiguration.forNamespace(namespace);
    } catch (Exception e) {
      logger.error("Namespace not found in October Sky: {}", namespace);
      return defaultValue;
    }
    Country osCountry;
    try {
      osCountry = namespaceLaunchConfig.getCountry(country.getId());
    } catch (IllegalArgumentException e) {
      logger.warn("Country not found in October Sky: {}", country.getId());
      return defaultValue;
    }
    if (osCountry == null) {
      // something bad happened in OS; this should never be null
      logger.warn("null country returned by October Sky for {}", country.getId());
      return defaultValue;
    }
    return osCountry.fetchProperty(
        OCTOBER_SKY_PRODUCT_PREFERRED_LANGUAGES_KEY, OCTOBER_SKY_DECODER, defaultValue);
  }

  private final Property<String> ulmImageUrlTemplate;
  private final Property<String> ulmImageUrlHttpsTemplate;
  private final Property<Boolean> ulmEnabled;
  private final Property<List<String>> ulmLogic2CountryBlacklist;
  private static final Map DEFAULT_MAP =
      ImmutableMap.builder()
          .put("ko", "ko")
          .put("ar", "ar")
          .put("zh-TW", "zh-Hant")
          .put("zh-HK", "zh-Hant")
          .put("zh-MO", "zh-Hant")
          .put("zh", "zh-Hans")
          .put("ar-IL", "")
          .put("th", "th")
          .put("ro", "ro")
          .put("he", "he")
          .put("el", "el")
          .put("id", "id")
          .build();
  private final Property<Map<String, String>> ulmLanguageCountryToLocaleDirectoryMapping;

  @Override
  @Deprecated
  public Observable<String> getUnsupportedLanguageMessagingImageUrl(
      String size,
      @Nullable final List<String> devicePreferredLanguageTags,
      @Nullable final List<String> deviceSupportedLanguages,
      @Nullable final List<String> deviceSupportedFontGroups,
      boolean secure) {
    DynamicCounter.increment(
        "api.localeservice.unsupportedlanguage.call", "secure", Boolean.toString(secure));
    return Observable.defer(
        () -> {
          String ulmLocale = null;
          if (ulmEnabled.get()
              && devicePreferredLanguageTags != null
              && deviceSupportedLanguages != null) {
            List<NFLocale> devicePreferredLanguageTagsNFLocales =
                devicePreferredLanguageTags.stream().map(NFLocale::createFromString).toList();
            List<NFLocale> deviceSupportedLanguagesNFLocales =
                deviceSupportedLanguages.stream()
                    .map(NFLocale::createFromString)
                    .collect(Collectors.toList());
            // DNA-2994 - use deviceSupportedFontGroups to augment deviceSupportedLanguages
            if (deviceSupportedFontGroups != null) {
              deviceSupportedLanguagesNFLocales.addAll(
                  localeAdapter.getLocalesForScripts(deviceSupportedFontGroups));
            }
            List<NFLocale> productPreferredNFLocales =
                getSupportedLanguageTagsFromOctoberSky(
                    APIRequest.getCurrentRequest().getRequestContext().getCountry());
            Set<String> productPreferredLanguageSet =
                productPreferredNFLocales.stream()
                    .map(NFLocale::getLanguage)
                    .collect(Collectors.toSet());
            Set<String> deviceSupportedLanguagesSet =
                deviceSupportedLanguagesNFLocales.stream()
                    .map(NFLocale::getLanguage)
                    .collect(Collectors.toSet());
            Set<String> deviceUnsupportedLanguageSet =
                difference(productPreferredLanguageSet, deviceSupportedLanguagesSet);
            if (!deviceUnsupportedLanguageSet.isEmpty()) {
              // logic #1
              List<NFLocale> preferredButUnsupportedLanguages =
                  NFLocale.filterLanguageTags(
                      devicePreferredLanguageTagsNFLocales,
                      deviceUnsupportedLanguageSet.stream()
                          .map(NFLocale::createFromString)
                          .toList());
              if (!preferredButUnsupportedLanguages.isEmpty()) {
                // show message in this language
                ulmLocale = preferredButUnsupportedLanguages.getFirst().getId();
              }

              // logic #2 : see method javadoc - in summary, even if unsupported locale is not in
              // user's
              // preferences,
              // but it is our catalog's preferred language, display the message
              String requestCountry =
                  RequestContextWrapper.get().getCountry() == null
                      ? "UNKNOWN"
                      : RequestContextWrapper.get().getCountry().getId();
              if (ulmLocale == null && !ulmLogic2CountryBlacklist.get().contains(requestCountry)) {
                for (NFLocale locale : productPreferredNFLocales) {
                  if (deviceUnsupportedLanguageSet.contains(locale.getId())) {
                    ulmLocale = locale.getId();
                    break;
                  }
                }
              }
            }
          }

          if (ulmLocale != null && ulmLocale.length() > 1) {
            // just get the language part from locale and append - and the country. nflx weirdness
            // :)
            ulmLocale =
                ulmLocale
                    .substring(0, 2)
                    .concat("-")
                    .concat(RequestContextWrapper.get().getCountry().getId());
          }
          String ulmImageLocaleDirectory =
              getUnsupportedLanguageMessagingLocaleDirectory(ulmLocale);
          String urlTemplate = secure ? ulmImageUrlHttpsTemplate.get() : ulmImageUrlTemplate.get();
          return Strings.isNullOrEmpty(ulmImageLocaleDirectory)
              ? Observable.empty()
              : Observable.just(String.format(urlTemplate, ulmImageLocaleDirectory, size));
        });
  }

  String getUnsupportedLanguageMessagingLocaleDirectory(String locale) {
    if (locale == null) return null;
    Map<String, String> mapping = ulmLanguageCountryToLocaleDirectoryMapping.get();
    return mapping.get(locale) != null ? mapping.get(locale) : mapping.get(locale.substring(0, 2));
  }

  @Override
  @Deprecated
  public Observable<List<APILocale>> getAvailableLanguagePreferenceLocales() {
    return Observable.fromCallable(
        () -> {
          List<NFLocale> source = NFLocale.getAvailableLanguagePreferenceLocales();
          return source == null
              ? Collections.emptyList()
              : source.stream().map(APILocaleServiceImpl::toAPILocale).toList();
        });
  }
}
