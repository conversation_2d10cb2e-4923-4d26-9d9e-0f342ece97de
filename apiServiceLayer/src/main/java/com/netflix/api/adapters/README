Service layer dependencies have several cases where dependency injection must be achieved through creating injectable adapters.
Four flavors are:
1. Factory method wrappers that provide interfaces to create-object-and-execute-method - example: ADC commands.
2. Singleton wrappers around static methods - example: TypeManager.findObject(id, class).
3. Adapters that adapt immutables (with private constructors) - example: PresentationVideo.
4. Adapters that adapt final methods into non-final methods to allow mocking - example: .

If you find yourself writing a bunch of adapters so that you can wire them into your classes for unit tests, look here instead.
If you don't find it here (and think that others may find a use for your adapter), will you add it here, pretty please?
And if you write an adapter interface, can you implement a mock adapter along with your production one, pretty please?

These are intended to be purely Adapters (no other logic), to help with dependency injection, to help in turn with unit tests.
So, no need for unit tests :).
Add extra logic only if you think that all clients will NEED to use the logic, i.e., no service layer client will ever want
to use the adaptee without the logic you put in. And if you add logic, add unit tests.
