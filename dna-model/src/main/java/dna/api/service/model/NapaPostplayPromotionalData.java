package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaPostplayPromotionalData {
  private NapaPostplayRecommendationChannel channel;

  private NapaPostplayRecommendationTactic tactic;

  private SearchPageEntityId promotedVideoId;

  private NapaPostplayImpressionData impressionData;

  private String theme;

  public NapaPostplayRecommendationChannel getChannel() {
    return channel;
  }

  public NapaPostplayRecommendationTactic getTactic() {
    return tactic;
  }

  public SearchPageEntityId getPromotedVideoId() {
    return promotedVideoId;
  }

  public NapaPostplayImpressionData getImpressionData() {
    return impressionData;
  }

  public String getTheme() {
    return theme;
  }

  public NapaPostplayPromotionalData setChannel(NapaPostplayRecommendationChannel channel) {
    this.channel = channel;
    return this;
  }

  public NapaPostplayPromotionalData setTactic(NapaPostplayRecommendationTactic tactic) {
    this.tactic = tactic;
    return this;
  }

  public NapaPostplayPromotionalData setPromotedVideoId(SearchPageEntityId promotedVideoId) {
    this.promotedVideoId = promotedVideoId;
    return this;
  }

  public NapaPostplayPromotionalData setImpressionData(NapaPostplayImpressionData impressionData) {
    this.impressionData = impressionData;
    return this;
  }

  public NapaPostplayPromotionalData setTheme(String theme) {
    this.theme = theme;
    return this;
  }
}
