#!/bin/bash

# source this to pick up NETFLIX_* env variables.
test -f /etc/profile.d/netflix_environment.sh && source /etc/profile.d/netflix_environment.sh

## update in running kernel

# increase stack depth
sysctl -w kernel.perf_event_max_stack=1024

# inc-2313
sysctl -w net.ipv4.neigh.default.gc_thresh1=512
sysctl -w net.ipv4.neigh.default.gc_thresh2=2048
sysctl -w net.ipv4.neigh.default.gc_thresh3=4096

# file system tunables
sysctl -w fs.aio-max-nr=1048576

# max connections and backlog
sysctl -w net.core.somaxconn=65536
sysctl -w net.ipv4.tcp_max_syn_backlog=65536
sysctl -w net.core.netdev_max_backlog=5000
sysctl -w net.core.netdev_budget=600

# tcp memory
sysctl -w net.core.rmem_default=12582912
sysctl -w net.core.wmem_default=12582912
sysctl -w net.core.rmem_max=16777216
sysctl -w net.core.wmem_max=16777216
sysctl -w net.ipv4.tcp_wmem="8388608 12582912 16777216"
sysctl -w net.ipv4.tcp_rmem="8388608 12582912 16777216"

# udp memory
sysctl -w net.ipv4.udp_rmem_min=16384
sysctl -w net.ipv4.udp_wmem_min=16384

# extend ephemeral port range as wide as possible
sysctl -w net.ipv4.ip_local_port_range="10240 65535"

# tcp settings
sysctl -w net.ipv4.tcp_keepalive_time=15
sysctl -w net.ipv4.tcp_keepalive_intvl=5
sysctl -w net.ipv4.tcp_keepalive_probes=1
sysctl -w net.ipv4.tcp_fin_timeout=10
sysctl -w net.ipv4.tcp_slow_start_after_idle=0
sysctl -w net.ipv4.tcp_orphan_retries=0

# recommended for hosts with jumbo frames enabled
# sysctl -w net.ipv4.tcp_mtu_probing=1

# Allows network protocol processing to scale across multiple CPU
echo 32768 > /proc/sys/net/core/rps_sock_flow_entries
echo 32768 > /sys/class/net/eth0/queues/rx-0/rps_flow_cnt
echo f > /sys/class/net/eth0/queues/rx-0/rps_cpus

/sbin/sysctl -p
