package com.netflix.api.service.titlegroup.vxs;

import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import rx.Observable;
import rx.functions.Action1;
import rx.functions.Func1;

public class APIVXSTitleGroupCache {
  private final ConcurrentHashMap<Key<?, ?>, Object> cache = new ConcurrentHashMap<>(8);

  public <E, T, I, K extends Key<T, I>> EntrySet<T, I, K> getCacheEntries(
      Collection<E> entities, Function<E, K> keySupplier) {
    var cacheEntries = entities.stream().map(entity -> get((keySupplier.apply(entity)))).toList();
    return new EntrySet<>(cacheEntries);
  }

  public <T, K extends Key<T, ?>> Entry<T, K> get(K key) {
    Object value = cache.get(key);
    return new Entry<>(key, key.cast(value));
  }

  public <T> void put(Key<T, ?> key, T value) {
    if (value == null) return;
    cache.put(key, value);
  }

  public <T, K extends Key<T, ?>> Func1<Observable<T>, Observable<T>> cacheOnNext(
      Function<T, K> keySupplier) {
    return origin -> {
      AddCacheOnNextAction<T, K> onNextAction = new AddCacheOnNextAction<>(keySupplier);
      return origin.doOnNext(onNextAction);
    };
  }

  class AddCacheOnNextAction<T, K extends Key<T, ?>> implements Action1<T> {
    private final Function<T, K> keySupplier;

    private AddCacheOnNextAction(Function<T, K> keySupplier) {
      this.keySupplier = keySupplier;
    }

    @Override
    public void call(T t) {
      put(keySupplier.apply(t), t);
    }
  }

  abstract static class Key<T, K> {

    private final Class<T> type;
    private final K key;
    private final Object[] identity;

    protected Key(Class<T> type, K key, Object... identity) {
      this.type = type;
      this.identity = identity;
      this.key = key;
    }

    public T cast(Object object) {
      if (!type.isInstance(object)) return null;
      return type.cast(object);
    }

    public K getKey() {
      return key;
    }

    @Override
    public int hashCode() {
      final int prime = 31;
      int result = 1;
      result = prime * result + Arrays.hashCode(identity);
      result = prime * result + ((key == null) ? 0 : key.hashCode());
      result = prime * result + ((type == null) ? 0 : type.hashCode());
      return result;
    }

    @Override
    public boolean equals(Object obj) {
      if (this == obj) return true;
      if (obj == null) return false;
      if (getClass() != obj.getClass()) return false;
      @SuppressWarnings("rawtypes")
      Key other = (Key) obj;
      if (!Arrays.equals(identity, other.identity)) return false;
      if (key == null) {
        if (other.key != null) return false;
      } else if (!key.equals(other.key)) return false;
      if (type == null) {
        if (other.type != null) return false;
      } else if (!type.equals(other.type)) return false;
      return true;
    }
  }

  static class EntrySet<T, I, K extends Key<T, I>> {
    private final Collection<Entry<T, K>> entries;

    EntrySet(Collection<Entry<T, K>> entries) {
      this.entries = entries;
    }

    public Collection<I> missingKeyIds() {
      return entries.stream().filter(Entry::notFound).map(Entry::getKey).map(Key::getKey).toList();
    }

    public Collection<T> existingEntries() {
      return entries.stream().map(Entry::getValue).filter(Objects::nonNull).toList();
    }
  }

  static final class Entry<T, K extends Key<T, ?>> {
    private final K key;
    private final T value;

    private Entry(K key, T value) {
      this.key = key;
      this.value = value;
    }

    public K getKey() {
      return key;
    }

    public T getValue() {
      return value;
    }

    public boolean notFound() {
      return value == null;
    }
  }
}
