package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class EvidenceData {
  private String text;

  private Map<String, String> annotations;

  @Deprecated private BiteSizeEvidenceTypes biteSizeEvidence;

  private StaffPicksEvidenceTypes staffPicksEvidence;

  private SocialProofEvidenceTypes socialProofEvidence;

  public String getText() {
    return text;
  }

  public Map<String, String> getAnnotations() {
    return annotations;
  }

  @Deprecated
  public BiteSizeEvidenceTypes getBiteSizeEvidence() {
    return biteSizeEvidence;
  }

  public StaffPicksEvidenceTypes getStaffPicksEvidence() {
    return staffPicksEvidence;
  }

  public SocialProofEvidenceTypes getSocialProofEvidence() {
    return socialProofEvidence;
  }

  public EvidenceData setText(String text) {
    this.text = text;
    return this;
  }

  public EvidenceData setAnnotations(Map<String, String> annotations) {
    this.annotations = annotations;
    return this;
  }

  @Deprecated
  public EvidenceData setBiteSizeEvidence(BiteSizeEvidenceTypes biteSizeEvidence) {
    this.biteSizeEvidence = biteSizeEvidence;
    return this;
  }

  public EvidenceData setStaffPicksEvidence(StaffPicksEvidenceTypes staffPicksEvidence) {
    this.staffPicksEvidence = staffPicksEvidence;
    return this;
  }

  public EvidenceData setSocialProofEvidence(SocialProofEvidenceTypes socialProofEvidence) {
    this.socialProofEvidence = socialProofEvidence;
    return this;
  }
}
