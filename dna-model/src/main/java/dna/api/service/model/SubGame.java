package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SubGame implements GraphData {
  public String universalLinkUri;

  public String customAndroidStoreUri;

  public String customIOSStoreUri;

  public Integer parentHubGameId;

  public String parentHubGameTitle;

  public String getUniversalLinkUri() {
    return universalLinkUri;
  }

  public String getCustomAndroidStoreUri() {
    return customAndroidStoreUri;
  }

  public String getCustomIOSStoreUri() {
    return customIOSStoreUri;
  }

  public Integer getParentHubGameId() {
    return parentHubGameId;
  }

  public String getParentHubGameTitle() {
    return parentHubGameTitle;
  }

  public SubGame setUniversalLinkUri(String universalLinkUri) {
    this.universalLinkUri = universalLinkUri;
    return this;
  }

  public SubGame setCustomAndroidStoreUri(String customAndroidStoreUri) {
    this.customAndroidStoreUri = customAndroidStoreUri;
    return this;
  }

  public SubGame setCustomIOSStoreUri(String customIOSStoreUri) {
    this.customIOSStoreUri = customIOSStoreUri;
    return this;
  }

  public SubGame setParentHubGameId(Integer parentHubGameId) {
    this.parentHubGameId = parentHubGameId;
    return this;
  }

  public SubGame setParentHubGameTitle(String parentHubGameTitle) {
    this.parentHubGameTitle = parentHubGameTitle;
    return this;
  }
}
