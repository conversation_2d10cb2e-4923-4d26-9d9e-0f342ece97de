package com.netflix.api.service.video;

import java.util.Objects;

public class APIResolutionsImpl implements APIResolutions {

  private final boolean hd;
  private final boolean superHD;
  private final boolean ultraHD;

  private static final APIResolutions DEFAULT = new APIResolutionsImpl(false, false, false);

  public static APIResolutions empty() {
    return DEFAULT;
  }

  public APIResolutionsImpl(boolean hd, boolean superHD, boolean ultraHD) {
    this.hd = hd;
    this.superHD = superHD;
    this.ultraHD = ultraHD;
  }

  public static APIResolutions merge(APIResolutions first, APIResolutions second) {
    return new APIResolutionsImpl(
        first.isHD() && second.isHD(),
        first.isSuperHD() && second.isSuperHD(),
        first.isUltraHD() && second.isUltraHD());
  }

  @Override
  public boolean isHD() {
    return hd;
  }

  @Override
  public boolean isSuperHD() {
    return superHD;
  }

  @Override
  public boolean isUltraHD() {
    return ultraHD;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIResolutionsImpl)) {
      return false;
    }
    APIResolutionsImpl that = (APIResolutionsImpl) o;
    return hd == that.hd && isSuperHD() == that.isSuperHD() && isUltraHD() == that.isUltraHD();
  }

  @Override
  public int hashCode() {
    return Objects.hash(hd, isSuperHD(), isUltraHD());
  }

  @Override
  public String toString() {
    return "APIResolutions{" + "hd=" + hd + ", superHD=" + superHD + ", ultraHD=" + ultraHD + '}';
  }
}
