package com.netflix.api.service.uipersonalization;

public interface APIUIPersonalization {
  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Personalization.video_merch_disabled"
   */
  Boolean getVideoMerchDisabled();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Personalization.secondary_languages"
   */
  APIUIPersonalizationSecondaryLanguages getSecondaryLanguages();

  /**
   * 3: add next attribute here
   *
   * <p>Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Personalization.disabled_voting_titles"
   */
  APIUIPersonalizationDisabledVotingTitles getDisabledVotingTitles();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Personalization.hidden_modules"
   */
  APIUIPersonalizationHiddenModules getHiddenModules();
}
