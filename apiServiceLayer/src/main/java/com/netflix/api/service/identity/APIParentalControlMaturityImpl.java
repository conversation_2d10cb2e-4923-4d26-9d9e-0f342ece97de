package com.netflix.api.service.identity;

public class APIParentalControlMaturityImpl implements APIParentalControlMaturity {

  private final Integer maturityValue;
  private final String maturityLabel;
  private final String description;

  public APIParentalControlMaturityImpl(
      Integer maturityValue, String maturityLabel, String description) {
    this.maturityValue = maturityValue;
    this.maturityLabel = maturityLabel;
    this.description = description;
  }

  @Override
  public String getMaturityLabel() {
    return maturityLabel;
  }

  @Override
  public Integer getMaturityValue() {
    return maturityValue;
  }

  @Override
  public String getDescription() {
    return description;
  }
}
