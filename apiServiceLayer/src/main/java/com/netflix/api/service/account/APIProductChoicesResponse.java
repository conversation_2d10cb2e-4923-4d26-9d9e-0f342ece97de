package com.netflix.api.service.account;

import java.util.List;

public interface APIProductChoicesResponse {
  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.customer_id"
   */
  Long getCustomerId();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.choices"
   */
  List<APIProductChoice> getChoices();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.next_billing_date"
   */
  Long getNextBillingDate();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.last_plan_change_date"
   */
  Long getLastPlanChangeDate();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.billing_partner"
   */
  String getBillingPartner();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.is_upgrade_on_us"
   */
  Boolean getIsUpgradeOnUs();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.fallback"
   */
  Boolean getFallback();

  /**
   * Auto-generated from gRPC proto
   *
   * @see "productchoice.proto#GetProductChoicesResponse.upsellChoice"
   */
  APIProductChoice getUpsellChoice();
}
