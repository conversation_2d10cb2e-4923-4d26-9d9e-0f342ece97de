package com.netflix.api.startup;

import com.google.inject.Binding;
import com.google.inject.Key;
import com.google.inject.Scopes;
import com.google.inject.matcher.AbstractMatcher;
import com.google.inject.matcher.Matcher;
import com.google.inject.spi.ProvisionListener;
import com.netflix.governator.guice.lazy.LazySingleton;
import com.netflix.governator.guice.lazy.LazySingletonScope;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AsyncInitializationManager implements ProvisionListener {

  private static final Logger LOGGER = LoggerFactory.getLogger(AsyncInitializationManager.class);

  private record InitBinding(Key<?> key, CompletableFuture<Void> future) {}

  private final AtomicBoolean isStarted = new AtomicBoolean(false);
  private final AtomicReference<CompletableFuture<Void>> cachedFuture = new AtomicReference<>();

  private final ConcurrentLinkedQueue<InitBinding> asyncInitQueue = new ConcurrentLinkedQueue<>();

  public static Matcher<Binding<?>> matcher() {
    return new AbstractMatcher<>() {
      @Override
      public boolean matches(Binding<?> t) {
        if (!AsyncStartupInitialization.class.isAssignableFrom(
            t.getKey().getTypeLiteral().getRawType())) return false;
        return Scopes.isSingleton(t)
            || Scopes.isScoped(t, LazySingletonScope.get(), LazySingleton.class);
      }
    };
  }

  @Override
  public <T> void onProvision(ProvisionInvocation<T> provision) {
    T object = provision.provision();
    if (object instanceof AsyncStartupInitialization obj) {
      CompletableFuture<Void> monitorFuture = obj.initializationFuture();
      if (isStarted.get()) {
        // the injector is created load right away
        monitorFuture.join();
      } else {
        asyncInitQueue.add(new InitBinding(provision.getBinding().getKey(), monitorFuture));
      }
    }
  }

  private CompletableFuture<Void> waitForCompletion() {
    CompletableFuture<?>[] futures =
        asyncInitQueue.stream()
            .map(
                item ->
                    item.future.whenComplete(
                        (unused, th) -> {
                          if (th != null) {
                            LOGGER.error("Can't initialize key={}", item.key, th);
                          }
                        }))
            .toArray(CompletableFuture[]::new);

    LOGGER.info("Waiting for {} asynchronous task to complete", futures.length);

    return CompletableFuture.allOf(futures)
        .whenComplete(
            (unused, error) -> {
              if (error != null) {
                LOGGER.error("One or many asynchronous tasks failed", error);
              } else {
                LOGGER.info("All asynchronous tasks are completed ( {} )", futures.length);
              }
            });
  }

  public CompletableFuture<Void> get() {
    if (!isStarted.getAndSet(true)) {
      try {
        cachedFuture.set(waitForCompletion());
      } finally {
        asyncInitQueue.clear();
      }
    }
    return cachedFuture.get();
  }
}
