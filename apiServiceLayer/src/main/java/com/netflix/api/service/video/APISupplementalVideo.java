package com.netflix.api.service.video;

/**
 * This interface extends the {@link APIVideo} interface and provides the operations expected from a
 * "Movie" in the Netflix catalog.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.class.png"
 * alt="APIVideo and related interfaces">
 */
public interface APISupplementalVideo extends APIViewable {

  /**
   * A supplemental video attribute denoting its subtype. Known subtypes today are: "MOTION_VIDEO",
   * "MONTAGE_VIDEO", "THEMATIC", "GENERAL", "ICONIC".
   *
   * @return the subtype of the supplemental video
   */
  String getSubType();
}
