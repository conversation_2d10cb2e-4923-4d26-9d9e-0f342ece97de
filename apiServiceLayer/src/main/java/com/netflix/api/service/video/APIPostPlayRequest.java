package com.netflix.api.service.video;

import com.netflix.api.service.list.MapRequest;
import java.util.Set;

/**
 * Request encapsulating parameters needed to get post play recommendations for a video. The video
 * is a required parameter. Client can add additional optional parameters using the provided methods
 * that follow the {@code builder} pattern.
 *
 * <AUTHOR>
 */
public interface APIPostPlayRequest extends MapRequest<APIPostPlayRequest> {

  /** Enumeration of settings in the request that describe desired lolomo composition */
  enum ClientCapabilityOption {
    /** Include recommendations row in the post play lolomo */
    recommendations,
    /** Include the next episode row in the post play lolomo */
    watchNext,
    /** Include original trailer in the post play lolomo */
    originalPostPlay,
    /** Tell lolomo service that the client is capable of auto playing */
    autoPlay
  }

  /**
   * Return a set of {@link ClientCapabilityOption}s to apply for this request
   *
   * @return a set of {@link ClientCapabilityOption}s
   */
  Set<ClientCapabilityOption> getClientCapabilityOptions();

  /**
   * Return the rating score that user gave to this video
   *
   * @return the rating score of the video
   */
  Double getRating();

  /**
   * Return the maximum number of recommendations to request
   *
   * @return the maximum number of recommendations
   */
  Integer getLimit();

  /**
   * Return the portion of the video watched by the user
   *
   * @return the portion of the video watched
   */
  Double getPercentPlayed();

  /**
   * Set debug flag of this request
   *
   * @param debug {@code true} to request recommendations in debug mode, otherwise {@code false}
   * @return this request
   */
  APIPostPlayRequest setDebug(Boolean debug);
}
