package com.netflix.api.service.list;

import java.util.Iterator;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Source of references of an APIList. */
public interface ReferenceSource<R> extends Iterable<R> {
  R getReference(Integer index);

  Integer getSize();

  class ListSource<R> implements ReferenceSource<R> {
    private static final Logger log = LoggerFactory.getLogger(ListSource.class);
    private final List<R> references;

    public ListSource(List<R> references) {
      this.references = references;
    }

    @Override
    public R getReference(Integer index) {
      if (references == null) {
        log.error("NO REFERENCE[{}]: references == null", index);
        return null;
      }
      if (index < 0 || index >= getSize()) {
        log.error("NO REFERENCE[{}]: index out of range [0,{}); ListSource", index, getSize());
        return null;
      }
      return references.get(index);
    }

    @Override
    public Integer getSize() {
      return references == null ? 0 : references.size();
    }

    @NotNull
    @Override
    public Iterator<R> iterator() {
      return references.iterator();
    }
  }
}
