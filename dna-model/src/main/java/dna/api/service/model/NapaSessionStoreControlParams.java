package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaSessionStoreControlParams {
  private Boolean lookupOnlyRequest;

  private Integer ttlInSeconds;

  private Boolean skipRead;

  private Boolean skipWrite;

  public Boolean getLookupOnlyRequest() {
    return lookupOnlyRequest;
  }

  public Integer getTtlInSeconds() {
    return ttlInSeconds;
  }

  public Boolean getSkipRead() {
    return skipRead;
  }

  public Boolean getSkipWrite() {
    return skipWrite;
  }

  public NapaSessionStoreControlParams setLookupOnlyRequest(Boolean lookupOnlyRequest) {
    this.lookupOnlyRequest = lookupOnlyRequest;
    return this;
  }

  public NapaSessionStoreControlParams setTtlInSeconds(Integer ttlInSeconds) {
    this.ttlInSeconds = ttlInSeconds;
    return this;
  }

  public NapaSessionStoreControlParams setSkipRead(Boolean skipRead) {
    this.skipRead = skipRead;
    return this;
  }

  public NapaSessionStoreControlParams setSkipWrite(Boolean skipWrite) {
    this.skipWrite = skipWrite;
    return this;
  }
}
