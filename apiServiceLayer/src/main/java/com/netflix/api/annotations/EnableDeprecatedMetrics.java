package com.netflix.api.annotations;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * Add this to any class which is injected by <PERSON><PERSON>ce and it will introspect any method that is
 * annotated with @{@link Deprecated} and emit a metric. You can use {@link
 * com.netflix.api.platform.deprecated.DeprecatedMethodTracker} directly if you can't or don't want
 * to use G<PERSON><PERSON> to intercept the methods dynamically
 */
@Retention(RetentionPolicy.RUNTIME)
public @interface EnableDeprecatedMetrics {}
