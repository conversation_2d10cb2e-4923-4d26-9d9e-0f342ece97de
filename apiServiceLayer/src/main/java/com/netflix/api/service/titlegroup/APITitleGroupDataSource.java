package com.netflix.api.service.titlegroup;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import rx.Observable;

public interface APITitleGroupDataSource {
  Observable<List<APITitleGroupSet>> findGroupSets(Collection<APITitleGroupSetMember> members);

  Observable<List<APITitleGroupSet>> getGroupSets(Collection<APITitleGroup> groups);

  Observable<Set<APITitleGroupSetMember>> findMembersFromIds(Collection<Integer> videoIds);

  Observable<List<APITitleGroup>> findGroups(Collection<Integer> groups);
}
