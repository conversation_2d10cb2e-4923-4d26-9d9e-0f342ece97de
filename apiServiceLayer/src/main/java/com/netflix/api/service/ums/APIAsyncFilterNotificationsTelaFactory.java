package com.netflix.api.service.ums;

import com.netflix.api.adapters.StreamingClientServiceAdapter;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.tela.notifications.protogen.TelaNotificationsServiceGrpc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class APIAsyncFilterNotificationsTelaFactory {
  private final TelaNotificationsServiceGrpc.TelaNotificationsServiceStub
      telaNotificationsServiceStub;
  private final StreamingClientServiceAdapter streamingClientServiceAdapter;

  @Autowired
  public APIAsyncFilterNotificationsTelaFactory(
      @GrpcSpringClient("tela")
          TelaNotificationsServiceGrpc.TelaNotificationsServiceStub telaNotificationsServiceStub,
      StreamingClientServiceAdapter streamingClientServiceAdapter) {
    this.telaNotificationsServiceStub = telaNotificationsServiceStub;
    this.streamingClientServiceAdapter = streamingClientServiceAdapter;
  }

  public APIAsyncFilterNotificationsTelaImpl create(
      APIFilterNotificationMessagesRequestImpl request) {
    return new APIAsyncFilterNotificationsTelaImpl(
        telaNotificationsServiceStub, streamingClientServiceAdapter, request);
  }
}
