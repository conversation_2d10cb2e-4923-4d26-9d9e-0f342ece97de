package com.netflix.api.service.identity;

import java.util.concurrent.CompletionStage;

/**
 * These values identify a customer outside of Netflix and inside it, and Netflix observes a very
 * high level of data hygiene when it comes to handling this data. I.e., should only be used for
 * specific use cases and not generally used or logged across systems.
 */
public interface APIUserPersonalData {

  /**
   * The profile level email address
   *
   * @return future of profile email address
   */
  CompletionStage<String> getEmailAddress();

  /**
   * The account level email address
   *
   * @return future of account email address
   */
  CompletionStage<String> getAccountEmailAddress();
}
