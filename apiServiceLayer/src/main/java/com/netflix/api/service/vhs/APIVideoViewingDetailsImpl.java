package com.netflix.api.service.vhs;

import com.netflix.vhs.protogen.ViewingHistory.Detail;
import java.util.Objects;

public class APIVideoViewingDetailsImpl implements APIVideoViewingDetails {
  private final int watchedDuration;
  private final String country;
  private final String esn;
  private final int deviceTypeId;
  private final long startTs;

  APIVideoViewingDetailsImpl(Detail detail) {
    this.watchedDuration = detail.getWatchedDuration();
    this.country = detail.getCountry();
    this.esn = detail.getEsn();
    this.deviceTypeId = detail.getDeviceTypeId();
    this.startTs = detail.getWatchedTs();
  }

  @Override
  public int getWatchedDurationInSeconds() {
    return watchedDuration;
  }

  @Override
  public String getCountry() {
    return country;
  }

  @Override
  public String getEsn() {
    return esn;
  }

  @Override
  public int getDeviceTypeId() {
    return deviceTypeId;
  }

  @Override
  public long getStartTs() {
    return startTs;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (!(o instanceof APIVideoViewingDetailsImpl that)) return false;
    return watchedDuration == that.watchedDuration
        && deviceTypeId == that.deviceTypeId
        && startTs == that.startTs
        && Objects.equals(country, that.country);
  }

  @Override
  public int hashCode() {
    return Objects.hash(watchedDuration, country, deviceTypeId, startTs);
  }

  @Override
  public String toString() {
    return "Details["
        + watchedDuration
        + ","
        + country
        + ","
        + esn
        + ","
        + deviceTypeId
        + ","
        + startTs
        + ']';
  }
}
