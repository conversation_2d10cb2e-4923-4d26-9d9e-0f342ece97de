package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.interactive.APIPlaybackProgress */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class IxPlaybackProgress {
  private Integer progressPercentage;

  public Integer getProgressPercentage() {
    return progressPercentage;
  }

  public IxPlaybackProgress setProgressPercentage(Integer progressPercentage) {
    this.progressPercentage = progressPercentage;
    return this;
  }
}
