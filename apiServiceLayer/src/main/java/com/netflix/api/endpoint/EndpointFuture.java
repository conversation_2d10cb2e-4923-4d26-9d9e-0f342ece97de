package com.netflix.api.endpoint;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.servo.monitor.Counter;
import com.netflix.util.concurrent.NFFuture;
import com.netflix.util.concurrent.NFFutureHook;
import java.lang.ref.WeakReference;
import java.util.Optional;
import java.util.concurrent.CancellationException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * This wrapper around NFFuture allows extending the <code>get</code> method to always include
 * timeout functionality.
 *
 * <p>We do not want developers queueing up commands and calling the normal <code>get()</code> and
 * blocking indefinitely.
 *
 * <p>This implementation routes all <code>get()</code> calls to <code>
 * get(long timeout, TimeUnit unit)</code> so that timeouts occur automatically for commands
 * executed via <code>execute()</code> or <code>queue().get()</code>
 */
public class EndpointFuture<ReturnType> extends NFFuture<ReturnType>
    implements EndpointThreadTask.TimeoutTaskHandler {
  private static final Logger logger = LoggerFactory.getLogger(EndpointFuture.class);
  private static final Property<Integer> commandCancelRetryAttemptCount =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.endpoint.async.command.cancel.attempt", Integer.class)
          .orElse(60);

  private volatile WeakReference<Thread> futureThread = new WeakReference<>(null);
  private volatile boolean interruptRequest = false;

  private final EndpointThreadScriptRequest scriptRequest;

  private final CancellableCallable<ReturnType> callable;
  private final Property<Integer> executionTimeoutInMilliseconds;
  private final Counter tomcatETCWTimeoutCounter;

  private int attemptCount;
  private boolean didReportInterruptAttempt = false;

  public EndpointFuture(
      String name,
      Executor executor,
      EndpointThreadScriptRequest scriptRequest,
      CancellableCallable<ReturnType> callable,
      NFFutureHook hook,
      Property<Integer> executionTimeoutInMilliseconds,
      Counter tomcatETCWTimeoutCounter) {
    super(name, executor, callable, hook);

    this.callable = callable;
    this.executionTimeoutInMilliseconds = executionTimeoutInMilliseconds;
    this.scriptRequest = scriptRequest;
    this.tomcatETCWTimeoutCounter = tomcatETCWTimeoutCounter;

    this.callable.setFuture(this);

    attemptCount = commandCancelRetryAttemptCount.get();
  }

  @Override
  public void run() {
    futureThread = new WeakReference<>(Thread.currentThread());
    try {
      super.run();
    } finally {
      synchronized (this) {
        futureThread = new WeakReference<>(null);
      }
    }
  }

  /**
   * We override the get() to force it to always have a timeout so developers can not accidentally
   * use the Command.queue().get() methods and block indefinitely.
   */
  @Override
  public ReturnType get() throws CancellationException, InterruptedException, ExecutionException {
    long timeoutInMillis = getTimeoutMillis();
    try {
      return get(timeoutInMillis, TimeUnit.MILLISECONDS);
    } catch (TimeoutException e) {
      requestCancel();
      try {
        reportTimeout(timeoutInMillis);
      } catch (TimeoutException te) {
        throw new ExecutionException(te);
      }
    }
    return null;
  }

  @Override
  public long getTimeoutMillis() {
    return executionTimeoutInMilliseconds.get();
  }

  @Override
  public String reportTimeout(long timeoutMillis) throws TimeoutException {
    // try and cancel the task
    tomcatETCWTimeoutCounter.increment();
    Optional<Thread> runningThread = Optional.ofNullable(futureThread.get());
    Optional<StackTraceElement[]> stackTrace = runningThread.map(Thread::getStackTrace);

    String scriptDuration =
        scriptRequest == null ? "null" : scriptRequest.scriptExecutionDuration() + "ms";

    TimeoutException execException =
        new TimeoutException(
            "Async command timed-out after "
                + timeoutMillis
                + "ms, script execution duration is "
                + scriptDuration);
    stackTrace.ifPresent(execException::setStackTrace);
    throw execException;
  }

  public boolean requestCancel() {
    interruptRequest = true;
    this.callable.cancel();
    return true; // call tryCancel later to ensure cancellation
  }

  @Override
  public boolean tryCancel() {
    if (this.isDone() && interruptRequest) {
      // The future is done, we need to check if the thread is still running for this task
      synchronized (this) {
        Thread thread = futureThread.get();
        if (thread == null) {
          return true;
        }

        if (!didReportInterruptAttempt) {
          didReportInterruptAttempt = true;
          InterruptedException t = new InterruptedException();
          t.setStackTrace(thread.getStackTrace());
          if (logger.isWarnEnabled()) {
            logger.warn("Thread is running after interruption (state={})", thread.getState(), t);
          }
        }
        attemptCount--;
        thread.interrupt();
        return attemptCount <= 0;
      }
    } else {
      return true;
    }
  }
}
