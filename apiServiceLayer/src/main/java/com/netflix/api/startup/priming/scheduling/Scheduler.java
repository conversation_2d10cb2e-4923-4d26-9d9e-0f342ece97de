package com.netflix.api.startup.priming.scheduling;

import static java.util.function.Predicate.not;

import com.google.common.base.Preconditions;
import com.google.inject.Inject;
import com.google.inject.Key;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;

public class Scheduler {

  private final List<ScheduledRunnable> entryPoints;
  private final StripedExecutorFactory executorFactory;
  private boolean started;

  @Inject
  public Scheduler(StripedExecutorFactory executorFactory) {
    this.entryPoints = new ArrayList<>();
    this.executorFactory = executorFactory;
    this.started = false;
  }

  /**
   * Schedule a runnable for execution that dependents on other scheduled execution
   *
   * @param dependencyKey dependency keys
   * @param runnable initialization block to run
   * @param dependencies list of dependencies
   * @return scheduled execution
   */
  public ScheduledDependencyInitialization schedule(
      Key<?> dependencyKey,
      Runnable runnable,
      List<ScheduledDependencyInitialization> dependencies) {
    Preconditions.checkState(!started);
    Executor executor = this.executorFactory.getExecutorForKey(dependencyKey);

    List<ScheduledRunnable> list =
        dependencies.stream()
            .map(ScheduledRunnable.class::cast)
            .filter(not(ScheduledRunnable::isDone))
            .toList();

    CompletableFuture<Void> dependenciesFuture =
        CompletableFuture.allOf(
            list.stream().map(ScheduledRunnable::getFuture).toArray(CompletableFuture[]::new));

    ScheduledRunnable task =
        new ScheduledRunnable(dependencyKey, executor, runnable, dependenciesFuture);
    if (dependencies.isEmpty()) {
      entryPoints.add(task);
    }
    for (ScheduledRunnable dependency : list) {
      dependency.addDependent(task);
    }
    return task;
  }

  public ScheduledDependencyInitialization schedule(
      Key<?> dependencyKey, List<ScheduledDependencyInitialization> dependencies) {
    return this.schedule(dependencyKey, null, dependencies);
  }

  /**
   * Start the concurrent execution of the scheduled runnable No other runnable can be scheduled
   * after this method is called
   */
  public void start() {
    this.started = true;
    entryPoints.forEach(ScheduledRunnable::execute);
  }

  private static final class ScheduledRunnable implements ScheduledDependencyInitialization {
    private final CompletableFuture<Void> dependenciesFuture;

    private final List<ScheduledRunnable> dependents;

    private final Key<?> dependencyKey;
    private final Executor executor;
    private final Runnable runnable;
    private final AtomicBoolean didExecute;

    private final CompletableFuture<Void> future;

    private ScheduledRunnable(
        Key<?> dependencyKey,
        Executor executor,
        Runnable runnable,
        CompletableFuture<Void> dependenciesFuture) {
      this.executor = executor;
      this.dependencyKey = dependencyKey;
      this.runnable = runnable;
      this.future = new CompletableFuture<>();
      this.didExecute = new AtomicBoolean(false);
      this.dependents = new ArrayList<>();
      this.dependenciesFuture = dependenciesFuture;
    }

    private void addDependent(ScheduledRunnable scheduledExecution) {
      this.dependents.add(scheduledExecution);
    }

    private boolean isDone() {
      return this.future.isDone();
    }

    private void execute() {
      if (isDone()) return;

      if (dependenciesFuture.isDone() && !didExecute.getAndSet(true)) {
        executeRunnable();
        this.future.whenCompleteAsync((v, t) -> executeDependents(), executor);
      }
    }

    public CompletableFuture<Void> getFuture() {
      return future;
    }

    private void executeRunnable() {
      // Do not execute the runnable if any dependency fail
      Throwable dependencyFailed = this.dependenciesFuture.handle((v, t) -> t).getNow(null);
      if (dependencyFailed != null) {
        this.future.completeExceptionally(dependencyFailed);
        return;
      }

      if (runnable != null) {
        executor.execute(
            () -> {
              try {
                runnable.run();
                this.future.complete(null);
              } catch (Exception e) {
                this.future.completeExceptionally(e);
              }
            });
      } else {
        this.future.complete(null);
      }
    }

    private void executeDependents() {
      for (ScheduledRunnable dep : this.dependents) {
        dep.execute();
      }
    }

    @Override
    public String toString() {
      return "ScheduledRunnable [dependencyKey="
          + dependencyKey
          + ", didExecute="
          + didExecute
          + "]";
    }
  }

  public void reset() {
    this.started = false;
    this.entryPoints.clear();
  }
}
