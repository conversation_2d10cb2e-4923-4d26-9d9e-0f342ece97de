package com.netflix.api.platform.util;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import rx.Completable;
import rx.Observable;
import rx.Single;
import rx.Subscription;

public class CompletionStageAdapter {
  private CompletionStageAdapter() {}

  public static <T> CompletionStage<T> from(Observable<T> observable) {
    return from(observable.limit(1).defaultIfEmpty(null).toSingle());
  }

  public static CompletionStage<Void> from(Completable completable) {
    CompletableFuture<Void> future = new CompletableFuture<>();
    completable.subscribe(() -> future.complete(null), future::completeExceptionally);
    return future;
  }

  public static CompletionStage<Void> fromVoid(Observable<Void> observable) {
    CompletableFuture<Void> future = new CompletableFuture<>();
    observable.subscribe(
        res -> future.complete(null), future::completeExceptionally, () -> future.complete(null));
    return future;
  }

  public static <T, V> CompletionStage<?> from(Observable<T> observable, V defaultValue) {
    return from(observable, Object.class, defaultValue);
  }

  public static <T, V> CompletionStage<V> from(
      Observable<T> observable, Class<V> classV, V defaultValue) {
    return from(observable.take(1).cast(classV).defaultIfEmpty(defaultValue).toSingle());
  }

  public static <T> CompletableFuture<T> from(Single<T> single) {
    CompletableFuture<T> future = new CompletableFuture<>();
    final Subscription subscription =
        single.subscribe(future::complete, future::completeExceptionally);
    return future.whenComplete((value, t) -> subscription.unsubscribe());
  }

  public static <T> Single<T> toSingle(CompletionStage<T> completionStage) {
    return toObservable(completionStage).toSingle();
  }

  public static <T> Observable<T> toObservable(CompletionStage<T> completionStage) {
    return toObservable(completionStage, false);
  }

  public static <T> Observable<T> toObservable(
      CompletionStage<T> completionStage, boolean ignoreNull) {
    return Observable.<T>unsafeCreate(
            subscriber ->
                completionStage.whenComplete(
                    (t, throwable) -> {
                      if (subscriber.isUnsubscribed()) return;
                      if (throwable != null) {
                        subscriber.onError(throwable);
                      } else {
                        if (!ignoreNull || t != null) {
                          subscriber.onNext(t);
                        }
                        subscriber.onCompleted();
                      }
                    }))
        .cache();
  }
}
