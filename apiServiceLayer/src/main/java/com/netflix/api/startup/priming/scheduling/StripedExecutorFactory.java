package com.netflix.api.startup.priming.scheduling;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.google.inject.Key;
import com.netflix.lang.BindingContexts;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class StripedExecutorFactory {

  private static final Logger LOG = LoggerFactory.getLogger(StripedExecutorFactory.class);

  private final Executor[] pool;
  private final ThreadFactory factory;

  private final List<Stripe> stripes;

  private StripedExecutorFactory(ThreadFactory threadFactory, List<Stripe> stripes) {
    int stripCount = stripes.stream().map(s -> s.stripesCount).reduce(0, Integer::sum);
    this.stripes = stripes;
    pool = new Executor[stripCount + 1];
    this.factory = new ThreadFactoryBuilder().setNameFormat("startup-thread-%d").build();
  }

  public static StripedExecutorFactory create(ThreadFactory threadFactory, Stripe... stripes) {
    return new StripedExecutorFactory(threadFactory, Arrays.asList(stripes));
  }

  private int getIndex(Key<?> key) {
    int offset = 1; // first index is reserve for unmatched key
    for (Stripe strip : stripes) {
      if (strip.selector.accept(key)) {
        int index = strip.selector.select(strip.stripesCount, key);
        return index + offset;
      }
      offset += strip.stripesCount;
    }
    LOG.warn("Can't find index for key {}", key);
    return 0;
  }

  public Executor getExecutorForKey(Key<?> key) {
    int index = getIndex(key);
    if (pool[index] == null) {
      pool[index] =
          new ThreadPoolExecutor(
              0, 1, 120L, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(), factory);
    }
    return BindingContexts.propagate(pool[index]);
  }

  public static class Stripe {
    private final int stripesCount;
    private final StripeSelector selector;

    private Stripe(int stripesCount, StripeSelector selector) {
      this.stripesCount = stripesCount;
      this.selector = selector;
    }

    public static Stripe packageSpan(int count, int pkgDepth) {
      return new Stripe(count, new StripeSelectorPackage(pkgDepth));
    }

    public static Stripe packageSpan(int count, String prefix) {
      int pkgs = (int) (prefix.chars().filter((c) -> c == '.').count() + 1);
      return new Stripe(
          count,
          new StripeSelectorPackage(pkgs + 1) {
            public boolean accept(Key<?> key) {
              String name = key.getTypeLiteral().getRawType().getCanonicalName();
              return name != null && name.startsWith(prefix);
            }
          });
    }

    public static Stripe single(String... prefixes) {
      return new Stripe(
          1,
          new StripeSelector() {
            public boolean accept(Key<?> key) {
              String name = key.getTypeLiteral().getRawType().getCanonicalName();
              if (name == null) return false;
              for (String prefix : prefixes) {
                if (name.startsWith(prefix)) {
                  return true;
                }
              }
              return false;
            }

            @Override
            public int select(int stripCount, Key<?> key) {
              return 0;
            }
          });
    }
  }
}
