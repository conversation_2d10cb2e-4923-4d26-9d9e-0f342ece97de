package com.netflix.api.service.onramp;

import com.netflix.api.service.APICountry;

/** Use {@code APIOnrampRequest} object as a parameter for {@link APIOnrampService} methods. */
public interface APIOnrampRequest {

  /**
   * Get the country where request is coming from.
   *
   * @return the country where request is coming from.
   */
  APICountry getCountry();

  /**
   * Get the ESN of the customer device
   *
   * @return the ESN of the customer device
   */
  String getEsn();

  /**
   * Is request made in the context of a signup flow?
   *
   * @return {@code true} if request is part of the signup flow, {@code false} otherwise
   */
  Boolean isSignupFlow();

  /**
   * Is onramp for secondary profiles disabled?
   *
   * @return {@code true} if onramp disabled for secondary profiles, {@code false} otherwise
   */
  Boolean isDisableOnrampForSecondaryProfiles();

  /**
   * Marks request relation to a signup flow.
   *
   * @param signupFlowFlag {@code true} to idicate that requests is part of a signup flow, {@code
   *     false} otherwise
   * @return the same {@code APIOnrampRequest} marked whether or not it is related to a signup flow
   */
  APIOnrampRequest setSignupFlow(boolean signupFlowFlag);

  /**
   * Marks disabling onramp for secondary profiles.
   *
   * @param disableOnrampForSecondaryProfiles {@code true} to indicate that onramp should be
   *     disabled for secondary profiles, {@code false} otherwise. Defaults to {@code false}
   * @return the same {@code APIOnrampRequest} marked whether or not to disable onramp for secondary
   *     profiles
   */
  APIOnrampRequest setDisableOnrampForSecondaryProfiles(boolean disableOnrampForSecondaryProfiles);
}
