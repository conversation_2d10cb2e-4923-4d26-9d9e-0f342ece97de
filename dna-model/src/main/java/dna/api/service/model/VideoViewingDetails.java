package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

/** @dotnext com.netflix.api.service.vhs.APIVideoViewingDetails */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class VideoViewingDetails implements GraphData {
  public Atom<VideoViewingDetailsId> videoViewingDetailsId;

  public Integer watchedDurationInSeconds;

  public String esn;

  public Long startTs;

  public Country country;

  public DeviceType deviceType;

  public Atom<VideoViewingDetailsId> getVideoViewingDetailsId() {
    return videoViewingDetailsId;
  }

  public Integer getWatchedDurationInSeconds() {
    return watchedDurationInSeconds;
  }

  public String getEsn() {
    return esn;
  }

  public Long getStartTs() {
    return startTs;
  }

  public VideoViewingDetails setVideoViewingDetailsId(
      Atom<VideoViewingDetailsId> videoViewingDetailsId) {
    this.videoViewingDetailsId = videoViewingDetailsId;
    return this;
  }

  public VideoViewingDetails setWatchedDurationInSeconds(Integer watchedDurationInSeconds) {
    this.watchedDurationInSeconds = watchedDurationInSeconds;
    return this;
  }

  public VideoViewingDetails setEsn(String esn) {
    this.esn = esn;
    return this;
  }

  public VideoViewingDetails setStartTs(Long startTs) {
    this.startTs = startTs;
    return this;
  }
}
