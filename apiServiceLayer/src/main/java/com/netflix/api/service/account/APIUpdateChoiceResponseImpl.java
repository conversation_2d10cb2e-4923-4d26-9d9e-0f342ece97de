package com.netflix.api.service.account;

import com.netflix.memberprice.protogen.UpdateChoiceResponse;

public class APIUpdateChoiceResponseImpl implements APIUpdateChoiceResponse {

  private final UpdateChoiceResponse updateChoiceResponse;

  APIUpdateChoiceResponseImpl(UpdateChoiceResponse updateChoiceResponse) {
    this.updateChoiceResponse = updateChoiceResponse;
  }

  @Override
  public boolean isSuccess() {
    return updateChoiceResponse.getSuccess();
  }

  @Override
  public boolean isFallback() {
    return updateChoiceResponse.getFallback();
  }

  @Override
  public boolean isDeferredPlanChange() {
    return updateChoiceResponse.getIsDeferredPlanChange();
  }
}
