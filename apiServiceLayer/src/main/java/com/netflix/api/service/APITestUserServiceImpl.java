package com.netflix.api.service;

import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.drpr.sd.overrides.SDApiOverrides;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@EnableDeprecatedMetrics
public class APITestUserServiceImpl implements APITestUserService {
  private final SDApiOverrides deviceOverridesReader;

  @Autowired
  public APITestUserServiceImpl(SDApiOverrides deviceOverridesReader) {
    this.deviceOverridesReader = deviceOverridesReader;
  }

  @Override
  @Deprecated
  public String getCertificationSDApiOverrides(String deviceId, String lookUpKey) {
    return new FetchCertificationSDOverrides(this.deviceOverridesReader, deviceId, lookUpKey)
        .execute();
  }

  private static class FetchCertificationSDOverrides extends HystrixCommand<String> {

    private final String deviceId;
    private final String lookUpKey;
    private final SDApiOverrides deviceOverridesReader;

    private FetchCertificationSDOverrides(
        SDApiOverrides deviceOverridesReader, String deviceId, String lookUpKey) {
      super(HystrixCommandGroupKey.Factory.asKey("NTSService"));
      this.deviceId = deviceId;
      this.lookUpKey = lookUpKey;
      this.deviceOverridesReader = deviceOverridesReader;
    }

    @Override
    protected String run() {
      return deviceOverridesReader.getSDApiOverride(deviceId, lookUpKey);
    }

    @Override
    protected String getFallback() {
      return null;
    }
  }
}
