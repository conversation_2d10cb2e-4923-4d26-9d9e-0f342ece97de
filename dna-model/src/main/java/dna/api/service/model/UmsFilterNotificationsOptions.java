package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.ums.APIFilterNotificationMessagesRequest */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class UmsFilterNotificationsOptions {
  /** Read States: 'ALL' | 'UNREAD' | 'READ' */
  private String readState;

  /** Number of messages to return Returns all messages if omitted */
  private Integer maxCount;

  /** Return only messages that are newer or older than this timestamp */
  private Long referenceTimestamp;

  /** If true, return only messages that are older than this timestamp */
  private Boolean messagesOlderThanRefTimestamp;

  /**
   * Only return this type of social messages Social Message Types: 'RECOMMENDATION' |
   * 'NON_RECOMMENDATION'
   */
  private String socialMessageType;

  /** The requested size of the boxart Required because we use it as a filtering criteria */
  private String boxArtSize;

  /** The requested size of the boxart for NSA */
  private String nsaBoxArtSize;

  /** Is secure */
  private Boolean isSecure;

  /** Previous eTag value */
  private String eTag;

  /** The requested boxart format */
  private String artWorkFormat;

  /**
   * The uiPlatform making the request Platforms: 'SHAKTI' | 'WEBSITE' | 'ANDROID' | 'IOS' |
   * 'DARWIN' | 'ATV3' | 'TVUI' | 'HELP_CENTER' | 'TVOS' | 'WINDOWS'
   */
  private String platform;

  /** Whether to fetch the template for this message */
  private Boolean fetchTemplate;

  /** Formats: 'HTML' | 'TEXT' | 'OBELIX_KEY' */
  private String templateStringFormat;

  /** the app version of the uiPlatform making the request */
  private String appVersion;

  /** the eventGuid of the message to return */
  private String eventGuid;

  public String getReadState() {
    return readState;
  }

  public Integer getMaxCount() {
    return maxCount;
  }

  public Long getReferenceTimestamp() {
    return referenceTimestamp;
  }

  public Boolean getMessagesOlderThanRefTimestamp() {
    return messagesOlderThanRefTimestamp;
  }

  public String getSocialMessageType() {
    return socialMessageType;
  }

  public String getBoxArtSize() {
    return boxArtSize;
  }

  public String getNsaBoxArtSize() {
    return nsaBoxArtSize;
  }

  public Boolean getIsSecure() {
    return isSecure;
  }

  public String getETag() {
    return eTag;
  }

  public String getArtWorkFormat() {
    return artWorkFormat;
  }

  public String getPlatform() {
    return platform;
  }

  public Boolean getFetchTemplate() {
    return fetchTemplate;
  }

  public String getTemplateStringFormat() {
    return templateStringFormat;
  }

  public String getAppVersion() {
    return appVersion;
  }

  public String getEventGuid() {
    return eventGuid;
  }

  public UmsFilterNotificationsOptions setReadState(String readState) {
    this.readState = readState;
    return this;
  }

  public UmsFilterNotificationsOptions setMaxCount(Integer maxCount) {
    this.maxCount = maxCount;
    return this;
  }

  public UmsFilterNotificationsOptions setReferenceTimestamp(Long referenceTimestamp) {
    this.referenceTimestamp = referenceTimestamp;
    return this;
  }

  public UmsFilterNotificationsOptions setMessagesOlderThanRefTimestamp(
      Boolean messagesOlderThanRefTimestamp) {
    this.messagesOlderThanRefTimestamp = messagesOlderThanRefTimestamp;
    return this;
  }

  public UmsFilterNotificationsOptions setSocialMessageType(String socialMessageType) {
    this.socialMessageType = socialMessageType;
    return this;
  }

  public UmsFilterNotificationsOptions setBoxArtSize(String boxArtSize) {
    this.boxArtSize = boxArtSize;
    return this;
  }

  public UmsFilterNotificationsOptions setNsaBoxArtSize(String nsaBoxArtSize) {
    this.nsaBoxArtSize = nsaBoxArtSize;
    return this;
  }

  public UmsFilterNotificationsOptions setIsSecure(Boolean isSecure) {
    this.isSecure = isSecure;
    return this;
  }

  public UmsFilterNotificationsOptions setETag(String eTag) {
    this.eTag = eTag;
    return this;
  }

  public UmsFilterNotificationsOptions setArtWorkFormat(String artWorkFormat) {
    this.artWorkFormat = artWorkFormat;
    return this;
  }

  public UmsFilterNotificationsOptions setPlatform(String platform) {
    this.platform = platform;
    return this;
  }

  public UmsFilterNotificationsOptions setFetchTemplate(Boolean fetchTemplate) {
    this.fetchTemplate = fetchTemplate;
    return this;
  }

  public UmsFilterNotificationsOptions setTemplateStringFormat(String templateStringFormat) {
    this.templateStringFormat = templateStringFormat;
    return this;
  }

  public UmsFilterNotificationsOptions setAppVersion(String appVersion) {
    this.appVersion = appVersion;
    return this;
  }

  public UmsFilterNotificationsOptions setEventGuid(String eventGuid) {
    this.eventGuid = eventGuid;
    return this;
  }
}
