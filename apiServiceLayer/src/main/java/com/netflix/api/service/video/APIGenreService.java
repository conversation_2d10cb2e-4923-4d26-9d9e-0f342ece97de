package com.netflix.api.service.video;

import java.util.Collection;
import java.util.Map;
import rx.Single;

/**
 * Service that returns {@link APIGenre}s for given IDs of the genres, and can also generate LoLoMos
 * based on particular genres.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIGenre.png"
 * alt="APIGenre class diagram">
 *
 * @see com.netflix.api.service.video.APIGenre
 */
public interface APIGenreService {
  /**
   * Emits a {@link Map} {@link APIGenre}s for the IDs requested.
   *
   * @param ids IDs of the genres needed
   * @return a map containing the {@link APIGenre} that were found from the ids, if a genre cannot
   *     be looked up there will be no value in the resulting map
   */
  Single<Map<Long, APIGenre>> getGenreMap(Collection<Long> ids);
}
