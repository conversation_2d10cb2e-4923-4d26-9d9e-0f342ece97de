package com.netflix.api.service.video;

import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.list.APIEntity;
import java.util.Date;
import java.util.List;
import java.util.Set;
import rx.Observable;

/**
 * This object encapsulates the genre of a movie.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIGenre.png">
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.related1.png">
 * <img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.related2.png">
 */
public interface APIGenre extends APIEntity {
  /**
   * Id of the genre. These ids must be treated as long lived, yet temporal. In other words, do not
   * hard code and base business logic on this.
   *
   * @deprecated use {@link #getSourceId()} ()}
   * @return the genre's id -- not null
   */
  @Override
  @Deprecated
  Integer getId();

  /**
   * @return Id of the genre
   */
  long getSourceId();

  /**
   * The display name of this genre. When displaying as a header for a list of videos, use this
   * name.
   *
   * @return display name of the genre, not null
   * @see {@link #getNavigationMenuName()}
   */
  @Override
  String getName();

  /**
   * A short name of this genre, to be used for navigation and menu bars. If data is not available,
   * this defaults to {@link #getName()}
   *
   * @return name to be displayed in Navigation or menu bars, not null
   */
  String getNavigationMenuName();

  /**
   * @return true if this genre has a NonMember type attached, false otherwise
   */
  boolean hasNonMemberType();

  /**
   * @return returns genre types
   */
  Set<String> types();

  /**
   * Get an attribute of this genre that is a string (localized attribute value based on the locale
   * of the request will be returned).
   *
   * @param name of the attribute (key). Consult Turbo team for the list of attribute names.
   * @return String attribute value - returns null if this attribute does not have a value, or not
   *     localized.
   */
  String getStringAttribute(String name);

  /**
   * Get an attribute of this genre that is an int.
   *
   * @param name of the attribute (key). Consult Turbo team for the list of attribute names.
   * @return the int value. Null if this attribute does not have an entered value.
   */
  Integer getIntAttribute(String name);

  /**
   * Get an attribute of this genre that is a long.
   *
   * @param name of the attribute (key). Consult Turbo team for the list of attribute names.
   * @return the long value. Null if this attribute does not have an entered value.
   */
  Long getLongAttribute(String name);

  /**
   * Get an attribute of this genre that is a date.
   *
   * @param name of the attribute (key). Consult Turbo team for the list of attribute names.
   * @return the date value. Null if this attribute does not have an entered value.
   */
  Date getDateAttribute(String name);

  /**
   * Get an attribute of this genre that is a boolean.
   *
   * @param name of the attribute (key). Consult Turbo team for the list of attribute names.
   * @return the boolean value. Null if this attribute does not have an entered value.
   */
  Boolean getBooleanAttribute(String name);

  /**
   * Expose the UUID of the associated MAP list for this genre.
   *
   * @return a UUID associated with this genre if one exists, null otherwise
   */
  String getUUID();

  /**
   * Returns an ordered (by maturity level in ascending order) list of certification ratings of the
   * gallery of videos served to the user for this genre.
   *
   * @param user {@link com.netflix.api.service.identity.APIUser} representing the Netflix customer.
   * @return An ordered list of unique certification ratings of all the videos in this genre's
   *     gallery
   */
  Observable<List<APICertification>> getGalleryCertifications(APIUser user);
}
