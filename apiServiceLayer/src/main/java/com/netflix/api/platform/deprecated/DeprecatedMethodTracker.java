package com.netflix.api.platform.deprecated;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.spectator.api.CompositeRegistry;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Spectator;
import java.util.List;

public class DeprecatedMethodTracker {

  private DeprecatedMethodTracker() {}

  private static final Property<Boolean> ENABLE =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("scriptTrace.deprecatedMethodCall.enabled", Boolean.class)
          .orElse(false);

  public static void incrementDeprecatedMetrics(
      String className, String methodName, List<String> params, String endpointPath) {
    if (ENABLE.get()) {
      StringBuilder paramsString = new StringBuilder();
      for (String parameter : params) {
        paramsString.append(parameter).append("_");
      }
      incrementDeprecatedMetrics(className, methodName, paramsString.toString(), endpointPath);
    }
  }

  public static void incrementDeprecatedMetrics(
      String className, String methodName, String paramTypes, String endpointPath) {
    final CompositeRegistry registry = Spectator.globalRegistry();
    Id id = registry.createId("api.serviceLayer.deprecatedMethod");
    id = id.withTag("className", className);
    id = id.withTag("method", methodName + "_" + paramTypes);
    id = id.withTag("endpoint", endpointPath);
    registry.counter(id).increment();
  }
}
