package com.netflix.api.platform.util;

import java.security.MessageDigest;
import org.apache.commons.codec.digest.DigestUtils;

public class MessageDigestStore {

  private static final ThreadLocal<MessageDigestStore> INSTANCE = new ThreadLocal<>();
  private MessageDigest sha1;

  private MessageDigestStore() {}

  public static MessageDigestStore getInstance() {
    MessageDigestStore store = INSTANCE.get();
    if (store == null) {
      store = new MessageDigestStore();
      INSTANCE.set(store);
    }
    return store;
  }

  public MessageDigest getSha1() {
    if (sha1 == null) {
      sha1 = DigestUtils.getSha1Digest();
    }
    sha1.reset();
    return sha1;
  }
}
