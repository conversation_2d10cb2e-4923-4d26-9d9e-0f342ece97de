{"_global_": {"@netflix-internal:dna-graphql-schema-bundled": {"locked": "1.1.0", "transitive": ["netflix:dna-router"]}, "antlr:antlr": {"locked": "2.7.7", "transitive": ["org.antlr:antlr-runtime", "org.antlr:stringtemplate"]}, "aopalliance:aopalliance": {"locked": "1.0", "transitive": ["com.netflix.inject:guice-all", "io.micrometer:micrometer-tracing", "netflix:api-global-dependencies"]}, "build.buf.protoc-gen-validate:pgv-java-stub": {"locked": "1.1.0", "transitive": ["com.netflix.mesh:mesh-api-java"]}, "ch.qos.reload4j:reload4j": {"locked": "1.2.25", "transitive": ["com.netflix.blitz4j:blitz4j", "netflix:platform-jdk-compat"]}, "ch.randelshofer:fastdoubleparser": {"locked": "1.0.0", "transitive": ["netflix:algo-commons-core"]}, "com.amazonaws:aws-java-sdk-core": {"locked": "1.12.791", "transitive": ["com.amazonaws:aws-java-sdk-ec2", "com.amazonaws:aws-java-sdk-kms", "com.amazonaws:aws-java-sdk-route53", "com.amazonaws:aws-java-sdk-s3", "com.amazonaws:aws-java-sdk-ses", "com.amazonaws:aws-java-sdk-sqs", "com.amazonaws:aws-java-sdk-sts", "com.amazonaws:aws-xray-recorder-sdk-aws-sdk", "com.netflix.s3:nfs3-common", "com.netflix.s3authsigncde:s3-auth-sign-client", "com.netflix.s3authsigncde:s3-auth-sign-common", "com.netflix.spectator:spectator-ext-aws", "netflix:nsac-client", "netflix:nsac-common", "netflix:platform-aws", "netflix:platform-core"]}, "com.amazonaws:aws-java-sdk-ec2": {"locked": "1.12.791", "transitive": ["netflix:platform-aws"]}, "com.amazonaws:aws-java-sdk-kms": {"locked": "1.12.791", "transitive": ["com.amazonaws:aws-java-sdk-s3"]}, "com.amazonaws:aws-java-sdk-route53": {"locked": "1.12.791", "transitive": ["netflix:platform-ipc"]}, "com.amazonaws:aws-java-sdk-s3": {"locked": "1.12.791", "transitive": ["com.netflix.s3:nfs3-aws-sdk", "com.netflix.s3authsigncde:s3-auth-sign-client", "netflix:gutenberg-common", "netflix:whitecastle"]}, "com.amazonaws:aws-java-sdk-ses": {"locked": "1.12.791", "transitive": ["netflix:platform-aws"]}, "com.amazonaws:aws-java-sdk-sqs": {"locked": "1.12.791", "transitive": ["com.netflix.hank:hank-client", "com.netflix.ksclient:platform-chukwaClient-legacy", "netflix:platform-aws", "netflix:platform-chukwaClient"]}, "com.amazonaws:aws-java-sdk-sts": {"locked": "1.12.791", "transitive": ["netflix:cis-aws-sdk-java-instrumentor-core", "netflix:gutenberg-common", "netflix:nsac-common", "netflix:platform-aws"]}, "com.amazonaws:aws-xray-recorder-sdk-aws-sdk": {"locked": "2.19.0", "transitive": ["netflix:cis-aws-sdk-java-instrumentor-core"]}, "com.amazonaws:aws-xray-recorder-sdk-aws-sdk-core": {"locked": "2.19.0", "transitive": ["com.amazonaws:aws-xray-recorder-sdk-aws-sdk", "netflix:cis-aws-sdk-java-instrumentor-core"]}, "com.amazonaws:aws-xray-recorder-sdk-core": {"locked": "2.19.0", "transitive": ["com.amazonaws:aws-xray-recorder-sdk-aws-sdk", "netflix:cis-aws-sdk-java-instrumentor-core"]}, "com.amazonaws:jmespath-java": {"locked": "1.12.791", "transitive": ["com.amazonaws:aws-java-sdk-ec2", "com.amazonaws:aws-java-sdk-kms", "com.amazonaws:aws-java-sdk-route53", "com.amazonaws:aws-java-sdk-s3", "com.amazonaws:aws-java-sdk-ses", "com.amazonaws:aws-java-sdk-sqs", "com.amazonaws:aws-java-sdk-sts"]}, "com.atomicjar:testcontainers-cloud-provider-strategy": {"locked": "0.0.45", "transitive": ["com.netflix.nebula.testcontainers:testcontainers-cloud-configurer"]}, "com.fasterxml.jackson.core:jackson-annotations": {"locked": "2.20", "transitive": ["com.amazonaws:aws-xray-recorder-sdk-core", "com.fasterxml.jackson.core:jackson-databind", "com.fasterxml.jackson.datatype:jackson-datatype-guava", "com.fasterxml.jackson.datatype:jackson-datatype-joda", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "com.fasterxml.jackson.module:jackson-module-jaxb-annotations", "com.fasterxml.jackson.module:jackson-module-kotlin", "com.netflix.archaius:archaius-core", "com.netflix.eureka:eureka-client", "com.netflix.graphql.dgs.codegen:graphql-dgs-codegen-shared-core", "com.netflix.hystrix:hystrix-serialization", "io.swagger.core.v3:swagger-core-jakarta", "io.swagger.core.v3:swagger-models-jakarta", "netflix:contentpreview-model", "netflix:country-launch", "netflix:cpeauthorization-common", "netflix:demograph-core", "netflix:demograph-scanner", "netflix:group-attribute", "netflix:mantis-realtime-events", "netflix:map-datamodel", "netflix:map-logging-core", "netflix:pandora-java-client", "netflix:recs-toolkit-protocol", "netflix:videometadata-client"]}, "com.fasterxml.jackson.core:jackson-core": {"locked": "2.20.0", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor", "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml", "com.fasterxml.jackson.datatype:jackson-datatype-guava", "com.fasterxml.jackson.datatype:jackson-datatype-jdk8", "com.fasterxml.jackson.datatype:jackson-datatype-joda", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "com.fasterxml.jackson.jaxrs:jackson-jaxrs-base", "com.fasterxml.jackson.module:jackson-module-afterburner", "com.fasterxml.jackson.module:jackson-module-jaxb-annotations", "com.fasterxml.jackson.module:jackson-module-parameter-names", "com.flipkart.zjsonpatch:zjsonpatch", "com.netflix.archaius:archaius-core", "com.netflix.archaius:archaius2-persisted2", "com.netflix.chaski2:chaski2-core", "com.netflix.chaski2:chaski2-parser-jackson", "com.netflix.chaski3:chaski3-api", "com.netflix.chaski3:chaski3-core", "com.netflix.chaski3:chaski3-serde-jackson", "com.netflix.chaski:chaski-parser-jackson", "com.netflix.eureka:eureka-client", "com.netflix.hystrix:hystrix-serialization", "com.netflix.microcontext:microcontext-init", "com.netflix.originals.row.filters:originals-row-filters-core", "io.sentry:sentry", "netflix:api-service-layer", "netflix:country-launch", "netflix:cpeauthorization-client-common", "netflix:cpeauthorization-common", "netflix:demograph-core", "netflix:demograph-scanner", "netflix:dhs-common", "netflix:mantis-realtime-events", "netflix:map-datamodel", "netflix:map-logging-core", "netflix:pandora-java-client", "netflix:recs-toolkit-protocol", "netflix:thumbs-datamodel", "netflix:videometadata-client", "netflix:vmscodeanalysistools"]}, "com.fasterxml.jackson.core:jackson-databind": {"locked": "2.20.0", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.amazonaws:aws-xray-recorder-sdk-core", "com.amazonaws:jmespath-java", "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor", "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml", "com.fasterxml.jackson.datatype:jackson-datatype-guava", "com.fasterxml.jackson.datatype:jackson-datatype-jdk8", "com.fasterxml.jackson.datatype:jackson-datatype-joda", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "com.fasterxml.jackson.jaxrs:jackson-jaxrs-base", "com.fasterxml.jackson.module:jackson-module-afterburner", "com.fasterxml.jackson.module:jackson-module-jaxb-annotations", "com.fasterxml.jackson.module:jackson-module-kotlin", "com.fasterxml.jackson.module:jackson-module-parameter-names", "com.flipkart.zjsonpatch:zjsonpatch", "com.netflix.archaius:archaius-core", "com.netflix.archaius:archaius2-persisted2", "com.netflix.chaski2:chaski2-core", "com.netflix.chaski2:chaski2-parser-jackson", "com.netflix.chaski3:chaski3-api", "com.netflix.chaski3:chaski3-core", "com.netflix.chaski3:chaski3-serde-jackson", "com.netflix.chaski:chaski-parser-jackson", "com.netflix.eureka:eureka-client", "com.netflix.governator:governator", "com.netflix.graphql.dgs.codegen:graphql-dgs-codegen-shared-core", "com.netflix.hystrix:hystrix-serialization", "com.netflix.ksclient:ksclient-api", "com.netflix.ksclient:ksclient-core", "com.netflix.microcontext:microcontext-init", "com.netflix.originals.row.filters:originals-row-filters-core", "com.netflix.runtime.metadata:runtime-metadata-client", "com.netflix.sbndevmetrics:sbn-dev-metrics-client", "com.netflix.spring:spring-boot-netflix", "com.netflix.spring:spring-boot-netflix-configuration", "com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-sso-authn-embedded", "io.swagger.core.v3:swagger-core-jakarta", "netflix.grpc:netflix-grpc-admin", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-metatron-client", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:country-launch", "netflix:cpeauthorization-client-common", "netflix:cpeauthorization-common", "netflix:demograph-core", "netflix:demograph-scanner", "netflix:mantis-realtime-events", "netflix:map-datamodel", "netflix:map-logging-core", "netflix:pandora-java-client", "netflix:platform-core", "netflix:recs-toolkit-protocol", "netflix:thumbs-datamodel", "netflix:videometadata-client", "netflix:vmscodeanalysistools", "org.springframework.boot:spring-boot-actuator-autoconfigure", "org.springframework.boot:spring-boot-starter-json"]}, "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor": {"locked": "2.20.0", "transitive": ["com.amazonaws:aws-java-sdk-core", "netflix:mantis-realtime-events"]}, "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml": {"locked": "2.20.0", "transitive": ["io.swagger.core.v3:swagger-core-jakarta"]}, "com.fasterxml.jackson.datatype:jackson-datatype-guava": {"locked": "2.20.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-service-layer", "netflix:contentpreview-dataprovider", "netflix:cpeauthorization-common", "netflix:pandora-java-client"]}, "com.fasterxml.jackson.datatype:jackson-datatype-jdk8": {"locked": "2.20.0", "transitive": ["netflix.grpc:netflix-grpc-admin", "netflix:mantis-realtime-events", "netflix:recs-toolkit-protocol", "org.springframework.boot:spring-boot-starter-json"]}, "com.fasterxml.jackson.datatype:jackson-datatype-joda": {"locked": "2.20.0", "transitive": ["netflix:cpeauthorization-common", "netflix:pandora-java-client"]}, "com.fasterxml.jackson.datatype:jackson-datatype-jsr310": {"locked": "2.20.0", "transitive": ["com.netflix.sbndevmetrics:sbn-dev-metrics-client", "com.netflix.spring:spring-boot-netflix", "io.swagger.core.v3:swagger-core-jakarta", "netflix:cpeauthorization-common", "org.springframework.boot:spring-boot-actuator-autoconfigure", "org.springframework.boot:spring-boot-starter-json"]}, "com.fasterxml.jackson.jaxrs:jackson-jaxrs-base": {"locked": "2.20.0", "transitive": ["com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider"]}, "com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider": {"locked": "2.20.0", "transitive": ["netflix:cpeauthorization-common"]}, "com.fasterxml.jackson.module:jackson-module-afterburner": {"locked": "2.20.0", "transitive": ["com.netflix.hystrix:hystrix-serialization", "netflix:api-service-layer", "netflix:mantis-realtime-events", "netflix:map-datamodel", "netflix:map-logging-core"]}, "com.fasterxml.jackson.module:jackson-module-jaxb-annotations": {"locked": "2.20.0", "transitive": ["com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider", "com.netflix.ksclient:ksclient-core"]}, "com.fasterxml.jackson.module:jackson-module-kotlin": {"locked": "2.20.0", "transitive": ["netflix.falcor.schema:falcor-schema-ast-model"]}, "com.fasterxml.jackson.module:jackson-module-parameter-names": {"locked": "2.20.0", "transitive": ["org.springframework.boot:spring-boot-starter-json"]}, "com.fasterxml.uuid:java-uuid-generator": {"locked": "5.1.0", "transitive": ["com.netflix.spring:spring-boot-netflix-logging"]}, "com.fasterxml.woodstox:woodstox-core": {"locked": "6.7.0", "transitive": ["com.netflix.s3authsigncde:s3-auth-sign-client"]}, "com.fasterxml:classmate": {"locked": "1.5.1", "transitive": ["org.hibernate.validator:hibernate-validator"]}, "com.flipkart.zjsonpatch:zjsonpatch": {"locked": "0.4.16", "transitive": ["netflix:api-global-dependencies"]}, "com.github.andrewoma.dexx:dexx-collections": {"locked": "0.2", "transitive": ["com.github.vlsi.compactmap:compactmap"]}, "com.github.ben-manes.caffeine:caffeine": {"locked": "3.2.2", "transitive": ["com.netflix.supermarket:supermarket-cache-client", "com.netflix.ust:ust-access", "netflix:api-global-dependencies", "netflix:entityindex", "netflix:search_common_630", "netflix:search_common_core"]}, "com.github.fzakaria:ascii85": {"locked": "1.2", "transitive": ["com.netflix.evcache:evcache-core"]}, "com.github.javaparser:javaparser-core": {"locked": "3.26.2", "transitive": ["com.netflix.edpr:java-parser"]}, "com.github.luben:zstd-jni": {"locked": "1.5.7-4", "transitive": ["com.netflix.dgw:dgw-dal-common-client", "com.netflix.ksclient:ksclient-core-kafka", "org.apache.kafka:kafka-clients"]}, "com.github.npathai:hamcrest-optional": {"locked": "1.0", "transitive": ["com.netflix.peas:peas-test-spectator"]}, "com.github.stephenc.high-scale-lib:high-scale-lib": {"locked": "1.1.2", "transitive": ["netflix:base-explorer", "netflix:nf-eventbus"]}, "com.github.stephenc.jcip:jcip-annotations": {"locked": "1.0-1", "transitive": ["com.nimbusds:nimbus-jose-jwt", "com.nimbusds:oauth2-oidc-sdk"]}, "com.github.vlsi.compactmap:compactmap": {"locked": "2.0", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.google.android:annotations": {"locked": "*******", "transitive": ["netflix.io.grpc:grpc-core-nflx"]}, "com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["com.netflix.ab:aballocator-proto-definition", "com.netflix.venkman:venkman-proto-definition", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-testkit", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.api:api-common": {"locked": "1.10.6", "transitive": ["com.spotify:futures-extra"]}, "com.google.auth:google-auth-library-credentials": {"locked": "1.22.0", "transitive": ["com.google.auth:google-auth-library-oauth2-http", "netflix.io.grpc:grpc-auth-nflx"]}, "com.google.auth:google-auth-library-oauth2-http": {"locked": "1.22.0", "transitive": ["netflix.io.grpc:grpc-alts-nflx"]}, "com.google.auto.service:auto-service-annotations": {"locked": "1.1.1", "transitive": ["com.amazonaws:aws-xray-recorder-sdk-core"]}, "com.google.auto.value:auto-value-annotations": {"locked": "1.10.4", "transitive": ["com.amazonaws:aws-xray-recorder-sdk-core", "com.google.api:api-common", "com.google.auth:google-auth-library-oauth2-http", "netflix.io.grpc:grpc-xds-nflx"]}, "com.google.code.findbugs:jFormatString": {"locked": "3.0.0", "transitive": ["com.netflix.gs2:group-service-2-client-guice", "com.netflix.gs2:group-service-2-proto-definition", "netflix:gps-client-grpc-page", "netflix:gps-client-grpc-page-ext", "netflix:gps-client-page-proto-definition"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.api:api-common", "com.google.auth:google-auth-library-oauth2-http", "com.google.http-client:google-http-client", "com.netflix.archaius:archaius-core", "com.netflix.eureka:eureka-client", "com.netflix.netflix-commons:netflix-infix", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:algo-commons-core", "netflix:algo-commons-protos", "netflix:algo-metrics", "netflix:countryset", "netflix:curator4-recipes-shaded", "netflix:dts-client", "netflix:dts-standalone-common", "netflix:netflix-config", "netflix:recs-toolkit-core", "netflix:streaming-metrics-core", "org.reflections:reflections"]}, "com.google.code.gson:gson": {"locked": "2.13.1", "transitive": ["com.google.http-client:google-http-client-gson", "com.netflix.hendrix:hendrix-model-gson", "com.netflix.karyon:karyon2-admin-web", "com.netflix.netflix-commons:netflix-infix", "io.gsonfire:gson-fire", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-json", "netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-xds-nflx", "netflix:algo-commons-core", "netflix:api-global-dependencies", "netflix:cpeauthorization-client-common", "netflix:dts-client", "netflix:fit-impl", "netflix:gandalf-agent-embedded", "netflix:gandalf-authz-client", "netflix:group-attribute", "netflix:metatron-common", "netflix:metatron-decrypt", "netflix:nf-eventbus", "netflix:pandora-java-client", "netflix:streaming-metrics"]}, "com.google.code.typica:typica": {"locked": "1.5.2a", "transitive": ["netflix:platform-management"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.40.0", "transitive": ["com.github.ben-manes.caffeine:caffeine", "com.google.auth:google-auth-library-oauth2-http", "com.google.code.gson:gson", "com.google.guava:guava", "com.google.http-client:google-http-client", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-grpclb-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-netty-shaded-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "com.google.guava:failureaccess": {"locked": "1.0.3", "transitive": ["com.google.guava:guava"]}, "com.google.guava:guava": {"locked": "33.4.8-jre", "transitive": ["com.fasterxml.jackson.datatype:jackson-datatype-guava", "com.google.api:api-common", "com.netflix.ab:aballocator-proto-definition", "com.netflix.archaius:archaius-core", "com.netflix.cryptex2:cryptex2-client-jvmonly-common", "com.netflix.hendrix:hendrix-core", "com.netflix.inject:guice-all", "com.netflix.microcontext:microcontext-init", "com.netflix.netflix-commons:netflix-infix", "com.netflix.peas:peas-test-spectator", "com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-core", "com.netflix.ribbon:ribbon-httpclient", "com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.spring:spring-boot-netflix-discovery", "com.netflix.vxs:vxs-proto-definition", "com.netflix.workloadfacts:workload-facts-proto-definition-workload-core", "com.netflix.zuul.push:zuul-push-api", "com.spotify:futures-extra", "netflix.grpc:netflix-grpc-common", "netflix:akmsclient", "netflix:algo-metrics", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:base-server", "netflix:base-server-plugins", "netflix:cgl-common-lite", "netflix:demograph-core", "netflix:group-attribute", "netflix:images-client-fallbacks", "netflix:images-client-fallbacks-guice", "netflix:images-client-guice", "netflix:images-proto-definition", "netflix:map-annotation-constants", "netflix:map-datamodel", "netflix:metatron-common", "netflix:metatron-ipc", "netflix:nf-eventbus", "netflix:nflibrary", "netflix:nfmantis-serde", "netflix:nsac-common", "netflix:pandora-java-client", "netflix:passport-actions-insights", "netflix:pds-commons-viewing-history-proto-datamodel", "netflix:platform-aws", "netflix:platform-core", "netflix:platform-ipc", "netflix:platform-logimpl", "netflix:platform-management", "netflix:platform-utils", "netflix:recs-toolkit-core", "netflix:recs-toolkit-protocol", "netflix:search-service-common", "netflix:streaming-client", "netflix:streaming-metrics", "netflix:streaming-metrics-core", "netflix:videometadata-client"]}, "com.google.guava:listenablefuture": {"locked": "9999.0-empty-to-avoid-conflict-with-guava", "transitive": ["com.google.guava:guava"]}, "com.google.http-client:google-http-client": {"locked": "1.43.3", "transitive": ["com.google.auth:google-auth-library-oauth2-http", "com.google.http-client:google-http-client-gson"]}, "com.google.http-client:google-http-client-gson": {"locked": "1.43.3", "transitive": ["com.google.auth:google-auth-library-oauth2-http"]}, "com.google.j2objc:j2objc-annotations": {"locked": "3.0.0", "transitive": ["com.google.guava:guava", "com.google.http-client:google-http-client"]}, "com.google.re2j:re2j": {"locked": "1.7", "transitive": ["build.buf.protoc-gen-validate:pgv-java-stub", "netflix.io.grpc:grpc-xds-nflx"]}, "com.googlecode.libphonenumber:libphonenumber": {"locked": "8.13.55", "transitive": ["com.netflix.partnersub.consors:consors-shared"]}, "com.googlecode.matrix-toolkits-java:mtj": {"locked": "1.0.3", "transitive": ["gov.sandia.foundry:gov-sandia-cognition-common-core"]}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1", "transitive": ["netflix:api", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:dna-client", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services", "netflix:dna-test"]}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0", "transitive": ["netflix:api", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:dna-client", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services", "netflix:dna-test"]}, "com.graphql-java:graphql-java": {"locked": "22.3", "transitive": ["com.netflix.graphql.dgs.codegen:graphql-dgs-codegen-shared-core"]}, "com.graphql-java:java-dataloader": {"locked": "5.0.1", "transitive": ["com.graphql-java:graphql-java"]}, "com.ibm.icu:icu4j": {"locked": "73.2", "transitive": ["netflix:nfi18n-core", "org.apache.lucene:lucene-analyzers-icu"]}, "com.jamesmurty.utils:java-xmlbuilder": {"locked": "1.1", "transitive": ["net.java.dev.jets3t:jets3t"]}, "com.jayway.jsonpath:json-path": {"locked": "2.9.0", "transitive": ["netflix:dna-test"]}, "com.ncr.teradata:tdgssconfig": {"locked": "14.10.00.26", "transitive": ["netflix:tools-utils"]}, "com.ncr.teradata:terajdbc4": {"locked": "14.10.00.26", "transitive": ["netflix:tools-utils"]}, "com.netflix.ab:aballocator-client-guice": {"locked": "1.1094.23", "transitive": ["com.netflix.microcontext:microcontext-resolver", "netflix:api-global-dependencies"]}, "com.netflix.ab:aballocator-common": {"locked": "1.1094.23", "transitive": ["com.netflix.ab:aballocator-client-guice"]}, "com.netflix.ab:aballocator-proto-definition": {"locked": "1.1094.23", "transitive": ["com.netflix.ab:aballocator-client-guice", "com.netflix.ab:aballocator-common", "com.netflix.evidence:dataprism", "com.netflix.evidence:dataprism-spring", "com.netflix.originals.row.filters:originals-row-filters-api", "com.netflix.originals.row.filters:originals-row-filters-core", "com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.ust:ust-model", "com.netflix.vxs:vxs-proto-definition", "netflix:cms-proto-definition", "netflix:demograph-core", "netflix:images-client-fallbacks", "netflix:images-client-fallbacks-guice", "netflix:images-client-guice", "netflix:images-proto-definition", "netflix:streaming-cdnurl"]}, "com.netflix.ads.openrtbprotov2:openrtb-proto-v2-proto-definition": {"locked": "2.46.0", "transitive": ["com.netflix.playapi:playapi-proto-definition"]}, "com.netflix.adsnativeadarbiter:ads-native-ad-arbiter-client": {"locked": "0.347.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.adsnativeadarbiter:ads-native-ad-arbiter-proto-definition": {"locked": "0.347.0", "transitive": ["com.netflix.adsnativeadarbiter:ads-native-ad-arbiter-client"]}, "com.netflix.apicollection:apicollection-proto-definition": {"locked": "1.91.0", "transitive": ["com.netflix.ust:ust-model"]}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.641.0", "transitive": ["com.netflix.appregistry.rt:appregistry-rt-client", "com.netflix.appregistry.rt:appregistry-rt-proto-definition", "com.netflix.microcontext:microcontext-model", "com.netflix.pacs:pacs-proto-definition", "com.netflix.passport.test:passport-test-core", "netflix:passport"]}, "com.netflix.appregistry.rt:appregistry-rt-client": {"locked": "0.538.0", "transitive": ["com.netflix.ust:ust-access"]}, "com.netflix.appregistry.rt:appregistry-rt-common": {"locked": "0.538.0", "transitive": ["com.netflix.appregistry.rt:appregistry-rt-client"]}, "com.netflix.appregistry.rt:appregistry-rt-proto-definition": {"locked": "0.538.0", "transitive": ["com.netflix.appregistry.rt:appregistry-rt-client", "com.netflix.appregistry.rt:appregistry-rt-common"]}, "com.netflix.archaius:archaius-core": {"locked": "0.7.12", "transitive": ["com.netflix.archaius:archaius2-archaius1-bridge", "com.netflix.blitz4j:blitz4j", "com.netflix.eureka:eureka-client", "com.netflix.governator:governator-arch<PERSON><PERSON>", "com.netflix.hystrix:hystrix-core", "com.netflix.karyon:karyon-eureka", "com.netflix.karyon:karyon2-archaius", "com.netflix.ksclient:ksclient-core", "com.netflix.ksclient:platform-chukwaClient-legacy", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-eureka", "netflix:algo-commons-core", "netflix:algo-metrics", "netflix:api-global-dependencies", "netflix:eventbus", "netflix:map-datamodel", "netflix:netflix-config", "netflix:nf-eventbus", "netflix:nflibrary", "netflix:nfsso", "netflix:platform-core", "netflix:request-expiry-impl", "netflix:search_common_630", "netflix:search_common_core", "netflix:streaming-metrics-core"]}, "com.netflix.archaius:archaius2-api": {"locked": "2.8.7", "transitive": ["com.netflix.ab:aballocator-client-guice", "com.netflix.archaius:archaius2-core", "com.netflix.cookie.partner:partner-cookie-core", "com.netflix.dcms:dcms-rt-common", "com.netflix.dgw.kv:dgw-kv-client-interceptor", "com.netflix.eureka:eureka-client-archaius2", "com.netflix.evcache:evcache-client", "com.netflix.evidence:dataprism", "com.netflix.ge.common:common-service", "com.netflix.gps.pagerouterservice:gps-page-router-client", "com.netflix.group.fallback.service:group-fallback-client", "com.netflix.gs2:group-service-2-client-guice", "com.netflix.hank:hank-client", "com.netflix.hendrix:hendrix-core", "com.netflix.ksclient:ksclient-core", "com.netflix.ksclient:platform-chukwaClient-legacy", "com.netflix.mantis:nfmantis-publish-grpc-client", "com.netflix.mesh:mesh-integration-java", "com.netflix.microcontext:microcontext-access", "com.netflix.microcontext:microcontext-init", "com.netflix.microcontext:microcontext-resolver", "com.netflix.nfkafka:nfkafka-producer", "com.netflix.originals.row.filters:originals-row-filters-core", "com.netflix.pacs:pacs-cache", "com.netflix.pacs:pacs-client", "com.netflix.peas:attestation-results-core", "com.netflix.peas:peas-auth-events-logging", "com.netflix.peas:peas-dials", "com.netflix.playapi:playapi-client", "com.netflix.plex:plex-member-client-guice", "com.netflix.s3:nfs3-config", "com.netflix.search.pash:pash-client", "com.netflix.simone:okja-client-guice", "com.netflix.sloar:sloar-client", "com.netflix.spring:spring-boot-netflix", "com.netflix.spring:spring-boot-netflix-configuration", "com.netflix.spring:spring-boot-netflix-metrics", "com.netflix.spring:spring-boot-netflix-starter-library", "com.netflix.supermarket:supermarket-cache-client", "com.netflix.vps:vps-client", "com.netflix.webclient:netflix-webclient-spring-boot", "io.mantisrx:mantis-publish-core", "netflix.contextflow:contextflow-access", "netflix.contextflow:contextflow-metrics", "netflix.contextflow:contextflow-transport", "netflix.ipc.metrics:ipc-metrics-core", "netflix.ipc.metrics:ipc-metrics-servlet", "netflix:cis-aws-sdk-java-instrumentor-core", "netflix:cms-client-guice-nocml", "netflix:eureka2-grpc-client-guice", "netflix:gps-client-grpc-page-ext", "netflix:gutenberg-grpc-client", "netflix:images-client-fallbacks", "netflix:images-client-fallbacks-guice", "netflix:images-client-guice", "netflix:nfcurator4", "netflix:nimble-int-java", "netflix:nsac-client", "netflix:nsac-common", "netflix:platform-tracing-zipkin"]}, "com.netflix.archaius:archaius2-archaius1-bridge": {"locked": "2.8.7", "transitive": ["com.netflix.spring:spring-boot-netflix-configuration", "netflix:nf-archaius2-platform-bridge"]}, "com.netflix.archaius:archaius2-commons-configuration": {"locked": "2.8.7", "transitive": ["com.netflix.archaius:archaius2-archaius1-bridge"]}, "com.netflix.archaius:archaius2-core": {"locked": "2.8.7", "transitive": ["com.netflix.archaius:archaius2-archaius1-bridge", "com.netflix.archaius:archaius2-commons-configuration", "com.netflix.archaius:archaius2-guice", "com.netflix.archaius:archaius2-persisted2", "com.netflix.cookie.partner:partner-cookie-core", "com.netflix.dgw:dgw-dal-common-manual-client", "com.netflix.eureka:eureka-client-archaius2", "com.netflix.evcache:evcache-client", "com.netflix.evcache:evcache-core", "com.netflix.ksclient:ksclient-lifecycle", "com.netflix.mantis:nfmantis-publish-common", "com.netflix.peas:attestation-results-core", "com.netflix.peas:peas-dials", "com.netflix.runtime.metadata:runtime-metadata-client", "com.netflix.runtime:health-integrations", "com.netflix.s3:nfs3-common", "com.netflix.s3authsigncde:s3-auth-sign-client", "com.netflix.s3authsigncde:s3-auth-sign-common", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spring:spring-boot-netflix", "com.netflix.spring:spring-boot-netflix-configuration", "com.netflix.spring:spring-boot-netflix-starter-library", "com.netflix.textevidence:text-evidence-common", "io.mantisrx:mantis-publish-core", "netflix.ipc.metrics:ipc-metrics-lifecycle", "netflix:algo-commons-core", "netflix:atlas-client", "netflix:cryptex-agent-guice", "netflix:dts-common", "netflix:dts-datamodel", "netflix:eureka2-grpc-client-guice", "netflix:nimble-int-java", "netflix:passport", "netflix:platform-tracing-zipkin", "netflix:streaming-steeredcdnurl"]}, "com.netflix.archaius:archaius2-guice": {"locked": "2.8.7", "transitive": ["com.netflix.evolutionobservation:evolution-observation", "com.netflix.runtime:health-guice", "netflix.grpc:netflix-grpc-archaius", "netflix.grpc:netflix-grpc-runtime-guice", "netflix.ipc.metrics:ipc-metrics-core", "netflix.ipc.metrics:ipc-metrics-lifecycle", "netflix:eureka2-eureka1-registration-guice", "netflix:nf-archaius2"]}, "com.netflix.archaius:archaius2-persisted2": {"locked": "2.8.7", "transitive": ["com.netflix.evcache:evcache-core", "com.netflix.spring:spring-boot-netflix-configuration", "netflix:nf-archaius2"]}, "com.netflix.blitz4j:blitz4j": {"locked": "1.42.0", "transitive": ["com.netflix.ksclient:ksclient-core", "com.netflix.ksclient:platform-chukwaClient-legacy", "netflix:platform-core", "netflix:platform-management"]}, "com.netflix.bulldozer:bulldozer-proto-definition": {"locked": "1.6.183", "transitive": ["com.netflix.messaging.varys:varys-proto-definition", "com.netflix.search.pash:pash-proto-definition"]}, "com.netflix.chaski2:chaski2-api": {"locked": "1.3.0", "transitive": ["com.netflix.chaski2:chaski2-core", "com.netflix.ksclient:ksclient-core-kafka"]}, "com.netflix.chaski2:chaski2-core": {"locked": "1.3.0", "transitive": ["com.netflix.chaski2:chaski2-parser-jackson", "com.netflix.ksclient:ksclient-core-kafka"]}, "com.netflix.chaski2:chaski2-parser-jackson": {"locked": "1.3.0", "transitive": ["com.netflix.ksclient:ksclient-core-kafka"]}, "com.netflix.chaski3:chaski3-api": {"locked": "0.25.1", "transitive": ["com.netflix.chaski3:chaski3-core", "com.netflix.chaski3:chaski3-platformrecord", "com.netflix.chaski3:chaski3-serde-jackson", "com.netflix.ksclient:ksclient-core", "com.netflix.ksclient:ksclient-core-kafka"]}, "com.netflix.chaski3:chaski3-core": {"locked": "0.25.1", "transitive": ["com.netflix.chaski3:chaski3-platformrecord", "com.netflix.chaski3:chaski3-serde-jackson", "com.netflix.ksclient:ksclient-core", "com.netflix.ksclient:ksclient-core-kafka"]}, "com.netflix.chaski3:chaski3-platformrecord": {"locked": "0.25.1", "transitive": ["com.netflix.chaski3:chaski3-platformrecord-serde-avro", "com.netflix.chaski3:chaski3-platformrecord-serde-jackson", "com.netflix.ksclient:ksclient-core"]}, "com.netflix.chaski3:chaski3-platformrecord-serde-avro": {"locked": "0.25.1", "transitive": ["com.netflix.chaski3:chaski3-platformrecord-serde-jackson", "com.netflix.ksclient:ksclient-core"]}, "com.netflix.chaski3:chaski3-platformrecord-serde-jackson": {"locked": "0.25.1", "transitive": ["com.netflix.ksclient:ksclient-core"]}, "com.netflix.chaski3:chaski3-serde-jackson": {"locked": "0.25.1", "transitive": ["com.netflix.ksclient:ksclient-core", "com.netflix.ksclient:ksclient-core-kafka"]}, "com.netflix.chaski:chaski-parser": {"locked": "1.2", "transitive": ["com.netflix.chaski:chaski-parser-jackson", "netflix:platform-chukwaClient"]}, "com.netflix.chaski:chaski-parser-jackson": {"locked": "1.2", "transitive": ["com.netflix.ksclient:ksclient-core-kafka", "com.netflix.ksclient:platform-chukwaClient-legacy", "netflix:platform-chukwaClient"]}, "com.netflix.chronos:chronos-client": {"locked": "1.3.2", "transitive": ["netflix:cinder-core"]}, "com.netflix.chronos:chronos-proto-definition": {"locked": "1.3.2", "transitive": ["com.netflix.chronos:chronos-client"]}, "com.netflix.concurrency-limits:concurrency-limits-core": {"locked": "0.5.3", "transitive": ["com.netflix.concurrency-limits:concurrency-limits-grpc", "com.netflix.concurrency-limits:concurrency-limits-servlet", "com.netflix.concurrency-limits:concurrency-limits-spectator", "com.netflix.spring:spring-boot-netflix-starter-rest-server", "netflix.grpc:netflix-grpc-common"]}, "com.netflix.concurrency-limits:concurrency-limits-grpc": {"locked": "0.5.3", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-metatron"]}, "com.netflix.concurrency-limits:concurrency-limits-servlet": {"locked": "0.5.3", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-rest-server"]}, "com.netflix.concurrency-limits:concurrency-limits-spectator": {"locked": "0.5.3", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-rest-server", "netflix.grpc:netflix-grpc-spectator"]}, "com.netflix.contentgroup:content-group-definition": {"locked": "3.9.0", "transitive": ["com.netflix.contentgroup:content-group-expression-proto", "com.netflix.memberattribute:memberattribute-proto-definition", "com.netflix.pacs:pacs-contentgroups-utils", "com.netflix.supermarket:supermarket-entityset-base"]}, "com.netflix.contentgroup:content-group-expression-proto": {"locked": "3.9.0", "transitive": ["com.netflix.contentgroup:content-group-expression-utils", "com.netflix.memberattribute:memberattribute-proto-definition", "com.netflix.pacs:pacs-client", "com.netflix.pacs:pacs-contentgroups-utils", "com.netflix.pacs:pacs-proto-definition", "com.netflix.supermarket:supermarket-entityset-base"]}, "com.netflix.contentgroup:content-group-expression-utils": {"locked": "3.9.0", "transitive": ["com.netflix.pacs:pacs-contentgroups-utils"]}, "com.netflix.contextuallog.relay:contextual-log-token-proto-definition": {"locked": "0.11.0", "transitive": ["com.netflix.page:noir-page-proto-definition"]}, "com.netflix.cookie.partner:partner-cookie-core": {"locked": "0.53.0", "transitive": ["com.netflix.venkman:venkman-proto-definition"]}, "com.netflix.cpe.merchintention:merchintention-cinderclientcodegen": {"locked": "2.267.0", "transitive": ["com.netflix.cpe.merchintention:merchintention-merchintentlanguages-client"]}, "com.netflix.cpe.merchintention:merchintention-merchintentlanguages-client": {"locked": "2.267.0", "transitive": ["com.netflix.evidence:dataprism"]}, "com.netflix.cryptex2:cryptex2-client-jvmonly-common": {"locked": "1.408.0", "transitive": ["com.netflix.cryptex2:cryptex2-client-jvmonly-sbn"]}, "com.netflix.cryptex2:cryptex2-client-jvmonly-sbn": {"locked": "1.408.0", "transitive": ["netflix:api-global-dependencies", "netflix:streaming-cdnurl"]}, "com.netflix.cryptex2:cryptex2-common": {"locked": "1.408.0", "transitive": ["com.netflix.cryptex2:cryptex2-client-jvmonly-common", "com.netflix.cryptex2:cryptex2-client-jvmonly-sbn"]}, "com.netflix.dcms:dcms-cinder-client": {"locked": "1.932.0", "transitive": ["netflix:dts-client"]}, "com.netflix.dcms:dcms-cinder-datamodel": {"locked": "1.932.0", "transitive": ["com.netflix.dcms:dcms-cinder-client", "netflix:dts-client"]}, "com.netflix.dcms:dcms-common": {"locked": "1.932.0", "transitive": ["netflix:dts-client"]}, "com.netflix.dcms:dcms-rt-client": {"locked": "1.794.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.dcms:dcms-rt-common": {"locked": "1.794.0", "transitive": ["com.netflix.dcms:dcms-rt-client"]}, "com.netflix.dcms:dcms-rt-proto-definition": {"locked": "1.794.0", "transitive": ["com.netflix.dcms:dcms-rt-client", "com.netflix.dcms:dcms-rt-common", "com.netflix.ust:ust-model"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.932.0", "transitive": ["com.netflix.dcms:dcms-common", "com.netflix.dcms:dcms-rt-proto-definition", "com.netflix.microcontext:microcontext-model", "netflix:dts-client"]}, "com.netflix.dgw.kv:dgw-kv-client": {"locked": "2.22.1", "transitive": ["com.netflix.dgw.kv:dgw-kv-client-interceptor", "com.netflix.partnersub.consors:consors-shared", "com.netflix.zuul.push:zuul-push-core", "netflix:api-global-dependencies"]}, "com.netflix.dgw.kv:dgw-kv-client-interceptor": {"locked": "2.22.1", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.dgw.kv:dgw-kv-common": {"locked": "2.22.1", "transitive": ["com.netflix.dgw.kv:dgw-kv-client"]}, "com.netflix.dgw.kv:dgw-kv-proto-definition": {"locked": "2.22.1", "transitive": ["com.netflix.dgw.kv:dgw-kv-client", "com.netflix.dgw.kv:dgw-kv-client-interceptor", "com.netflix.dgw.kv:dgw-kv-common", "com.netflix.hydrus:hydrus-proto-definition", "netflix:bookmark-query-service-proto-definition"]}, "com.netflix.dgw:dgw-dal-common-client": {"locked": "1.8.2", "transitive": ["com.netflix.dgw.kv:dgw-kv-client", "com.netflix.dgw.kv:dgw-kv-common"]}, "com.netflix.dgw:dgw-dal-common-configuration": {"locked": "1.7.4", "transitive": ["com.netflix.dgw:dgw-dal-common-manual-client"]}, "com.netflix.dgw:dgw-dal-common-lib": {"locked": "1.8.2", "transitive": ["com.netflix.dgw.kv:dgw-kv-client", "com.netflix.dgw.kv:dgw-kv-common", "com.netflix.dgw:dgw-dal-common-client"]}, "com.netflix.dgw:dgw-dal-common-manual-client": {"locked": "1.7.4", "transitive": ["com.netflix.hollowschemadefinition:hollowschemadefinition-client", "com.netflix.zuul.push:zuul-push-core"]}, "com.netflix.dgw:dgw-dal-common-proto-definition": {"locked": "1.8.2", "transitive": ["com.netflix.dgw.kv:dgw-kv-proto-definition", "com.netflix.dgw:dgw-dal-common-client", "com.netflix.dgw:dgw-dal-common-lib"]}, "com.netflix.dgw:dgw-dal-common-shaded-lib": {"locked": "1.8.2", "transitive": ["com.netflix.dgw.kv:dgw-kv-client"]}, "com.netflix.dloc:dloc-client": {"locked": "2.0.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies", "netflix:p3-converter"]}, "com.netflix.dloc:dloc-proto-definition": {"locked": "2.0.0", "transitive": ["com.netflix.dloc:dloc-client"]}, "com.netflix.dynimo.proxy:dynimoproxy-client": {"locked": "1.3.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.dynimo.proxy:dynimoproxy-proto-definition": {"locked": "1.3.0", "transitive": ["com.netflix.dynimo.proxy:dynimoproxy-client"]}, "com.netflix.edpr:java-parser": {"locked": "2.0.1", "transitive": ["com.netflix.rawhollow:rawhollow-client-core"]}, "com.netflix.ember:ember-client": {"locked": "1.1304.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.ember:ember-proto-definition": {"locked": "1.1304.0", "transitive": ["com.netflix.ember:ember-client"]}, "com.netflix.emf:emf-proto-definition": {"locked": "0.101.0", "transitive": ["com.netflix.search.pash:pash-proto-definition"]}, "com.netflix.eureka:eureka-client": {"locked": "2.0.4", "transitive": ["com.netflix.dgw.kv:dgw-kv-client-interceptor", "com.netflix.eureka:eureka-client-archaius2", "com.netflix.evcache:evcache-client", "com.netflix.karyon:karyon-eureka", "com.netflix.karyon:karyon2-admin-eureka-plugin", "com.netflix.karyon:karyon2-eureka", "com.netflix.ksclient:platform-chukwaClient-legacy", "com.netflix.ribbon:ribbon-eureka", "com.netflix.spring:spring-boot-netflix-discovery", "com.netflix.webclient:netflix-webclient-spring-boot", "netflix:api-global-dependencies", "netflix:nf-eureka-client", "netflix:platform-ipc", "netflix:streaming-client", "org.springframework.cloud:spring-cloud-netflix-eureka-client", "org.springframework.cloud:spring-cloud-starter-netflix-eureka-client"]}, "com.netflix.eureka:eureka-client-archaius2": {"locked": "2.0.4", "transitive": ["com.netflix.evcache:evcache-client", "com.netflix.evcache:evcache-core", "com.netflix.nfkafka:nfkafka-producer", "netflix:nf-eureka-client"]}, "com.netflix.evcache:evcache-client": {"locked": "5.23.0-rc.1", "transitive": ["netflix:evcache-nflx-client"]}, "com.netflix.evcache:evcache-core": {"locked": "5.23.0-rc.1", "transitive": ["com.netflix.evcache:evcache-client", "com.netflix.evcache:evcache-zipkin-tracing", "com.netflix.spring:spring-boot-netflix-evcache", "com.netflix.spring:spring-boot-netflix-starter-evcache", "com.netflix.ust:ust-access", "netflix:evcache-nflx-client"]}, "com.netflix.evcache:evcache-zipkin-tracing": {"locked": "5.26.0", "transitive": ["com.netflix.spring:spring-boot-netflix-tracing"]}, "com.netflix.evidence:dataprism": {"locked": "2.13.0", "transitive": ["com.netflix.evidence:dataprism-guice", "com.netflix.evidence:dataprism-spring", "com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.evidence:dataprism-guice": {"locked": "2.11.0", "transitive": ["com.netflix.ust:ust-access"]}, "com.netflix.evidence:dataprism-spring": {"locked": "2.13.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.evolutionmetadata:evolution-metadata-client": {"locked": "2.336.0", "transitive": ["com.netflix.hank:hank-client"]}, "com.netflix.evolutionmetadata:evolution-metadata-proto-definition": {"locked": "2.336.0", "transitive": ["com.netflix.evolutionmetadata:evolution-metadata-client", "netflix.evolution.easel:easel-proto-definition"]}, "com.netflix.evolutionobservation:evolution-observation": {"locked": "0.1297.0", "transitive": ["com.netflix.hank:hank-client"]}, "com.netflix.evolutionprotodefinition:evolution-proto-definition": {"locked": "0.1368.0", "transitive": ["com.netflix.evolutionmetadata:evolution-metadata-proto-definition", "com.netflix.evolutionobservation:evolution-observation", "com.netflix.hank:hank-client", "com.netflix.hank:hank-proto-definition", "com.netflix.messaging.bran:bran-proto-definition", "com.netflix.messaging.varys:varys-proto-definition", "com.netflix.rasel:rasel-proto-definition", "com.netflix.tela:tela-proto-definition", "netflix.evolution.easel:easel-proto-definition", "netflix.evolution:message_interfaces"]}, "com.netflix.firewood:firewood-sdk-sentry": {"locked": "0.9.2", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-logging"]}, "com.netflix.frankenstein:frankenstein-proto-definition": {"locked": "0.194.0", "transitive": ["com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.trailerpark:trailer-park-proto-definition", "netflix:images-proto-definition"]}, "com.netflix.frigga:frigga": {"locked": "0.28.0", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-metatron", "netflix:platform-core"]}, "com.netflix.ge.common:common-grpc": {"locked": "9.2.388", "transitive": ["com.netflix.plex:plex-member-client-guice", "com.netflix.plex:plex-member-proto-definition", "com.netflix.plex:plex-shared-proto-definition"]}, "com.netflix.ge.common:common-service": {"locked": "9.2.388", "transitive": ["com.netflix.ge.common:common-grpc"]}, "com.netflix.gengo:gengo-cinderclient": {"locked": "1.124.0", "transitive": ["netflix:obelix-client"]}, "com.netflix.governator:governator": {"locked": "1.17.13", "transitive": ["com.netflix.evcache:evcache-client", "com.netflix.governator:governator-arch<PERSON><PERSON>", "com.netflix.karyon:karyon-core", "com.netflix.karyon:karyon-spi", "com.netflix.karyon:karyon2-governator", "netflix:base-server", "netflix:nf-archaius2-platform-bridge", "netflix:nflibrary", "netflix:platform-ipc"]}, "com.netflix.governator:governator-api": {"locked": "1.17.13", "transitive": ["com.netflix.chaski2:chaski2-core", "com.netflix.chaski3:chaski3-core", "com.netflix.governator:governator", "com.netflix.governator:governator-core", "com.netflix.ksclient:ksclient-core", "com.netflix.ksclient:ksclient-lifecycle", "com.netflix.ksclient:ksclient-lifecycle-kafka", "com.netflix.nts.deviceperfmetrics.reader:ntsdeviceperfmetricsreader-lifecycle", "com.netflix.originals.row.filters:originals-row-filters-lifecycle", "com.netflix.runtime:health-core", "com.netflix.spring:spring-boot-netflix-starter-governator-guice-bridge", "com.netflix.zuul.push:zuul-push-lifecycle", "netflix:nfcontext-impl", "netflix:nfi18n-core", "netflix:nflibrary"]}, "com.netflix.governator:governator-archaius": {"locked": "1.17.13", "transitive": ["com.netflix.karyon:karyon-core", "com.netflix.karyon:karyon2-archaius"]}, "com.netflix.governator:governator-core": {"locked": "1.17.13", "transitive": ["com.netflix.governator:governator", "com.netflix.runtime:health-guice", "com.netflix.spring:spring-boot-netflix-governator", "netflix:eureka2-eureka1-registration-guice", "netflix:nf-archaius2"]}, "com.netflix.governator:governator-providers": {"locked": "1.17.13", "transitive": ["com.netflix.vxs:vxs-client-nofallbacks", "netflix.grpc:netflix-grpc-metatron", "netflix.grpc:netflix-grpc-metatron-netty"]}, "com.netflix.gps.pageclientslib:gps-page-clients-common": {"locked": "10.2.1", "transitive": ["com.netflix.gps.pageclientslib:gps-page-clients-nes"]}, "com.netflix.gps.pageclientslib:gps-page-clients-nes": {"locked": "10.2.1", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.gps.pagefallbackservice:gps-page-fallback-client": {"locked": "4.325.0", "transitive": ["com.netflix.ust:ust-access"]}, "com.netflix.gps.pagefallbackservice:gps-page-fallback-proto-definition": {"locked": "4.325.0", "transitive": ["com.netflix.gps.pagefallbackservice:gps-page-fallback-client"]}, "com.netflix.gps.pagerouterservice:gps-page-router-client": {"locked": "370.0.0", "transitive": ["com.netflix.gps.pageclientslib:gps-page-clients-common", "com.netflix.gps.pageclientslib:gps-page-clients-nes"]}, "com.netflix.gps.pagerouterservice:gps-page-router-proto-definition": {"locked": "370.0.0", "transitive": ["com.netflix.gps.pageclientslib:gps-page-clients-common", "com.netflix.gps.pageclientslib:gps-page-clients-nes", "com.netflix.gps.pagerouterservice:gps-page-router-client"]}, "com.netflix.graphql.dgs.codegen:graphql-dgs-codegen-shared-core": {"locked": "8.1.1", "transitive": ["com.netflix.sbndevmetrics:sbn-dev-metrics-client"]}, "com.netflix.group.fallback.service:group-fallback-client": {"locked": "1.653.1", "transitive": ["com.netflix.gs2:group-service-2-client-guice"]}, "com.netflix.group.fallback.service:group-fallback-proto-definition": {"locked": "1.653.1", "transitive": ["com.netflix.group.fallback.service:group-fallback-client"]}, "com.netflix.growth.paymentexperiences:paymentexperiences-grpc-client": {"locked": "1.1659.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.growth.paymentexperiences:paymentexperiences-proto-definition": {"locked": "1.1659.0", "transitive": ["com.netflix.growth.paymentexperiences:paymentexperiences-grpc-client"]}, "com.netflix.growth.planspricing:growthplanspricing-grpc-client": {"locked": "3.639.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.growth.planspricing:growthplanspricing-proto-definition": {"locked": "3.639.0", "transitive": ["com.netflix.growth.planspricing:growthplanspricing-grpc-client"]}, "com.netflix.gs2:group-service-2-client-guice": {"locked": "323.0.126", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.gs2:group-service-2-proto-definition": {"locked": "323.0.126", "transitive": ["com.netflix.gs2:group-service-2-client-guice"]}, "com.netflix.hank:hank-client": {"locked": "1.1481.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.hank:hank-proto-definition": {"locked": "1.1481.0", "transitive": ["com.netflix.hank:hank-client"]}, "com.netflix.helpdgs:help-dgs-client": {"locked": "0.41.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.helpdgs:help-dgs-proto-definition": {"locked": "0.41.0", "transitive": ["com.netflix.helpdgs:help-dgs-client"]}, "com.netflix.hendrix:hendrix-core": {"locked": "3.39.0", "transitive": ["com.netflix.hendrix:hendrix-model-gson"]}, "com.netflix.hendrix:hendrix-model-gson": {"locked": "3.39.0", "transitive": ["com.netflix.contentgroup:content-group-expression-utils"]}, "com.netflix.hollow:hollow": {"locked": "7.14.21", "transitive": ["com.netflix.hollowschemadefinition:hollowschemadefinition-client", "com.netflix.rawhollow:rawhollow-client-core", "com.netflix.turboservice:turboservice-client", "com.netflix.vms:live-event-metadata-client", "netflix:cinder-core"]}, "com.netflix.hollowschemadefinition:hollowschemadefinition-client": {"locked": "1.368.0", "transitive": ["com.netflix.rawhollow:rawhollow-client-core"]}, "com.netflix.hollowschemadefinition:hollowschemadefinition-proto-definition": {"locked": "1.368.0", "transitive": ["com.netflix.hollowschemadefinition:hollowschemadefinition-client"]}, "com.netflix.hollowtransformer:approved-vmsinmemory-client": {"locked": "0.1.2", "transitive": ["netflix:videometadata-client"]}, "com.netflix.hydrus:hydrus-client": {"locked": "1.273.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.hydrus:hydrus-proto-definition": {"locked": "1.273.0", "transitive": ["com.netflix.hydrus:hydrus-client"]}, "com.netflix.hystrix:hystrix-core": {"locked": "1.5.18", "transitive": ["com.netflix.hystrix:hystrix-metrics-event-stream", "com.netflix.hystrix:hystrix-serialization", "com.netflix.hystrix:hystrix-servo-metrics-publisher", "com.netflix.partnersub.consors:consors-client", "com.netflix.ust:ust-access", "netflix:dhs-common", "netflix:obelix-client", "netflix:platform-dependency-command", "netflix:platform-ipc"]}, "com.netflix.hystrix:hystrix-metrics-event-stream": {"locked": "1.5.9", "transitive": ["com.netflix.growth.paymentexperiences:paymentexperiences-grpc-client", "netflix:platform-dependency-command"]}, "com.netflix.hystrix:hystrix-serialization": {"locked": "1.5.9", "transitive": ["com.netflix.growth.paymentexperiences:paymentexperiences-grpc-client", "com.netflix.hystrix:hystrix-metrics-event-stream"]}, "com.netflix.hystrix:hystrix-servo-metrics-publisher": {"locked": "1.5.9", "transitive": ["com.netflix.growth.paymentexperiences:paymentexperiences-grpc-client", "netflix:platform-dependency-command"]}, "com.netflix.identity:identity-context-api": {"locked": "1.11.2", "transitive": ["netflix:gandalf-authz-client"]}, "com.netflix.identity:identity-context-xfcc": {"locked": "1.11.2", "transitive": ["com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-sso-authn-embedded", "netflix.grpc:netflix-grpc-metatron"]}, "com.netflix.imagesfallbackgenerator:images-fallback-datamodel": {"locked": "1.0.1", "transitive": ["com.netflix.imagesfallbackgenerator:images-fallback-generator-client"]}, "com.netflix.imagesfallbackgenerator:images-fallback-generator-client": {"locked": "1.0.1", "transitive": ["netflix:images-client-fallbacks", "netflix:images-client-fallbacks-guice"]}, "com.netflix.inject:guice-all": {"locked": "5.1.0", "transitive": ["com.netflix.archaius:archaius2-guice", "com.netflix.chaski2:chaski2-core", "com.netflix.chaski3:chaski3-core", "com.netflix.dgw.kv:dgw-kv-client-interceptor", "com.netflix.evcache:evcache-core", "com.netflix.ge.common:common-grpc", "com.netflix.ge.common:common-service", "com.netflix.governator:governator-core", "com.netflix.governator:governator-providers", "com.netflix.gps.pageclientslib:gps-page-clients-common", "com.netflix.gps.pageclientslib:gps-page-clients-nes", "com.netflix.gps.pagerouterservice:gps-page-router-client", "com.netflix.group.fallback.service:group-fallback-client", "com.netflix.gs2:group-service-2-client-guice", "com.netflix.jaxrs:netflix-jaxrs-extensions-exceptionmapper", "com.netflix.karyon:karyon2-admin", "com.netflix.ksclient:ksclient-core", "com.netflix.ksclient:ksclient-lifecycle", "com.netflix.ksclient:ksclient-lifecycle-kafka", "com.netflix.mantis:nfmantis-publish-grpc-client", "com.netflix.netflix-commons:netflix-eventbus-bridge", "com.netflix.nfkafka:nfkafka-producer", "com.netflix.nts.deviceperfmetrics.reader:ntsdeviceperfmetricsreader-lifecycle", "com.netflix.originals.row.filters:originals-row-filters-lifecycle", "com.netflix.pacs:pacs-client", "com.netflix.peas:attestation-results-core", "com.netflix.plex:plex-member-client-guice", "com.netflix.search.pash:pash-client", "com.netflix.simone:okja-client-guice", "com.netflix.sloar:sloar-client", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spring:spring-boot-netflix-governator", "com.netflix.vps:vps-client", "com.netflix.zuul.push:zuul-push-lifecycle", "com.sun.jersey.contribs:jersey-guice", "netflix.grpc:netflix-grpc-name-resolver-guice", "netflix.grpc:netflix-grpc-netty", "netflix.ipc.metrics:ipc-metrics-core", "netflix.ipc.metrics:ipc-metrics-lifecycle", "netflix.ipc.metrics:ipc-metrics-servlet", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:atlas-client", "netflix:base-server", "netflix:cgl-common-lite", "netflix:cinder-lifecycle", "netflix:cms-client-guice-nocml", "netflix:contentpreview-api-guice", "netflix:contentpreview-dataprovider", "netflix:cp1-proto-bridge", "netflix:eureka2-grpc-client-guice", "netflix:gps-client-grpc-page-ext", "netflix:group-attribute", "netflix:group-impl", "netflix:gutenberg-grpc-client", "netflix:images-client-fallbacks-guice", "netflix:images-client-guice", "netflix:metatron-ipc", "netflix:nf-eventbus-guice", "netflix:nfcontext-impl", "netflix:nflibrary", "netflix:nfsso", "netflix:nsac-client", "netflix:nsac-common", "netflix:platform-core", "netflix:platform-jdk-compat", "netflix:recs-toolkit-guice", "netflix:recs-toolkit-protocol", "netflix:request-expiry-impl", "netflix:streaming-client", "netflix:whitecastle", "org.springframework.guice:spring-guice"]}, "com.netflix.jaxrs:netflix-jaxrs-extensions-exceptionmapper": {"locked": "1.0", "transitive": ["netflix:nf-eventbus"]}, "com.netflix.jaxrs:netflix-jaxrs-extensions-protobuf": {"locked": "1.7", "transitive": ["netflix:platform-ipc"]}, "com.netflix.karyon:karyon-admin-healthcheck-plugin": {"locked": "2.9.2", "transitive": ["netflix:base-server"]}, "com.netflix.karyon:karyon-core": {"locked": "1.0.28", "transitive": ["netflix:base-server"]}, "com.netflix.karyon:karyon-eureka": {"locked": "1.0.28", "transitive": ["com.netflix.karyon:karyon-admin-healthcheck-plugin", "com.netflix.karyon:karyon-core", "netflix:nflibrary", "netflix:platform-ipc"]}, "com.netflix.karyon:karyon-spi": {"locked": "1.0.28", "transitive": ["com.netflix.karyon:karyon-eureka", "netflix:nf-filter", "netflix:nflibrary"]}, "com.netflix.karyon:karyon2-admin": {"locked": "2.9.2", "transitive": ["com.netflix.karyon:karyon-admin-healthcheck-plugin", "com.netflix.karyon:karyon2-admin-web", "netflix.grpc:netflix-grpc-runtime-guice", "netflix:adminresources", "netflix:base-explorer", "netflix:base-server", "netflix:nf-karyon-core", "netflix:nflibrary"]}, "com.netflix.karyon:karyon2-admin-eureka-plugin": {"locked": "2.9.2", "transitive": ["netflix:nf-karyon-admin-plugins"]}, "com.netflix.karyon:karyon2-admin-web": {"locked": "2.9.2", "transitive": ["com.netflix.karyon:karyon2-admin-eureka-plugin", "netflix:nf-karyon-admin-plugins", "netflix:nf-karyon-core", "netflix:nflibrary", "netflix:platform-management"]}, "com.netflix.karyon:karyon2-archaius": {"locked": "2.9.2", "transitive": ["netflix:nf-karyon-core"]}, "com.netflix.karyon:karyon2-core": {"locked": "2.9.2", "transitive": ["com.netflix.karyon:karyon2-governator"]}, "com.netflix.karyon:karyon2-eureka": {"locked": "2.9.2", "transitive": ["netflix:nf-karyon-core"]}, "com.netflix.karyon:karyon2-governator": {"locked": "2.9.2", "transitive": ["com.netflix.karyon:karyon2-archaius", "com.netflix.karyon:karyon2-eureka", "com.netflix.karyon:karyon2-servo", "netflix:nf-karyon-core"]}, "com.netflix.karyon:karyon2-servo": {"locked": "2.9.2", "transitive": ["netflix:nf-karyon-core"]}, "com.netflix.ksavro:ksavro-utils": {"locked": "1.6.1", "transitive": ["com.netflix.chaski3:chaski3-serde-jackson", "com.netflix.ksclient:ksclient-core"]}, "com.netflix.ksclient:ksclient-api": {"locked": "3.11.0", "transitive": ["com.netflix.ge.common:common-grpc", "com.netflix.ge.common:common-service", "com.netflix.ksclient:ksclient-config", "com.netflix.ksclient:ksclient-core", "com.netflix.ksclient:ksclient-core-kafka", "com.netflix.ksclient:ksclient-core-ksgw-http", "com.netflix.ksclient:ksclient-lifecycle", "com.netflix.ksclient:ksclient-lifecycle-kafka", "com.netflix.ksclient:platform-chukwaClient-legacy", "com.netflix.peas:peas-auth-events-logging", "com.netflix.zuul.push:zuul-push-api", "com.netflix.zuul.push:zuul-push-core", "com.netflix.zuul.push:zuul-push-lifecycle", "netflix:api", "netflix:platform-core", "netflix:platform-logimpl"]}, "com.netflix.ksclient:ksclient-config": {"locked": "3.11.0", "transitive": ["com.netflix.ksclient:ksclient-core-kafka"]}, "com.netflix.ksclient:ksclient-core": {"locked": "3.11.0", "transitive": ["com.netflix.ksclient:ksclient-core-kafka", "com.netflix.ksclient:ksclient-core-ksgw-http", "com.netflix.ksclient:ksclient-lifecycle", "com.netflix.ksclient:platform-chukwaClient-legacy"]}, "com.netflix.ksclient:ksclient-core-kafka": {"locked": "3.11.0", "transitive": ["com.netflix.ksclient:ksclient-lifecycle", "com.netflix.ksclient:ksclient-lifecycle-kafka"]}, "com.netflix.ksclient:ksclient-core-ksgw-http": {"locked": "3.11.0", "transitive": ["com.netflix.ksclient:ksclient-lifecycle"]}, "com.netflix.ksclient:ksclient-grpc-guice": {"locked": "3.11.0", "transitive": ["com.netflix.ust:ust-access"]}, "com.netflix.ksclient:ksclient-lifecycle": {"locked": "3.11.0", "transitive": ["com.netflix.evolutionobservation:evolution-observation", "com.netflix.ksclient:ksclient-lifecycle-kafka", "netflix:platform-chukwaClient"]}, "com.netflix.ksclient:ksclient-lifecycle-kafka": {"locked": "3.11.0", "transitive": ["com.netflix.peas:peas-auth-events-logging", "com.netflix.zuul.push:zuul-push-lifecycle"]}, "com.netflix.ksclient:platform-chukwaClient-legacy": {"locked": "3.11.0", "transitive": ["netflix:platform-chukwaClient"]}, "com.netflix.ksschemaclient:ksschemaclient-shadow": {"locked": "0.9.2", "transitive": ["com.netflix.chaski3:chaski3-api", "com.netflix.ksclient:ksclient-core"]}, "com.netflix.littlebirds:little-birds-proto-definition": {"locked": "2.0.0", "transitive": ["com.netflix.gs2:group-service-2-proto-definition", "com.netflix.napa:napa-proto-definition", "com.netflix.shiraz:shiraz-proto-definition", "com.netflix.supermarket:supermarket-proto-definition", "com.netflix.vxs:vxs-proto-definition", "netflix:gps-client-page-proto-definition", "netflix:images-proto-definition"]}, "com.netflix.loadshedding:netflix-load-shedding-mesh": {"locked": "1.3.7", "transitive": ["com.netflix.dgw:dgw-dal-common-lib"]}, "com.netflix.mantis:nfmantis-publish-common": {"locked": "0.9.3", "transitive": ["com.netflix.mantis:nfmantis-publish-grpc-client-core", "netflix:noir-feed-converter"]}, "com.netflix.mantis:nfmantis-publish-grpc-client": {"locked": "0.9.3", "transitive": ["com.netflix.ust:ust-access"]}, "com.netflix.mantis:nfmantis-publish-grpc-client-core": {"locked": "0.9.3", "transitive": ["com.netflix.mantis:nfmantis-publish-grpc-client", "com.netflix.mantis:nfmantis-publish-grpc-client-sbn3"]}, "com.netflix.mantis:nfmantis-publish-grpc-client-sbn3": {"locked": "0.9.3", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-realtime-events"]}, "com.netflix.mantis:nfmantis-publish-grpc-proto-definition": {"locked": "0.9.3", "transitive": ["com.netflix.mantis:nfmantis-publish-grpc-client-core"]}, "com.netflix.memberattribute:memberattribute-client": {"locked": "1.10.1", "transitive": ["com.netflix.pacs:pacs-client"]}, "com.netflix.memberattribute:memberattribute-proto-definition": {"locked": "1.10.1", "transitive": ["com.netflix.memberattribute:memberattribute-client"]}, "com.netflix.membercommerce:membercommerce-client": {"locked": "2.8.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.membercommerce:membercommerce-proto-definition": {"locked": "2.8.0", "transitive": ["com.netflix.membercommerce:membercommerce-client", "com.netflix.ust:ust-model"]}, "com.netflix.memberprice:memberprice-adapter": {"locked": "2.54.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.memberprice:memberprice-client": {"locked": "2.54.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.memberprice:memberprice-proto-definition": {"locked": "2.54.0", "transitive": ["com.netflix.memberprice:memberprice-adapter", "com.netflix.memberprice:memberprice-client"]}, "com.netflix.membership.memberdata:member-data-client": {"locked": "1.97.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.membership.memberdata:member-data-proto-definition": {"locked": "1.97.0", "transitive": ["com.netflix.membership.memberdata:member-data-client"]}, "com.netflix.mesh:mesh-api-java": {"locked": "0.80.0", "transitive": ["com.netflix.dgw:dgw-dal-common-lib", "com.netflix.mesh:mesh-integration-java"]}, "com.netflix.mesh:mesh-integration-java": {"locked": "0.5.217", "transitive": ["com.netflix.dgw:dgw-dal-common-manual-client", "com.netflix.mesh:mesh-integration-spring-boot", "com.netflix.spring:spring-boot-netflix-starter-grpc-client", "com.netflix.webclient:netflix-webclient", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-fit", "netflix.grpc:netflix-grpc-metatron-client", "netflix.grpc:netflix-grpc-netty", "netflix.grpc:netflix-grpc-spectator", "netflix:platform-ipc"]}, "com.netflix.mesh:mesh-integration-spring-boot": {"locked": "0.5.217", "transitive": ["com.netflix.spring:spring-boot-netflix-grpc-client", "com.netflix.spring:spring-boot-netflix-niws", "com.netflix.spring:spring-boot-netflix-starter-rest-server", "com.netflix.webclient:netflix-webclient-spring-boot", "com.netflix.webclient:netflix-webclient-spring-boot-starter"]}, "com.netflix.messaging.bran:bran-proto-definition": {"locked": "0.985.0", "transitive": ["netflix.evolution.easel:easel-proto-definition"]}, "com.netflix.messaging.varys:varys-proto-definition": {"locked": "1.882.0", "transitive": ["com.netflix.messaging.bran:bran-proto-definition"]}, "com.netflix.messagingconsent:messaging-consent-client": {"locked": "1.906.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.messagingconsent:messaging-consent-proto-definition": {"locked": "1.906.0", "transitive": ["com.netflix.evolutionmetadata:evolution-metadata-proto-definition", "com.netflix.messagingconsent:messaging-consent-client"]}, "com.netflix.microcontext:microcontext-access": {"locked": "1.179.6", "transitive": ["com.netflix.dcms:dcms-rt-common", "com.netflix.microcontext:microcontext-resolver", "com.netflix.microcontext:microcontext-test", "com.netflix.pacs:pacs-cache", "com.netflix.pacs:pacs-client", "com.netflix.request:request-attrs-base-server", "com.netflix.simone:okja-client-common", "com.netflix.textevidence:text-evidence-client", "com.netflix.trailerpark:trailer-park-proto-definition", "com.netflix.ust:ust-access", "com.netflix.ust:ust-common", "netflix:api-global-dependencies", "netflix:images-client-fallbacks", "netflix:images-client-fallbacks-guice", "netflix:images-client-guice", "netflix:images-proto-definition", "netflix:p3-converter", "netflix:streaming-steeredcdnurl", "netflix:videometadata-client"]}, "com.netflix.microcontext:microcontext-init": {"locked": "1.179.6", "transitive": ["com.netflix.microcontext:microcontext-access", "com.netflix.ust:ust-access", "com.netflix.ust:ust-common"]}, "com.netflix.microcontext:microcontext-model": {"locked": "1.179.6", "transitive": ["com.netflix.dloc:dloc-proto-definition", "com.netflix.microcontext:microcontext-access", "com.netflix.microcontext:microcontext-init", "com.netflix.patron:patron-proto-definition", "com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.ust:ust-access", "com.netflix.ust:ust-common", "com.netflix.ust:ust-model", "netflix:api-global-dependencies", "netflix:gps-client-page-proto-definition"]}, "com.netflix.microcontext:microcontext-resolver": {"locked": "1.179.6", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.microcontext:microcontext-test": {"locked": "1.179.6", "transitive": ["netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services"]}, "com.netflix.mosaic.construction:mosaic-rules-proto-definitions": {"locked": "3.285.0", "transitive": ["com.netflix.mosaic.datamodel:mosaic-noir-proto-definition"]}, "com.netflix.mosaic.datamodel:mosaic-noir-proto-definition": {"locked": "0.52.0", "transitive": ["com.netflix.page:noir-page-proto-definition"]}, "com.netflix.mylist:mylist-cache": {"locked": "1.1054.0", "transitive": ["com.netflix.mylist:mylist-client"]}, "com.netflix.mylist:mylist-client": {"locked": "1.1054.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.mylist:mylist-common": {"locked": "1.1054.0", "transitive": ["com.netflix.mylist:mylist-cache", "com.netflix.mylist:mylist-client"]}, "com.netflix.mylist:mylist-proto-definition": {"locked": "1.1054.0", "transitive": ["com.netflix.mylist:mylist-cache", "com.netflix.mylist:mylist-client"]}, "com.netflix.myratings:myratings-client": {"locked": "2.355.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.myratings:myratings-proto-definition": {"locked": "2.355.0", "transitive": ["com.netflix.myratings:myratings-client", "com.netflix.ust:ust-model", "netflix:group-attribute", "netflix:group-impl", "netflix:group-interface"]}, "com.netflix.napa:napa-client": {"locked": "0.113.0", "transitive": ["com.netflix.search.pash:search-napa-caller", "netflix:api-global-dependencies", "netflix:p3-converter", "netflix:search_aggregator-client-sal"]}, "com.netflix.napa:napa-page-proto-definition": {"locked": "0.113.0", "transitive": ["com.netflix.napa:napa-proto-definition"]}, "com.netflix.napa:napa-proto-definition": {"locked": "0.113.0", "transitive": ["com.netflix.napa:napa-client", "netflix:noir-feed-converter"]}, "com.netflix.nebula.testcontainers:testcontainers-cloud-configurer": {"locked": "1.20.0", "transitive": ["netflix:api", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:dna-client", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services", "netflix:dna-test"]}, "com.netflix.nes:nes-proto-definition": {"locked": "11.157.0", "transitive": ["com.netflix.gps.pagefallbackservice:gps-page-fallback-proto-definition", "com.netflix.gs2:group-service-2-proto-definition", "com.netflix.littlebirds:little-birds-proto-definition", "com.netflix.napa:napa-page-proto-definition", "com.netflix.napa:napa-proto-definition", "com.netflix.nes:nes-utils-dispatch", "com.netflix.page:noir-page-proto-definition", "com.netflix.page:noir-proto-definition", "com.netflix.pagecontentcommon:page-content-common-proto-definition", "com.netflix.pulseobjectproto:pulse-object-proto-definition", "com.netflix.shiraz:shiraz-proto-definition", "com.netflix.ust:ust-model", "netflix:api-global-dependencies", "netflix:gps-client-grpc-page-ext", "netflix:gps-client-page-proto-definition", "netflix:gps-proto-common-definition", "netflix:gps-proto-page-definition", "netflix:group-attribute", "netflix:noir-feed-converter", "netflix:p3-converter"]}, "com.netflix.nes:nes-utils-dispatch": {"locked": "5.28.0", "transitive": ["com.netflix.ust:ust-access", "netflix:gps-client-grpc-page-ext"]}, "com.netflix.netflix-commons:netflix-commons-util": {"locked": "0.3.0", "transitive": ["com.netflix.ksclient:ksclient-core", "com.netflix.ksclient:ksclient-core-kafka", "com.netflix.ksclient:platform-chukwaClient-legacy", "com.netflix.ribbon:ribbon-httpclient", "com.netflix.ribbon:ribbon-loadbalancer", "netflix:platform-chukwaClient"]}, "com.netflix.netflix-commons:netflix-eventbus": {"locked": "0.3.0", "transitive": ["com.netflix.eureka:eureka-client", "com.netflix.netflix-commons:netflix-eventbus-bridge", "netflix:nf-eventbus-core", "netflix:platform-core"]}, "com.netflix.netflix-commons:netflix-eventbus-bridge": {"locked": "0.3.0", "transitive": ["netflix:nf-eventbus"]}, "com.netflix.netflix-commons:netflix-infix": {"locked": "0.3.0", "transitive": ["com.netflix.netflix-commons:netflix-eventbus"]}, "com.netflix.nfkafka:nfkafka-common": {"locked": "2.7.1", "transitive": ["com.netflix.nfkafka:nfkafka-legacy-schlep", "com.netflix.nfkafka:nfkafka-producer"]}, "com.netflix.nfkafka:nfkafka-legacy-schlep": {"locked": "2.7.1", "transitive": ["netflix:evcache-nflx-client"]}, "com.netflix.nfkafka:nfkafka-producer": {"locked": "2.7.1", "transitive": ["netflix:evcache-nflx-client"]}, "com.netflix.nflxe2etokens:nflx-e2etokens-common": {"locked": "1.167.0", "transitive": ["com.netflix.nflxe2etokens:nflx-e2etokens-validation", "com.netflix.s3authsigncde:s3-auth-sign-client"]}, "com.netflix.nflxe2etokens:nflx-e2etokens-validation": {"locked": "1.167.0", "transitive": ["com.netflix.nflxe2etokens:nflx-e2etokens-validation-spring", "com.netflix.spring:spring-boot-netflix-security-api"]}, "com.netflix.nflxe2etokens:nflx-e2etokens-validation-spring": {"locked": "1.167.0", "transitive": ["com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-sso-authn-embedded"]}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.885.0", "transitive": ["com.netflix.userauthservice2:userauthservice2-gamer-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-gaming-websocket-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-proto-definition", "com.netflix.venkman:venkman-proto-definition", "netflix:passport"]}, "com.netflix.nts.deviceperfmetrics.reader:ntsdeviceperfmetricsreader-api": {"locked": "4.0.2", "transitive": ["com.netflix.nts.deviceperfmetrics.reader:ntsdeviceperfmetricsreader-core", "com.netflix.nts.deviceperfmetrics.reader:ntsdeviceperfmetricsreader-lifecycle"]}, "com.netflix.nts.deviceperfmetrics.reader:ntsdeviceperfmetricsreader-core": {"locked": "4.0.2", "transitive": ["com.netflix.nts.deviceperfmetrics.reader:ntsdeviceperfmetricsreader-lifecycle"]}, "com.netflix.nts.deviceperfmetrics.reader:ntsdeviceperfmetricsreader-lifecycle": {"locked": "4.0.2", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.ocp.coda:coda-client": {"locked": "1.371.0", "transitive": ["netflix:streaming-steeredcdnurl", "netflix:streaming-steeredcdnurl-spring-autoconfigure"]}, "com.netflix.ocp.coda:coda-proto-definition": {"locked": "1.371.0", "transitive": ["com.netflix.ocp.coda:coda-client"]}, "com.netflix.ocp:ocp-datamodel": {"locked": "0.8.3", "transitive": ["com.netflix.ocp:ocp-utilities"]}, "com.netflix.ocp:ocp-utilities": {"locked": "0.8.3", "transitive": ["netflix:streaming-cdnurl"]}, "com.netflix.originals.row.filters:originals-row-filters-api": {"locked": "2.3.2", "transitive": ["com.netflix.originals.row.filters:originals-row-filters-core", "com.netflix.originals.row.filters:originals-row-filters-lifecycle"]}, "com.netflix.originals.row.filters:originals-row-filters-core": {"locked": "2.3.2", "transitive": ["com.netflix.originals.row.filters:originals-row-filters-lifecycle"]}, "com.netflix.originals.row.filters:originals-row-filters-lifecycle": {"locked": "2.3.2", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.pacs:pacs-cache": {"locked": "1.220.0", "transitive": ["com.netflix.pacs:pacs-client"]}, "com.netflix.pacs:pacs-client": {"locked": "1.220.0", "transitive": ["com.netflix.evidence:dataprism-spring", "com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.pacs:pacs-contentgroups-utils": {"locked": "1.220.0", "transitive": ["com.netflix.evidence:dataprism", "com.netflix.supermarket:supermarket-entityset-base"]}, "com.netflix.pacs:pacs-proto-definition": {"locked": "1.220.0", "transitive": ["com.netflix.evidence:dataprism", "com.netflix.evolutionprotodefinition:evolution-proto-definition", "com.netflix.littlebirds:little-birds-proto-definition", "com.netflix.pacs:pacs-cache", "com.netflix.pacs:pacs-client", "com.netflix.pacs:pacs-contentgroups-utils", "com.netflix.search.pash:pash-proto-definition", "com.netflix.supermarket:esql-proto-definition", "com.netflix.supermarket:supermarket-entityset", "com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.ust:ust-model", "com.netflix.vxs:vxs-proto-definition", "netflix:images-proto-definition"]}, "com.netflix.page:noir-extension-proto-definition": {"locked": "3.161.0", "transitive": ["com.netflix.mosaic.datamodel:mosaic-noir-proto-definition", "com.netflix.page:noir-page-proto-definition"]}, "com.netflix.page:noir-page-proto-definition": {"locked": "3.161.0", "transitive": ["com.netflix.gps.pagefallbackservice:gps-page-fallback-proto-definition", "com.netflix.napa:napa-page-proto-definition", "com.netflix.page:noir-proto-definition", "com.netflix.search.pash:search-napa-caller", "com.netflix.shiraz:shiraz-proto-definition", "com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.trailerpark:trailer-park-proto-definition", "netflix:api-global-dependencies", "netflix:gps-client-page-proto-definition", "netflix:gps-proto-page-definition", "netflix:images-proto-definition"]}, "com.netflix.page:noir-proto-definition": {"locked": "3.161.0", "transitive": ["com.netflix.gps.pagefallbackservice:gps-page-fallback-proto-definition", "com.netflix.gps.pagerouterservice:gps-page-router-proto-definition", "com.netflix.napa:napa-proto-definition", "com.netflix.search.pash:search-napa-caller", "netflix:api-global-dependencies", "netflix:gps-client-grpc-page-ext", "netflix:gps-client-page-proto-definition", "netflix:gps-proto-page-definition", "netflix:noir-feed-converter", "netflix:p3-converter"]}, "com.netflix.pagecontentcommon:page-content-common-proto-definition": {"locked": "2.5.0", "transitive": ["com.netflix.supermarket:supermarket-proto-definition"]}, "com.netflix.pagetaxonomy:pagetaxonomy-proto-definition": {"locked": "0.23.0", "transitive": ["com.netflix.page:noir-page-proto-definition"]}, "com.netflix.partnersub.consors:consors-client": {"locked": "2.44.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.partnersub.consors:consors-proto-definition": {"locked": "2.44.0", "transitive": ["com.netflix.partnersub.consors:consors-client"]}, "com.netflix.partnersub.consors:consors-shared": {"locked": "2.44.0", "transitive": ["com.netflix.partnersub.consors:consors-client"]}, "com.netflix.partnersub.psecommon:pse-common-client": {"locked": "1.1157.0", "transitive": ["com.netflix.partnersub.consors:consors-shared"]}, "com.netflix.passport.test:passport-test-core": {"locked": "0.43.0", "transitive": ["com.netflix.microcontext:microcontext-test", "netflix:api", "netflix:api-service-layer", "netflix:dna-test"]}, "com.netflix.patron:patron-client": {"locked": "0.32.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.patron:patron-proto-definition": {"locked": "0.32.0", "transitive": ["com.netflix.patron:patron-client"]}, "com.netflix.peas:attestation-results-core": {"locked": "1.13.0", "transitive": ["com.netflix.sloar:sloar-proto-definition"]}, "com.netflix.peas:auth-token-scopes": {"locked": "1.51.0", "transitive": ["com.netflix.slimer:slimer-proto-definition", "com.netflix.userauthservice2:userauthservice2-gamer-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-gaming-websocket-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-proto-definition", "com.netflix.venkman:venkman-proto-definition", "netflix:api-global-dependencies", "netflix:passport"]}, "com.netflix.peas:peas-auth-events-logging": {"locked": "1.91.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.peas:peas-dials": {"locked": "1.91.0", "transitive": ["com.netflix.pacs:pacs-cache"]}, "com.netflix.peas:peas-logging": {"locked": "1.91.0", "transitive": ["com.netflix.peas:peas-auth-events-logging"]}, "com.netflix.peas:peas-test-spectator": {"locked": "1.91.0", "transitive": ["netflix:api", "netflix:api-service-layer"]}, "com.netflix.playapi:playapi-client": {"locked": "1.1.2496", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.playapi:playapi-proto-definition": {"locked": "1.1.2496", "transitive": ["com.netflix.playapi:playapi-client", "com.netflix.ust:ust-model", "netflix:api-global-dependencies"]}, "com.netflix.plex:plex-member-client-guice": {"locked": "40.714.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.plex:plex-member-proto-definition": {"locked": "40.714.0", "transitive": ["com.netflix.plex:plex-member-client-guice"]}, "com.netflix.plex:plex-shared-proto-definition": {"locked": "40.714.0", "transitive": ["com.netflix.plex:plex-member-proto-definition"]}, "com.netflix.protovalidate:proto-validate": {"locked": "0.7.0", "transitive": ["com.netflix.ust:ust-model"]}, "com.netflix.pulsedgs:pulsedgs-client": {"locked": "0.3.1", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.pulsedgs:pulsedgs-proto-definition": {"locked": "0.3.1", "transitive": ["com.netflix.pulsedgs:pulsedgs-client", "netflix:api-global-dependencies"]}, "com.netflix.pulseobjectproto:pulse-object-proto-definition": {"locked": "0.105.0", "transitive": ["com.netflix.pulsedgs:pulsedgs-proto-definition", "com.netflix.shiraz:shiraz-proto-definition"]}, "com.netflix.rasel:rasel-proto-definition": {"locked": "1.827.0", "transitive": ["com.netflix.hank:hank-proto-definition"]}, "com.netflix.rawhollow:rawhollow-client": {"locked": "4.9.6", "transitive": ["com.netflix.vms:live-event-metadata-client"]}, "com.netflix.rawhollow:rawhollow-client-core": {"locked": "4.9.6", "transitive": ["com.netflix.rawhollow:rawhollow-client"]}, "com.netflix.rawhollow:rawhollow-proto-definition": {"locked": "4.9.6", "transitive": ["com.netflix.rawhollow:rawhollow-client"]}, "com.netflix.request:request-attrs-api": {"locked": "0.7.1", "transitive": ["com.netflix.littlebirds:little-birds-proto-definition", "com.netflix.mylist:mylist-cache", "com.netflix.mylist:mylist-proto-definition", "com.netflix.myratings:myratings-proto-definition", "com.netflix.request:request-attrs-base-server", "com.netflix.request:request-attrs-core", "com.netflix.request:request-attrs-lifecycle", "netflix:cms-proto-definition", "netflix:gps-client-page-proto-definition", "netflix:gps-proto-page-definition"]}, "com.netflix.request:request-attrs-base-server": {"locked": "0.7.1", "transitive": ["com.netflix.request:request-attrs-lifecycle"]}, "com.netflix.request:request-attrs-core": {"locked": "0.7.1", "transitive": ["com.netflix.request:request-attrs-base-server", "com.netflix.request:request-attrs-lifecycle", "netflix:cp1-datamodel"]}, "com.netflix.request:request-attrs-lifecycle": {"locked": "0.7.1", "transitive": ["com.netflix.mylist:mylist-cache"]}, "com.netflix.ribbon:ribbon-archaius": {"locked": "2.4.8", "transitive": ["netflix:platform-ipc"]}, "com.netflix.ribbon:ribbon-core": {"locked": "2.4.8", "transitive": ["com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-eureka", "com.netflix.ribbon:ribbon-httpclient", "com.netflix.ribbon:ribbon-loadbalancer", "netflix:metatron-ipc", "netflix:platform-ipc"]}, "com.netflix.ribbon:ribbon-eureka": {"locked": "2.4.8", "transitive": ["netflix:platform-ipc"]}, "com.netflix.ribbon:ribbon-httpclient": {"locked": "2.4.8", "transitive": ["netflix:platform-ipc"]}, "com.netflix.ribbon:ribbon-loadbalancer": {"locked": "2.4.8", "transitive": ["com.netflix.ribbon:ribbon-eureka", "com.netflix.ribbon:ribbon-httpclient", "netflix.ipc.metrics:ipc-metrics-core"]}, "com.netflix.runtime.metadata:runtime-metadata-client": {"locked": "0.0.30", "transitive": ["com.netflix.spring:spring-boot-netflix-starter", "netflix:base-server"]}, "com.netflix.runtime:health-api": {"locked": "1.1.4", "transitive": ["com.netflix.runtime:health-core"]}, "com.netflix.runtime:health-core": {"locked": "1.1.4", "transitive": ["com.netflix.runtime:health-guice", "com.netflix.runtime:health-integrations"]}, "com.netflix.runtime:health-guice": {"locked": "1.1.4", "transitive": ["netflix:base-server"]}, "com.netflix.runtime:health-integrations": {"locked": "1.1.4", "transitive": ["netflix:base-server"]}, "com.netflix.s3:nfs3-api": {"locked": "1.2.0", "transitive": ["com.netflix.s3:nfs3-common"]}, "com.netflix.s3:nfs3-api-config": {"locked": "1.2.0", "transitive": ["com.netflix.s3:nfs3-api", "com.netflix.s3:nfs3-config"]}, "com.netflix.s3:nfs3-aws-sdk": {"locked": "1.2.0", "transitive": ["com.netflix.s3:nfs3-core"]}, "com.netflix.s3:nfs3-common": {"locked": "1.2.0", "transitive": ["com.netflix.s3:nfs3-aws-sdk", "com.netflix.s3:nfs3-jets3t"]}, "com.netflix.s3:nfs3-config": {"locked": "1.2.0", "transitive": ["com.netflix.s3:nfs3-common"]}, "com.netflix.s3:nfs3-core": {"locked": "1.2.0", "transitive": ["com.netflix.plex:plex-member-client-guice", "com.netflix.plex:plex-member-proto-definition", "com.netflix.plex:plex-shared-proto-definition", "netflix:platform-aws", "netflix:platform-management"]}, "com.netflix.s3:nfs3-jets3t": {"locked": "1.2.0", "transitive": ["netflix:platform-aws"]}, "com.netflix.s3authsigncde:s3-auth-sign-client": {"locked": "3.0.3", "transitive": ["netflix:gutenberg-client", "netflix:gutenberg-common"]}, "com.netflix.s3authsigncde:s3-auth-sign-common": {"locked": "3.0.3", "transitive": ["com.netflix.rawhollow:rawhollow-client-core", "com.netflix.s3authsigncde:s3-auth-sign-client"]}, "com.netflix.sbndevmetrics:sbn-dev-metrics-client": {"locked": "1.111.0", "transitive": ["com.netflix.spring:spring-boot-netflix"]}, "com.netflix.schema:app.platformrecord": {"locked": "v3-b863dff00d04777baaadf1583cfbbad3b3d90c2c", "transitive": ["com.netflix.chaski3:chaski3-platformrecord"]}, "com.netflix.schema:dm.messaging_candidate_decision_event_source": {"locked": "v24-92bb699e6b7935e74617eba31cef0bd294822820", "transitive": ["com.netflix.evolutionobservation:evolution-observation"]}, "com.netflix.schema:dm.messaging_click_event": {"locked": "v11-e8f7442eae52c3f92efb9b8e210a67413c8fdccb", "transitive": ["com.netflix.evolutionobservation:evolution-observation"]}, "com.netflix.schema:dm.messaging_conversion_event": {"locked": "v3-48bbfb102dc8e8ec876c3a5cc69ca61e9b3ddf42", "transitive": ["com.netflix.evolutionobservation:evolution-observation"]}, "com.netflix.schema:dm.messaging_deliver_event_source": {"locked": "v20-98f73fd79ae2ad07b53693f418ebf10fef837ea5", "transitive": ["com.netflix.evolutionobservation:evolution-observation"]}, "com.netflix.schema:dm.messaging_open_event": {"locked": "v8-f99a026208f7aa30d11d8d2755ef7987ceba198d", "transitive": ["com.netflix.evolutionobservation:evolution-observation"]}, "com.netflix.schema:dm.messaging_send_event": {"locked": "v22-68ede6656b3124355ee0b0de263a15bfb0bd84e5", "transitive": ["com.netflix.evolutionobservation:evolution-observation"]}, "com.netflix.schema:keystone.auth_events_v2": {"locked": "v10-47b718afdd8c01b9d8506c6eee66fc28c2c42f68", "transitive": ["com.netflix.peas:peas-auth-events-logging"]}, "com.netflix.scout:scout-client": {"locked": "1.821.0", "transitive": ["com.netflix.ust:ust-access"]}, "com.netflix.scout:scout-proto-definition": {"locked": "1.821.0", "transitive": ["com.netflix.scout:scout-client", "com.netflix.ust:ust-model"]}, "com.netflix.search.pash:pash-client": {"locked": "0.130.0", "transitive": ["netflix:api-global-dependencies", "netflix:search_aggregator-client-sal"]}, "com.netflix.search.pash:pash-proto-definition": {"locked": "0.130.0", "transitive": ["com.netflix.napa:napa-page-proto-definition", "com.netflix.napa:napa-proto-definition", "com.netflix.page:noir-page-proto-definition", "com.netflix.page:noir-proto-definition", "com.netflix.search.pash:pash-client", "com.netflix.search.pash:search-napa-caller"]}, "com.netflix.search.pash:search-napa-caller": {"locked": "0.130.0", "transitive": ["netflix:api-global-dependencies", "netflix:search_aggregator-client-sal"]}, "com.netflix.securekafka:ks-secure-kafka-files": {"locked": "4.2.0", "transitive": ["com.netflix.ksclient:ksclient-core-kafka", "com.netflix.nfkafka:nfkafka-common"]}, "com.netflix.servo:servo-apache": {"locked": "0.13.2", "transitive": ["netflix:atlas-client"]}, "com.netflix.servo:servo-core": {"locked": "0.13.2", "transitive": ["com.netflix.blitz4j:blitz4j", "com.netflix.hystrix:hystrix-servo-metrics-publisher", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.ribbon:ribbon-httpclient", "com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.servo:servo-apache", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.vxs:vxs-proto-definition", "io.reactivex:rxnetty-servo", "netflix:api-global-dependencies", "netflix:atlas-client", "netflix:eventbus", "netflix:geoip-common", "netflix:mantis-realtime-events", "netflix:map-datamodel", "netflix:netflix-config", "netflix:nf-eventbus", "netflix:nfsso", "netflix:p3-converter", "netflix:platform-aws", "netflix:platform-dependency-command", "netflix:platform-sla"]}, "com.netflix.shiraz:shiraz-client": {"locked": "10.97.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.shiraz:shiraz-proto-definition": {"locked": "10.97.0", "transitive": ["com.netflix.napa:napa-proto-definition", "com.netflix.page:noir-proto-definition", "com.netflix.shiraz:shiraz-client"]}, "com.netflix.simone:okja-client-common": {"locked": "1.20.4", "transitive": ["com.netflix.simone:okja-client-guice", "com.netflix.simone:okja-client-spring"]}, "com.netflix.simone:okja-client-guice": {"locked": "1.20.4", "transitive": ["com.netflix.ust:ust-access"]}, "com.netflix.simone:okja-client-spring": {"locked": "1.20.4", "transitive": ["netflix:p3-converter"]}, "com.netflix.simone:okja-proto-definition": {"locked": "1.20.4", "transitive": ["com.netflix.simone:okja-client-common", "com.netflix.simone:okja-client-spring"]}, "com.netflix.singleingest:single-ingest-events-schema-definition": {"locked": "2.0.851", "transitive": ["netflix:dhs-common", "netflix:session-logs-grpc-datamodel"]}, "com.netflix.skuservice:skuservice-client": {"locked": "1.77.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.skuservice:skuservice-common": {"locked": "1.77.0", "transitive": ["com.netflix.skuservice:skuservice-client"]}, "com.netflix.skuservice:skuservice-proto-definition": {"locked": "1.77.0", "transitive": ["com.netflix.skuservice:skuservice-client", "com.netflix.skuservice:skuservice-common", "com.netflix.ust:ust-model"]}, "com.netflix.slimer:slimer-client": {"locked": "6.183.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.slimer:slimer-proto-definition": {"locked": "6.183.0", "transitive": ["com.netflix.slimer:slimer-client", "com.netflix.sloar:sloar-proto-definition", "com.netflix.userauthservice2:userauthservice2-proto-definition"]}, "com.netflix.sloar:sloar-client": {"locked": "4.102.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.sloar:sloar-proto-definition": {"locked": "4.102.0", "transitive": ["com.netflix.sloar:sloar-client"]}, "com.netflix.spectator:spectator-api": {"locked": "1.9.0", "transitive": ["com.netflix.concurrency-limits:concurrency-limits-spectator", "com.netflix.cookie.partner:partner-cookie-core", "com.netflix.cryptex2:cryptex2-client-jvmonly-common", "com.netflix.dgw:dgw-dal-common-lib", "com.netflix.eureka:eureka-client", "com.netflix.evcache:evcache-client", "com.netflix.evcache:evcache-core", "com.netflix.firewood:firewood-sdk-sentry", "com.netflix.hendrix:hendrix-core", "com.netflix.ksclient:ksclient-api", "com.netflix.ksclient:ksclient-core", "com.netflix.memberprice:memberprice-adapter", "com.netflix.nfkafka:nfkafka-common", "com.netflix.nflxe2etokens:nflx-e2etokens-validation", "com.netflix.passport.test:passport-test-core", "com.netflix.peas:attestation-results-core", "com.netflix.peas:peas-dials", "com.netflix.peas:peas-test-spectator", "com.netflix.runtime.metadata:runtime-metadata-client", "com.netflix.runtime:health-core", "com.netflix.s3authsigncde:s3-auth-sign-client", "com.netflix.s3authsigncde:s3-auth-sign-common", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.simone:okja-client-common", "com.netflix.spectator:spectator-ext-aws", "com.netflix.spectator:spectator-ext-aws2", "com.netflix.spectator:spectator-ext-gc", "com.netflix.spectator:spectator-ext-ipc", "com.netflix.spectator:spectator-ext-jvm", "com.netflix.spectator:spectator-ext-log4j2", "com.netflix.spectator:spectator-ext-sandbox", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spectator:spectator-reg-atlas", "com.netflix.spring:spring-boot-netflix", "com.netflix.spring:spring-boot-netflix-actuators", "com.netflix.spring:spring-boot-netflix-metrics", "com.netflix.spring:spring-boot-netflix-starter-library", "com.netflix.supermarket:supermarket-cache-client", "com.netflix.supermarket:supermarket-entityset", "com.netflix.supermarket:supermarket-entityset-base", "com.netflix.supermarket:supermarket-fallback", "com.netflix.tracing:netflix-tracing-reporter", "com.netflix.tracing:netflix-tracing-reporter-sbn", "com.netflix.ust:ust-access", "io.mantisrx:mantis-publish-core", "netflix.contextflow.models:contextflow-model-abii", "netflix.contextflow:contextflow-metrics", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-netty", "netflix.grpc:netflix-grpc-request-context-bridge", "netflix.grpc:netflix-grpc-spectator", "netflix.ipc.metrics:ipc-metrics-core", "netflix.ipc.metrics:ipc-metrics-servlet", "netflix:algo-metrics", "netflix:api-global-dependencies", "netflix:atlas-client", "netflix:cinder-core", "netflix:dts-metrics", "netflix:fit-impl", "netflix:metatron-decrypt", "netflix:nfcurator4", "netflix:nimble-int-java", "netflix:nsac-client", "netflix:nsac-common", "netflix:p3-converter", "netflix:passport-actions-insights", "netflix:platform-core", "netflix:request-expiry-impl", "netflix:server-context"]}, "com.netflix.spectator:spectator-ext-aws": {"locked": "1.9.0", "transitive": ["com.netflix.spring:spring-boot-netflix-metrics", "netflix:gutenberg-common"]}, "com.netflix.spectator:spectator-ext-aws2": {"locked": "1.9.0", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-aws", "netflix:gutenberg-common"]}, "com.netflix.spectator:spectator-ext-gc": {"locked": "1.9.0", "transitive": ["com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spring:spring-boot-netflix-metrics"]}, "com.netflix.spectator:spectator-ext-ipc": {"locked": "1.9.0", "transitive": ["com.netflix.dgw:dgw-dal-common-lib", "com.netflix.evcache:evcache-core", "com.netflix.runtime.metadata:runtime-metadata-client", "com.netflix.spectator:spectator-ext-aws2", "com.netflix.spectator:spectator-reg-atlas", "com.netflix.spring:spring-boot-netflix-metrics", "io.mantisrx:mantis-publish-core", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-spectator", "netflix.ipc.metrics:ipc-metrics-core", "netflix:atlas-client"]}, "com.netflix.spectator:spectator-ext-jvm": {"locked": "1.9.0", "transitive": ["com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spring:spring-boot-netflix-metrics", "netflix:atlas-client"]}, "com.netflix.spectator:spectator-ext-log4j2": {"locked": "1.9.0", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-logging"]}, "com.netflix.spectator:spectator-ext-sandbox": {"locked": "1.9.0", "transitive": ["netflix:algo-metrics"]}, "com.netflix.spectator:spectator-nflx-plugin": {"locked": "1.9.0", "transitive": ["com.netflix.evcache:evcache-client", "com.netflix.peas:peas-auth-events-logging", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-spectator", "netflix:atlas-client", "netflix:ums-common", "netflix:ums-common-alerts", "netflix:ums-common-core"]}, "com.netflix.spectator:spectator-nflx-tagging": {"locked": "1.9.0", "transitive": ["com.netflix.firewood:firewood-sdk-sentry", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spring:spring-boot-netflix-metrics"]}, "com.netflix.spectator:spectator-reg-atlas": {"locked": "1.9.0", "transitive": ["com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spring:spring-boot-netflix-metrics", "io.micrometer:micrometer-registry-atlas"]}, "com.netflix.spring.devagent:sbn-dev-agent-client": {"locked": "0.0.26", "transitive": ["netflix:api"]}, "com.netflix.spring.platform.defaults:sbn-platform-defaults": {"locked": "0.0.309", "transitive": ["netflix:api"]}, "com.netflix.spring:spring-boot-netflix": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-actuators", "com.netflix.spring:spring-boot-netflix-admin", "com.netflix.spring:spring-boot-netflix-aws", "com.netflix.spring:spring-boot-netflix-configuration", "com.netflix.spring:spring-boot-netflix-crypto-provider-corretto", "com.netflix.spring:spring-boot-netflix-discovery", "com.netflix.spring:spring-boot-netflix-evcache", "com.netflix.spring:spring-boot-netflix-governator", "com.netflix.spring:spring-boot-netflix-hybrid-dev", "com.netflix.spring:spring-boot-netflix-logging", "com.netflix.spring:spring-boot-netflix-metrics", "com.netflix.spring:spring-boot-netflix-niws", "com.netflix.spring:spring-boot-netflix-resiliency", "com.netflix.spring:spring-boot-netflix-resttemplate-metatron", "com.netflix.spring:spring-boot-netflix-security-api", "com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-sso-authn-embedded", "com.netflix.spring:spring-boot-netflix-starter", "com.netflix.spring:spring-boot-netflix-starter-library", "com.netflix.spring:spring-boot-netflix-webmvc"]}, "com.netflix.spring:spring-boot-netflix-actuators": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-actuators", "com.netflix.spring:spring-boot-netflix-starter-discovery"]}, "com.netflix.spring:spring-boot-netflix-admin": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-actuators"]}, "com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "3.5.38", "transitive": ["netflix:api", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:dna-router"]}, "com.netflix.spring:spring-boot-netflix-aws": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-aws"]}, "com.netflix.spring:spring-boot-netflix-bom": {"locked": "3.5.38", "transitive": ["netflix:api", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:dna-client", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services", "netflix:dna-test"]}, "com.netflix.spring:spring-boot-netflix-cinder": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-cinder-consumer"]}, "com.netflix.spring:spring-boot-netflix-configuration": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-governator", "com.netflix.spring:spring-boot-netflix-logging", "com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-sso-authn-embedded", "com.netflix.spring:spring-boot-netflix-starter-configuration", "com.netflix.spring:spring-boot-netflix-webmvc"]}, "com.netflix.spring:spring-boot-netflix-crypto-provider-corretto": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-corretto"]}, "com.netflix.spring:spring-boot-netflix-discovery": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-discovery", "com.netflix.spring:spring-boot-netflix-webmvc"]}, "com.netflix.spring:spring-boot-netflix-evcache": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-evcache"]}, "com.netflix.spring:spring-boot-netflix-governator": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-governator-guice-bridge"]}, "com.netflix.spring:spring-boot-netflix-grpc-client": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-grpc-client"]}, "com.netflix.spring:spring-boot-netflix-grpc-common": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-grpc-client"]}, "com.netflix.spring:spring-boot-netflix-hybrid-dev": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter"]}, "com.netflix.spring:spring-boot-netflix-logging": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-logging"]}, "com.netflix.spring:spring-boot-netflix-metrics": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-logging", "com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-sso-authn-embedded", "com.netflix.spring:spring-boot-netflix-sso-authz", "com.netflix.spring:spring-boot-netflix-starter-metrics"]}, "com.netflix.spring:spring-boot-netflix-niws": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-niws"]}, "com.netflix.spring:spring-boot-netflix-resiliency": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-fit", "com.netflix.spring:spring-boot-netflix-starter-nimble"]}, "com.netflix.spring:spring-boot-netflix-resttemplate-metatron": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-admin", "com.netflix.spring:spring-boot-netflix-sso-authn-embedded"]}, "com.netflix.spring:spring-boot-netflix-security-api": {"locked": "3.5.38", "transitive": ["com.netflix.mylist:mylist-cache", "com.netflix.spring:spring-boot-netflix-admin", "com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-webmvc"]}, "com.netflix.spring:spring-boot-netflix-sso": {"locked": "3.5.38", "transitive": ["com.netflix.dgw:dgw-dal-common-lib", "com.netflix.mylist:mylist-common", "com.netflix.spring:spring-boot-netflix-sso-authn-embedded", "com.netflix.spring:spring-boot-netflix-sso-authz", "com.netflix.spring:spring-boot-netflix-starter-security"]}, "com.netflix.spring:spring-boot-netflix-sso-authn-embedded": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-security-authn-embedded"]}, "com.netflix.spring:spring-boot-netflix-sso-authz": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-security-authz"]}, "com.netflix.spring:spring-boot-netflix-starter": {"locked": "3.5.38", "transitive": ["com.netflix.mylist:mylist-common", "com.netflix.spring:spring-boot-netflix-starter-actuators", "com.netflix.spring:spring-boot-netflix-starter-aws", "com.netflix.spring:spring-boot-netflix-starter-cinder-consumer", "com.netflix.spring:spring-boot-netflix-starter-contextflow", "com.netflix.spring:spring-boot-netflix-starter-discovery", "com.netflix.spring:spring-boot-netflix-starter-evcache", "com.netflix.spring:spring-boot-netflix-starter-fit", "com.netflix.spring:spring-boot-netflix-starter-governator-guice-bridge", "com.netflix.spring:spring-boot-netflix-starter-grpc-client", "com.netflix.spring:spring-boot-netflix-starter-grpc-client-evcache", "com.netflix.spring:spring-boot-netflix-starter-nimble", "com.netflix.spring:spring-boot-netflix-starter-niws", "com.netflix.spring:spring-boot-netflix-starter-realtime-events", "com.netflix.spring:spring-boot-netflix-starter-rest-server", "com.netflix.spring:spring-boot-netflix-starter-security", "com.netflix.spring:spring-boot-netflix-starter-security-authn-embedded", "com.netflix.spring:spring-boot-netflix-starter-security-authz", "com.netflix.spring:spring-boot-netflix-starter-swagger", "com.netflix.spring:spring-boot-netflix-starter-tracing", "com.netflix.spring:spring-boot-netflix-starter-webclient"]}, "com.netflix.spring:spring-boot-netflix-starter-actuators": {"locked": "3.5.38", "transitive": ["com.netflix.mantis:nfmantis-publish-grpc-client-sbn3", "com.netflix.spring:spring-boot-netflix-starter-rest-server"]}, "com.netflix.spring:spring-boot-netflix-starter-aws": {"locked": "3.5.38", "transitive": ["com.netflix.hank:hank-client", "com.netflix.s3authsigncde:s3-auth-sign-common", "com.netflix.spring:spring-boot-netflix-starter-cinder-consumer", "netflix:api", "netflix:gutenberg-client", "netflix:whitecastle_v2"]}, "com.netflix.spring:spring-boot-netflix-starter-cinder-consumer": {"locked": "3.5.38", "transitive": ["com.netflix.cpe.merchintention:merchintention-cinderclientcodegen", "com.netflix.cpe.merchintention:merchintention-merchintentlanguages-client", "com.netflix.evidence:dataprism-spring", "com.netflix.hollowtransformer:approved-vmsinmemory-client", "com.netflix.rawhollow:rawhollow-client", "com.netflix.textevidence.titlefeed:text-evidence-title-feed-client", "com.netflix.textevidence:text-evidence-client", "com.netflix.turbo.vmsnamedlist:vms-namedlist-client", "com.netflix.vms:live-event-metadata-client", "netflix:asset-arrival-dates-client", "netflix:contentpreview-dataprovider", "netflix:dts-client", "netflix:entityindex", "netflix:videometadata-spring-core"]}, "com.netflix.spring:spring-boot-netflix-starter-configuration": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter", "com.netflix.spring:spring-boot-netflix-starter-contextflow"]}, "com.netflix.spring:spring-boot-netflix-starter-contextflow": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-grpc-client", "com.netflix.spring:spring-boot-netflix-starter-niws", "com.netflix.spring:spring-boot-netflix-starter-rest-server", "com.netflix.spring:spring-boot-netflix-starter-webclient"]}, "com.netflix.spring:spring-boot-netflix-starter-corretto": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter"]}, "com.netflix.spring:spring-boot-netflix-starter-discovery": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-evcache", "com.netflix.spring:spring-boot-netflix-starter-niws", "com.netflix.spring:spring-boot-netflix-starter-rest-server"]}, "com.netflix.spring:spring-boot-netflix-starter-evcache": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-grpc-client-evcache", "netflix:subscriberservice-client-guice"]}, "com.netflix.spring:spring-boot-netflix-starter-fit": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-grpc-client", "com.netflix.spring:spring-boot-netflix-starter-rest-server"]}, "com.netflix.spring:spring-boot-netflix-starter-governator-guice-bridge": {"locked": "3.5.38", "transitive": ["netflix:api"]}, "com.netflix.spring:spring-boot-netflix-starter-grpc-client": {"locked": "3.5.38", "transitive": ["com.netflix.ab:aballocator-client-guice", "com.netflix.adsnativeadarbiter:ads-native-ad-arbiter-client", "com.netflix.appregistry.rt:appregistry-rt-client", "com.netflix.chronos:chronos-client", "com.netflix.cryptex2:cryptex2-client-jvmonly-sbn", "com.netflix.dcms:dcms-rt-client", "com.netflix.dgw.kv:dgw-kv-client", "com.netflix.dgw:dgw-dal-common-client", "com.netflix.dloc:dloc-client", "com.netflix.dynimo.proxy:dynimoproxy-client", "com.netflix.ember:ember-client", "com.netflix.evolutionmetadata:evolution-metadata-client", "com.netflix.gps.pagefallbackservice:gps-page-fallback-client", "com.netflix.gps.pagerouterservice:gps-page-router-client", "com.netflix.group.fallback.service:group-fallback-client", "com.netflix.growth.paymentexperiences:paymentexperiences-grpc-client", "com.netflix.growth.planspricing:growthplanspricing-grpc-client", "com.netflix.gs2:group-service-2-client-guice", "com.netflix.hank:hank-client", "com.netflix.helpdgs:help-dgs-client", "com.netflix.hollowschemadefinition:hollowschemadefinition-client", "com.netflix.hydrus:hydrus-client", "com.netflix.mantis:nfmantis-publish-grpc-client", "com.netflix.mantis:nfmantis-publish-grpc-client-core", "com.netflix.mantis:nfmantis-publish-grpc-client-sbn3", "com.netflix.memberattribute:memberattribute-client", "com.netflix.membercommerce:membercommerce-client", "com.netflix.memberprice:memberprice-client", "com.netflix.membership.memberdata:member-data-client", "com.netflix.messagingconsent:messaging-consent-client", "com.netflix.mylist:mylist-client", "com.netflix.myratings:myratings-client", "com.netflix.napa:napa-client", "com.netflix.ocp.coda:coda-client", "com.netflix.pacs:pacs-client", "com.netflix.partnersub.consors:consors-client", "com.netflix.patron:patron-client", "com.netflix.playapi:playapi-client", "com.netflix.rawhollow:rawhollow-client", "com.netflix.scout:scout-client", "com.netflix.search.pash:pash-client", "com.netflix.shiraz:shiraz-client", "com.netflix.simone:okja-client-guice", "com.netflix.simone:okja-client-spring", "com.netflix.skuservice:skuservice-client", "com.netflix.slimer:slimer-client", "com.netflix.sloar:sloar-client", "com.netflix.spring:spring-boot-netflix-starter-cinder-consumer", "com.netflix.spring:spring-boot-netflix-starter-grpc-client-evcache", "com.netflix.streamsaccountingservice:streams-accounting-service-client", "com.netflix.supermarket:supermarket-client", "com.netflix.tela:tela-client", "com.netflix.textevidence:text-evidence-client", "com.netflix.trailerpark:trailer-park-client", "com.netflix.turboservice:turboservice-client", "com.netflix.uipersonalization:ui-personalization-client", "com.netflix.userauthservice2:userauthservice2-client", "com.netflix.userpreferences2:userpreferences2-client", "com.netflix.ust:ust-access", "com.netflix.venkman:venkman-client", "com.netflix.vhs:viewinghistoryservice-client", "com.netflix.vxs:vxs-client-nofallbacks", "netflix:api", "netflix:bookmark-query-service-client", "netflix:cms-client-guice-nocml", "netflix:dhs-grpc-client", "netflix:gps-client-grpc-page", "netflix:gutenberg-grpc-client", "netflix:images-client-fallbacks", "netflix:images-client-fallbacks-guice", "netflix:images-client-guice", "netflix:onramp-client-guice", "netflix:session-logs-grpc-client", "netflix:subscriberservice-client-guice"]}, "com.netflix.spring:spring-boot-netflix-starter-grpc-client-evcache": {"locked": "3.5.38", "transitive": ["com.netflix.ab:aballocator-client-guice", "com.netflix.dcms:dcms-rt-client", "com.netflix.memberattribute:memberattribute-client", "com.netflix.membership.memberdata:member-data-client", "com.netflix.mylist:mylist-client", "com.netflix.myratings:myratings-client", "com.netflix.textevidence:text-evidence-client", "com.netflix.trailerpark:trailer-park-client", "com.netflix.vxs:vxs-client-nofallbacks", "netflix:api", "netflix:onramp-client-guice", "netflix:subscriberservice-client-guice"]}, "com.netflix.spring:spring-boot-netflix-starter-library": {"locked": "3.5.38", "transitive": ["com.netflix.mantis:nfmantis-publish-grpc-client-sbn3", "com.netflix.webclient:netflix-webclient-spring-boot"]}, "com.netflix.spring:spring-boot-netflix-starter-logging": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter"]}, "com.netflix.spring:spring-boot-netflix-starter-metrics": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter", "com.netflix.spring:spring-boot-netflix-starter-rest-server"]}, "com.netflix.spring:spring-boot-netflix-starter-nimble": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-rest-server"]}, "com.netflix.spring:spring-boot-netflix-starter-niws": {"locked": "3.5.38", "transitive": ["netflix:api", "netflix:country-launch", "netflix:gutenberg-client"]}, "com.netflix.spring:spring-boot-netflix-starter-realtime-events": {"locked": "3.5.38", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.spring:spring-boot-netflix-starter-rest-server": {"locked": "3.5.38", "transitive": ["netflix:api"]}, "com.netflix.spring:spring-boot-netflix-starter-security": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-rest-server", "com.netflix.spring:spring-boot-netflix-starter-security-authn-embedded"]}, "com.netflix.spring:spring-boot-netflix-starter-security-authn-embedded": {"locked": "3.5.38", "transitive": ["netflix:api"]}, "com.netflix.spring:spring-boot-netflix-starter-security-authz": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-rest-server"]}, "com.netflix.spring:spring-boot-netflix-starter-swagger": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-rest-server"]}, "com.netflix.spring:spring-boot-netflix-starter-tracing": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-rest-server"]}, "com.netflix.spring:spring-boot-netflix-starter-webclient": {"locked": "3.5.38", "transitive": ["com.netflix.rawhollow:rawhollow-client", "netflix:cinder-lifecycle"]}, "com.netflix.spring:spring-boot-netflix-swagger": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-swagger"]}, "com.netflix.spring:spring-boot-netflix-tracing": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-tracing"]}, "com.netflix.spring:spring-boot-netflix-webmvc": {"locked": "3.5.38", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-rest-server"]}, "com.netflix.streamsaccountingservice:streams-accounting-service-client": {"locked": "0.196.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.streamsaccountingservice:streams-accounting-service-proto-definition": {"locked": "0.196.0", "transitive": ["com.netflix.streamsaccountingservice:streams-accounting-service-client"]}, "com.netflix.supermarket:entitysets-query-language": {"locked": "1.158.0", "transitive": ["com.netflix.supermarket:supermarket-client", "com.netflix.supermarket:supermarket-entityset", "com.netflix.supermarket:supermarket-fallback"]}, "com.netflix.supermarket:entitysets-query-language-base": {"locked": "1.158.0", "transitive": ["com.netflix.supermarket:entitysets-query-language", "com.netflix.supermarket:supermarket-entityset-base", "com.netflix.supermarket:supermarket-evaluator"]}, "com.netflix.supermarket:esql-proto-definition": {"locked": "1.158.0", "transitive": ["com.netflix.supermarket:entitysets-query-language", "com.netflix.supermarket:supermarket-entityset-base", "com.netflix.supermarket:supermarket-evaluator", "com.netflix.supermarket:supermarket-proto-definition"]}, "com.netflix.supermarket:supermarket-cache-client": {"locked": "1.158.0", "transitive": ["com.netflix.ust:ust-access"]}, "com.netflix.supermarket:supermarket-client": {"locked": "1.158.0", "transitive": ["com.netflix.supermarket:supermarket-cache-client"]}, "com.netflix.supermarket:supermarket-entityset": {"locked": "1.158.0", "transitive": ["com.netflix.supermarket:supermarket-fallback"]}, "com.netflix.supermarket:supermarket-entityset-base": {"locked": "1.158.0", "transitive": ["com.netflix.supermarket:supermarket-entityset", "com.netflix.supermarket:supermarket-evaluator"]}, "com.netflix.supermarket:supermarket-evaluator": {"locked": "1.158.0", "transitive": ["com.netflix.supermarket:supermarket-fallback"]}, "com.netflix.supermarket:supermarket-fallback": {"locked": "1.158.0", "transitive": ["com.netflix.ust:ust-access"]}, "com.netflix.supermarket:supermarket-proto-definition": {"locked": "1.158.0", "transitive": ["com.netflix.supermarket:supermarket-client", "com.netflix.supermarket:supermarket-fallback"]}, "com.netflix.supermarket:supermarket-util": {"locked": "1.158.0", "transitive": ["com.netflix.supermarket:supermarket-cache-client", "com.netflix.supermarket:supermarket-evaluator"]}, "com.netflix.tela:tela-client": {"locked": "1.946.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies", "netflix:ums-common"]}, "com.netflix.tela:tela-proto-definition": {"locked": "1.946.0", "transitive": ["com.netflix.hank:hank-proto-definition", "com.netflix.tela:tela-client"]}, "com.netflix.textevidence.synopsisfeedclient:text-evidence-synopsis-feed-client": {"locked": "1.4.0", "transitive": ["com.netflix.textevidence:text-evidence-client"]}, "com.netflix.textevidence.titlefeed:text-evidence-title-feed-client": {"locked": "1.2.0", "transitive": ["com.netflix.textevidence:text-evidence-client"]}, "com.netflix.textevidence:text-evidence-client": {"locked": "2.72.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.textevidence:text-evidence-common": {"locked": "2.72.0", "transitive": ["com.netflix.textevidence.titlefeed:text-evidence-title-feed-client", "com.netflix.textevidence:text-evidence-client"]}, "com.netflix.textevidence:text-evidence-proto-definition": {"locked": "2.72.0", "transitive": ["com.netflix.textevidence:text-evidence-client", "com.netflix.textevidence:text-evidence-common", "com.netflix.ust:ust-model"]}, "com.netflix.tracing:netflix-tracing-brave": {"locked": "0.739.0", "transitive": ["netflix:platform-tracing-zipkin"]}, "com.netflix.tracing:netflix-tracing-reporter": {"locked": "0.739.0", "transitive": ["com.netflix.tracing:netflix-tracing-reporter-sbn", "netflix:platform-tracing-zipkin"]}, "com.netflix.tracing:netflix-tracing-reporter-sbn": {"locked": "0.739.0", "transitive": ["com.netflix.spring:spring-boot-netflix-tracing"]}, "com.netflix.tracing:netflix-tracing-secondary-sampling": {"locked": "0.739.0", "transitive": ["com.netflix.spring:spring-boot-netflix-tracing", "netflix:platform-tracing-zipkin"]}, "com.netflix.tracing:netflix-tracing-tags": {"locked": "0.739.0", "transitive": ["com.netflix.spring:spring-boot-netflix-tracing", "com.netflix.tracing:netflix-tracing-secondary-sampling", "netflix.grpc:netflix-grpc-salp", "netflix.grpc:netflix-grpc-zipkin", "netflix:platform-core", "netflix:platform-tracing-zipkin"]}, "com.netflix.tracing:netflix-tracing-utils": {"locked": "0.739.0", "transitive": ["com.netflix.tracing:netflix-tracing-brave", "netflix:platform-tracing-zipkin"]}, "com.netflix.tracing:netflix-tracing-w3c-tracecontext": {"locked": "0.739.0", "transitive": ["com.netflix.tracing:netflix-tracing-secondary-sampling", "netflix:platform-tracing-zipkin"]}, "com.netflix.traffic.resilience:resilience-configuration-proto-definition": {"locked": "0.204.0", "transitive": ["com.netflix.dgw:dgw-dal-common-proto-definition"]}, "com.netflix.trailerpark:trailer-park-client": {"locked": "6.6.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.trailerpark:trailer-park-proto-definition": {"locked": "6.6.0", "transitive": ["com.netflix.trailerpark:trailer-park-client", "com.netflix.ust:ust-model"]}, "com.netflix.turbo.vmsnamedlist:vms-namedlist-client": {"locked": "2.6.5", "transitive": ["netflix:videometadata-client"]}, "com.netflix.turboservice:turboservice-client": {"locked": "1.7.1", "transitive": ["com.netflix.ust:ust-access"]}, "com.netflix.turboservice:turboservice-proto-definition": {"locked": "1.7.1", "transitive": ["com.netflix.turboservice:turboservice-client"]}, "com.netflix.uipersonalization:ui-personalization-client": {"locked": "0.23.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.uipersonalization:ui-personalization-proto-definition": {"locked": "0.23.0", "transitive": ["com.netflix.uipersonalization:ui-personalization-client"]}, "com.netflix.unifiedconfigservice:unifiedconfigservice-validation-proto-definition": {"locked": "0.261.0", "transitive": ["com.netflix.textevidence:text-evidence-proto-definition"]}, "com.netflix.urlkeys:urlkeys-cinderclient": {"locked": "3.0.0", "transitive": ["netflix:streaming-cdnurl"]}, "com.netflix.userauthservice2:userauthservice2-client": {"locked": "0.1776.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.userauthservice2:userauthservice2-gamer-access-token-proto-definition": {"locked": "0.1776.0", "transitive": ["com.netflix.userauthservice2:userauthservice2-proto-definition", "com.netflix.venkman:venkman-proto-definition"]}, "com.netflix.userauthservice2:userauthservice2-gaming-websocket-access-token-proto-definition": {"locked": "0.1776.0", "transitive": ["com.netflix.userauthservice2:userauthservice2-proto-definition", "com.netflix.venkman:venkman-proto-definition"]}, "com.netflix.userauthservice2:userauthservice2-proto-common": {"locked": "0.1776.0", "transitive": ["com.netflix.userauthservice2:userauthservice2-gamer-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-gaming-websocket-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-proto-definition"]}, "com.netflix.userauthservice2:userauthservice2-proto-definition": {"locked": "0.1776.0", "transitive": ["com.netflix.userauthservice2:userauthservice2-client"]}, "com.netflix.userauthservice2:userauthservice2-sso-token-proto-definition": {"locked": "0.1775.0", "transitive": ["com.netflix.venkman:venkman-proto-definition"]}, "com.netflix.userpreferences2:userpreferences2-client": {"locked": "1.13.2", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.userpreferences2:userpreferences2-proto-definition": {"locked": "1.13.2", "transitive": ["com.netflix.userpreferences2:userpreferences2-client"]}, "com.netflix.ust:ust-access": {"locked": "1.315.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.ust:ust-common": {"locked": "1.315.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.ust:ust-model": {"locked": "1.315.0", "transitive": ["com.netflix.ust:ust-access", "com.netflix.ust:ust-common", "netflix:api-global-dependencies"]}, "com.netflix.venkman:venkman-client": {"locked": "2.1076.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.venkman:venkman-proto-definition": {"locked": "2.1076.0", "transitive": ["com.netflix.venkman:venkman-client"]}, "com.netflix.vhs:viewinghistoryservice-client": {"locked": "2.805.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.vhs:viewinghistoryservice-proto-definition": {"locked": "2.805.0", "transitive": ["com.netflix.vhs:viewinghistoryservice-client"]}, "com.netflix.vms:live-event-metadata-client": {"locked": "1.1.230", "transitive": ["netflix:videometadata-client"]}, "com.netflix.vps:vps-client": {"locked": "0.6.0", "transitive": ["netflix:api-global-dependencies"]}, "com.netflix.vps:vps-proto-definition": {"locked": "0.6.0", "transitive": ["com.netflix.vps:vps-client"]}, "com.netflix.vxs:vxs-client-nofallbacks": {"locked": "4.19.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.vxs:vxs-proto-definition": {"locked": "4.19.0", "transitive": ["com.netflix.ust:ust-model", "com.netflix.vxs:vxs-client-nofallbacks"]}, "com.netflix.webclient:netflix-webclient": {"locked": "2.3.32", "transitive": ["com.netflix.webclient:netflix-webclient-spring-boot", "com.netflix.webclient:netflix-webclient-spring-boot-starter", "netflix:cinder-core"]}, "com.netflix.webclient:netflix-webclient-spring-boot": {"locked": "2.3.32", "transitive": ["com.netflix.webclient:netflix-webclient-spring-boot-starter"]}, "com.netflix.webclient:netflix-webclient-spring-boot-starter": {"locked": "2.3.32", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-webclient"]}, "com.netflix.workloadfacts:workload-facts-proto-definition-workload-core": {"locked": "0.262.0", "transitive": ["com.netflix.traffic.resilience:resilience-configuration-proto-definition"]}, "com.netflix.zipurl:zipurl-client": {"locked": "0.1.1", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "com.netflix.zipurl:zipurl-proto-definition": {"locked": "0.1.1", "transitive": ["com.netflix.zipurl:zipurl-client"]}, "com.netflix.zuul.push:zuul-push-api": {"locked": "2.11.2", "transitive": ["com.netflix.zuul.push:zuul-push-core", "com.netflix.zuul.push:zuul-push-lifecycle", "netflix:api-global-dependencies"]}, "com.netflix.zuul.push:zuul-push-core": {"locked": "2.11.2", "transitive": ["com.netflix.zuul.push:zuul-push-lifecycle"]}, "com.netflix.zuul.push:zuul-push-lifecycle": {"locked": "2.11.2", "transitive": ["netflix:api-global-dependencies"]}, "com.nimbusds:content-type": {"locked": "2.2", "transitive": ["com.nimbusds:oauth2-oidc-sdk"]}, "com.nimbusds:lang-tag": {"locked": "1.7", "transitive": ["com.nimbusds:oauth2-oidc-sdk"]}, "com.nimbusds:nimbus-jose-jwt": {"locked": "9.37.4", "transitive": ["com.nimbusds:oauth2-oidc-sdk", "org.springframework.security:spring-security-oauth2-jose"]}, "com.nimbusds:oauth2-oidc-sdk": {"locked": "9.43.6", "transitive": ["org.springframework.security:spring-security-oauth2-client"]}, "com.spotify:futures-extra": {"locked": "4.3.3", "transitive": ["netflix:streaming-steeredcdnurl"]}, "com.squareup.okhttp3:okhttp": {"locked": "3.14.2", "transitive": ["io.zipkin.reporter2:zipkin-sender-okhttp3"]}, "com.squareup.okhttp:logging-interceptor": {"locked": "2.7.5", "transitive": ["netflix:cpeauthorization-client-common", "netflix:pandora-java-client"]}, "com.squareup.okhttp:okhttp": {"locked": "2.7.5", "transitive": ["com.squareup.okhttp:logging-interceptor", "netflix:cpeauthorization-client-common", "netflix:pandora-java-client"]}, "com.squareup.okio:okio": {"locked": "1.17.2", "transitive": ["com.squareup.okhttp3:okhttp", "com.squareup.okhttp:okhttp"]}, "com.stoyanr:evictor": {"locked": "1.0.0", "transitive": ["org.springframework.cloud:spring-cloud-starter-loadbalancer"]}, "com.sun.jersey.contribs:jersey-apache-client": {"locked": "1.20.3", "transitive": ["netflix:platform-aws", "netflix:platform-ipc", "netflix:platform-management"]}, "com.sun.jersey.contribs:jersey-apache-client4": {"locked": "1.20.3", "transitive": ["com.netflix.ribbon:ribbon-httpclient"]}, "com.sun.jersey.contribs:jersey-guice": {"locked": "1.20.3", "transitive": ["com.netflix.karyon:karyon2-admin", "netflix:base-server"]}, "com.sun.jersey.contribs:jersey-multipart": {"locked": "1.20.3", "transitive": ["netflix:base-explorer", "netflix:platform-ipc"]}, "com.sun.jersey:jersey-client": {"locked": "1.20.3", "transitive": ["com.netflix.ribbon:ribbon-httpclient", "com.sun.jersey.contribs:jersey-apache-client", "com.sun.jersey.contribs:jersey-apache-client4", "netflix:api-global-dependencies", "netflix:nfsso", "netflix:platform-aws", "netflix:platform-core", "netflix:platform-ipc", "netflix:platform-logimpl"]}, "com.sun.jersey:jersey-core": {"locked": "1.20.3", "transitive": ["com.netflix.jaxrs:netflix-jaxrs-extensions-exceptionmapper", "com.netflix.karyon:karyon2-admin", "com.sun.jersey.contribs:jersey-multipart", "com.sun.jersey:jersey-client", "com.sun.jersey:jersey-json", "com.sun.jersey:jersey-server", "netflix:akmsclient", "netflix:api-service-layer", "netflix:base-explorer", "netflix:base-server", "netflix:base-server-plugins", "netflix:eventbus", "netflix:nf-eventbus", "netflix:platform-aws", "netflix:platform-ipc", "netflix:platform-jdk-compat"]}, "com.sun.jersey:jersey-json": {"locked": "1.20.3", "transitive": ["com.netflix.karyon:karyon2-admin", "netflix:base-explorer", "netflix:base-server", "netflix:nf-eventbus", "netflix:platform-ipc"]}, "com.sun.jersey:jersey-server": {"locked": "1.20.3", "transitive": ["com.netflix.karyon:karyon2-admin", "com.sun.jersey:jersey-servlet", "netflix:base-explorer", "netflix:base-server", "netflix:nf-eventbus", "netflix:platform-ipc"]}, "com.sun.jersey:jersey-servlet": {"locked": "1.20.3", "transitive": ["com.netflix.jaxrs:netflix-jaxrs-extensions-exceptionmapper", "com.netflix.karyon:karyon2-admin", "com.sun.jersey.contribs:jersey-guice"]}, "com.sun.xml.bind:jaxb-core": {"locked": "4.0.0", "transitive": ["com.sun.xml.bind:jaxb-impl"]}, "com.sun.xml.bind:jaxb-impl": {"locked": "4.0.0", "transitive": ["com.sun.jersey:jersey-json", "netflix:api-global-dependencies", "netflix:platform-jdk-compat", "netflix:ums-common"]}, "com.thoughtworks.paranamer:paranamer": {"locked": "2.7", "transitive": ["org.apache.avro:avro"]}, "com.thoughtworks.xstream:xstream": {"locked": "1.4.21", "transitive": ["com.netflix.eureka:eureka-client", "gov.sandia.foundry:gov-sandia-cognition-common-core", "netflix:mercury-common", "netflix:netflix-config", "netflix:platform-aws", "netflix:platform-ipc", "netflix:platform-management"]}, "com.typesafe:config": {"locked": "1.4.4", "transitive": ["com.netflix.spectator:spectator-ext-jvm", "netflix:atlas-client"]}, "com.vdurmont:emoji-java": {"locked": "4.0.0", "transitive": ["netflix:mercury-common"]}, "commons-beanutils:commons-beanutils": {"locked": "1.9.4", "transitive": ["net.sf.json-lib:json-lib", "netflix:netflix-config", "netflix:nflibrary", "netflix:platform-dependency-command"]}, "commons-codec:commons-codec": {"locked": "1.17.1", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.google.code.typica:typica", "com.netflix.archaius:archaius2-core", "com.netflix.microcontext:microcontext-access", "commons-httpclient:commons-httpclient", "net.java.dev.jets3t:jets3t", "netflix:map-logging-core", "netflix:platform-core", "netflix:platform-ipc", "org.apache.httpcomponents:httpclient", "software.amazon.awssdk:apache-client"]}, "commons-collections:commons-collections": {"locked": "3.2.2", "transitive": ["com.netflix.blitz4j:blitz4j", "com.netflix.karyon:karyon-core", "com.netflix.ribbon:ribbon-httpclient", "commons-beanutils:commons-beanutils", "net.sf.json-lib:json-lib", "netflix:cpeauthorization-client-common", "netflix:cpeauthorization-common", "netflix:dts-client", "netflix:dts-common", "netflix:dts-datamodel", "netflix:nflibrary", "netflix:pds-commons-viewing-history-proto-datamodel", "netflix:platform-aws", "netflix:platform-core", "netflix:platform-ipc", "netflix:platform-management", "netflix:streaming-metrics-core", "netflix:whitecastle", "netflix:whitecastle_v2"]}, "commons-configuration:commons-configuration": {"locked": "1.10", "transitive": ["com.netflix.archaius:archaius-core", "com.netflix.archaius:archaius2-commons-configuration", "com.netflix.blitz4j:blitz4j", "com.netflix.eureka:eureka-client", "com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-eureka", "com.netflix.s3:nfs3-common", "netflix:base-server", "netflix:netflix-config", "netflix:nfi18n-core", "netflix:nflibrary", "netflix:nflibrary-slim", "netflix:nfsso", "netflix:platform-aws", "netflix:platform-core", "netflix:platform-ipc", "netflix:platform-logimpl", "netflix:platform-management"]}, "commons-httpclient:commons-httpclient": {"locked": "3.1", "transitive": ["com.google.code.typica:typica", "com.sun.jersey.contribs:jersey-apache-client", "netflix:akmsclient", "netflix:platform-aws", "netflix:platform-ipc", "netflix:platform-management"]}, "commons-io:commons-io": {"locked": "2.11.0", "transitive": ["com.netflix.microcontext:microcontext-access", "netflix.contextflow:contextflow-transport-web", "netflix:base-explorer", "netflix:base-server", "netflix:gandalf-authz-client", "netflix:nfi18n-core", "netflix:nfsso", "netflix:pds-commons-viewing-history-proto-datamodel", "netflix:platform-aws", "netflix:platform-ipc", "netflix:whitecastle", "netflix:whitecastle_v2"]}, "commons-jxpath:commons-jxpath": {"locked": "1.3", "transitive": ["com.netflix.netflix-commons:netflix-infix", "netflix:nf-eventbus"]}, "commons-lang:commons-lang": {"locked": "2.6", "transitive": ["com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-core", "commons-configuration:commons-configuration", "net.sf.ezmorph:ezmorph", "net.sf.json-lib:json-lib", "netflix.ipc.metrics:ipc-metrics-servlet", "netflix:akmsclient", "netflix:api-global-dependencies", "netflix:base-server-plugins", "netflix:cpeauthorization-common", "netflix:geoip-common", "netflix:nflibrary", "netflix:obelix-common", "netflix:pds-commons-viewing-history-proto-datamodel", "netflix:platform-aws", "netflix:platform-core", "netflix:platform-ipc", "netflix:platform-management", "netflix:platform-utils", "netflix:search_common_630", "netflix:search_common_core", "netflix:streaming-client", "netflix:streaming-metrics-core"]}, "commons-logging:commons-logging": {"locked": "1.3.5", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.amazonaws:aws-xray-recorder-sdk-core", "com.google.code.typica:typica", "commons-beanutils:commons-beanutils", "commons-configuration:commons-configuration", "commons-httpclient:commons-httpclient", "net.java.dev.jets3t:jets3t", "net.sf.json-lib:json-lib", "netflix:nflibrary", "netflix:platform-core", "org.apache.httpcomponents:httpasyncclient", "org.apache.httpcomponents:httpclient", "org.apache.httpcomponents:httpclient-cache"]}, "commons-validator:commons-validator": {"locked": "1.8.0", "transitive": ["build.buf.protoc-gen-validate:pgv-java-stub"]}, "de.codecentric:spring-boot-admin-client": {"locked": "3.4.7", "transitive": ["com.netflix.spring:spring-boot-netflix-admin"]}, "gov.sandia.foundry:gov-sandia-cognition-common-core": {"locked": "4.0.1", "transitive": ["gov.sandia.foundry:gov-sandia-cognition-common-data", "gov.sandia.foundry:gov-sandia-cognition-learning-core", "netflix:algo-commons-core"]}, "gov.sandia.foundry:gov-sandia-cognition-common-data": {"locked": "4.0.1", "transitive": ["gov.sandia.foundry:gov-sandia-cognition-learning-core"]}, "gov.sandia.foundry:gov-sandia-cognition-learning-core": {"locked": "4.0.1", "transitive": ["netflix:algo-commons-core"]}, "io.github.x-stream:mxparser": {"locked": "1.2.2", "transitive": ["com.thoughtworks.xstream:xstream"]}, "io.gsonfire:gson-fire": {"locked": "1.8.0", "transitive": ["netflix:cpeauthorization-client-common"]}, "io.mantisrx:mantis-common-serde": {"locked": "3.4.0", "transitive": ["io.mantisrx:mantis-publish-core"]}, "io.mantisrx:mantis-discovery-proto": {"locked": "3.4.0", "transitive": ["io.mantisrx:mantis-publish-core"]}, "io.mantisrx:mantis-publish-core": {"locked": "3.4.0", "transitive": ["com.netflix.mantis:nfmantis-publish-common"]}, "io.mantisrx:mantis-shaded": {"locked": "2.0.97", "transitive": ["io.mantisrx:mantis-common-serde", "io.mantisrx:mantis-discovery-proto"]}, "io.mantisrx:mql-jvm": {"locked": "3.4.0", "transitive": ["com.netflix.mantis:nfmantis-publish-common", "io.mantisrx:mantis-publish-core", "netflix:mantis-realtime-events"]}, "io.micrometer:context-propagation": {"locked": "1.1.3", "transitive": ["com.netflix.spring:spring-boot-netflix", "io.micrometer:micrometer-tracing"]}, "io.micrometer:micrometer-commons": {"locked": "1.15.3", "transitive": ["io.micrometer:micrometer-core", "io.micrometer:micrometer-jakarta9", "io.micrometer:micrometer-observation"]}, "io.micrometer:micrometer-core": {"locked": "1.15.3", "transitive": ["com.netflix.spring:spring-boot-netflix", "com.netflix.spring:spring-boot-netflix-actuators", "com.netflix.spring:spring-boot-netflix-metrics", "com.netflix.spring:spring-boot-netflix-starter-library", "io.micrometer:micrometer-jakarta9", "io.micrometer:micrometer-registry-atlas"]}, "io.micrometer:micrometer-jakarta9": {"locked": "1.15.3", "transitive": ["org.springframework.boot:spring-boot-starter-actuator"]}, "io.micrometer:micrometer-observation": {"locked": "1.15.3", "transitive": ["io.micrometer:micrometer-core", "io.micrometer:micrometer-jakarta9", "io.micrometer:micrometer-tracing", "org.springframework.boot:spring-boot-starter-actuator", "org.springframework.security:spring-security-core", "org.springframework:spring-context", "org.springframework:spring-web"]}, "io.micrometer:micrometer-registry-atlas": {"locked": "1.15.3", "transitive": ["com.netflix.spring:spring-boot-netflix-metrics"]}, "io.micrometer:micrometer-tracing": {"locked": "1.5.3", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-tracing", "com.netflix.spring:spring-boot-netflix-tracing", "io.micrometer:micrometer-tracing-bridge-brave"]}, "io.micrometer:micrometer-tracing-bridge-brave": {"locked": "1.5.3", "transitive": ["com.netflix.spring:spring-boot-netflix-tracing"]}, "io.netty.incubator:netty-incubator-codec-classes-quic": {"locked": "0.0.74.Final", "transitive": ["io.netty.incubator:netty-incubator-codec-native-quic"]}, "io.netty.incubator:netty-incubator-codec-native-quic": {"locked": "0.0.74.Final", "transitive": ["io.projectreactor.netty.incubator:reactor-netty-incubator-quic"]}, "io.netty:netty": {"locked": "3.10.6.Final", "transitive": ["netflix:curator4-recipes-shaded"]}, "io.netty:netty-buffer": {"locked": "4.2.2.Final", "transitive": ["io.netty.incubator:netty-incubator-codec-classes-quic", "io.netty:netty-codec", "io.netty:netty-codec-base", "io.netty:netty-codec-compression", "io.netty:netty-codec-dns", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-codec-marshalling", "io.netty:netty-codec-protobuf", "io.netty:netty-codec-socks", "io.netty:netty-handler", "io.netty:netty-handler-proxy", "io.netty:netty-resolver-dns", "io.netty:netty-transport", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-native-epoll", "io.netty:netty-transport-native-unix-common", "software.amazon.awssdk:netty-nio-client"]}, "io.netty:netty-codec": {"locked": "4.2.2.Final", "transitive": ["io.netty.incubator:netty-incubator-codec-classes-quic", "software.amazon.awssdk:netty-nio-client"]}, "io.netty:netty-codec-base": {"locked": "4.2.2.Final", "transitive": ["io.netty:netty-codec", "io.netty:netty-codec-compression", "io.netty:netty-codec-dns", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-codec-marshalling", "io.netty:netty-codec-protobuf", "io.netty:netty-codec-socks", "io.netty:netty-handler", "io.netty:netty-handler-proxy", "io.netty:netty-resolver-dns"]}, "io.netty:netty-codec-compression": {"locked": "4.2.2.Final", "transitive": ["io.netty:netty-codec", "io.netty:netty-codec-http"]}, "io.netty:netty-codec-dns": {"locked": "4.2.2.Final", "transitive": ["io.netty:netty-resolver-dns"]}, "io.netty:netty-codec-http": {"locked": "4.2.2.Final", "transitive": ["io.netty:netty-codec-http2", "io.netty:netty-handler-proxy", "io.projectreactor.netty:reactor-netty-http", "software.amazon.awssdk:netty-nio-client"]}, "io.netty:netty-codec-http2": {"locked": "4.2.2.Final", "transitive": ["io.projectreactor.netty:reactor-netty-http", "netflix.io.grpc:grpc-netty-shaded-nflx", "software.amazon.awssdk:netty-nio-client"]}, "io.netty:netty-codec-marshalling": {"locked": "4.2.2.Final", "transitive": ["io.netty:netty-codec"]}, "io.netty:netty-codec-protobuf": {"locked": "4.2.2.Final", "transitive": ["io.netty:netty-codec"]}, "io.netty:netty-codec-socks": {"locked": "4.2.2.Final", "transitive": ["io.netty:netty-handler-proxy"]}, "io.netty:netty-common": {"locked": "4.2.2.Final", "transitive": ["io.netty.incubator:netty-incubator-codec-classes-quic", "io.netty:netty-buffer", "io.netty:netty-codec", "io.netty:netty-codec-base", "io.netty:netty-codec-compression", "io.netty:netty-codec-dns", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-codec-marshalling", "io.netty:netty-codec-protobuf", "io.netty:netty-codec-socks", "io.netty:netty-handler", "io.netty:netty-handler-proxy", "io.netty:netty-resolver", "io.netty:netty-resolver-dns", "io.netty:netty-resolver-dns-classes-macos", "io.netty:netty-transport", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-native-epoll", "io.netty:netty-transport-native-unix-common", "software.amazon.awssdk:netty-nio-client"]}, "io.netty:netty-handler": {"locked": "4.2.2.Final", "transitive": ["io.netty.incubator:netty-incubator-codec-classes-quic", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler-proxy", "io.netty:netty-resolver-dns", "io.projectreactor.netty:reactor-netty-core", "software.amazon.awssdk:netty-nio-client"]}, "io.netty:netty-handler-proxy": {"locked": "4.2.2.Final", "transitive": ["io.projectreactor.netty:reactor-netty-core", "netflix.io.grpc:grpc-netty-shaded-nflx"]}, "io.netty:netty-resolver": {"locked": "4.2.2.Final", "transitive": ["io.netty:netty-handler", "io.netty:netty-resolver-dns", "io.netty:netty-transport", "software.amazon.awssdk:netty-nio-client"]}, "io.netty:netty-resolver-dns": {"locked": "4.2.2.Final", "transitive": ["io.netty:netty-resolver-dns-classes-macos", "io.projectreactor.netty:reactor-netty-core", "io.projectreactor.netty:reactor-netty-http"]}, "io.netty:netty-resolver-dns-classes-macos": {"locked": "4.2.2.Final", "transitive": ["io.netty:netty-resolver-dns-native-macos"]}, "io.netty:netty-resolver-dns-native-macos": {"locked": "4.2.2.Final", "transitive": ["com.netflix.webclient:netflix-webclient-spring-boot-starter", "io.projectreactor.netty:reactor-netty-core", "io.projectreactor.netty:reactor-netty-http"]}, "io.netty:netty-tcnative-boringssl-static": {"locked": "2.0.72.Final", "transitive": ["com.netflix.webclient:netflix-webclient-spring-boot-starter"]}, "io.netty:netty-tcnative-classes": {"locked": "2.0.72.Final", "transitive": ["io.netty:netty-tcnative-boringssl-static"]}, "io.netty:netty-transport": {"locked": "4.2.2.Final", "transitive": ["io.netty.incubator:netty-incubator-codec-classes-quic", "io.netty:netty-codec", "io.netty:netty-codec-base", "io.netty:netty-codec-compression", "io.netty:netty-codec-dns", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-codec-marshalling", "io.netty:netty-codec-protobuf", "io.netty:netty-codec-socks", "io.netty:netty-handler", "io.netty:netty-handler-proxy", "io.netty:netty-resolver-dns", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-native-epoll", "io.netty:netty-transport-native-unix-common", "software.amazon.awssdk:netty-nio-client"]}, "io.netty:netty-transport-classes-epoll": {"locked": "4.2.2.Final", "transitive": ["io.netty:netty-transport-native-epoll", "software.amazon.awssdk:netty-nio-client"]}, "io.netty:netty-transport-native-epoll": {"locked": "4.2.2.Final", "transitive": ["io.projectreactor.netty:reactor-netty-core", "io.projectreactor.netty:reactor-netty-http"]}, "io.netty:netty-transport-native-unix-common": {"locked": "4.2.2.Final", "transitive": ["io.netty:netty-handler", "io.netty:netty-resolver-dns-classes-macos", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-native-epoll", "netflix.io.grpc:grpc-netty-shaded-nflx"]}, "io.opencensus:opencensus-api": {"locked": "0.31.1", "transitive": ["com.google.http-client:google-http-client", "io.opencensus:opencensus-contrib-http-util"]}, "io.opencensus:opencensus-contrib-http-util": {"locked": "0.31.1", "transitive": ["com.google.http-client:google-http-client"]}, "io.perfmark:perfmark-api": {"locked": "0.26.0", "transitive": ["netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-netty-shaded-nflx"]}, "io.projectreactor.addons:reactor-extra": {"locked": "3.5.2", "transitive": ["org.springframework.cloud:spring-cloud-loadbalancer"]}, "io.projectreactor.netty.incubator:reactor-netty-incubator-quic": {"locked": "0.2.10", "transitive": ["io.projectreactor.netty:reactor-netty"]}, "io.projectreactor.netty:reactor-netty": {"locked": "1.2.10", "transitive": ["com.netflix.webclient:netflix-webclient-spring-boot", "com.netflix.webclient:netflix-webclient-spring-boot-starter"]}, "io.projectreactor.netty:reactor-netty-core": {"locked": "1.2.10", "transitive": ["io.projectreactor.netty.incubator:reactor-netty-incubator-quic", "io.projectreactor.netty:reactor-netty", "io.projectreactor.netty:reactor-netty-http"]}, "io.projectreactor.netty:reactor-netty-http": {"locked": "1.2.10", "transitive": ["io.projectreactor.netty:reactor-netty"]}, "io.projectreactor:reactor-core": {"locked": "3.7.11", "transitive": ["com.netflix.webclient:netflix-webclient", "io.projectreactor.addons:reactor-extra", "io.projectreactor.netty:reactor-netty-core", "io.projectreactor.netty:reactor-netty-http", "org.springframework.cloud:spring-cloud-loadbalancer", "org.springframework:spring-webflux"]}, "io.reactivex:rxjava": {"locked": "1.3.8", "transitive": ["com.netflix.evcache:evcache-client", "com.netflix.evcache:evcache-core", "com.netflix.hystrix:hystrix-core", "com.netflix.ksclient:platform-chukwaClient-legacy", "com.netflix.originals.row.filters:originals-row-filters-api", "com.netflix.originals.row.filters:originals-row-filters-core", "com.netflix.originals.row.filters:originals-row-filters-lifecycle", "com.netflix.ribbon:ribbon-loadbalancer", "io.mantisrx:mql-jvm", "io.reactivex:rxnetty-contexts", "io.reactivex:rxnetty-servo", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:demograph-core", "netflix:platform-ipc"]}, "io.reactivex:rxnetty-contexts": {"locked": "0.4.7", "transitive": ["com.netflix.karyon:karyon2-core"]}, "io.reactivex:rxnetty-servo": {"locked": "0.4.7", "transitive": ["com.netflix.karyon:karyon2-servo"]}, "io.sentry:sentry": {"locked": "1.7.30", "transitive": ["com.netflix.firewood:firewood-sdk-sentry", "io.sentry:sentry-log4j2", "io.sentry:sentry-spring"]}, "io.sentry:sentry-log4j2": {"locked": "1.7.30", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-logging"]}, "io.sentry:sentry-spring": {"locked": "1.7.30", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-logging"]}, "io.swagger.core.v3:swagger-annotations-jakarta": {"locked": "2.2.37", "transitive": ["io.swagger.core.v3:swagger-core-jakarta", "netflix:pandora-java-client"]}, "io.swagger.core.v3:swagger-core-jakarta": {"locked": "2.2.37", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-swagger", "com.netflix.spring:spring-boot-netflix-swagger", "org.springdoc:springdoc-openapi-starter-common"]}, "io.swagger.core.v3:swagger-models-jakarta": {"locked": "2.2.37", "transitive": ["io.swagger.core.v3:swagger-core-jakarta"]}, "io.swagger:swagger-annotations": {"locked": "1.6.2", "transitive": ["netflix:cpeauthorization-client-common", "netflix:cpeauthorization-common", "netflix:pandora-java-client"]}, "io.vavr:vavr": {"locked": "0.9.2", "transitive": ["io.mantisrx:mantis-shaded"]}, "io.vavr:vavr-match": {"locked": "0.9.2", "transitive": ["io.vavr:vavr"]}, "io.zipkin.aws:brave-propagation-aws": {"locked": "1.3.0", "transitive": ["io.micrometer:micrometer-tracing-bridge-brave"]}, "io.zipkin.brave:brave": {"locked": "6.2.0", "transitive": ["com.netflix.evcache:evcache-zipkin-tracing", "com.netflix.spring:spring-boot-netflix-starter-tracing", "com.netflix.tracing:netflix-tracing-brave", "com.netflix.tracing:netflix-tracing-secondary-sampling", "io.micrometer:micrometer-tracing-bridge-brave", "io.zipkin.aws:brave-propagation-aws", "io.zipkin.brave:brave-context-slf4j", "io.zipkin.brave:brave-instrumentation-grpc", "io.zipkin.brave:brave-instrumentation-http", "io.zipkin.brave:brave-instrumentation-rpc", "netflix.grpc:netflix-grpc-salp", "netflix:nf-filter", "netflix:platform-core", "netflix:platform-tracing-zipkin"]}, "io.zipkin.brave:brave-context-slf4j": {"locked": "6.2.0", "transitive": ["io.micrometer:micrometer-tracing-bridge-brave"]}, "io.zipkin.brave:brave-instrumentation-grpc": {"locked": "6.2.0", "transitive": ["netflix.grpc:netflix-grpc-salp", "netflix.grpc:netflix-grpc-zipkin"]}, "io.zipkin.brave:brave-instrumentation-http": {"locked": "6.2.0", "transitive": ["com.netflix.tracing:netflix-tracing-secondary-sampling", "io.micrometer:micrometer-tracing-bridge-brave"]}, "io.zipkin.brave:brave-instrumentation-rpc": {"locked": "6.2.0", "transitive": ["com.netflix.tracing:netflix-tracing-secondary-sampling", "io.zipkin.brave:brave-instrumentation-grpc"]}, "io.zipkin.contrib.brave-propagation-w3c:brave-propagation-tracecontext": {"locked": "0.2.0", "transitive": ["io.micrometer:micrometer-tracing-bridge-brave"]}, "io.zipkin.reporter2:zipkin-reporter": {"locked": "3.5.1", "transitive": ["com.netflix.spring:spring-boot-netflix-tracing", "com.netflix.tracing:netflix-tracing-reporter", "com.netflix.tracing:netflix-tracing-reporter-sbn", "io.zipkin.reporter2:zipkin-reporter-brave", "io.zipkin.reporter2:zipkin-sender-okhttp3", "netflix:platform-tracing-zipkin"]}, "io.zipkin.reporter2:zipkin-reporter-brave": {"locked": "3.5.1", "transitive": ["com.netflix.spring:spring-boot-netflix-tracing"]}, "io.zipkin.reporter2:zipkin-sender-okhttp3": {"locked": "2.10.2", "transitive": ["netflix:platform-tracing-zipkin"]}, "io.zipkin.zipkin2:zipkin": {"locked": "2.27.1", "transitive": ["io.zipkin.reporter2:zipkin-reporter", "io.zipkin.reporter2:zipkin-sender-okhttp3"]}, "it.unimi.dsi:fastutil-core": {"locked": "8.5.16", "transitive": ["netflix:streaming-cdnurl"]}, "jakarta.activation:jakarta.activation-api": {"locked": "2.1.3", "transitive": ["com.fasterxml.jackson.module:jackson-module-jaxb-annotations", "com.netflix.spring:spring-boot-netflix-resiliency", "com.netflix.spring:spring-boot-netflix-sso", "jakarta.mail:jakarta.mail-api", "jakarta.xml.bind:jakarta.xml.bind-api", "org.eclipse.angus:angus-activation", "org.eclipse.angus:angus-mail"]}, "jakarta.annotation:jakarta.annotation-api": {"locked": "3.0.0", "transitive": ["com.google.api:api-common", "com.netflix.archaius:archaius2-persisted2", "com.netflix.eureka:eureka-client", "com.netflix.evcache:evcache-core", "com.netflix.governator:governator-core", "com.netflix.gps.pagerouterservice:gps-page-router-proto-definition", "com.netflix.mosaic.construction:mosaic-rules-proto-definitions", "com.netflix.myratings:myratings-proto-definition", "com.netflix.spring:spring-boot-netflix-governator", "com.netflix.spring:spring-boot-netflix-starter-evcache", "com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.webclient:netflix-webclient-spring-boot", "netflix.contextflow:contextflow-access", "netflix.contextflow:contextflow-api", "netflix.contextflow:contextflow-loader", "netflix.contextflow:contextflow-registry", "netflix.evolution:message_interfaces", "netflix.grpc:netflix-grpc-common", "netflix:algo-commons-core", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:images-proto-definition", "netflix:netflix-schemas", "netflix:passport", "netflix:session-logs-grpc-datamodel", "org.apache.tomcat.embed:tomcat-embed-core", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-starter-tomcat"]}, "jakarta.inject:jakarta.inject-api": {"locked": "2.0.1", "transitive": ["com.netflix.ab:aballocator-common", "com.netflix.archaius:archaius2-api", "com.netflix.archaius:archaius2-guice", "com.netflix.chaski2:chaski2-api", "com.netflix.eureka:eureka-client", "com.netflix.governator:governator-api", "com.netflix.governator:governator-core", "com.netflix.governator:governator-providers", "com.netflix.inject:guice-all", "com.netflix.jaxrs:netflix-jaxrs-extensions-exceptionmapper", "com.netflix.ksclient:ksclient-api", "com.netflix.netflix-commons:netflix-commons-util", "com.netflix.nts.deviceperfmetrics.reader:ntsdeviceperfmetricsreader-core", "com.netflix.originals.row.filters:originals-row-filters-core", "com.netflix.passport.test:passport-test-core", "com.netflix.peas:attestation-results-core", "com.netflix.request:request-attrs-base-server", "com.netflix.request:request-attrs-core", "com.netflix.ribbon:ribbon-eureka", "com.netflix.runtime:health-core", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spring:spring-boot-netflix", "com.netflix.zuul.push:zuul-push-core", "com.sun.jersey.contribs:jersey-guice", "netflix.chestnut:chestnut3-logger", "netflix.grpc:netflix-grpc-common", "netflix.ipc.metrics:ipc-metrics-core", "netflix.ipc.metrics:ipc-metrics-servlet", "netflix:dts-datamodel", "netflix:dts-standalone-common", "netflix:metatron-decrypt", "netflix:metatron-ipc", "netflix:nf-eventbus-core", "netflix:recs-toolkit-guice", "netflix:recs-toolkit-protocol", "netflix:request-expiry", "netflix:request-expiry-impl"]}, "jakarta.jms:jakarta.jms-api": {"locked": "3.1.0", "transitive": ["netflix:mercury-common"]}, "jakarta.mail:jakarta.mail-api": {"locked": "2.1.2", "transitive": ["org.eclipse.angus:angus-mail"]}, "jakarta.persistence:jakarta.persistence-api": {"locked": "3.1.0", "transitive": ["netflix:platform-aws"]}, "jakarta.servlet:jakarta.servlet-api": {"locked": "6.0.0", "transitive": ["com.netflix.karyon:karyon2-admin", "com.netflix.mantis:nfmantis-publish-grpc-client-sbn3", "com.netflix.netflix-commons:netflix-infix", "com.netflix.spring:spring-boot-netflix-resiliency", "com.netflix.tracing:netflix-tracing-secondary-sampling", "netflix.ipc.metrics:ipc-metrics-servlet", "netflix:api-service-layer", "netflix:base-server", "netflix:dna-model", "netflix:dna-router", "netflix:nf-eventbus", "netflix:server-context", "netflix:streaming-client", "netflix:tools-utils"]}, "jakarta.validation:jakarta.validation-api": {"locked": "3.0.2", "transitive": ["com.netflix.runtime.metadata:runtime-metadata-client", "com.netflix.spring:spring-boot-netflix-webmvc", "io.swagger.core.v3:swagger-core-jakarta", "netflix:passport-actions-insights", "org.hibernate.validator:hibernate-validator"]}, "jakarta.ws.rs:jakarta.ws.rs-api": {"locked": "3.1.0", "transitive": ["com.netflix.eureka:eureka-client", "com.netflix.karyon:karyon2-admin", "com.netflix.spring:spring-boot-netflix-resiliency", "com.sun.jersey:jersey-core", "netflix.grpc:netflix-grpc-admin", "netflix:cpeauthorization-common", "netflix:dts-datamodel", "netflix:nfsso", "netflix:pandora-java-client", "netflix:platform-core"]}, "jakarta.xml.bind:jakarta.xml.bind-api": {"locked": "4.0.2", "transitive": ["com.fasterxml.jackson.module:jackson-module-jaxb-annotations", "com.netflix.governator:governator", "com.netflix.ksclient:ksclient-core", "com.netflix.spring:spring-boot-netflix-resiliency", "com.netflix.spring:spring-boot-netflix-sso", "com.sun.xml.bind:jaxb-core", "io.swagger.core.v3:swagger-core-jakarta", "netflix:api-global-dependencies", "netflix:platform-jdk-compat"]}, "jdom:jdom": {"locked": "1.0", "transitive": ["netflix:platform-management"]}, "joda-time:joda-time": {"locked": "2.12.7", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.fasterxml.jackson.datatype:jackson-datatype-joda", "com.netflix.chaski3:chaski3-platformrecord", "com.netflix.chaski3:chaski3-platformrecord-serde-avro", "com.netflix.chaski3:chaski3-platformrecord-serde-jackson", "com.netflix.evcache:evcache-core", "com.netflix.netflix-commons:netflix-infix", "netflix:akmsclient", "netflix:base-explorer", "netflix:cpeauthorization-common", "netflix:nf-eventbus", "netflix:pandora-java-client", "netflix:platform-utils"]}, "junit:junit": {"locked": "4.13.2", "transitive": ["com.netflix.microcontext:microcontext-test", "netflix.grpc:netflix-grpc-testkit", "netflix.io.grpc:grpc-testing-nflx", "netflix:api-service-layer", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services", "netflix:dna-test", "org.apache.ant:ant-junit"]}, "net.bytebuddy:byte-buddy": {"locked": "1.17.7", "transitive": ["com.netflix.spring:spring-boot-netflix-governator", "netflix:algo-commons-core", "org.assertj:assertj-core", "org.mockito:mockito-core"]}, "net.bytebuddy:byte-buddy-agent": {"locked": "1.17.7", "transitive": ["com.netflix.spring:spring-boot-netflix-governator", "org.mockito:mockito-core"]}, "net.iharder:base64": {"locked": "2.3.8", "transitive": ["com.jamesmurty.utils:java-xmlbuilder"]}, "net.java.dev.jets3t:jets3t": {"locked": "0.9.5-nf.8", "transitive": ["netflix:ums-common", "netflix:ums-common-alerts", "netflix:ums-common-core"]}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.netflix.chaski3:chaski3-serde-jackson"]}, "net.minidev:accessors-smart": {"locked": "2.5.2", "transitive": ["net.minidev:json-smart"]}, "net.minidev:json-smart": {"locked": "2.5.2", "transitive": ["com.jayway.jsonpath:json-path", "com.nimbusds:oauth2-oidc-sdk"]}, "net.sf.ezmorph:ezmorph": {"locked": "1.0.6", "transitive": ["net.sf.json-lib:json-lib"]}, "net.sf.jopt-simple:jopt-simple": {"locked": "4.6", "transitive": ["org.openjdk.jmh:jmh-core"]}, "net.sf.json-lib:json-lib": {"locked": "2.2.3", "transitive": ["netflix:apputils"]}, "net.sf.scannotation:scannotation": {"locked": "1.0.2", "transitive": ["netflix:platform-aws", "netflix:platform-dependency-command", "netflix:platform-management"]}, "net.sf.trove4j:trove4j": {"locked": "3.0.3", "transitive": ["netflix:whitecastle_v2"]}, "net.spy:spymemcached": {"locked": "2.11.4+14", "transitive": ["com.netflix.evcache:evcache-client", "com.netflix.evcache:evcache-core", "netflix:evcache-nflx-client"]}, "netflix.async.util:async-util": {"locked": "0.0.3", "transitive": ["com.netflix.microcontext:microcontext-resolver", "com.netflix.ust:ust-access", "netflix.grpc:netflix-grpc-testkit"]}, "netflix.chestnut:chestnut3-api": {"locked": "3.1.1", "transitive": ["netflix.chestnut:chestnut3-logger", "netflix:cinder-core"]}, "netflix.chestnut:chestnut3-logger": {"locked": "3.1.1", "transitive": ["netflix:cinder-core"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.8", "transitive": ["build.buf.protoc-gen-validate:pgv-java-stub", "com.google.api.grpc:proto-google-common-protos", "com.netflix.ab:aballocator-common", "com.netflix.ab:aballocator-proto-definition", "com.netflix.ads.openrtbprotov2:openrtb-proto-v2-proto-definition", "com.netflix.adsnativeadarbiter:ads-native-ad-arbiter-proto-definition", "com.netflix.apicollection:apicollection-proto-definition", "com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.appregistry.rt:appregistry-rt-proto-definition", "com.netflix.bulldozer:bulldozer-proto-definition", "com.netflix.chronos:chronos-proto-definition", "com.netflix.contentgroup:content-group-definition", "com.netflix.contentgroup:content-group-expression-proto", "com.netflix.contextuallog.relay:contextual-log-token-proto-definition", "com.netflix.cookie.partner:partner-cookie-core", "com.netflix.cryptex2:cryptex2-client-jvmonly-common", "com.netflix.cryptex2:cryptex2-common", "com.netflix.dcms:dcms-rt-proto-definition", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.dgw.kv:dgw-kv-proto-definition", "com.netflix.dgw:dgw-dal-common-proto-definition", "com.netflix.dloc:dloc-proto-definition", "com.netflix.dynimo.proxy:dynimoproxy-proto-definition", "com.netflix.ember:ember-proto-definition", "com.netflix.emf:emf-proto-definition", "com.netflix.evolutionmetadata:evolution-metadata-proto-definition", "com.netflix.frankenstein:frankenstein-proto-definition", "com.netflix.gps.pagefallbackservice:gps-page-fallback-proto-definition", "com.netflix.gps.pagerouterservice:gps-page-router-proto-definition", "com.netflix.group.fallback.service:group-fallback-proto-definition", "com.netflix.growth.paymentexperiences:paymentexperiences-proto-definition", "com.netflix.growth.planspricing:growthplanspricing-proto-definition", "com.netflix.gs2:group-service-2-proto-definition", "com.netflix.hank:hank-proto-definition", "com.netflix.helpdgs:help-dgs-proto-definition", "com.netflix.hollowschemadefinition:hollowschemadefinition-proto-definition", "com.netflix.hydrus:hydrus-proto-definition", "com.netflix.jaxrs:netflix-jaxrs-extensions-protobuf", "com.netflix.littlebirds:little-birds-proto-definition", "com.netflix.loadshedding:netflix-load-shedding-mesh", "com.netflix.mantis:nfmantis-publish-grpc-proto-definition", "com.netflix.memberattribute:memberattribute-proto-definition", "com.netflix.membercommerce:membercommerce-proto-definition", "com.netflix.memberprice:memberprice-proto-definition", "com.netflix.membership.memberdata:member-data-proto-definition", "com.netflix.mesh:mesh-api-java", "com.netflix.messaging.bran:bran-proto-definition", "com.netflix.messaging.varys:varys-proto-definition", "com.netflix.messagingconsent:messaging-consent-proto-definition", "com.netflix.microcontext:microcontext-model", "com.netflix.mosaic.construction:mosaic-rules-proto-definitions", "com.netflix.mosaic.datamodel:mosaic-noir-proto-definition", "com.netflix.mylist:mylist-proto-definition", "com.netflix.myratings:myratings-proto-definition", "com.netflix.napa:napa-page-proto-definition", "com.netflix.napa:napa-proto-definition", "com.netflix.nes:nes-proto-definition", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.pacs:pacs-proto-definition", "com.netflix.page:noir-extension-proto-definition", "com.netflix.page:noir-page-proto-definition", "com.netflix.page:noir-proto-definition", "com.netflix.pagecontentcommon:page-content-common-proto-definition", "com.netflix.pagetaxonomy:pagetaxonomy-proto-definition", "com.netflix.partnersub.consors:consors-proto-definition", "com.netflix.patron:patron-proto-definition", "com.netflix.peas:attestation-results-core", "com.netflix.peas:auth-token-scopes", "com.netflix.playapi:playapi-proto-definition", "com.netflix.plex:plex-shared-proto-definition", "com.netflix.protovalidate:proto-validate", "com.netflix.pulsedgs:pulsedgs-proto-definition", "com.netflix.pulseobjectproto:pulse-object-proto-definition", "com.netflix.rasel:rasel-proto-definition", "com.netflix.rawhollow:rawhollow-proto-definition", "com.netflix.request:request-attrs-api", "com.netflix.scout:scout-proto-definition", "com.netflix.search.pash:pash-proto-definition", "com.netflix.shiraz:shiraz-proto-definition", "com.netflix.simone:okja-proto-definition", "com.netflix.singleingest:single-ingest-events-schema-definition", "com.netflix.skuservice:skuservice-proto-definition", "com.netflix.slimer:slimer-proto-definition", "com.netflix.sloar:sloar-proto-definition", "com.netflix.streamsaccountingservice:streams-accounting-service-proto-definition", "com.netflix.supermarket:esql-proto-definition", "com.netflix.supermarket:supermarket-proto-definition", "com.netflix.tela:tela-proto-definition", "com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.traffic.resilience:resilience-configuration-proto-definition", "com.netflix.trailerpark:trailer-park-proto-definition", "com.netflix.turboservice:turboservice-proto-definition", "com.netflix.uipersonalization:ui-personalization-proto-definition", "com.netflix.unifiedconfigservice:unifiedconfigservice-validation-proto-definition", "com.netflix.userauthservice2:userauthservice2-gamer-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-gaming-websocket-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-proto-common", "com.netflix.userauthservice2:userauthservice2-proto-definition", "com.netflix.userauthservice2:userauthservice2-sso-token-proto-definition", "com.netflix.userpreferences2:userpreferences2-proto-definition", "com.netflix.ust:ust-model", "com.netflix.venkman:venkman-proto-definition", "com.netflix.vhs:viewinghistoryservice-proto-definition", "com.netflix.vps:vps-proto-definition", "com.netflix.workloadfacts:workload-facts-proto-definition-workload-core", "com.netflix.zipurl:zipurl-proto-definition", "netflix.async.util:async-util", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.contextflow:contextflow-proto-definition", "netflix.evolution.easel:easel-proto-definition", "netflix.evolution:message_interfaces", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-json", "netflix.grpc:netflix-grpc-logging-context", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.grpc:netflix-grpc-request-context-bridge", "netflix.grpc:netflix-grpc-spectator", "netflix.io.grpc:grpc-alts-nflx", "netflix.io.grpc:grpc-grpclb-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:abzuulcontext", "netflix:algo-commons-protos", "netflix:authorizable-context", "netflix:basicTypes-proto", "netflix:bookmark-query-service-proto-definition", "netflix:cinder-proto-definition", "netflix:cms-proto-definition", "netflix:cp1-proto", "netflix:cp1-proto-bridge", "netflix:cpeauthorization-proto-definition", "netflix:data-protobuf-codec", "netflix:demograph-core", "netflix:dhs-grpc-datamodel", "netflix:dts-datamodel", "netflix:dts-standalone-common", "netflix:entityindex-proto-definition", "netflix:eureka2-eureka1-bridge", "netflix:gandalf-authz-client", "netflix:gps-client-page-proto-definition", "netflix:gps-proto-common-definition", "netflix:gps-proto-group-definition", "netflix:gps-proto-page-definition", "netflix:gps-proto-section-definition", "netflix:gutenberg-proto-definition", "netflix:images-proto-definition", "netflix:metatron-common", "netflix:metatron-decrypt", "netflix:metatron-decryptserver-proto-definitions", "netflix:metatron-ipc-common", "netflix:metatron-ipc-common-assume", "netflix:netflix-schemas", "netflix:onramp-proto-definition", "netflix:pds-commons-viewing-history-proto-datamodel", "netflix:platform-aws", "netflix:platform-management", "netflix:playapi-common-proto-definition", "netflix:playapi-events-proto-definition", "netflix:recs-toolkit-proto-definition", "netflix:session-logs-grpc-datamodel", "netflix:staypuft-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition", "netflix:subscriptiondata-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.8", "transitive": ["build.buf.protoc-gen-validate:pgv-java-stub", "com.netflix.cookie.partner:partner-cookie-core", "com.netflix.loadshedding:netflix-load-shedding-mesh", "com.netflix.mesh:mesh-integration-java", "com.netflix.pacs:pacs-client", "com.netflix.peas:attestation-results-core", "netflix.contextflow:contextflow-access", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-json", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-grpclb-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-xds-nflx", "netflix:cinder-core", "netflix:dts-standalone-common", "netflix:gandalf-authz-client", "netflix:metatron-common", "netflix:metatron-decrypt", "netflix:metatron-decryptserver-proto-definitions"]}, "netflix.contextflow.models:contextflow-model-abii": {"locked": "1.6.1", "transitive": ["com.netflix.ab:aballocator-client-guice"]}, "netflix.contextflow:contextflow-access": {"locked": "1.1.8", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-contextflow"]}, "netflix.contextflow:contextflow-api": {"locked": "1.1.8", "transitive": ["netflix.contextflow:contextflow-access", "netflix.contextflow:contextflow-bindings-spring", "netflix.contextflow:contextflow-loader", "netflix.contextflow:contextflow-metrics", "netflix.contextflow:contextflow-model", "netflix.contextflow:contextflow-registry", "netflix.contextflow:contextflow-transport"]}, "netflix.contextflow:contextflow-bindings-spring": {"locked": "1.1.8", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-contextflow"]}, "netflix.contextflow:contextflow-loader": {"locked": "1.1.8", "transitive": ["netflix.contextflow:contextflow-access"]}, "netflix.contextflow:contextflow-metrics": {"locked": "1.1.8", "transitive": ["netflix.contextflow:contextflow-access", "netflix.contextflow:contextflow-transport"]}, "netflix.contextflow:contextflow-model": {"locked": "1.1.8", "transitive": ["netflix.contextflow.models:contextflow-model-abii", "netflix.contextflow:contextflow-transport"]}, "netflix.contextflow:contextflow-proto-definition": {"locked": "1.1.8", "transitive": ["netflix.contextflow.models:contextflow-model-abii", "netflix.contextflow:contextflow-api"]}, "netflix.contextflow:contextflow-registry": {"locked": "1.1.8", "transitive": ["netflix.contextflow:contextflow-bindings-spring"]}, "netflix.contextflow:contextflow-transport": {"locked": "1.1.8", "transitive": ["com.netflix.spring:spring-boot-netflix-niws", "netflix.contextflow:contextflow-transport-grpc", "netflix.contextflow:contextflow-transport-web", "netflix.contextflow:contextflow-transport-webclient", "netflix:platform-ipc"]}, "netflix.contextflow:contextflow-transport-grpc": {"locked": "1.1.8", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-grpc-client"]}, "netflix.contextflow:contextflow-transport-web": {"locked": "1.1.8", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-rest-server"]}, "netflix.contextflow:contextflow-transport-webclient": {"locked": "1.1.8", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-webclient"]}, "netflix.evolution.easel:easel-proto-definition": {"locked": "0.1776.0", "transitive": ["com.netflix.hank:hank-proto-definition"]}, "netflix.evolution:message_interfaces": {"locked": "2.14780.0", "transitive": ["com.netflix.evolutionobservation:evolution-observation", "com.netflix.hank:hank-client", "netflix:api-global-dependencies", "netflix:ums-common"]}, "netflix.falcor.client:falcor-client-core": {"locked": "0.3.10", "transitive": ["netflix:api-global-dependencies", "netflix:dna-model"]}, "netflix.falcor.schema:falcor-schema-ast-model": {"locked": "3.19.1", "transitive": ["netflix:api-global-dependencies"]}, "netflix.grpc:netflix-grpc-admin": {"locked": "1.63.39", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-grpc-client", "netflix.grpc:netflix-grpc-runtime-guice"]}, "netflix.grpc:netflix-grpc-archaius": {"locked": "1.63.39", "transitive": ["com.netflix.dgw:dgw-dal-common-manual-client", "com.netflix.spring:spring-boot-netflix-grpc-client", "netflix.grpc:netflix-grpc-runtime-guice"]}, "netflix.grpc:netflix-grpc-binding-context": {"locked": "1.63.39", "transitive": ["netflix.contextflow:contextflow-transport-grpc", "netflix.grpc:netflix-grpc-request-context-bridge"]}, "netflix.grpc:netflix-grpc-common": {"locked": "1.63.39", "transitive": ["com.netflix.appregistry.rt:appregistry-rt-common", "com.netflix.dcms:dcms-rt-common", "com.netflix.dgw.kv:dgw-kv-client-interceptor", "com.netflix.dgw:dgw-dal-common-manual-client", "com.netflix.mantis:nfmantis-publish-grpc-client-core", "com.netflix.microcontext:microcontext-access", "com.netflix.mylist:mylist-cache", "com.netflix.skuservice:skuservice-common", "com.netflix.spring:spring-boot-netflix-grpc-client", "com.netflix.trailerpark:trailer-park-proto-definition", "com.netflix.vxs:vxs-proto-definition", "netflix.contextflow:contextflow-transport-grpc", "netflix.grpc:netflix-grpc-admin", "netflix.grpc:netflix-grpc-archaius", "netflix.grpc:netflix-grpc-binding-context", "netflix.grpc:netflix-grpc-evcache", "netflix.grpc:netflix-grpc-fit", "netflix.grpc:netflix-grpc-json", "netflix.grpc:netflix-grpc-loadbalancer", "netflix.grpc:netflix-grpc-logging-context", "netflix.grpc:netflix-grpc-metatron", "netflix.grpc:netflix-grpc-metatron-client", "netflix.grpc:netflix-grpc-name-resolver-guice", "netflix.grpc:netflix-grpc-netty", "netflix.grpc:netflix-grpc-request-context-bridge", "netflix.grpc:netflix-grpc-runtime-guice", "netflix.grpc:netflix-grpc-salp", "netflix.grpc:netflix-grpc-spectator", "netflix.grpc:netflix-grpc-testkit", "netflix.grpc:netflix-grpc-zipkin", "netflix:cpeauthorization-client-common", "netflix:cpeauthorization-client-spring", "netflix:cpeauthorization-common", "netflix:cpeauthorization-proto-definition", "netflix:dts-standalone-common", "netflix:eureka2-eureka1-registration-guice", "netflix:eureka2-grpc-client-core", "netflix:eureka2-grpc-client-guice", "netflix:gutenberg-proto-definition", "netflix:mantis-grpc-events", "netflix:ums-common", "netflix:ums-common-alerts", "netflix:ums-common-core"]}, "netflix.grpc:netflix-grpc-evcache": {"locked": "1.63.39", "transitive": ["com.netflix.ab:aballocator-client-guice", "com.netflix.dcms:dcms-rt-client", "com.netflix.memberattribute:memberattribute-client", "com.netflix.membership.memberdata:member-data-client", "com.netflix.mylist:mylist-client", "com.netflix.myratings:myratings-client", "com.netflix.spring:spring-boot-netflix-starter-grpc-client-evcache", "com.netflix.textevidence:text-evidence-client", "com.netflix.trailerpark:trailer-park-client", "com.netflix.vxs:vxs-proto-definition", "netflix:onramp-client-guice", "netflix:subscriberservice-client-guice"]}, "netflix.grpc:netflix-grpc-fit": {"locked": "1.63.39", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-grpc-client", "netflix.grpc:netflix-grpc-runtime-guice"]}, "netflix.grpc:netflix-grpc-json": {"locked": "1.63.39", "transitive": ["com.netflix.ge.common:common-grpc"]}, "netflix.grpc:netflix-grpc-loadbalancer": {"locked": "1.63.39", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-grpc-client", "netflix.grpc:netflix-grpc-runtime-guice"]}, "netflix.grpc:netflix-grpc-logging-context": {"locked": "1.63.39", "transitive": ["com.netflix.spring:spring-boot-netflix-grpc-client", "netflix.grpc:netflix-grpc-runtime-guice"]}, "netflix.grpc:netflix-grpc-metatron": {"locked": "1.63.39", "transitive": ["com.netflix.dgw:dgw-dal-common-manual-client", "com.netflix.gs2:group-service-2-client-guice", "com.netflix.mantis:nfmantis-publish-grpc-client", "com.netflix.mantis:nfmantis-publish-grpc-client-core", "com.netflix.membership.memberdata:member-data-client", "com.netflix.playapi:playapi-client", "com.netflix.plex:plex-member-client-guice", "com.netflix.simone:okja-client-guice", "com.netflix.spring:spring-boot-netflix-grpc-client", "com.netflix.vps:vps-client", "netflix.grpc:netflix-grpc-runtime-guice", "netflix.grpc:netflix-grpc-salp", "netflix:cms-client-guice-nocml", "netflix:gps-client-grpc-page", "netflix:gps-client-grpc-page-ext", "netflix:gutenberg-grpc-client", "netflix:onramp-client-guice", "netflix:subscriberservice-client-guice"]}, "netflix.grpc:netflix-grpc-metatron-client": {"locked": "1.63.39", "transitive": ["com.netflix.spring:spring-boot-netflix-grpc-client"]}, "netflix.grpc:netflix-grpc-metatron-netty": {"locked": "1.63.39", "transitive": ["netflix.grpc:netflix-grpc-metatron"]}, "netflix.grpc:netflix-grpc-name-resolver-guice": {"locked": "1.63.39", "transitive": ["netflix.grpc:netflix-grpc-request-context-bridge", "netflix.grpc:netflix-grpc-runtime-guice"]}, "netflix.grpc:netflix-grpc-netty": {"locked": "1.63.39", "transitive": ["netflix.grpc:netflix-grpc-metatron-netty", "netflix.grpc:netflix-grpc-runtime-guice"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.39", "transitive": ["com.netflix.ab:aballocator-proto-definition", "com.netflix.adsnativeadarbiter:ads-native-ad-arbiter-proto-definition", "com.netflix.apicollection:apicollection-proto-definition", "com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.appregistry.rt:appregistry-rt-proto-definition", "com.netflix.bulldozer:bulldozer-proto-definition", "com.netflix.chronos:chronos-proto-definition", "com.netflix.contentgroup:content-group-definition", "com.netflix.contentgroup:content-group-expression-proto", "com.netflix.dcms:dcms-rt-proto-definition", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.dgw.kv:dgw-kv-proto-definition", "com.netflix.dgw:dgw-dal-common-proto-definition", "com.netflix.dloc:dloc-proto-definition", "com.netflix.dynimo.proxy:dynimoproxy-proto-definition", "com.netflix.ember:ember-proto-definition", "com.netflix.emf:emf-proto-definition", "com.netflix.evolutionmetadata:evolution-metadata-proto-definition", "com.netflix.evolutionprotodefinition:evolution-proto-definition", "com.netflix.frankenstein:frankenstein-proto-definition", "com.netflix.gps.pagefallbackservice:gps-page-fallback-proto-definition", "com.netflix.gps.pagerouterservice:gps-page-router-proto-definition", "com.netflix.group.fallback.service:group-fallback-proto-definition", "com.netflix.growth.paymentexperiences:paymentexperiences-proto-definition", "com.netflix.growth.planspricing:growthplanspricing-proto-definition", "com.netflix.gs2:group-service-2-proto-definition", "com.netflix.hank:hank-proto-definition", "com.netflix.helpdgs:help-dgs-proto-definition", "com.netflix.hollowschemadefinition:hollowschemadefinition-proto-definition", "com.netflix.hydrus:hydrus-proto-definition", "com.netflix.littlebirds:little-birds-proto-definition", "com.netflix.mantis:nfmantis-publish-grpc-proto-definition", "com.netflix.memberattribute:memberattribute-proto-definition", "com.netflix.membercommerce:membercommerce-proto-definition", "com.netflix.memberprice:memberprice-proto-definition", "com.netflix.membership.memberdata:member-data-proto-definition", "com.netflix.mesh:mesh-api-java", "com.netflix.messaging.bran:bran-proto-definition", "com.netflix.messaging.varys:varys-proto-definition", "com.netflix.messagingconsent:messaging-consent-proto-definition", "com.netflix.microcontext:microcontext-model", "com.netflix.mosaic.datamodel:mosaic-noir-proto-definition", "com.netflix.mylist:mylist-proto-definition", "com.netflix.myratings:myratings-proto-definition", "com.netflix.napa:napa-page-proto-definition", "com.netflix.napa:napa-proto-definition", "com.netflix.nes:nes-proto-definition", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.ocp.coda:coda-proto-definition", "com.netflix.pacs:pacs-client", "com.netflix.pacs:pacs-proto-definition", "com.netflix.page:noir-page-proto-definition", "com.netflix.page:noir-proto-definition", "com.netflix.pagecontentcommon:page-content-common-proto-definition", "com.netflix.pagetaxonomy:pagetaxonomy-proto-definition", "com.netflix.partnersub.consors:consors-proto-definition", "com.netflix.patron:patron-proto-definition", "com.netflix.peas:auth-token-scopes", "com.netflix.playapi:playapi-proto-definition", "com.netflix.plex:plex-shared-proto-definition", "com.netflix.protovalidate:proto-validate", "com.netflix.pulsedgs:pulsedgs-proto-definition", "com.netflix.pulseobjectproto:pulse-object-proto-definition", "com.netflix.rasel:rasel-proto-definition", "com.netflix.rawhollow:rawhollow-proto-definition", "com.netflix.request:request-attrs-api", "com.netflix.scout:scout-proto-definition", "com.netflix.search.pash:pash-proto-definition", "com.netflix.shiraz:shiraz-proto-definition", "com.netflix.simone:okja-proto-definition", "com.netflix.singleingest:single-ingest-events-schema-definition", "com.netflix.skuservice:skuservice-proto-definition", "com.netflix.slimer:slimer-proto-definition", "com.netflix.sloar:sloar-proto-definition", "com.netflix.streamsaccountingservice:streams-accounting-service-proto-definition", "com.netflix.supermarket:esql-proto-definition", "com.netflix.supermarket:supermarket-proto-definition", "com.netflix.tela:tela-proto-definition", "com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.traffic.resilience:resilience-configuration-proto-definition", "com.netflix.trailerpark:trailer-park-proto-definition", "com.netflix.turboservice:turboservice-proto-definition", "com.netflix.uipersonalization:ui-personalization-proto-definition", "com.netflix.unifiedconfigservice:unifiedconfigservice-validation-proto-definition", "com.netflix.userauthservice2:userauthservice2-gamer-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-gaming-websocket-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-proto-common", "com.netflix.userauthservice2:userauthservice2-proto-definition", "com.netflix.userauthservice2:userauthservice2-sso-token-proto-definition", "com.netflix.userpreferences2:userpreferences2-proto-definition", "com.netflix.ust:ust-model", "com.netflix.venkman:venkman-proto-definition", "com.netflix.vhs:viewinghistoryservice-proto-definition", "com.netflix.vps:vps-proto-definition", "com.netflix.vxs:vxs-proto-definition", "com.netflix.workloadfacts:workload-facts-proto-definition-workload-core", "com.netflix.zipurl:zipurl-proto-definition", "netflix.contextflow:contextflow-proto-definition", "netflix.evolution.easel:easel-proto-definition", "netflix.evolution:message_interfaces", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-evcache", "netflix.grpc:netflix-grpc-logging-context", "netflix.grpc:netflix-grpc-runtime-guice", "netflix.grpc:netflix-grpc-testkit", "netflix:abzuulcontext", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge", "netflix:bookmark-query-service-proto-definition", "netflix:cinder-proto-definition", "netflix:cms-proto-definition", "netflix:cp1-proto", "netflix:demograph-core", "netflix:dhs-grpc-datamodel", "netflix:entityindex-proto-definition", "netflix:gandalf-authz-client", "netflix:gps-client-page-proto-definition", "netflix:gps-proto-common-definition", "netflix:gps-proto-group-definition", "netflix:gps-proto-page-definition", "netflix:gps-proto-section-definition", "netflix:gutenberg-proto-definition", "netflix:images-proto-definition", "netflix:onramp-proto-definition", "netflix:pds-commons-viewing-history-proto-datamodel", "netflix:playapi-common-proto-definition", "netflix:playapi-events-proto-definition", "netflix:recs-toolkit-proto-definition", "netflix:session-logs-grpc-datamodel", "netflix:staypuft-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition", "netflix:subscriptiondata-proto-definition"]}, "netflix.grpc:netflix-grpc-request-context-bridge": {"locked": "1.63.39", "transitive": ["com.netflix.dgw:dgw-dal-common-manual-client", "com.netflix.spring:spring-boot-netflix-grpc-client", "netflix.grpc:netflix-grpc-runtime-guice", "netflix.grpc:netflix-grpc-salp"]}, "netflix.grpc:netflix-grpc-runtime-guice": {"locked": "1.63.39", "transitive": ["com.netflix.ab:aballocator-client-guice", "com.netflix.ge.common:common-grpc", "com.netflix.gs2:group-service-2-client-guice", "com.netflix.mantis:nfmantis-publish-grpc-client", "com.netflix.napa:napa-client", "com.netflix.pacs:pacs-client", "com.netflix.playapi:playapi-client", "com.netflix.plex:plex-member-client-guice", "com.netflix.search.pash:pash-client", "com.netflix.simone:okja-client-guice", "com.netflix.sloar:sloar-client", "com.netflix.textevidence:text-evidence-common", "com.netflix.vps:vps-client", "netflix:cms-client-guice-nocml", "netflix:cryptex-agent-guice", "netflix:gps-client-grpc-page-ext", "netflix:gutenberg-grpc-client", "netflix:images-client-fallbacks-guice", "netflix:images-client-guice", "netflix:streaming-cdnurl", "netflix:ums-common"]}, "netflix.grpc:netflix-grpc-salp": {"locked": "1.63.39", "transitive": ["netflix.grpc:netflix-grpc-runtime-guice"]}, "netflix.grpc:netflix-grpc-spectator": {"locked": "1.63.39", "transitive": ["com.netflix.dgw:dgw-dal-common-manual-client", "netflix.grpc:netflix-grpc-metatron", "netflix.grpc:netflix-grpc-metatron-netty", "netflix.grpc:netflix-grpc-runtime-guice", "netflix.grpc:netflix-grpc-salp", "netflix.grpc:netflix-grpc-zipkin", "netflix:eureka2-grpc-client-guice"]}, "netflix.grpc:netflix-grpc-testkit": {"locked": "1.63.39", "transitive": ["netflix:api-service-layer"]}, "netflix.grpc:netflix-grpc-zipkin": {"locked": "1.63.39", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-grpc-client"]}, "netflix.io.grpc:grpc-alts-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-xds-nflx"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.2", "transitive": ["netflix.contextflow:contextflow-transport-grpc", "netflix.io.grpc:grpc-auth-nflx", "netflix.io.grpc:grpc-context-nflx", "netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-inprocess-nflx", "netflix.io.grpc:grpc-netty-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx", "netflix.io.grpc:grpc-testing-nflx", "netflix.io.grpc:grpc-util-nflx", "netflix:eureka2-eureka1-bridge"]}, "netflix.io.grpc:grpc-auth-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-alts-nflx", "netflix.io.grpc:grpc-xds-nflx"]}, "netflix.io.grpc:grpc-context-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-fit", "netflix.grpc:netflix-grpc-metatron", "netflix.grpc:netflix-grpc-request-context-bridge", "netflix.grpc:netflix-grpc-spectator", "netflix.grpc:netflix-grpc-testkit", "netflix.io.grpc:grpc-alts-nflx", "netflix.io.grpc:grpc-core-nflx"]}, "netflix.io.grpc:grpc-core-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-admin", "netflix.grpc:netflix-grpc-binding-context", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-evcache", "netflix.grpc:netflix-grpc-fit", "netflix.grpc:netflix-grpc-json", "netflix.grpc:netflix-grpc-loadbalancer", "netflix.grpc:netflix-grpc-logging-context", "netflix.grpc:netflix-grpc-metatron", "netflix.grpc:netflix-grpc-metatron-client", "netflix.grpc:netflix-grpc-metatron-netty", "netflix.grpc:netflix-grpc-name-resolver-guice", "netflix.grpc:netflix-grpc-netty", "netflix.grpc:netflix-grpc-request-context-bridge", "netflix.grpc:netflix-grpc-salp", "netflix.grpc:netflix-grpc-spectator", "netflix.grpc:netflix-grpc-testkit", "netflix.grpc:netflix-grpc-zipkin", "netflix.io.grpc:grpc-alts-nflx", "netflix.io.grpc:grpc-grpclb-nflx", "netflix.io.grpc:grpc-inprocess-nflx", "netflix.io.grpc:grpc-netty-shaded-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-testing-nflx", "netflix.io.grpc:grpc-util-nflx", "netflix.io.grpc:grpc-xds-nflx", "netflix:metatron-decrypt", "netflix:metatron-decryptserver-proto-definitions", "netflix:metatron-ipc-common-assume"]}, "netflix.io.grpc:grpc-grpclb-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-alts-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.2", "transitive": ["netflix.io.grpc:grpc-alts-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-auth-nflx", "netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-grpclb-nflx", "netflix.io.grpc:grpc-inprocess-nflx", "netflix.io.grpc:grpc-netty-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-stub-nflx", "netflix.io.grpc:grpc-util-nflx"]}, "netflix.io.grpc:grpc-inprocess-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.io.grpc:grpc-testing-nflx"]}, "netflix.io.grpc:grpc-netty-shaded-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-netty", "netflix.grpc:netflix-grpc-testkit", "netflix.io.grpc:grpc-alts-nflx", "netflix.io.grpc:grpc-xds-nflx", "netflix:metatron-decrypt", "netflix:metatron-ipc-common-assume"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-request-context-bridge", "netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.2", "transitive": ["com.netflix.ab:aballocator-proto-definition", "com.netflix.adsnativeadarbiter:ads-native-ad-arbiter-proto-definition", "com.netflix.apicollection:apicollection-proto-definition", "com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.appregistry.rt:appregistry-rt-proto-definition", "com.netflix.bulldozer:bulldozer-proto-definition", "com.netflix.chronos:chronos-proto-definition", "com.netflix.contentgroup:content-group-definition", "com.netflix.contentgroup:content-group-expression-proto", "com.netflix.dcms:dcms-rt-proto-definition", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.dgw.kv:dgw-kv-proto-definition", "com.netflix.dgw:dgw-dal-common-proto-definition", "com.netflix.dloc:dloc-proto-definition", "com.netflix.dynimo.proxy:dynimoproxy-proto-definition", "com.netflix.ember:ember-proto-definition", "com.netflix.emf:emf-proto-definition", "com.netflix.evolutionmetadata:evolution-metadata-proto-definition", "com.netflix.evolutionprotodefinition:evolution-proto-definition", "com.netflix.frankenstein:frankenstein-proto-definition", "com.netflix.gps.pagefallbackservice:gps-page-fallback-proto-definition", "com.netflix.gps.pagerouterservice:gps-page-router-proto-definition", "com.netflix.group.fallback.service:group-fallback-proto-definition", "com.netflix.growth.paymentexperiences:paymentexperiences-proto-definition", "com.netflix.growth.planspricing:growthplanspricing-proto-definition", "com.netflix.gs2:group-service-2-proto-definition", "com.netflix.hank:hank-proto-definition", "com.netflix.helpdgs:help-dgs-proto-definition", "com.netflix.hollowschemadefinition:hollowschemadefinition-proto-definition", "com.netflix.hydrus:hydrus-proto-definition", "com.netflix.littlebirds:little-birds-proto-definition", "com.netflix.mantis:nfmantis-publish-grpc-proto-definition", "com.netflix.memberattribute:memberattribute-proto-definition", "com.netflix.membercommerce:membercommerce-proto-definition", "com.netflix.memberprice:memberprice-proto-definition", "com.netflix.membership.memberdata:member-data-proto-definition", "com.netflix.mesh:mesh-api-java", "com.netflix.messaging.bran:bran-proto-definition", "com.netflix.messaging.varys:varys-proto-definition", "com.netflix.messagingconsent:messaging-consent-proto-definition", "com.netflix.microcontext:microcontext-model", "com.netflix.mylist:mylist-proto-definition", "com.netflix.myratings:myratings-proto-definition", "com.netflix.napa:napa-page-proto-definition", "com.netflix.napa:napa-proto-definition", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.ocp.coda:coda-proto-definition", "com.netflix.pacs:pacs-proto-definition", "com.netflix.page:noir-page-proto-definition", "com.netflix.page:noir-proto-definition", "com.netflix.pagecontentcommon:page-content-common-proto-definition", "com.netflix.partnersub.consors:consors-proto-definition", "com.netflix.patron:patron-proto-definition", "com.netflix.peas:auth-token-scopes", "com.netflix.playapi:playapi-proto-definition", "com.netflix.plex:plex-member-client-guice", "com.netflix.plex:plex-member-proto-definition", "com.netflix.plex:plex-shared-proto-definition", "com.netflix.protovalidate:proto-validate", "com.netflix.pulsedgs:pulsedgs-proto-definition", "com.netflix.rasel:rasel-proto-definition", "com.netflix.rawhollow:rawhollow-proto-definition", "com.netflix.scout:scout-proto-definition", "com.netflix.search.pash:pash-proto-definition", "com.netflix.shiraz:shiraz-proto-definition", "com.netflix.simone:okja-proto-definition", "com.netflix.singleingest:single-ingest-events-schema-definition", "com.netflix.skuservice:skuservice-proto-definition", "com.netflix.slimer:slimer-proto-definition", "com.netflix.sloar:sloar-proto-definition", "com.netflix.streamsaccountingservice:streams-accounting-service-proto-definition", "com.netflix.supermarket:esql-proto-definition", "com.netflix.supermarket:supermarket-proto-definition", "com.netflix.tela:tela-proto-definition", "com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.traffic.resilience:resilience-configuration-proto-definition", "com.netflix.trailerpark:trailer-park-proto-definition", "com.netflix.turboservice:turboservice-proto-definition", "com.netflix.uipersonalization:ui-personalization-proto-definition", "com.netflix.unifiedconfigservice:unifiedconfigservice-validation-proto-definition", "com.netflix.userauthservice2:userauthservice2-gamer-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-gaming-websocket-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-proto-common", "com.netflix.userauthservice2:userauthservice2-proto-definition", "com.netflix.userauthservice2:userauthservice2-sso-token-proto-definition", "com.netflix.userpreferences2:userpreferences2-proto-definition", "com.netflix.ust:ust-model", "com.netflix.venkman:venkman-proto-definition", "com.netflix.vhs:viewinghistoryservice-proto-definition", "com.netflix.vps:vps-proto-definition", "com.netflix.zipurl:zipurl-proto-definition", "netflix.evolution.easel:easel-proto-definition", "netflix.evolution:message_interfaces", "netflix.grpc:netflix-grpc-admin", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-json", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.grpc:netflix-grpc-request-context-bridge", "netflix.grpc:netflix-grpc-testkit", "netflix.io.grpc:grpc-alts-nflx", "netflix.io.grpc:grpc-grpclb-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-xds-nflx", "netflix:abzuulcontext", "netflix:basicTypes-proto", "netflix:bookmark-query-service-proto-definition", "netflix:cinder-proto-definition", "netflix:cms-proto-definition", "netflix:cp1-proto", "netflix:cpeauthorization-proto-definition", "netflix:demograph-core", "netflix:dhs-grpc-datamodel", "netflix:entityindex-proto-definition", "netflix:eureka2-proto-definition", "netflix:gandalf-authz-client", "netflix:gps-client-page-proto-definition", "netflix:gutenberg-proto-definition", "netflix:images-proto-definition", "netflix:metatron-decryptserver-proto-definitions", "netflix:metatron-ipc-common-assume", "netflix:netflix-schemas", "netflix:onramp-proto-definition", "netflix:pds-commons-viewing-history-proto-datamodel", "netflix:playapi-common-proto-definition", "netflix:playapi-events-proto-definition", "netflix:recs-toolkit-proto-definition", "netflix:session-logs-grpc-datamodel", "netflix:staypuft-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition", "netflix:subscriptiondata-proto-definition"]}, "netflix.io.grpc:grpc-services-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.io.grpc:grpc-xds-nflx"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.2", "transitive": ["com.netflix.ab:aballocator-proto-definition", "com.netflix.adsnativeadarbiter:ads-native-ad-arbiter-proto-definition", "com.netflix.apicollection:apicollection-proto-definition", "com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.appregistry.rt:appregistry-rt-proto-definition", "com.netflix.bulldozer:bulldozer-proto-definition", "com.netflix.chronos:chronos-proto-definition", "com.netflix.contentgroup:content-group-definition", "com.netflix.cryptex2:cryptex2-client-jvmonly-common", "com.netflix.dcms:dcms-rt-proto-definition", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.dgw.kv:dgw-kv-proto-definition", "com.netflix.dgw:dgw-dal-common-proto-definition", "com.netflix.dloc:dloc-proto-definition", "com.netflix.dynimo.proxy:dynimoproxy-proto-definition", "com.netflix.ember:ember-proto-definition", "com.netflix.emf:emf-proto-definition", "com.netflix.evolutionmetadata:evolution-metadata-proto-definition", "com.netflix.evolutionprotodefinition:evolution-proto-definition", "com.netflix.frankenstein:frankenstein-proto-definition", "com.netflix.gps.pageclientslib:gps-page-clients-common", "com.netflix.gps.pagefallbackservice:gps-page-fallback-proto-definition", "com.netflix.gps.pagerouterservice:gps-page-router-proto-definition", "com.netflix.group.fallback.service:group-fallback-proto-definition", "com.netflix.growth.paymentexperiences:paymentexperiences-proto-definition", "com.netflix.growth.planspricing:growthplanspricing-proto-definition", "com.netflix.gs2:group-service-2-proto-definition", "com.netflix.hank:hank-proto-definition", "com.netflix.helpdgs:help-dgs-proto-definition", "com.netflix.hollowschemadefinition:hollowschemadefinition-proto-definition", "com.netflix.hydrus:hydrus-proto-definition", "com.netflix.littlebirds:little-birds-proto-definition", "com.netflix.mantis:nfmantis-publish-grpc-proto-definition", "com.netflix.memberattribute:memberattribute-proto-definition", "com.netflix.membercommerce:membercommerce-proto-definition", "com.netflix.memberprice:memberprice-proto-definition", "com.netflix.membership.memberdata:member-data-proto-definition", "com.netflix.mesh:mesh-api-java", "com.netflix.messaging.bran:bran-proto-definition", "com.netflix.messaging.varys:varys-proto-definition", "com.netflix.messagingconsent:messaging-consent-proto-definition", "com.netflix.microcontext:microcontext-model", "com.netflix.mylist:mylist-proto-definition", "com.netflix.myratings:myratings-proto-definition", "com.netflix.napa:napa-page-proto-definition", "com.netflix.napa:napa-proto-definition", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.ocp.coda:coda-proto-definition", "com.netflix.pacs:pacs-proto-definition", "com.netflix.page:noir-page-proto-definition", "com.netflix.page:noir-proto-definition", "com.netflix.pagecontentcommon:page-content-common-proto-definition", "com.netflix.partnersub.consors:consors-proto-definition", "com.netflix.patron:patron-proto-definition", "com.netflix.peas:auth-token-scopes", "com.netflix.playapi:playapi-proto-definition", "com.netflix.plex:plex-shared-proto-definition", "com.netflix.protovalidate:proto-validate", "com.netflix.pulsedgs:pulsedgs-proto-definition", "com.netflix.rasel:rasel-proto-definition", "com.netflix.rawhollow:rawhollow-proto-definition", "com.netflix.scout:scout-proto-definition", "com.netflix.search.pash:pash-proto-definition", "com.netflix.shiraz:shiraz-proto-definition", "com.netflix.simone:okja-proto-definition", "com.netflix.singleingest:single-ingest-events-schema-definition", "com.netflix.skuservice:skuservice-proto-definition", "com.netflix.slimer:slimer-proto-definition", "com.netflix.sloar:sloar-proto-definition", "com.netflix.streamsaccountingservice:streams-accounting-service-proto-definition", "com.netflix.supermarket:esql-proto-definition", "com.netflix.supermarket:supermarket-proto-definition", "com.netflix.tela:tela-proto-definition", "com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.traffic.resilience:resilience-configuration-proto-definition", "com.netflix.trailerpark:trailer-park-proto-definition", "com.netflix.turboservice:turboservice-proto-definition", "com.netflix.uipersonalization:ui-personalization-proto-definition", "com.netflix.userauthservice2:userauthservice2-gamer-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-gaming-websocket-access-token-proto-definition", "com.netflix.userauthservice2:userauthservice2-proto-common", "com.netflix.userauthservice2:userauthservice2-proto-definition", "com.netflix.userauthservice2:userauthservice2-sso-token-proto-definition", "com.netflix.userpreferences2:userpreferences2-proto-definition", "com.netflix.ust:ust-model", "com.netflix.venkman:venkman-proto-definition", "com.netflix.vhs:viewinghistoryservice-proto-definition", "com.netflix.vps:vps-proto-definition", "com.netflix.zipurl:zipurl-proto-definition", "netflix.async.util:async-util", "netflix.evolution.easel:easel-proto-definition", "netflix.evolution:message_interfaces", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-json", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.grpc:netflix-grpc-testkit", "netflix.io.grpc:grpc-alts-nflx", "netflix.io.grpc:grpc-grpclb-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-testing-nflx", "netflix.io.grpc:grpc-xds-nflx", "netflix:abzuulcontext", "netflix:basicTypes-proto", "netflix:bookmark-query-service-proto-definition", "netflix:cinder-proto-definition", "netflix:cms-proto-definition", "netflix:cp1-proto", "netflix:cpeauthorization-proto-definition", "netflix:demograph-core", "netflix:dhs-grpc-datamodel", "netflix:entityindex-proto-definition", "netflix:eureka2-proto-definition", "netflix:gps-client-page-proto-definition", "netflix:gutenberg-proto-definition", "netflix:images-proto-definition", "netflix:map-logging-core", "netflix:metatron-decryptserver-proto-definitions", "netflix:metatron-ipc-common-assume", "netflix:netflix-schemas", "netflix:onramp-proto-definition", "netflix:pds-commons-viewing-history-proto-datamodel", "netflix:playapi-common-proto-definition", "netflix:playapi-events-proto-definition", "netflix:recs-toolkit-proto-definition", "netflix:session-logs-grpc-datamodel", "netflix:staypuft-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition", "netflix:subscriptiondata-proto-definition"]}, "netflix.io.grpc:grpc-testing-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-testkit", "netflix:api-service-layer"]}, "netflix.io.grpc:grpc-util-nflx": {"locked": "1.63.2", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.io.grpc:grpc-netty-shaded-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-testing-nflx", "netflix.io.grpc:grpc-xds-nflx"]}, "netflix.io.grpc:grpc-xds-nflx": {"locked": "1.63.2", "transitive": ["com.netflix.loadshedding:netflix-load-shedding-mesh"]}, "netflix.ipc.metrics:ipc-metrics-core": {"locked": "0.21.1", "transitive": ["netflix.ipc.metrics:ipc-metrics-lifecycle", "netflix.ipc.metrics:ipc-metrics-servlet", "netflix:nf-filter", "netflix:platform-ipc"]}, "netflix.ipc.metrics:ipc-metrics-lifecycle": {"locked": "0.21.1", "transitive": ["netflix:nf-filter", "netflix:platform-ipc"]}, "netflix.ipc.metrics:ipc-metrics-servlet": {"locked": "0.21.1", "transitive": ["netflix:nf-filter"]}, "netflix:abzuulcontext": {"locked": "0.26.0", "transitive": ["com.netflix.request:request-attrs-base-server", "netflix:api-global-dependencies"]}, "netflix:adminresources": {"locked": "4.7.710", "transitive": ["netflix:platform"]}, "netflix:akmsclient": {"locked": "4.7.710", "transitive": ["netflix:platform", "netflix:platform-aws"]}, "netflix:algo-commons-core": {"locked": "7.2.46", "transitive": ["netflix:api-global-dependencies", "netflix:group-impl"]}, "netflix:algo-commons-protos": {"locked": "7.2.46", "transitive": ["netflix:algo-commons-core"]}, "netflix:algo-metrics": {"locked": "7.2.46", "transitive": ["netflix:algo-commons-core", "netflix:group-impl"]}, "netflix:api": {"project": true, "transitive": ["netflix:api-global-dependencies", "netflix:dna-client", "netflix:dna-model"]}, "netflix:api-global-dependencies": {"project": true, "transitive": ["netflix:api", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:dna-client", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services", "netflix:dna-test"]}, "netflix:api-service-layer": {"project": true, "transitive": ["netflix:api", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:dna-client", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services", "netflix:dna-test"]}, "netflix:apputils": {"locked": "1.53.0", "transitive": ["netflix:membership-types", "netflix:urlutil", "netflix:webplatform"]}, "netflix:asset-arrival-dates-client": {"locked": "1.2.2", "transitive": ["com.netflix.evidence:dataprism"]}, "netflix:atlas-client": {"locked": "2.29.10", "transitive": ["netflix.grpc:netflix-grpc-runtime-guice", "netflix.grpc:netflix-grpc-salp", "netflix:api-global-dependencies", "netflix:base-server", "netflix:mantis-realtime-events", "netflix:metrics-explorer", "netflix:nf-karyon-core", "netflix:platform-utils", "netflix:streaming-metrics-core"]}, "netflix:atlas-plugin": {"locked": "2.29.10", "transitive": ["netflix:base-server", "netflix:mantis-realtime-events", "netflix:streaming-metrics"]}, "netflix:authorizable-context": {"locked": "4.0.2", "transitive": ["com.netflix.identity:identity-context-api", "com.netflix.nflxe2etokens:nflx-e2etokens-validation", "com.netflix.spring:spring-boot-netflix-security-api", "netflix:gandalf-authz-client"]}, "netflix:axion-formats": {"locked": "5.40.4", "transitive": ["com.netflix.messaging.varys:varys-proto-definition"]}, "netflix:base-explorer": {"locked": "4.7.710", "transitive": ["netflix:metrics-explorer", "netflix:platform", "netflix:platform-chukwaClient"]}, "netflix:base-server": {"locked": "4.7.710", "transitive": ["com.netflix.ge.common:common-service", "netflix:api"]}, "netflix:base-server-explorer": {"locked": "2.1573.0", "transitive": ["netflix:api"]}, "netflix:base-server-plugins": {"locked": "1.16.0", "transitive": ["netflix:base-server", "netflix:platform-management"]}, "netflix:basicTypes": {"locked": "1.88.0", "transitive": ["com.netflix.evidence:dataprism", "com.netflix.evidence:dataprism-guice", "com.netflix.evidence:dataprism-spring", "com.netflix.gps.pageclientslib:gps-page-clients-common", "com.netflix.gps.pageclientslib:gps-page-clients-nes", "com.netflix.nes:nes-utils-dispatch", "com.netflix.page:noir-page-proto-definition", "com.netflix.request:request-attrs-api", "com.netflix.request:request-attrs-base-server", "com.netflix.request:request-attrs-core", "com.netflix.supermarket:supermarket-entityset-base", "com.netflix.ust:ust-access", "com.netflix.vxs:vxs-proto-definition", "netflix.grpc:netflix-grpc-logging-context", "netflix:api-global-dependencies", "netflix:asset-arrival-dates-client", "netflix:base-server", "netflix:basicTypes-proto-bridge", "netflix:cgl-common-lite", "netflix:countryset", "netflix:cp1-datamodel", "netflix:entityindex", "netflix:geoip-common", "netflix:group-attribute", "netflix:group-interface", "netflix:l10ncommon-resourcelookup", "netflix:map-datamodel", "netflix:nfcontext-interface", "netflix:nfi18n-core", "netflix:playlist-datamodel", "netflix:pnp-datamodel", "netflix:recs-toolkit-core", "netflix:recs-toolkit-protocol", "netflix:search_common_630", "netflix:search_common_core", "netflix:server-context", "netflix:thumbs-data", "netflix:thumbs-datamodel", "netflix:urlutil", "netflix:vmsmodel"]}, "netflix:basicTypes-proto": {"locked": "1.88.0", "transitive": ["com.netflix.evolutionprotodefinition:evolution-proto-definition", "com.netflix.littlebirds:little-birds-proto-definition", "com.netflix.microcontext:microcontext-model", "com.netflix.microcontext:microcontext-resolver", "com.netflix.myratings:myratings-proto-definition", "com.netflix.napa:napa-page-proto-definition", "com.netflix.nes:nes-proto-definition", "com.netflix.page:noir-page-proto-definition", "com.netflix.pagecontentcommon:page-content-common-proto-definition", "com.netflix.pagetaxonomy:pagetaxonomy-proto-definition", "com.netflix.patron:patron-proto-definition", "com.netflix.search.pash:pash-proto-definition", "com.netflix.supermarket:esql-proto-definition", "com.netflix.supermarket:supermarket-proto-definition", "com.netflix.trailerpark:trailer-park-proto-definition", "com.netflix.vxs:vxs-proto-definition", "netflix:basicTypes-proto-bridge", "netflix:cp1-proto", "netflix:gps-proto-group-definition", "netflix:passport"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.88.0", "transitive": ["com.netflix.ember:ember-proto-definition", "com.netflix.evidence:dataprism-spring", "com.netflix.group.fallback.service:group-fallback-proto-definition", "com.netflix.gs2:group-service-2-proto-definition", "com.netflix.microcontext:microcontext-access", "com.netflix.microcontext:microcontext-init", "com.netflix.microcontext:microcontext-resolver", "com.netflix.mylist:mylist-common", "com.netflix.mylist:mylist-proto-definition", "com.netflix.myratings:myratings-client", "com.netflix.page:noir-page-proto-definition", "com.netflix.request:request-attrs-api", "com.netflix.supermarket:entitysets-query-language", "com.netflix.supermarket:supermarket-entityset", "com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.trailerpark:trailer-park-proto-definition", "com.netflix.ust:ust-access", "netflix:cms-proto-definition", "netflix:cp1-proto-bridge", "netflix:group-impl", "netflix:images-proto-definition", "netflix:onramp-proto-definition", "netflix:recs-toolkit-proto-definition"]}, "netflix:bookmark-query-service-client": {"locked": "1.739.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "netflix:bookmark-query-service-proto-definition": {"locked": "1.739.0", "transitive": ["com.netflix.ust:ust-access", "netflix:bookmark-query-service-client"]}, "netflix:cgl-common-lite": {"locked": "449.1.208", "transitive": ["com.netflix.gps.pageclientslib:gps-page-clients-common", "com.netflix.gps.pageclientslib:gps-page-clients-nes", "netflix:gps-client-grpc-page-ext", "netflix:group-impl", "netflix:map-logging-core"]}, "netflix:cinder-core": {"locked": "7.22.10", "transitive": ["com.netflix.cpe.merchintention:merchintention-cinderclientcodegen", "com.netflix.cpe.merchintention:merchintention-merchintentlanguages-client", "com.netflix.dcms:dcms-cinder-client", "com.netflix.dcms:dcms-cinder-datamodel", "com.netflix.gengo:gengo-cinderclient", "com.netflix.imagesfallbackgenerator:images-fallback-datamodel", "com.netflix.imagesfallbackgenerator:images-fallback-generator-client", "com.netflix.rawhollow:rawhollow-client-core", "com.netflix.spring:spring-boot-netflix-starter-cinder-consumer", "com.netflix.urlkeys:urlkeys-cinderclient", "netflix:api-global-dependencies", "netflix:cinder-lifecycle", "netflix:contentpreview-contentpreview-apis", "netflix:contentpreview-model", "netflix:entityindex-rules-engine", "netflix:turbodata-ext", "netflix:videometadata-client"]}, "netflix:cinder-lifecycle": {"locked": "7.22.10", "transitive": ["com.netflix.evidence:dataprism-guice", "com.netflix.hollowtransformer:approved-vmsinmemory-client", "com.netflix.textevidence.synopsisfeedclient:text-evidence-synopsis-feed-client", "com.netflix.textevidence.titlefeed:text-evidence-title-feed-client", "com.netflix.textevidence:text-evidence-client", "com.netflix.turbo.vmsnamedlist:vms-namedlist-client", "netflix:api-global-dependencies", "netflix:asset-arrival-dates-client", "netflix:contentpreview-api-guice", "netflix:dts-client", "netflix:entityindex", "netflix:images-client-fallbacks", "netflix:images-client-fallbacks-guice", "netflix:obelix-client", "netflix:oscar-containers-client", "netflix:oscar-containers-model", "netflix:streaming-cdnurl", "netflix:turbodata", "netflix:turbodata-ext", "netflix:videometadata-client"]}, "netflix:cinder-proto-definition": {"locked": "7.22.10", "transitive": ["netflix:cinder-core"]}, "netflix:cis-aws-sdk-java-instrumentor-core": {"locked": "0.504.0", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-aws"]}, "netflix:cmp-core": {"locked": "1.343.0", "transitive": ["netflix:mercury-common", "netflix:ums-common"]}, "netflix:cms-client-guice-nocml": {"locked": "13.732.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "netflix:cms-proto-definition": {"locked": "13.732.0", "transitive": ["com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.ust:ust-model", "netflix:cms-client-guice-nocml"]}, "netflix:contentpreview-api-guice": {"locked": "267.1.0", "transitive": ["netflix:videometadata-client"]}, "netflix:contentpreview-contentpreview-apis": {"locked": "267.1.0", "transitive": ["netflix:contentpreview-api-guice", "netflix:contentpreview-dataprovider"]}, "netflix:contentpreview-dataprovider": {"locked": "267.1.0", "transitive": ["netflix:contentpreview-lookup"]}, "netflix:contentpreview-lookup": {"locked": "267.1.0", "transitive": ["netflix:videometadata-client"]}, "netflix:contentpreview-model": {"locked": "267.1.0", "transitive": ["netflix:contentpreview-dataprovider"]}, "netflix:country-launch": {"locked": "74.627", "transitive": ["com.netflix.evidence:dataprism-guice", "com.netflix.evidence:dataprism-spring", "netflix:videometadata-client"]}, "netflix:countryset": {"locked": "1.11", "transitive": ["netflix:entityindex", "netflix:turbodata"]}, "netflix:cp1-datamodel": {"locked": "1.24.3", "transitive": ["netflix:cp1-proto-bridge", "netflix:map-logging-core"]}, "netflix:cp1-proto": {"locked": "1.24.3", "transitive": ["com.netflix.gps.pagerouterservice:gps-page-router-proto-definition", "netflix:cp1-proto-bridge", "netflix:gps-client-page-proto-definition", "netflix:gps-proto-common-definition", "netflix:gps-proto-page-definition"]}, "netflix:cp1-proto-bridge": {"locked": "1.24.3", "transitive": ["com.netflix.group.fallback.service:group-fallback-proto-definition", "com.netflix.gs2:group-service-2-proto-definition", "netflix:group-interface"]}, "netflix:cpeauthorization-client-common": {"locked": "103.11.0", "transitive": ["netflix:cpeauthorization-client-spring"]}, "netflix:cpeauthorization-client-spring": {"locked": "103.11.0", "transitive": ["com.netflix.spring:spring-boot-netflix-sso-authz"]}, "netflix:cpeauthorization-common": {"locked": "103.11.0", "transitive": ["netflix:cpeauthorization-client-common", "netflix:cpeauthorization-client-spring"]}, "netflix:cpeauthorization-proto-definition": {"locked": "103.11.0", "transitive": ["netflix:cpeauthorization-common"]}, "netflix:cryptex-agent-guice": {"locked": "1.1.2", "transitive": ["netflix:api-global-dependencies"]}, "netflix:cryptex-agent-sbn": {"locked": "1.2.0", "transitive": ["netflix:api"]}, "netflix:crypto-common-util": {"locked": "1.74.0", "transitive": ["com.netflix.cryptex2:cryptex2-common"]}, "netflix:curator4-recipes-shaded": {"locked": "1.62.0", "transitive": ["netflix:nfcurator4"]}, "netflix:data-protobuf-codec": {"locked": "1.0.6", "transitive": ["com.netflix.dgw.kv:dgw-kv-client", "com.netflix.mylist:mylist-cache", "com.netflix.zuul.push:zuul-push-core", "netflix:dhs-common"]}, "netflix:demograph-core": {"locked": "4.2.605", "transitive": ["netflix.falcor.client:falcor-client-core", "netflix:api-global-dependencies", "netflix:demograph-scanner", "netflix:dna-model"]}, "netflix:demograph-scanner": {"locked": "4.2.605", "transitive": ["netflix:api-global-dependencies"]}, "netflix:dhs-common": {"locked": "11.394.0", "transitive": ["netflix:dhs-grpc-client"]}, "netflix:dhs-grpc-client": {"locked": "11.394.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:dhs-grpc-datamodel": {"locked": "11.394.0", "transitive": ["netflix:dhs-common", "netflix:dhs-grpc-client"]}, "netflix:dna-client": {"project": true, "transitive": ["netflix:api-global-dependencies", "netflix:dna-client", "netflix:dna-model", "netflix:dna-router", "netflix:dna-test"]}, "netflix:dna-model": {"project": true, "transitive": ["netflix:api-global-dependencies", "netflix:dna-client", "netflix:dna-model", "netflix:dna-router", "netflix:dna-test"]}, "netflix:dna-router": {"project": true, "transitive": ["netflix:api", "netflix:api-global-dependencies", "netflix:dna-client", "netflix:dna-model"]}, "netflix:dna-services": {"project": true, "transitive": ["netflix:api-global-dependencies", "netflix:dna-client", "netflix:dna-model"]}, "netflix:dna-test": {"project": true, "transitive": ["netflix:api-global-dependencies", "netflix:dna-client", "netflix:dna-model"]}, "netflix:dts-client": {"locked": "603.273.0", "transitive": ["com.netflix.dcms:dcms-rt-client", "netflix:api-global-dependencies", "netflix:dts-metrics", "netflix:map-logging-core"]}, "netflix:dts-common": {"locked": "603.273.0", "transitive": ["netflix:dts-client", "netflix:dts-datamodel"]}, "netflix:dts-datamodel": {"locked": "603.273.0", "transitive": ["netflix:dts-client"]}, "netflix:dts-metrics": {"locked": "603.273.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:dts-standalone-common": {"locked": "603.273.0", "transitive": ["netflix:api-global-dependencies", "netflix:dts-client", "netflix:dts-common", "netflix:map-logging-core"]}, "netflix:entityindex": {"locked": "1.1.19", "transitive": ["com.netflix.supermarket:supermarket-client", "com.netflix.supermarket:supermarket-entityset", "com.netflix.supermarket:supermarket-entityset-base", "com.netflix.supermarket:supermarket-fallback", "com.netflix.turboservice:turboservice-client", "netflix:api-global-dependencies", "netflix:entityindex-proto-bridge", "netflix:entityindex-rules-engine", "netflix:turbodata", "netflix:videometadata-client"]}, "netflix:entityindex-proto-bridge": {"locked": "1.1.13", "transitive": ["com.netflix.supermarket:supermarket-client"]}, "netflix:entityindex-proto-definition": {"locked": "1.1.13", "transitive": ["com.netflix.supermarket:supermarket-proto-definition", "netflix:entityindex-proto-bridge"]}, "netflix:entityindex-rules-antlr-shaded": {"locked": "1.9.4", "transitive": ["netflix:entityindex-rules-core"]}, "netflix:entityindex-rules-common": {"locked": "1.9.4", "transitive": ["com.netflix.turbo.vmsnamedlist:vms-namedlist-client"]}, "netflix:entityindex-rules-core": {"locked": "1.9.4", "transitive": ["com.netflix.turbo.vmsnamedlist:vms-namedlist-client"]}, "netflix:entityindex-rules-engine": {"locked": "1.9.4", "transitive": ["netflix:entityindex-rules-common", "netflix:entityindex-rules-core"]}, "netflix:eureka2-eureka1-bridge": {"locked": "0.84.0", "transitive": ["netflix:eureka2-eureka1-registration-guice"]}, "netflix:eureka2-eureka1-registration-guice": {"locked": "0.84.0", "transitive": ["netflix.grpc:netflix-grpc-runtime-guice"]}, "netflix:eureka2-grpc-client-core": {"locked": "2.8.1", "transitive": ["com.netflix.dgw:dgw-dal-common-manual-client", "com.netflix.spring:spring-boot-netflix-starter-grpc-client", "netflix:eureka2-grpc-client-guice", "netflix:gutenberg-client"]}, "netflix:eureka2-grpc-client-guice": {"locked": "2.8.1", "transitive": ["netflix.grpc:netflix-grpc-runtime-guice"]}, "netflix:eureka2-proto-definition": {"locked": "2.8.1", "transitive": ["netflix:eureka2-eureka1-bridge", "netflix:eureka2-grpc-client-core", "netflix:eureka2-grpc-client-guice"]}, "netflix:eureka2-tag-definition": {"locked": "2.8.1", "transitive": ["netflix:eureka2-eureka1-bridge", "netflix:eureka2-grpc-client-core", "netflix:eureka2-grpc-client-guice"]}, "netflix:evcache-client2": {"locked": "4.309.0", "transitive": ["com.netflix.nts.deviceperfmetrics.reader:ntsdeviceperfmetricsreader-core", "com.netflix.originals.row.filters:originals-row-filters-lifecycle", "com.netflix.plex:plex-member-client-guice", "netflix.grpc:netflix-grpc-evcache", "netflix:api-global-dependencies", "netflix:gps-client-grpc-page-ext", "netflix:obelix-client", "netflix:obelix-common", "netflix:onramp-client-guice", "netflix:streaming-steeredcdnurl", "netflix:subscriberservice-client-guice"]}, "netflix:evcache-nflx-client": {"locked": "5.134.0", "transitive": ["com.netflix.pacs:pacs-cache", "com.netflix.pacs:pacs-client", "com.netflix.spring:spring-boot-netflix-evcache", "netflix:evcache-client2"]}, "netflix:eventbus": {"locked": "1.12.0", "transitive": ["netflix:platform-dependency-command"]}, "netflix:fit": {"locked": "1.50.0", "transitive": ["com.netflix.s3:nfs3-core", "com.netflix.spring:spring-boot-netflix-starter-fit", "netflix.grpc:netflix-grpc-fit", "netflix:fit-impl", "netflix:platform-dependency-command"]}, "netflix:fit-impl": {"locked": "1.132.0", "transitive": ["com.netflix.appregistry.rt:appregistry-rt-common", "com.netflix.dcms:dcms-rt-common", "com.netflix.s3:nfs3-core", "com.netflix.spring:spring-boot-netflix-starter-fit", "netflix.grpc:netflix-grpc-fit", "netflix.grpc:netflix-grpc-testkit", "netflix:api-global-dependencies", "netflix:base-server", "netflix:dts-client", "netflix:platform-aws", "netflix:platform-dependency-command", "netflix:platform-ipc"]}, "netflix:gandalf-agent-embedded": {"locked": "0.138.0", "transitive": ["com.netflix.spring:spring-boot-netflix-sso-authz"]}, "netflix:gandalf-authz-client": {"locked": "0.138.0", "transitive": ["com.netflix.spring:spring-boot-netflix-sso-authz"]}, "netflix:geoip-common": {"locked": "2.384", "transitive": ["com.netflix.evidence:dataprism-spring", "com.netflix.microcontext:microcontext-init", "com.netflix.peas:peas-auth-events-logging", "com.netflix.request:request-attrs-base-server", "netflix:api-global-dependencies", "netflix:mantis-realtime-events", "netflix:nfcontext-impl", "netflix:streaming-steeredcdnurl", "netflix:videometadata-client"]}, "netflix:gps-client-grpc-page": {"locked": "857.65.0", "transitive": ["com.netflix.gps.pageclientslib:gps-page-clients-nes", "com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "netflix:gps-client-grpc-page-ext": {"locked": "857.65.0", "transitive": ["com.netflix.gps.pageclientslib:gps-page-clients-nes", "com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "netflix:gps-client-page-proto-definition": {"locked": "857.65.0", "transitive": ["com.netflix.gps.pageclientslib:gps-page-clients-nes", "com.netflix.gps.pagefallbackservice:gps-page-fallback-proto-definition", "com.netflix.gps.pagerouterservice:gps-page-router-proto-definition", "netflix:gps-client-grpc-page"]}, "netflix:gps-proto-common-definition": {"locked": "3.1059.0", "transitive": ["com.netflix.gps.pageclientslib:gps-page-clients-nes", "com.netflix.gps.pagerouterservice:gps-page-router-proto-definition", "com.netflix.gs2:group-service-2-proto-definition", "com.netflix.shiraz:shiraz-proto-definition", "netflix:gps-client-page-proto-definition", "netflix:gps-proto-group-definition", "netflix:gps-proto-section-definition", "netflix:group-attribute", "netflix:group-impl"]}, "netflix:gps-proto-group-definition": {"locked": "3.1059.0", "transitive": ["com.netflix.gps.pageclientslib:gps-page-clients-nes", "com.netflix.group.fallback.service:group-fallback-proto-definition", "com.netflix.gs2:group-service-2-proto-definition", "com.netflix.textevidence:text-evidence-proto-definition", "netflix:gps-proto-page-definition", "netflix:group-attribute", "netflix:group-impl", "netflix:group-interface"]}, "netflix:gps-proto-page-definition": {"locked": "3.1059.0", "transitive": ["com.netflix.gps.pageclientslib:gps-page-clients-nes", "com.netflix.gps.pagefallbackservice:gps-page-fallback-proto-definition", "com.netflix.gps.pagerouterservice:gps-page-router-proto-definition", "com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.ust:ust-model", "netflix:api-global-dependencies", "netflix:gps-client-grpc-page-ext", "netflix:gps-client-page-proto-definition", "netflix:group-attribute"]}, "netflix:gps-proto-section-definition": {"locked": "3.1059.0", "transitive": ["com.netflix.gps.pageclientslib:gps-page-clients-nes", "netflix:gps-proto-page-definition", "netflix:group-attribute"]}, "netflix:group-attribute": {"locked": "35.1710.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:group-impl": {"locked": "35.1710.0", "transitive": ["netflix:api-global-dependencies", "netflix:group-attribute"]}, "netflix:group-interface": {"locked": "35.1710.0", "transitive": ["netflix:group-attribute", "netflix:group-impl"]}, "netflix:gutenberg-client": {"locked": "7.5.13", "transitive": ["com.netflix.ab:aballocator-client-guice", "com.netflix.dcms:dcms-rt-client", "com.netflix.evolutionmetadata:evolution-metadata-client", "com.netflix.hank:hank-client", "netflix:api-global-dependencies", "netflix:cinder-core", "netflix:country-launch", "netflix:dts-client", "netflix:gutenberg-client-test", "netflix:streaming-steeredcdnurl", "netflix:thumbs-data", "netflix:turbodata", "netflix:videometadata-client", "netflix:whitecastle_v2"]}, "netflix:gutenberg-client-test": {"locked": "7.5.13", "transitive": ["netflix:api-global-dependencies"]}, "netflix:gutenberg-common": {"locked": "7.5.13", "transitive": ["netflix:api-global-dependencies", "netflix:gutenberg-client"]}, "netflix:gutenberg-grpc-client": {"locked": "7.5.13", "transitive": ["netflix:api-global-dependencies", "netflix:gutenberg-client"]}, "netflix:gutenberg-proto-definition": {"locked": "7.5.13", "transitive": ["netflix:api-global-dependencies", "netflix:gutenberg-common", "netflix:gutenberg-grpc-client"]}, "netflix:i18n-dictionaries": {"locked": "0.205.0", "transitive": ["netflix:nfi18n-core"]}, "netflix:images-client-fallbacks": {"locked": "30.5.0", "transitive": ["netflix:images-client-fallbacks-guice"]}, "netflix:images-client-fallbacks-guice": {"locked": "30.5.0", "transitive": ["com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "netflix:images-client-guice": {"locked": "30.5.0", "transitive": ["netflix:api-global-dependencies", "netflix:images-client-fallbacks-guice"]}, "netflix:images-proto-definition": {"locked": "30.5.0", "transitive": ["com.netflix.ust:ust-model", "netflix:images-client-fallbacks", "netflix:images-client-guice"]}, "netflix:jetty-alpn-provider": {"locked": "8.1.12.v20180117", "transitive": ["netflix:netty-handler-jettyalpn"]}, "netflix:l10ncommon-resourcelookup": {"locked": "1.6.0", "transitive": ["netflix:videometadata-client"]}, "netflix:mantis-grpc-events": {"locked": "1.44.0", "transitive": ["com.netflix.peas:peas-auth-events-logging", "com.netflix.peas:peas-logging"]}, "netflix:mantis-realtime-events": {"locked": "1.44.0", "transitive": ["com.netflix.ust:ust-access", "netflix:mantis-grpc-events", "netflix:map-logging-core"]}, "netflix:map-annotation-constants": {"locked": "232.35.0", "transitive": ["netflix:api-global-dependencies", "netflix:map-datamodel"]}, "netflix:map-datamodel": {"locked": "232.35.0", "transitive": ["com.netflix.ust:ust-common", "netflix:api-global-dependencies", "netflix:map-logging-core"]}, "netflix:map-logging-core": {"locked": "1.31.0", "transitive": ["com.netflix.ust:ust-access"]}, "netflix:membership-types": {"locked": "1.77.0", "transitive": ["netflix:ums-common-core"]}, "netflix:memory-tools": {"locked": "1.15", "transitive": ["netflix:videometadata-client"]}, "netflix:mercury-common": {"locked": "1.1606.0", "transitive": ["netflix:ums-common", "netflix:ums-common-alerts"]}, "netflix:metatron-common": {"locked": "1.493.0", "transitive": ["netflix:metatron-decrypt", "netflix:metatron-decryptserver-proto-definitions", "netflix:metatron-ipc-common-assume"]}, "netflix:metatron-decrypt": {"locked": "1.493.0", "transitive": ["com.netflix.spring:spring-boot-netflix-niws", "com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-sso-authn-embedded", "com.netflix.spring:spring-boot-netflix-starter-security", "com.netflix.ust:ust-access", "netflix:pandora-java-client"]}, "netflix:metatron-decryptserver-proto-definitions": {"locked": "1.493.0", "transitive": ["netflix:metatron-decrypt"]}, "netflix:metatron-ipc": {"locked": "1.493.0", "transitive": ["com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-starter-niws"]}, "netflix:metatron-ipc-common": {"locked": "1.493.0", "transitive": ["com.netflix.dgw:dgw-dal-common-lib", "com.netflix.identity:identity-context-api", "com.netflix.nflxe2etokens:nflx-e2etokens-validation", "com.netflix.runtime.metadata:runtime-metadata-client", "com.netflix.s3authsigncde:s3-auth-sign-common", "com.netflix.sbndevmetrics:sbn-dev-metrics-client", "com.netflix.spring:spring-boot-netflix-admin", "com.netflix.webclient:netflix-webclient", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-metatron", "netflix.grpc:netflix-grpc-metatron-netty", "netflix.grpc:netflix-grpc-spectator", "netflix:algo-commons-core", "netflix:api-global-dependencies", "netflix:cpeauthorization-client-spring", "netflix:evcache-nflx-client", "netflix:gandalf-authz-client", "netflix:gutenberg-common", "netflix:metatron-decrypt", "netflix:metatron-ipc", "netflix:metatron-ipc-common-assume", "netflix:nsac-client", "netflix:pandora-java-client"]}, "netflix:metatron-ipc-common-assume": {"locked": "1.493.0", "transitive": ["com.netflix.cryptex2:cryptex2-client-jvmonly-sbn", "com.netflix.spring:spring-boot-netflix-sso"]}, "netflix:metrics-explorer": {"locked": "1.47.0", "transitive": ["netflix:nf-karyon-admin-plugins"]}, "netflix:moduleRegistry": {"locked": "1.50.0", "transitive": ["netflix:nflibrary", "netflix:nflibrary-slim", "netflix:platform-management"]}, "netflix:netflix-bouncycastle-170": {"locked": "1.0.0", "transitive": ["com.netflix.cryptex2:cryptex2-common", "netflix:api-global-dependencies", "netflix:crypto-common-util", "netflix:group-impl", "netflix:group-interface"]}, "netflix:netflix-bouncycastle-176": {"locked": "1.0", "transitive": ["com.netflix.cryptex2:cryptex2-client-jvmonly-common"]}, "netflix:netflix-bouncycastle-180": {"locked": "1.0.0", "transitive": ["netflix:metatron-common"]}, "netflix:netflix-config": {"locked": "4.7.710", "transitive": ["com.netflix.appregistry.rt:appregistry-rt-common", "com.netflix.jaxrs:netflix-jaxrs-extensions-exceptionmapper", "com.netflix.s3:nfs3-api", "com.netflix.s3:nfs3-common", "netflix:abzuulcontext", "netflix:api-global-dependencies", "netflix:base-explorer", "netflix:dhs-common", "netflix:fit-impl", "netflix:geoip-common", "netflix:mantis-realtime-events", "netflix:nflibrary", "netflix:nflibrary-slim", "netflix:nfmantis-serde", "netflix:pandora-java-client", "netflix:platform", "netflix:platform-aws", "netflix:platform-chukwaClient", "netflix:platform-core", "netflix:platform-ipc", "netflix:platform-logimpl", "netflix:platform-pool", "netflix:platform-sla", "netflix:platform-utils"]}, "netflix:netflix-schemas": {"locked": "main-datetime.20250916172236", "transitive": ["com.netflix.cryptex2:cryptex2-client-jvmonly-common", "com.netflix.cryptex2:cryptex2-client-jvmonly-sbn", "com.netflix.cryptex2:cryptex2-common", "com.netflix.pacs:pacs-proto-definition", "com.netflix.spring:spring-boot-netflix-sso", "netflix:authorizable-context", "netflix:cpeauthorization-proto-definition", "netflix:cryptex-agent-guice"]}, "netflix:netty-handler-jettyalpn": {"locked": "4.1.51.Final-nflx-2", "transitive": ["netflix.grpc:netflix-grpc-metatron-netty", "netflix:metatron-decrypt", "netflix:metatron-ipc-common-assume"]}, "netflix:nf-archaius2": {"locked": "1.253.0", "transitive": ["netflix.grpc:netflix-grpc-archaius", "netflix.grpc:netflix-grpc-fit", "netflix.grpc:netflix-grpc-request-context-bridge", "netflix:nf-archaius2-platform-bridge"]}, "netflix:nf-archaius2-platform-bridge": {"locked": "1.253.0", "transitive": ["netflix:platform-chukwaClient", "netflix:platform-management"]}, "netflix:nf-build": {"locked": "1.4.0", "transitive": ["netflix:platform-management"]}, "netflix:nf-eureka-client": {"locked": "1.81", "transitive": ["com.netflix.spring:spring-boot-netflix-discovery", "netflix:eureka2-eureka1-bridge", "netflix:nflibrary", "netflix:platform-ipc"]}, "netflix:nf-eventbus": {"locked": "1.32.0", "transitive": ["netflix:base-server", "netflix:mantis-realtime-events"]}, "netflix:nf-eventbus-core": {"locked": "1.32.0", "transitive": ["netflix:nf-eventbus-guice", "netflix:platform-core", "netflix:platform-logimpl"]}, "netflix:nf-eventbus-guice": {"locked": "1.32.0", "transitive": ["netflix:nf-eventbus", "netflix:nflibrary"]}, "netflix:nf-filter": {"locked": "4.7.710", "transitive": ["netflix:base-server"]}, "netflix:nf-karyon-admin-plugins": {"locked": "3.0.1", "transitive": ["netflix:base-server"]}, "netflix:nf-karyon-core": {"locked": "3.0.1", "transitive": ["netflix:nf-karyon-admin-plugins"]}, "netflix:nfcontext-impl": {"locked": "1.16.0", "transitive": ["netflix:videometadata-client"]}, "netflix:nfcontext-interface": {"locked": "1.16.0", "transitive": ["com.netflix.vxs:vxs-proto-definition", "netflix:group-impl", "netflix:nfcontext-impl", "netflix:videometadata-client"]}, "netflix:nfcurator4": {"locked": "1.62.0", "transitive": ["netflix:cinder-core"]}, "netflix:nfi18n": {"locked": "2.251", "transitive": ["com.netflix.textevidence:text-evidence-common", "com.netflix.vxs:vxs-proto-definition", "netflix:obelix-client"]}, "netflix:nfi18n-core": {"locked": "2.250", "transitive": ["com.netflix.evidence:dataprism", "com.netflix.evidence:dataprism-guice", "com.netflix.evidence:dataprism-spring", "com.netflix.microcontext:microcontext-init", "com.netflix.supermarket:supermarket-cache-client", "com.netflix.supermarket:supermarket-entityset", "com.netflix.supermarket:supermarket-entityset-base", "com.netflix.textevidence:text-evidence-common", "netflix:api", "netflix:api-global-dependencies", "netflix:asset-arrival-dates-client", "netflix:base-server", "netflix:cmp-core", "netflix:country-launch", "netflix:images-client-fallbacks", "netflix:images-client-fallbacks-guice", "netflix:images-client-guice", "netflix:images-proto-definition", "netflix:l10ncommon-resourcelookup", "netflix:map-logging-core", "netflix:nf-filter", "netflix:nfcontext-interface", "netflix:nfi18n", "netflix:obelix-client", "netflix:obelix-common", "netflix:p3-converter", "netflix:partner-model-lib", "netflix:search_aggregator-client-sal", "netflix:search_aggregator-common", "netflix:turbodata", "netflix:turbodata-ext", "netflix:ums-common", "netflix:urlutil", "netflix:vmsmodel"]}, "netflix:nflibrary": {"locked": "4.7.710", "transitive": ["netflix:adminresources", "netflix:akmsclient", "netflix:nf-archaius2-platform-bridge", "netflix:nf-filter", "netflix:nf-karyon-admin-plugins", "netflix:nf-karyon-core", "netflix:platform", "netflix:platform-management", "netflix:webplatform"]}, "netflix:nflibrary-slim": {"locked": "4.7.710", "transitive": ["netflix:nfi18n-core", "netflix:nflibrary", "netflix:platform", "netflix:streaming-client"]}, "netflix:nfmantis-serde": {"locked": "1.7.0", "transitive": ["netflix:mantis-realtime-events"]}, "netflix:nfsso": {"locked": "1.56.0", "transitive": ["netflix:adminresources", "netflix:base-explorer"]}, "netflix:ngl": {"locked": "0.49.0", "transitive": ["netflix:nfi18n-core"]}, "netflix:nimble-int-java": {"locked": "0.8.0", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-nimble", "netflix:nf-eureka-client"]}, "netflix:noir-feed-converter": {"locked": "0.19.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:nsac-client": {"locked": "2.19.0", "transitive": ["netflix:platform-aws", "netflix:videometadata-client"]}, "netflix:nsac-common": {"locked": "2.19.0", "transitive": ["netflix:nsac-client", "netflix:platform-aws"]}, "netflix:obelix-client": {"locked": "8.0.0", "transitive": ["com.netflix.textevidence:text-evidence-client"]}, "netflix:obelix-common": {"locked": "8.0.0", "transitive": ["netflix:obelix-client"]}, "netflix:onramp-client-guice": {"locked": "2.326.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:onramp-proto-definition": {"locked": "2.326.0", "transitive": ["netflix:onramp-client-guice"]}, "netflix:oscar-containers-client": {"locked": "399.7.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:oscar-containers-model": {"locked": "399.7.0", "transitive": ["netflix:oscar-containers-client"]}, "netflix:p3-converter": {"locked": "1.59.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:pandora-java-client": {"locked": "0.84.3", "transitive": ["com.netflix.spring:spring-boot-netflix-sso", "netflix:cpeauthorization-common", "netflix:gandalf-authz-client"]}, "netflix:partner-model-lib": {"locked": "1.314", "transitive": ["com.netflix.partnersub.consors:consors-shared"]}, "netflix:passport": {"locked": "4.20.0", "transitive": ["com.netflix.appregistry.rt:appregistry-rt-common", "com.netflix.appregistry.rt:appregistry-rt-proto-definition", "com.netflix.dcms:dcms-rt-common", "com.netflix.dcms:dcms-rt-proto-definition", "com.netflix.hydrus:hydrus-proto-definition", "com.netflix.mantis:nfmantis-publish-common", "com.netflix.mylist:mylist-cache", "com.netflix.mylist:mylist-common", "com.netflix.pacs:pacs-cache", "com.netflix.pacs:pacs-client", "com.netflix.passport.test:passport-test-core", "com.netflix.peas:peas-auth-events-logging", "com.netflix.peas:peas-logging", "com.netflix.playapi:playapi-proto-definition", "com.netflix.plex:plex-member-proto-definition", "com.netflix.simone:okja-client-common", "com.netflix.slimer:slimer-proto-definition", "com.netflix.sloar:sloar-proto-definition", "com.netflix.spring:spring-boot-netflix-security-api", "com.netflix.userauthservice2:userauthservice2-proto-definition", "com.netflix.ust:ust-access", "com.netflix.venkman:venkman-proto-definition", "netflix:api-global-dependencies", "netflix:mantis-grpc-events", "netflix:mantis-realtime-events", "netflix:passport-actions-insights", "netflix:playapi-events-proto-definition", "netflix:subscriberservice-client-guice", "netflix:subscriberservice-proto-definition"]}, "netflix:passport-actions-insights": {"locked": "4.10.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:pds-commons-viewing-history-proto-datamodel": {"locked": "1.4585.0", "transitive": ["com.netflix.vhs:viewinghistoryservice-proto-definition"]}, "netflix:platform": {"locked": "4.7.710", "transitive": ["netflix:apputils", "netflix:base-server", "netflix:cmp-core", "netflix:map-logging-core", "netflix:mercury-common", "netflix:search_aggregator-client-esl", "netflix:search_aggregator-common", "netflix:thumbs-data", "netflix:ums-common", "netflix:ums-common-alerts", "netflix:ums-common-core", "netflix:urlutil", "netflix:webSecurity", "netflix:webplatform"]}, "netflix:platform-aws": {"locked": "4.7.710", "transitive": ["netflix:base-explorer", "netflix:platform", "netflix:platform-management"]}, "netflix:platform-chukwaClient": {"locked": "4.7.710", "transitive": ["netflix:mantis-realtime-events", "netflix:platform", "netflix:platform-management"]}, "netflix:platform-core": {"locked": "4.7.710", "transitive": ["netflix.grpc:netflix-grpc-logging-context", "netflix.grpc:netflix-grpc-salp", "netflix:api-global-dependencies", "netflix:base-explorer", "netflix:fit-impl", "netflix:nf-filter", "netflix:nflibrary", "netflix:platform", "netflix:platform-aws", "netflix:platform-dependency-command", "netflix:platform-ipc", "netflix:platform-logimpl", "netflix:platform-management", "netflix:platform-messaging", "netflix:platform-pool", "netflix:platform-sla", "netflix:platform-tracing-zipkin", "netflix:platform-utils"]}, "netflix:platform-dependency-command": {"locked": "2.0.6", "transitive": ["com.netflix.growth.paymentexperiences:paymentexperiences-grpc-client", "netflix:api-global-dependencies", "netflix:base-server", "netflix:platform-ipc", "netflix:search_aggregator-client-esl"]}, "netflix:platform-ipc": {"locked": "4.7.710", "transitive": ["com.netflix.memberprice:memberprice-adapter", "com.netflix.partnersub.psecommon:pse-common-client", "com.netflix.spring:spring-boot-netflix-niws", "com.netflix.spring:spring-boot-netflix-starter-niws", "com.netflix.zuul.push:zuul-push-core", "com.netflix.zuul.push:zuul-push-lifecycle", "netflix:akmsclient", "netflix:api-global-dependencies", "netflix:base-explorer", "netflix:cpeauthorization-common", "netflix:dhs-common", "netflix:gutenberg-common", "netflix:metatron-ipc", "netflix:nf-filter", "netflix:nf-karyon-admin-plugins", "netflix:partner-model-lib", "netflix:platform", "netflix:platform-chukwaClient", "netflix:platform-management", "netflix:streaming-client", "netflix:turbodata-ext"]}, "netflix:platform-jdk-compat": {"locked": "4.7.710", "transitive": ["netflix:platform-core"]}, "netflix:platform-logimpl": {"locked": "4.7.710", "transitive": ["netflix:platform", "netflix:platform-management", "netflix:platform-utils"]}, "netflix:platform-management": {"locked": "4.7.710", "transitive": ["netflix:platform"]}, "netflix:platform-messaging": {"locked": "4.7.710", "transitive": ["netflix:platform", "netflix:platform-aws", "netflix:platform-management"]}, "netflix:platform-pool": {"locked": "4.7.710", "transitive": ["netflix:platform", "netflix:platform-ipc", "netflix:platform-management"]}, "netflix:platform-sla": {"locked": "4.7.710", "transitive": ["netflix:platform", "netflix:platform-ipc"]}, "netflix:platform-tracing-zipkin": {"locked": "4.7.710", "transitive": ["netflix.grpc:netflix-grpc-runtime-guice", "netflix:platform", "netflix:platform-ipc"]}, "netflix:platform-utils": {"locked": "4.7.710", "transitive": ["com.netflix.s3:nfs3-common", "com.netflix.s3:nfs3-core", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:dhs-common", "netflix:nfcontext-impl", "netflix:nfi18n-core", "netflix:platform", "netflix:platform-aws", "netflix:platform-dependency-command", "netflix:platform-ipc", "netflix:platform-management", "netflix:streaming-metrics"]}, "netflix:playapi-common-proto-definition": {"locked": "0.5.1211", "transitive": ["com.netflix.playapi:playapi-proto-definition"]}, "netflix:playapi-events-proto-definition": {"locked": "0.2.807", "transitive": ["com.netflix.ust:ust-model", "netflix:api-global-dependencies"]}, "netflix:playlist-datamodel": {"locked": "4.4.0", "transitive": ["com.netflix.mylist:mylist-client"]}, "netflix:pnp-datamodel": {"locked": "3.0.1", "transitive": ["netflix:onramp-client-guice"]}, "netflix:recs-toolkit-core": {"locked": "3.890.0", "transitive": ["netflix:recs-toolkit-protocol"]}, "netflix:recs-toolkit-guice": {"locked": "3.890.0", "transitive": ["netflix:recs-toolkit-protocol"]}, "netflix:recs-toolkit-proto-definition": {"locked": "3.890.0", "transitive": ["com.netflix.shiraz:shiraz-proto-definition"]}, "netflix:recs-toolkit-protocol": {"locked": "3.890.0", "transitive": ["netflix:recs-toolkit-proto-definition"]}, "netflix:request-expiry": {"locked": "2.3.0", "transitive": ["com.netflix.webclient:netflix-webclient-spring-boot-starter", "netflix.grpc:netflix-grpc-request-context-bridge", "netflix:request-expiry-impl"]}, "netflix:request-expiry-impl": {"locked": "2.3.0", "transitive": ["netflix.grpc:netflix-grpc-runtime-guice", "netflix:base-server", "netflix:platform-ipc"]}, "netflix:search-data-common": {"locked": "1.36", "transitive": ["netflix:api-global-dependencies", "netflix:search-service-common", "netflix:search_common_630", "netflix:search_common_core"]}, "netflix:search-service-common": {"locked": "1.46", "transitive": ["netflix:api-global-dependencies"]}, "netflix:search_aggregator-client-esl": {"locked": "1.61.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:search_aggregator-client-sal": {"locked": "1.61.0", "transitive": ["netflix:search_aggregator-client-esl"]}, "netflix:search_aggregator-common": {"locked": "1.92.0", "transitive": ["netflix:api-global-dependencies", "netflix:search_aggregator-client-sal"]}, "netflix:search_common_630": {"locked": "1.72", "transitive": ["com.netflix.search.pash:search-napa-caller"]}, "netflix:search_common_core": {"locked": "1.8", "transitive": ["netflix:search_common_630"]}, "netflix:server-context": {"locked": "4.7.710", "transitive": ["com.netflix.cryptex2:cryptex2-client-jvmonly-common", "com.netflix.dgw.kv:dgw-kv-client-interceptor", "com.netflix.mantis:nfmantis-publish-common", "com.netflix.microcontext:microcontext-access", "com.netflix.microcontext:microcontext-init", "com.netflix.microcontext:microcontext-test", "com.netflix.mylist:mylist-cache", "com.netflix.mylist:mylist-common", "com.netflix.passport.test:passport-test-core", "com.netflix.spring:spring-boot-netflix-webmvc", "com.netflix.tracing:netflix-tracing-brave", "com.netflix.vxs:vxs-proto-definition", "com.netflix.webclient:netflix-webclient-spring-boot-starter", "netflix.contextflow:contextflow-access", "netflix.contextflow:contextflow-transport-web", "netflix.contextflow:contextflow-transport-webclient", "netflix.grpc:netflix-grpc-binding-context", "netflix.grpc:netflix-grpc-evcache", "netflix.grpc:netflix-grpc-json", "netflix.grpc:netflix-grpc-logging-context", "netflix.grpc:netflix-grpc-request-context-bridge", "netflix:api", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:fit", "netflix:fit-impl", "netflix:geoip-common", "netflix:netflix-config", "netflix:nfcontext-impl", "netflix:nfcontext-interface", "netflix:p3-converter", "netflix:platform", "netflix:platform-aws", "netflix:platform-core", "netflix:platform-dependency-command", "netflix:platform-ipc", "netflix:platform-utils", "netflix:request-expiry-impl", "netflix:server-context-test-junit-jupiter", "netflix:streaming-metrics-core"]}, "netflix:server-context-test-junit-jupiter": {"locked": "4.7.710", "transitive": ["netflix:platform"]}, "netflix:session-logs-grpc-client": {"locked": "2.960.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:session-logs-grpc-datamodel": {"locked": "2.960.0", "transitive": ["netflix:session-logs-grpc-client"]}, "netflix:statistics": {"locked": "1.7.0", "transitive": ["com.netflix.ribbon:ribbon-loadbalancer", "netflix:base-server", "netflix:platform-ipc", "netflix:platform-sla", "netflix:platform-utils"]}, "netflix:staypuft-proto-definition": {"locked": "1.369.0", "transitive": ["com.netflix.venkman:venkman-proto-definition"]}, "netflix:streaming-cdnurl": {"locked": "2.1345.0-rc.7", "transitive": ["netflix:streaming-steeredcdnurl"]}, "netflix:streaming-client": {"locked": "1.491.685", "transitive": ["netflix:dhs-common", "netflix:obelix-client", "netflix:obelix-common", "netflix:streaming-steeredcdnurl"]}, "netflix:streaming-metrics": {"locked": "1.491.685", "transitive": ["netflix:streaming-monitoring", "netflix:streaming-steeredcdnurl"]}, "netflix:streaming-metrics-core": {"locked": "1.491.686", "transitive": ["netflix:dts-client", "netflix:streaming-metrics"]}, "netflix:streaming-monitoring": {"locked": "1.491.685", "transitive": ["netflix:streaming-client"]}, "netflix:streaming-steeredcdnurl": {"locked": "1.1061.0", "transitive": ["netflix:streaming-steeredcdnurl-spring-autoconfigure"]}, "netflix:streaming-steeredcdnurl-spring-autoconfigure": {"locked": "1.1061.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:subscriberservice-client-guice": {"locked": "5.135.0", "transitive": ["com.netflix.gps.pageclientslib:gps-page-clients-nes", "com.netflix.microcontext:microcontext-resolver", "com.netflix.simone:okja-client-common", "com.netflix.ust:ust-access", "netflix:api-global-dependencies", "netflix:map-logging-core", "netflix:search_aggregator-client-sal", "netflix:ums-common-core"]}, "netflix:subscriberservice-common": {"locked": "5.135.0", "transitive": ["com.netflix.ab:aballocator-common", "com.netflix.microcontext:microcontext-init", "com.netflix.pacs:pacs-client", "com.netflix.search.pash:search-napa-caller", "netflix:membership-types", "netflix:subscriberservice-client-guice"]}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.135.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.135.0", "transitive": ["com.netflix.ab:aballocator-common", "com.netflix.dloc:dloc-proto-definition", "com.netflix.evolutionmetadata:evolution-metadata-proto-definition", "com.netflix.gps.pageclientslib:gps-page-clients-nes", "com.netflix.microcontext:microcontext-model", "com.netflix.pacs:pacs-client", "com.netflix.peas:peas-auth-events-logging", "com.netflix.supermarket:esql-proto-definition", "com.netflix.textevidence:text-evidence-proto-definition", "com.netflix.userauthservice2:userauthservice2-proto-definition", "com.netflix.ust:ust-model", "com.netflix.venkman:venkman-proto-definition", "netflix:subscriberservice-client-guice", "netflix:subscriberservice-common"]}, "netflix:subscriptiondata-proto-definition": {"locked": "1.3084.0", "transitive": ["com.netflix.growth.planspricing:growthplanspricing-proto-definition", "com.netflix.plex:plex-member-proto-definition"]}, "netflix:thumbs-data": {"locked": "4.152.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:thumbs-datamodel": {"locked": "4.152.0", "transitive": ["netflix:thumbs-data"]}, "netflix:tools-utils": {"locked": "1.130.0", "transitive": ["com.netflix.partnersub.consors:consors-shared"]}, "netflix:turbodata": {"locked": "2.17.14", "transitive": ["com.netflix.nes:nes-utils-dispatch", "com.netflix.textevidence:text-evidence-client", "com.netflix.textevidence:text-evidence-common", "com.netflix.ust:ust-access", "netflix:group-attribute", "netflix:group-interface"]}, "netflix:turbodata-ext": {"locked": "4.0.41", "transitive": ["com.netflix.textevidence:text-evidence-client", "com.netflix.textevidence:text-evidence-common"]}, "netflix:ums-common": {"locked": "2.6.881", "transitive": ["netflix:api-global-dependencies", "netflix:ums-common-alerts", "netflix:ums-common-core"]}, "netflix:ums-common-alerts": {"locked": "2.6.881", "transitive": ["netflix:api-global-dependencies"]}, "netflix:ums-common-core": {"locked": "2.6.881", "transitive": ["netflix:api-global-dependencies", "netflix:ums-common-alerts"]}, "netflix:urlutil": {"locked": "2.17.0", "transitive": ["netflix:api-global-dependencies"]}, "netflix:videometadata-client": {"locked": "71.10.1", "transitive": ["com.netflix.evidence:dataprism-guice", "netflix:videometadata-spring-starter"]}, "netflix:videometadata-spring-admin": {"locked": "71.10.1", "transitive": ["netflix:videometadata-spring-starter"]}, "netflix:videometadata-spring-core": {"locked": "71.10.1", "transitive": ["netflix:videometadata-spring-starter"]}, "netflix:videometadata-spring-starter": {"locked": "71.10.1", "transitive": ["com.netflix.evidence:dataprism-spring", "com.netflix.ust:ust-access", "netflix:api-global-dependencies"]}, "netflix:vmscodeanalysistools": {"locked": "71.10.1", "transitive": ["netflix:videometadata-client", "netflix:vmsmodel"]}, "netflix:vmsmodel": {"locked": "71.10.1", "transitive": ["netflix:api-service-layer", "netflix:noir-feed-converter", "netflix:videometadata-client"]}, "netflix:webSecurity": {"locked": "1.3.0", "transitive": ["netflix:urlutil"]}, "netflix:webplatform": {"locked": "1.19.0", "transitive": ["netflix:urlutil"]}, "netflix:whitecastle": {"locked": "1.44.0", "transitive": ["com.netflix.spring:spring-boot-netflix-webmvc"]}, "netflix:whitecastle_v2": {"locked": "1.61.0", "transitive": ["netflix:api-global-dependencies", "netflix:nfsso"]}, "nl.basjes.collections:prefixmap": {"locked": "1.0", "transitive": ["netflix:dts-standalone-common"]}, "org.agrona:agrona": {"locked": "1.5.1", "transitive": ["netflix:api-global-dependencies"]}, "org.antlr:antlr-runtime": {"locked": "3.4", "transitive": ["com.netflix.netflix-commons:netflix-infix", "netflix:nf-eventbus"]}, "org.antlr:stringtemplate": {"locked": "3.2.1", "transitive": ["org.antlr:antlr-runtime"]}, "org.apache.ant:ant": {"locked": "1.10.13", "transitive": ["netflix:api-global-dependencies", "org.apache.ant:ant-junit"]}, "org.apache.ant:ant-junit": {"locked": "1.10.13", "transitive": ["netflix:api-service-layer"]}, "org.apache.ant:ant-launcher": {"locked": "1.10.13", "transitive": ["org.apache.ant:ant"]}, "org.apache.avro:avro": {"locked": "1.8.2", "transitive": ["com.netflix.singleingest:single-ingest-events-schema-definition"]}, "org.apache.commons:commons-collections4": {"locked": "4.4", "transitive": ["com.flipkart.zjsonpatch:zjsonpatch", "netflix:cis-aws-sdk-java-instrumentor-core"]}, "org.apache.commons:commons-compress": {"locked": "1.8.1", "transitive": ["org.apache.avro:avro"]}, "org.apache.commons:commons-lang3": {"locked": "3.18.0", "transitive": ["com.netflix.appregistry.rt:appregistry-rt-common", "com.netflix.archaius:archaius2-core", "com.netflix.archaius:archaius2-persisted2", "com.netflix.chaski3:chaski3-core", "com.netflix.chaski3:chaski3-serde-jackson", "com.netflix.cryptex2:cryptex2-client-jvmonly-common", "com.netflix.cryptex2:cryptex2-common", "com.netflix.dcms:dcms-rt-common", "com.netflix.dgw.kv:dgw-kv-common", "com.netflix.evcache:evcache-core", "com.netflix.ge.common:common-service", "com.netflix.jaxrs:netflix-jaxrs-extensions-exceptionmapper", "com.netflix.runtime.metadata:runtime-metadata-client", "com.netflix.s3authsigncde:s3-auth-sign-common", "com.netflix.spring:spring-boot-netflix-logging", "com.netflix.spring:spring-boot-netflix-metrics", "com.netflix.tracing:netflix-tracing-brave", "com.netflix.tracing:netflix-tracing-utils", "com.netflix.vxs:vxs-proto-definition", "com.netflix.webclient:netflix-webclient", "io.swagger.core.v3:swagger-core-jakarta", "netflix.chestnut:chestnut3-logger", "netflix.grpc:netflix-grpc-common", "netflix:api-service-layer", "netflix:cpeauthorization-common", "netflix:dhs-common", "netflix:dts-client", "netflix:dts-datamodel", "netflix:dts-metrics", "netflix:map-datamodel", "netflix:nfmantis-serde", "netflix:pandora-java-client", "netflix:partner-model-lib", "netflix:platform-ipc", "netflix:recs-toolkit-protocol", "netflix:whitecastle_v2"]}, "org.apache.commons:commons-math": {"locked": "2.2", "transitive": ["com.netflix.netflix-commons:netflix-eventbus", "netflix:nf-eventbus", "netflix:platform-sla", "netflix:platform-utils"]}, "org.apache.commons:commons-math3": {"locked": "3.6.1", "transitive": ["netflix:p3-converter", "org.openjdk.jmh:jmh-core"]}, "org.apache.commons:commons-pool2": {"locked": "2.11.1", "transitive": ["com.netflix.spring:spring-boot-netflix-sso-authn-embedded", "netflix:gandalf-authz-client"]}, "org.apache.commons:commons-rng-client-api": {"locked": "1.6", "transitive": ["netflix:api-global-dependencies", "org.apache.commons:commons-rng-core", "org.apache.commons:commons-rng-simple"]}, "org.apache.commons:commons-rng-core": {"locked": "1.6", "transitive": ["org.apache.commons:commons-rng-simple"]}, "org.apache.commons:commons-rng-simple": {"locked": "1.6", "transitive": ["netflix:api-global-dependencies"]}, "org.apache.httpcomponents.client5:httpclient5": {"locked": "5.4.4", "transitive": ["com.netflix.spring:spring-boot-netflix-admin", "com.netflix.spring:spring-boot-netflix-sso", "org.springframework.cloud:spring-cloud-netflix-eureka-client"]}, "org.apache.httpcomponents.core5:httpcore5": {"locked": "5.3.4", "transitive": ["org.apache.httpcomponents.client5:httpclient5", "org.apache.httpcomponents.core5:httpcore5-h2"]}, "org.apache.httpcomponents.core5:httpcore5-h2": {"locked": "5.3.4", "transitive": ["org.apache.httpcomponents.client5:httpclient5"]}, "org.apache.httpcomponents:httpasyncclient": {"locked": "4.1.5", "transitive": ["com.netflix.ksclient:ksclient-core-ksgw-http"]}, "org.apache.httpcomponents:httpclient": {"locked": "4.5.14", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.amazonaws:aws-xray-recorder-sdk-core", "com.google.http-client:google-http-client", "com.netflix.eureka:eureka-client", "com.netflix.evcache:evcache-core", "com.netflix.karyon:karyon2-admin-web", "com.netflix.ribbon:ribbon-httpclient", "com.netflix.runtime.metadata:runtime-metadata-client", "com.sun.jersey.contribs:jersey-apache-client4", "net.java.dev.jets3t:jets3t", "netflix:base-explorer", "netflix:metatron-ipc", "netflix:nfsso", "netflix:nsac-client", "netflix:platform-ipc", "org.apache.httpcomponents:httpasyncclient", "org.apache.httpcomponents:httpclient-cache", "software.amazon.awssdk:apache-client"]}, "org.apache.httpcomponents:httpclient-cache": {"locked": "4.5.14", "transitive": ["com.netflix.s3authsigncde:s3-auth-sign-common"]}, "org.apache.httpcomponents:httpcore": {"locked": "4.4.16", "transitive": ["com.google.http-client:google-http-client", "net.java.dev.jets3t:jets3t", "netflix:nfsso", "org.apache.httpcomponents:httpasyncclient", "org.apache.httpcomponents:httpclient", "org.apache.httpcomponents:httpcore-nio", "software.amazon.awssdk:apache-client"]}, "org.apache.httpcomponents:httpcore-nio": {"locked": "4.4.15", "transitive": ["org.apache.httpcomponents:httpasyncclient"]}, "org.apache.kafka:kafka-clients": {"locked": "2.6.3-nflx.1", "transitive": ["com.netflix.nfkafka:nfkafka-common", "com.netflix.nfkafka:nfkafka-legacy-schlep", "com.netflix.nfkafka:nfkafka-producer"]}, "org.apache.logging.log4j:log4j-api": {"locked": "2.25.1", "transitive": ["com.netflix.spectator:spectator-ext-log4j2", "io.sentry:sentry-log4j2", "org.apache.logging.log4j:log4j-core", "org.apache.logging.log4j:log4j-jul", "org.apache.logging.log4j:log4j-slf4j2-impl"]}, "org.apache.logging.log4j:log4j-core": {"locked": "2.25.1", "transitive": ["com.netflix.spectator:spectator-ext-log4j2", "com.netflix.spring:spring-boot-netflix-starter-logging", "io.sentry:sentry-log4j2", "org.apache.logging.log4j:log4j-slf4j2-impl", "org.springframework.boot:spring-boot-starter-log4j2"]}, "org.apache.logging.log4j:log4j-jul": {"locked": "2.25.1", "transitive": ["org.springframework.boot:spring-boot-starter-log4j2"]}, "org.apache.logging.log4j:log4j-slf4j2-impl": {"locked": "2.25.1", "transitive": ["netflix:api", "org.springframework.boot:spring-boot-starter-log4j2"]}, "org.apache.lucene:lucene-analyzers-common": {"locked": "4.7.0", "transitive": ["netflix:search_common_630", "org.apache.lucene:lucene-analyzers-icu"]}, "org.apache.lucene:lucene-analyzers-icu": {"locked": "4.7.0", "transitive": ["netflix:search_common_630"]}, "org.apache.lucene:lucene-core": {"locked": "4.7.0", "transitive": ["org.apache.lucene:lucene-analyzers-common", "org.apache.lucene:lucene-analyzers-icu"]}, "org.apache.maven:maven-artifact": {"locked": "3.9.11", "transitive": ["com.netflix.microcontext:microcontext-access"]}, "org.apache.tomcat.embed:tomcat-embed-core": {"locked": "10.1.44", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-rest-server", "com.netflix.spring:spring-boot-netflix-webmvc", "org.apache.tomcat.embed:tomcat-embed-websocket", "org.springframework.boot:spring-boot-starter-tomcat"]}, "org.apache.tomcat.embed:tomcat-embed-el": {"locked": "10.1.44", "transitive": ["org.springframework.boot:spring-boot-starter-tomcat", "org.springframework.boot:spring-boot-starter-validation"]}, "org.apache.tomcat.embed:tomcat-embed-websocket": {"locked": "10.1.44", "transitive": ["org.springframework.boot:spring-boot-starter-tomcat"]}, "org.apache.yetus:audience-annotations": {"locked": "0.5.0", "transitive": ["netflix:curator4-recipes-shaded"]}, "org.apiguardian:apiguardian-api": {"locked": "1.1.2", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.jupiter:junit-jupiter-engine", "org.junit.jupiter:junit-jupiter-params", "org.junit.platform:junit-platform-commons", "org.junit.platform:junit-platform-engine", "org.junit.platform:junit-platform-launcher"]}, "org.aspectj:aspectjweaver": {"locked": "1.9.24", "transitive": ["com.netflix.ksclient:ksclient-core", "com.netflix.spring:spring-boot-netflix-resttemplate-metatron", "com.netflix.spring:spring-boot-netflix-sso-authz", "netflix:memory-tools", "netflix:platform-core", "org.springframework.boot:spring-boot-starter-aop", "org.springframework:spring-aspects"]}, "org.assertj:assertj-core": {"locked": "3.27.3", "transitive": ["com.netflix.partnersub.psecommon:pse-common-client", "netflix:api-service-layer", "netflix:dna-router"]}, "org.bitbucket.b_c:jose4j": {"locked": "0.6.5", "transitive": ["netflix:streaming-cdnurl"]}, "org.bouncycastle:bcpkix-jdk15on": {"locked": "1.69", "transitive": ["com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-starter-security"]}, "org.bouncycastle:bcprov-jdk18on": {"locked": "1.80", "transitive": ["org.springframework.cloud:spring-cloud-starter"]}, "org.bouncycastle:bcutil-jdk15on": {"locked": "1.69", "transitive": ["org.bouncycastle:bcpkix-jdk15on"]}, "org.checkerframework:checker-qual": {"locked": "3.41.0", "transitive": ["com.netflix.mosaic.construction:mosaic-rules-proto-definitions", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix:curator4-recipes-shaded"]}, "org.codehaus.jackson:jackson-core-asl": {"locked": "1.10.3", "transitive": ["com.netflix.jaxrs:netflix-jaxrs-extensions-exceptionmapper", "com.sun.jersey:jersey-json", "net.java.dev.jets3t:jets3t", "netflix:base-explorer", "netflix:platform-aws", "netflix:platform-ipc", "netflix:platform-jdk-compat", "netflix:platform-management", "netflix:whitecastle_v2", "org.apache.avro:avro", "org.codehaus.jackson:jackson-jaxrs", "org.codehaus.jackson:jackson-mapper-asl", "org.codehaus.jackson:jackson-xc"]}, "org.codehaus.jackson:jackson-jaxrs": {"locked": "1.10.3", "transitive": ["com.sun.jersey:jersey-json"]}, "org.codehaus.jackson:jackson-mapper-asl": {"locked": "1.10.3", "transitive": ["com.netflix.jaxrs:netflix-jaxrs-extensions-exceptionmapper", "com.netflix.karyon:karyon2-admin", "com.netflix.karyon:karyon2-admin-web", "com.netflix.memberprice:memberprice-adapter", "com.sun.jersey:jersey-json", "net.java.dev.jets3t:jets3t", "netflix:base-explorer", "netflix:netflix-config", "netflix:nfsso", "netflix:platform-aws", "netflix:platform-ipc", "netflix:platform-management", "netflix:whitecastle", "org.apache.avro:avro", "org.codehaus.jackson:jackson-jaxrs", "org.codehaus.jackson:jackson-xc"]}, "org.codehaus.jackson:jackson-xc": {"locked": "1.10.3", "transitive": ["com.sun.jersey:jersey-json", "netflix:platform-ipc"]}, "org.codehaus.jettison:jettison": {"locked": "1.5.4", "transitive": ["com.netflix.eureka:eureka-client", "com.netflix.jaxrs:netflix-jaxrs-extensions-exceptionmapper", "com.sun.jersey:jersey-json", "netflix:base-explorer", "netflix:base-server", "netflix:base-server-plugins", "netflix:gutenberg-common", "netflix:nf-karyon-admin-plugins", "netflix:nsac-common"]}, "org.codehaus.mojo:animal-sniffer-annotations": {"locked": "1.23", "transitive": ["netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-util-nflx", "netflix:curator4-recipes-shaded"]}, "org.codehaus.plexus:plexus-utils": {"locked": "3.6.0", "transitive": ["org.apache.maven:maven-artifact"]}, "org.codehaus.woodstox:stax2-api": {"locked": "4.2.2", "transitive": ["com.fasterxml.woodstox:woodstox-core"]}, "org.conscrypt:conscrypt-openjdk-uber": {"locked": "2.5.2", "transitive": ["com.netflix.cryptex2:cryptex2-client-jvmonly-common", "netflix.io.grpc:grpc-alts-nflx"]}, "org.eclipse.angus:angus-activation": {"locked": "2.0.1", "transitive": ["com.sun.xml.bind:jaxb-core", "net.java.dev.jets3t:jets3t", "org.eclipse.angus:angus-mail"]}, "org.eclipse.angus:angus-mail": {"locked": "2.0.2", "transitive": ["netflix:obelix-common", "netflix:tools-utils"]}, "org.eclipse.jetty.alpn:alpn-api": {"locked": "1.1.3.v20160715", "transitive": ["netflix:jetty-alpn-provider"]}, "org.freemarker:freemarker": {"locked": "2.3.15", "transitive": ["com.netflix.karyon:karyon2-admin-web", "netflix:base-explorer"]}, "org.hamcrest:hamcrest": {"locked": "2.2", "transitive": ["org.hamcrest:hamcrest-core"]}, "org.hamcrest:hamcrest-core": {"locked": "2.2", "transitive": ["com.github.npathai:hamcrest-optional", "junit:junit", "netflix:dna-test", "org.hamcrest:hamcrest-library"]}, "org.hamcrest:hamcrest-library": {"locked": "2.2", "transitive": ["com.netflix.peas:peas-test-spectator", "netflix:dna-router", "netflix:dna-test"]}, "org.hdrhistogram:HdrHistogram": {"locked": "2.2.2", "transitive": ["com.netflix.hystrix:hystrix-core", "io.micrometer:micrometer-core", "netflix:mantis-realtime-events"]}, "org.hibernate.validator:hibernate-validator": {"locked": "8.0.3.Final", "transitive": ["org.springframework.boot:spring-boot-starter-validation"]}, "org.immutables:value": {"locked": "2.8.2", "transitive": ["netflix:algo-commons-core", "netflix:algo-commons-protos"]}, "org.javassist:javassist": {"locked": "3.28.0-GA", "transitive": ["net.sf.scannotation:scannotation", "netflix:platform-dependency-command", "org.reflections:reflections"]}, "org.jboss.logging:jboss-logging": {"locked": "3.4.3.Final", "transitive": ["org.hibernate.validator:hibernate-validator"]}, "org.jetbrains.kotlin:kotlin-reflect": {"locked": "2.2.0", "transitive": ["com.fasterxml.jackson.module:jackson-module-kotlin", "com.netflix.graphql.dgs.codegen:graphql-dgs-codegen-shared-core"]}, "org.jetbrains.kotlin:kotlin-stdlib": {"locked": "2.2.0", "transitive": ["com.graphql-java:java-dataloader", "com.netflix.graphql.dgs.codegen:graphql-dgs-codegen-shared-core", "netflix:staypuft-proto-definition", "org.jetbrains.kotlin:kotlin-reflect", "org.jetbrains.kotlin:kotlin-stdlib-jdk7", "org.jetbrains.kotlin:kotlin-stdlib-jdk8"]}, "org.jetbrains.kotlin:kotlin-stdlib-jdk7": {"locked": "2.2.0", "transitive": ["org.jetbrains.kotlin:kotlin-stdlib-jdk8"]}, "org.jetbrains.kotlin:kotlin-stdlib-jdk8": {"locked": "2.2.0", "transitive": ["netflix.falcor.schema:falcor-schema-ast-model"]}, "org.jetbrains:annotations": {"locked": "24.0.0", "transitive": ["com.netflix.sbndevmetrics:sbn-dev-metrics-client", "org.jetbrains.kotlin:kotlin-stdlib"]}, "org.json:json": {"locked": "20250517", "transitive": ["com.netflix.evcache:evcache-client", "com.netflix.evcache:evcache-core", "com.vdurmont:emoji-java", "netflix:api", "netflix:tools-utils"]}, "org.jspecify:jspecify": {"locked": "1.0.0", "transitive": ["com.github.ben-manes.caffeine:caffeine", "com.google.guava:guava", "com.graphql-java:java-dataloader", "com.netflix.microcontext:microcontext-access", "com.netflix.microcontext:microcontext-init", "com.netflix.microcontext:microcontext-model", "com.netflix.microcontext:microcontext-resolver", "com.netflix.microcontext:microcontext-test", "netflix.contextflow:contextflow-access", "netflix.contextflow:contextflow-api", "org.webjars:webjars-locator-lite"]}, "org.junit.jupiter:junit-jupiter": {"locked": "5.13.4", "transitive": ["netflix:api-service-layer", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services", "netflix:dna-test"]}, "org.junit.jupiter:junit-jupiter-api": {"locked": "5.13.4", "transitive": ["netflix:server-context-test-junit-jupiter", "org.junit.jupiter:junit-jupiter", "org.junit.jupiter:junit-jupiter-engine", "org.junit.jupiter:junit-jupiter-params"]}, "org.junit.jupiter:junit-jupiter-engine": {"locked": "5.13.4", "transitive": ["org.junit.jupiter:junit-jupiter"]}, "org.junit.jupiter:junit-jupiter-params": {"locked": "5.13.4", "transitive": ["org.junit.jupiter:junit-jupiter"]}, "org.junit.platform:junit-platform-commons": {"locked": "1.13.4", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.platform:junit-platform-engine"]}, "org.junit.platform:junit-platform-engine": {"locked": "1.13.4", "transitive": ["org.junit.jupiter:junit-jupiter-engine", "org.junit.platform:junit-platform-launcher"]}, "org.junit.platform:junit-platform-launcher": {"locked": "1.13.4", "transitive": ["netflix:api", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:dna-client", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services", "netflix:dna-test"]}, "org.junit:junit-bom": {"locked": "5.13.4", "transitive": ["netflix:api-service-layer", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services", "netflix:dna-test"]}, "org.jvnet.mimepull:mimepull": {"locked": "1.9.3", "transitive": ["com.sun.jersey.contribs:jersey-multipart"]}, "org.latencyutils:LatencyUtils": {"locked": "2.0.3", "transitive": ["io.micrometer:micrometer-core"]}, "org.lz4:lz4-java": {"locked": "1.8.0", "transitive": ["com.netflix.dgw.kv:dgw-kv-common", "com.netflix.dgw:dgw-dal-common-client", "com.netflix.ksclient:ksclient-core-kafka", "netflix:cinder-core", "netflix:dts-standalone-common", "netflix:turbodata", "netflix:videometadata-client", "org.apache.kafka:kafka-clients"]}, "org.mockito:mockito-core": {"locked": "5.17.0", "transitive": ["netflix.grpc:netflix-grpc-testkit", "netflix:api-service-layer", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services", "netflix:streaming-cdnurl"]}, "org.mortbay.jetty:jetty": {"locked": "6.1.26", "transitive": ["com.netflix.karyon:karyon2-admin"]}, "org.mortbay.jetty:jetty-util": {"locked": "6.1.26", "transitive": ["org.mortbay.jetty:jetty"]}, "org.objenesis:objenesis": {"locked": "3.3", "transitive": ["org.mockito:mockito-core"]}, "org.openjdk.jmh:jmh-core": {"locked": "1.20", "transitive": ["netflix:api-service-layer", "org.openjdk.jmh:jmh-generator-annprocess"]}, "org.openjdk.jmh:jmh-generator-annprocess": {"locked": "1.20", "transitive": ["netflix:api-service-layer"]}, "org.opentest4j:opentest4j": {"locked": "1.3.0", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.platform:junit-platform-engine"]}, "org.ow2.asm:asm": {"locked": "9.7.1", "transitive": ["net.minidev:accessors-smart"]}, "org.picocontainer:picocontainer": {"locked": "2.14.1", "transitive": ["netflix:base-explorer"]}, "org.projectlombok:lombok": {"locked": "1.18.38", "transitive": ["netflix:api", "netflix:dna-client", "netflix:dna-model", "netflix:dna-router", "netflix:dna-services"]}, "org.reactivestreams:reactive-streams": {"locked": "1.0.4", "transitive": ["com.graphql-java:graphql-java", "com.graphql-java:java-dataloader", "io.projectreactor:reactor-core", "software.amazon.awssdk:http-auth-spi", "software.amazon.awssdk:http-client-spi", "software.amazon.awssdk:netty-nio-client", "software.amazon.awssdk:sdk-core", "software.amazon.awssdk:utils"]}, "org.reflections:reflections": {"locked": "0.10", "transitive": ["com.netflix.evolutionobservation:evolution-observation", "netflix:vmscodeanalysistools"]}, "org.roaringbitmap:RoaringBitmap": {"locked": "1.0.6", "transitive": ["netflix:entityindex"]}, "org.slf4j:slf4j-api": {"locked": "2.0.9", "transitive": ["com.fasterxml.uuid:java-uuid-generator", "com.jayway.jsonpath:json-path", "com.netflix.archaius:archaius-core", "com.netflix.archaius:archaius2-api", "com.netflix.archaius:archaius2-core", "com.netflix.archaius:archaius2-guice", "com.netflix.archaius:archaius2-persisted2", "com.netflix.blitz4j:blitz4j", "com.netflix.concurrency-limits:concurrency-limits-core", "com.netflix.concurrency-limits:concurrency-limits-grpc", "com.netflix.concurrency-limits:concurrency-limits-servlet", "com.netflix.concurrency-limits:concurrency-limits-spectator", "com.netflix.cryptex2:cryptex2-client-jvmonly-common", "com.netflix.dcms:dcms-common", "com.netflix.dgw.kv:dgw-kv-common", "com.netflix.eureka:eureka-client", "com.netflix.evcache:evcache-client", "com.netflix.evcache:evcache-core", "com.netflix.ge.common:common-service", "com.netflix.governator:governator-core", "com.netflix.hendrix:hendrix-core", "com.netflix.hystrix:hystrix-core", "com.netflix.identity:identity-context-xfcc", "com.netflix.jaxrs:netflix-jaxrs-extensions-exceptionmapper", "com.netflix.jaxrs:netflix-jaxrs-extensions-protobuf", "com.netflix.karyon:karyon-admin-healthcheck-plugin", "com.netflix.karyon:karyon-core", "com.netflix.karyon:karyon-eureka", "com.netflix.karyon:karyon-spi", "com.netflix.karyon:karyon2-admin", "com.netflix.karyon:karyon2-admin-eureka-plugin", "com.netflix.karyon:karyon2-admin-web", "com.netflix.karyon:karyon2-archaius", "com.netflix.karyon:karyon2-core", "com.netflix.karyon:karyon2-eureka", "com.netflix.karyon:karyon2-governator", "com.netflix.karyon:karyon2-servo", "com.netflix.ksclient:ksclient-core", "com.netflix.loadshedding:netflix-load-shedding-mesh", "com.netflix.microcontext:microcontext-init", "com.netflix.mosaic.construction:mosaic-rules-proto-definitions", "com.netflix.netflix-commons:netflix-commons-util", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.netflix-commons:netflix-eventbus-bridge", "com.netflix.netflix-commons:netflix-infix", "com.netflix.nflxe2etokens:nflx-e2etokens-validation", "com.netflix.nflxe2etokens:nflx-e2etokens-validation-spring", "com.netflix.peas:peas-dials", "com.netflix.protovalidate:proto-validate", "com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-core", "com.netflix.ribbon:ribbon-eureka", "com.netflix.ribbon:ribbon-httpclient", "com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.s3:nfs3-config", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.spectator:spectator-api", "com.netflix.spectator:spectator-ext-aws", "com.netflix.spectator:spectator-ext-aws2", "com.netflix.spectator:spectator-ext-gc", "com.netflix.spectator:spectator-ext-ipc", "com.netflix.spectator:spectator-ext-jvm", "com.netflix.spectator:spectator-ext-log4j2", "com.netflix.spectator:spectator-ext-sandbox", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spectator:spectator-nflx-tagging", "com.netflix.spectator:spectator-reg-atlas", "com.netflix.spring:spring-boot-netflix", "com.netflix.spring:spring-boot-netflix-starter-library", "com.netflix.tracing:netflix-tracing-secondary-sampling", "com.netflix.tracing:netflix-tracing-w3c-tracecontext", "io.mantisrx:mantis-publish-core", "io.micrometer:micrometer-tracing-bridge-brave", "io.sentry:sentry", "io.swagger.core.v3:swagger-core-jakarta", "netflix.chestnut:chestnut3-logger", "netflix.contextflow.models:contextflow-model-abii", "netflix.contextflow:contextflow-access", "netflix.contextflow:contextflow-api", "netflix.contextflow:contextflow-loader", "netflix.contextflow:contextflow-registry", "netflix.contextflow:contextflow-transport", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-fit", "netflix.grpc:netflix-grpc-logging-context", "netflix.grpc:netflix-grpc-request-context-bridge", "netflix.grpc:netflix-grpc-testkit", "netflix:abzuulcontext", "netflix:akmsclient", "netflix:algo-commons-core", "netflix:algo-commons-protos", "netflix:algo-metrics", "netflix:api-global-dependencies", "netflix:api-service-layer", "netflix:atlas-client", "netflix:base-server-plugins", "netflix:cis-aws-sdk-java-instrumentor-core", "netflix:cmp-core", "netflix:cpeauthorization-common", "netflix:crypto-common-util", "netflix:curator4-recipes-shaded", "netflix:demograph-core", "netflix:dts-standalone-common", "netflix:entityindex-rules-core", "netflix:eureka2-eureka1-bridge", "netflix:eureka2-eureka1-registration-guice", "netflix:eureka2-grpc-client-core", "netflix:eureka2-grpc-client-guice", "netflix:gandalf-agent-embedded", "netflix:gandalf-authz-client", "netflix:geoip-common", "netflix:metatron-common", "netflix:metatron-decrypt", "netflix:metatron-decryptserver-proto-definitions", "netflix:metatron-ipc", "netflix:metatron-ipc-common", "netflix:metatron-ipc-common-assume", "netflix:nf-archaius2", "netflix:nf-archaius2-platform-bridge", "netflix:nf-eventbus-core", "netflix:nfcurator4", "netflix:nflibrary-slim", "netflix:noir-feed-converter", "netflix:nsac-client", "netflix:nsac-common", "netflix:pandora-java-client", "netflix:pds-commons-viewing-history-proto-datamodel", "netflix:request-expiry", "netflix:server-context", "netflix:streaming-monitoring", "netflix:subscriberservice-common", "netflix:whitecastle", "org.apache.avro:avro", "org.apache.httpcomponents.client5:httpclient5", "org.apache.kafka:kafka-clients", "org.apache.logging.log4j:log4j-slf4j2-impl", "org.bitbucket.b_c:jose4j", "org.reflections:reflections", "software.amazon.awssdk:netty-nio-client", "software.amazon.awssdk:regions", "software.amazon.awssdk:sdk-core", "software.amazon.awssdk:utils"]}, "org.springdoc:springdoc-openapi-starter-common": {"locked": "2.8.13", "transitive": ["org.springdoc:springdoc-openapi-starter-webmvc-api"]}, "org.springdoc:springdoc-openapi-starter-webmvc-api": {"locked": "2.8.13", "transitive": ["com.netflix.spring:spring-boot-netflix-swagger", "org.springdoc:springdoc-openapi-starter-webmvc-ui"]}, "org.springdoc:springdoc-openapi-starter-webmvc-ui": {"locked": "2.8.13", "transitive": ["com.netflix.spring:spring-boot-netflix-swagger"]}, "org.springframework.boot:spring-boot": {"locked": "3.5.5", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot-actuator", "org.springframework.boot:spring-boot-actuator-autoconfigure", "org.springframework.boot:spring-boot-autoconfigure", "org.springframework.boot:spring-boot-starter"]}, "org.springframework.boot:spring-boot-actuator": {"locked": "3.5.5", "transitive": ["com.netflix.spring:spring-boot-netflix-actuators", "com.netflix.spring:spring-boot-netflix-sso", "org.springframework.boot:spring-boot-actuator-autoconfigure"]}, "org.springframework.boot:spring-boot-actuator-autoconfigure": {"locked": "3.5.5", "transitive": ["com.netflix.spring:spring-boot-netflix-actuators", "com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-sso-authn-embedded", "com.netflix.spring:spring-boot-netflix-tracing", "org.springframework.boot:spring-boot-starter-actuator"]}, "org.springframework.boot:spring-boot-autoconfigure": {"locked": "3.5.5", "transitive": ["com.netflix.spring:spring-boot-netflix", "com.netflix.spring:spring-boot-netflix-starter-library", "com.netflix.spring:spring-boot-netflix-swagger", "com.netflix.ust:ust-common", "netflix:country-launch", "netflix:turbodata-ext", "org.springdoc:springdoc-openapi-starter-common", "org.springframework.boot:spring-boot-actuator-autoconfigure", "org.springframework.boot:spring-boot-starter"]}, "org.springframework.boot:spring-boot-starter": {"locked": "3.5.5", "transitive": ["com.netflix.spring:spring-boot-netflix-starter", "de.codecentric:spring-boot-admin-client", "org.springframework.boot:spring-boot-starter-actuator", "org.springframework.boot:spring-boot-starter-aop", "org.springframework.boot:spring-boot-starter-cache", "org.springframework.boot:spring-boot-starter-json", "org.springframework.boot:spring-boot-starter-security", "org.springframework.boot:spring-boot-starter-validation", "org.springframework.boot:spring-boot-starter-web", "org.springframework.cloud:spring-cloud-starter"]}, "org.springframework.boot:spring-boot-starter-actuator": {"locked": "3.5.5", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-actuators", "de.codecentric:spring-boot-admin-client"]}, "org.springframework.boot:spring-boot-starter-aop": {"locked": "3.5.5", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-discovery", "com.netflix.spring:spring-boot-netflix-webmvc"]}, "org.springframework.boot:spring-boot-starter-cache": {"locked": "3.5.0", "transitive": ["org.springframework.cloud:spring-cloud-starter-loadbalancer"]}, "org.springframework.boot:spring-boot-starter-json": {"locked": "3.5.5", "transitive": ["org.springframework.boot:spring-boot-starter-web"]}, "org.springframework.boot:spring-boot-starter-log4j2": {"locked": "3.5.5", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-logging"]}, "org.springframework.boot:spring-boot-starter-security": {"locked": "3.5.5", "transitive": ["com.netflix.spring:spring-boot-netflix-sso"]}, "org.springframework.boot:spring-boot-starter-tomcat": {"locked": "3.5.5", "transitive": ["org.springframework.boot:spring-boot-starter-web"]}, "org.springframework.boot:spring-boot-starter-validation": {"locked": "3.5.5", "transitive": ["org.springdoc:springdoc-openapi-starter-common"]}, "org.springframework.boot:spring-boot-starter-web": {"locked": "3.5.5", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-rest-server", "com.netflix.spring:spring-boot-netflix-webmvc"]}, "org.springframework.cloud:spring-cloud-commons": {"locked": "4.3.0", "transitive": ["com.netflix.spring:spring-boot-netflix-discovery", "com.netflix.spring:spring-boot-netflix-logging", "org.springframework.cloud:spring-cloud-loadbalancer", "org.springframework.cloud:spring-cloud-starter"]}, "org.springframework.cloud:spring-cloud-context": {"locked": "4.3.0", "transitive": ["com.netflix.spring:spring-boot-netflix-configuration", "com.netflix.spring:spring-boot-netflix-crypto-provider-corretto", "org.springframework.cloud:spring-cloud-loadbalancer", "org.springframework.cloud:spring-cloud-starter"]}, "org.springframework.cloud:spring-cloud-loadbalancer": {"locked": "4.3.0", "transitive": ["org.springframework.cloud:spring-cloud-starter-loadbalancer"]}, "org.springframework.cloud:spring-cloud-netflix-eureka-client": {"locked": "4.3.0", "transitive": ["com.netflix.spring:spring-boot-netflix-discovery", "org.springframework.cloud:spring-cloud-starter-netflix-eureka-client"]}, "org.springframework.cloud:spring-cloud-starter": {"locked": "4.3.0", "transitive": ["org.springframework.cloud:spring-cloud-starter-loadbalancer", "org.springframework.cloud:spring-cloud-starter-netflix-eureka-client"]}, "org.springframework.cloud:spring-cloud-starter-loadbalancer": {"locked": "4.3.0", "transitive": ["org.springframework.cloud:spring-cloud-starter-netflix-eureka-client"]}, "org.springframework.cloud:spring-cloud-starter-netflix-eureka-client": {"locked": "4.3.0", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-discovery"]}, "org.springframework.guice:spring-guice": {"locked": "2.0.3-rc.2", "transitive": ["com.netflix.spring:spring-boot-netflix-governator"]}, "org.springframework.security:spring-security-config": {"locked": "6.5.4", "transitive": ["com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-sso-authn-embedded", "org.springframework.boot:spring-boot-starter-security"]}, "org.springframework.security:spring-security-core": {"locked": "6.5.4", "transitive": ["com.netflix.spring:spring-boot-netflix-security-api", "com.netflix.spring:spring-boot-netflix-sso", "org.springframework.security:spring-security-config", "org.springframework.security:spring-security-oauth2-client", "org.springframework.security:spring-security-oauth2-core", "org.springframework.security:spring-security-oauth2-jose", "org.springframework.security:spring-security-web"]}, "org.springframework.security:spring-security-crypto": {"locked": "6.5.4", "transitive": ["org.springframework.cloud:spring-cloud-commons", "org.springframework.cloud:spring-cloud-context", "org.springframework.security:spring-security-core"]}, "org.springframework.security:spring-security-oauth2-client": {"locked": "6.5.4", "transitive": ["com.netflix.spring:spring-boot-netflix-sso-authn-embedded"]}, "org.springframework.security:spring-security-oauth2-core": {"locked": "6.5.4", "transitive": ["com.netflix.spring:spring-boot-netflix-sso-authn-embedded", "org.springframework.security:spring-security-oauth2-client", "org.springframework.security:spring-security-oauth2-jose"]}, "org.springframework.security:spring-security-oauth2-jose": {"locked": "6.5.4", "transitive": ["com.netflix.spring:spring-boot-netflix-sso-authn-embedded"]}, "org.springframework.security:spring-security-web": {"locked": "6.5.4", "transitive": ["com.netflix.spring:spring-boot-netflix-sso", "com.netflix.spring:spring-boot-netflix-sso-authn-embedded", "org.springframework.boot:spring-boot-starter-security", "org.springframework.security:spring-security-oauth2-client"]}, "org.springframework:spring-aop": {"locked": "6.2.11", "transitive": ["org.springframework.boot:spring-boot-starter-aop", "org.springframework.boot:spring-boot-starter-security", "org.springframework.security:spring-security-config", "org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-context", "org.springframework:spring-webmvc"]}, "org.springframework:spring-aspects": {"locked": "6.2.11", "transitive": ["com.netflix.spring:spring-boot-netflix-metrics"]}, "org.springframework:spring-beans": {"locked": "6.2.11", "transitive": ["com.netflix.simone:okja-client-spring", "org.springframework.security:spring-security-config", "org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-aop", "org.springframework:spring-context", "org.springframework:spring-context-support", "org.springframework:spring-web", "org.springframework:spring-webflux", "org.springframework:spring-webmvc"]}, "org.springframework:spring-context": {"locked": "6.2.11", "transitive": ["com.netflix.ab:aballocator-common", "com.netflix.pacs:pacs-client", "com.netflix.simone:okja-client-spring", "com.netflix.spring:spring-boot-netflix-starter-library", "com.netflix.ust:ust-access", "com.netflix.webclient:netflix-webclient", "netflix:demograph-scanner", "org.springframework.boot:spring-boot", "org.springframework.guice:spring-guice", "org.springframework.security:spring-security-config", "org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-context-support", "org.springframework:spring-webmvc"]}, "org.springframework:spring-context-support": {"locked": "6.2.11", "transitive": ["org.springframework.boot:spring-boot-starter-cache"]}, "org.springframework:spring-core": {"locked": "6.2.11", "transitive": ["com.netflix.simone:okja-client-spring", "com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot", "org.springframework.boot:spring-boot-starter", "org.springframework.security:spring-security-config", "org.springframework.security:spring-security-core", "org.springframework.security:spring-security-oauth2-client", "org.springframework.security:spring-security-oauth2-core", "org.springframework.security:spring-security-oauth2-jose", "org.springframework.security:spring-security-web", "org.springframework:spring-aop", "org.springframework:spring-beans", "org.springframework:spring-context", "org.springframework:spring-context-support", "org.springframework:spring-expression", "org.springframework:spring-test", "org.springframework:spring-web", "org.springframework:spring-webflux", "org.springframework:spring-webmvc"]}, "org.springframework:spring-expression": {"locked": "6.2.11", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-context", "org.springframework:spring-webmvc"]}, "org.springframework:spring-jcl": {"locked": "6.2.11", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-logging", "org.springframework:spring-core"]}, "org.springframework:spring-test": {"locked": "6.2.11", "transitive": ["netflix:api", "netflix:api-service-layer", "netflix:dna-model"]}, "org.springframework:spring-web": {"locked": "6.2.11", "transitive": ["com.netflix.mantis:nfmantis-publish-grpc-client-sbn3", "com.netflix.spring:spring-boot-netflix-actuators", "com.netflix.spring:spring-boot-netflix-starter-niws", "de.codecentric:spring-boot-admin-client", "netflix:api", "netflix:api-service-layer", "netflix:dna-model", "org.springframework.boot:spring-boot-starter-json", "org.springframework.boot:spring-boot-starter-web", "org.springframework.security:spring-security-oauth2-core", "org.springframework.security:spring-security-web", "org.springframework:spring-webflux", "org.springframework:spring-webmvc"]}, "org.springframework:spring-webflux": {"locked": "6.2.11", "transitive": ["com.netflix.webclient:netflix-webclient"]}, "org.springframework:spring-webmvc": {"locked": "6.2.11", "transitive": ["org.springdoc:springdoc-openapi-starter-webmvc-api", "org.springframework.boot:spring-boot-starter-web"]}, "org.tukaani:xz": {"locked": "1.5", "transitive": ["org.apache.avro:avro"]}, "org.webjars:swagger-ui": {"locked": "5.28.1", "transitive": ["org.springdoc:springdoc-openapi-starter-webmvc-ui"]}, "org.webjars:webjars-locator-lite": {"locked": "1.1.0", "transitive": ["org.springdoc:springdoc-openapi-starter-webmvc-ui"]}, "org.xerial.snappy:snappy-java": {"locked": "1.1.10.7", "transitive": ["com.netflix.ksclient:ksclient-core-kafka", "com.netflix.nfkafka:nfkafka-producer", "netflix:gutenberg-common", "org.apache.avro:avro", "org.apache.kafka:kafka-clients"]}, "org.yaml:snakeyaml": {"locked": "2.4", "transitive": ["com.fasterxml.jackson.dataformat:jackson-dataformat-yaml", "io.swagger.core.v3:swagger-core-jakarta", "org.springframework.boot:spring-boot-starter"]}, "software.amazon.awssdk.crt:aws-crt": {"locked": "0.38.9", "transitive": ["software.amazon.awssdk:aws-crt-client"]}, "software.amazon.awssdk:annotations": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:apache-client", "software.amazon.awssdk:arns", "software.amazon.awssdk:auth", "software.amazon.awssdk:aws-core", "software.amazon.awssdk:aws-crt-client", "software.amazon.awssdk:aws-query-protocol", "software.amazon.awssdk:aws-xml-protocol", "software.amazon.awssdk:checksums", "software.amazon.awssdk:checksums-spi", "software.amazon.awssdk:crt-core", "software.amazon.awssdk:endpoints-spi", "software.amazon.awssdk:http-auth", "software.amazon.awssdk:http-auth-aws", "software.amazon.awssdk:http-auth-aws-eventstream", "software.amazon.awssdk:http-auth-spi", "software.amazon.awssdk:http-client-spi", "software.amazon.awssdk:identity-spi", "software.amazon.awssdk:json-utils", "software.amazon.awssdk:metrics-spi", "software.amazon.awssdk:netty-nio-client", "software.amazon.awssdk:profiles", "software.amazon.awssdk:protocol-core", "software.amazon.awssdk:regions", "software.amazon.awssdk:retries", "software.amazon.awssdk:retries-spi", "software.amazon.awssdk:s3", "software.amazon.awssdk:s3-transfer-manager", "software.amazon.awssdk:sdk-core", "software.amazon.awssdk:sts", "software.amazon.awssdk:utils"]}, "software.amazon.awssdk:apache-client": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:s3", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:arns": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:s3", "software.amazon.awssdk:s3-transfer-manager"]}, "software.amazon.awssdk:auth": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:aws-core", "software.amazon.awssdk:s3", "software.amazon.awssdk:s3-transfer-manager", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:aws-core": {"locked": "2.33.11", "transitive": ["com.netflix.spectator:spectator-ext-aws2", "netflix:cis-aws-sdk-java-instrumentor-core", "software.amazon.awssdk:aws-query-protocol", "software.amazon.awssdk:aws-xml-protocol", "software.amazon.awssdk:s3", "software.amazon.awssdk:s3-transfer-manager", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:aws-crt-client": {"locked": "2.33.11", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-aws", "netflix:gutenberg-common"]}, "software.amazon.awssdk:aws-query-protocol": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:aws-xml-protocol", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:aws-xml-protocol": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:s3"]}, "software.amazon.awssdk:checksums": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:http-auth-aws", "software.amazon.awssdk:s3", "software.amazon.awssdk:sdk-core"]}, "software.amazon.awssdk:checksums-spi": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:checksums", "software.amazon.awssdk:http-auth-aws", "software.amazon.awssdk:s3", "software.amazon.awssdk:sdk-core"]}, "software.amazon.awssdk:crt-core": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:aws-crt-client", "software.amazon.awssdk:s3"]}, "software.amazon.awssdk:endpoints-spi": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:aws-core", "software.amazon.awssdk:s3", "software.amazon.awssdk:sdk-core", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:http-auth": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:auth", "software.amazon.awssdk:aws-core", "software.amazon.awssdk:s3", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:http-auth-aws": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:auth", "software.amazon.awssdk:s3", "software.amazon.awssdk:sdk-core", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:http-auth-aws-eventstream": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:auth"]}, "software.amazon.awssdk:http-auth-spi": {"locked": "2.33.11", "transitive": ["netflix:gutenberg-common", "software.amazon.awssdk:auth", "software.amazon.awssdk:aws-core", "software.amazon.awssdk:http-auth", "software.amazon.awssdk:http-auth-aws", "software.amazon.awssdk:s3", "software.amazon.awssdk:sdk-core", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:http-client-spi": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:apache-client", "software.amazon.awssdk:auth", "software.amazon.awssdk:aws-core", "software.amazon.awssdk:aws-crt-client", "software.amazon.awssdk:aws-query-protocol", "software.amazon.awssdk:aws-xml-protocol", "software.amazon.awssdk:http-auth", "software.amazon.awssdk:http-auth-aws", "software.amazon.awssdk:http-auth-spi", "software.amazon.awssdk:netty-nio-client", "software.amazon.awssdk:protocol-core", "software.amazon.awssdk:s3", "software.amazon.awssdk:s3-transfer-manager", "software.amazon.awssdk:sdk-core", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:identity-spi": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:auth", "software.amazon.awssdk:aws-core", "software.amazon.awssdk:http-auth", "software.amazon.awssdk:http-auth-aws", "software.amazon.awssdk:http-auth-spi", "software.amazon.awssdk:s3", "software.amazon.awssdk:sdk-core", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:json-utils": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:auth", "software.amazon.awssdk:regions", "software.amazon.awssdk:s3", "software.amazon.awssdk:s3-transfer-manager", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:metrics-spi": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:apache-client", "software.amazon.awssdk:aws-core", "software.amazon.awssdk:aws-crt-client", "software.amazon.awssdk:http-client-spi", "software.amazon.awssdk:netty-nio-client", "software.amazon.awssdk:s3", "software.amazon.awssdk:sdk-core", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:netty-nio-client": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:s3", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:profiles": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:auth", "software.amazon.awssdk:aws-core", "software.amazon.awssdk:regions", "software.amazon.awssdk:s3", "software.amazon.awssdk:sdk-core", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:protocol-core": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:aws-query-protocol", "software.amazon.awssdk:aws-xml-protocol", "software.amazon.awssdk:s3", "software.amazon.awssdk:s3-transfer-manager", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:regions": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:auth", "software.amazon.awssdk:aws-core", "software.amazon.awssdk:s3", "software.amazon.awssdk:s3-transfer-manager", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:retries": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:aws-core", "software.amazon.awssdk:sdk-core"]}, "software.amazon.awssdk:retries-spi": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:aws-core", "software.amazon.awssdk:retries", "software.amazon.awssdk:s3", "software.amazon.awssdk:sdk-core", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:s3": {"locked": "2.33.11", "transitive": ["com.netflix.hank:hank-client", "com.netflix.s3authsigncde:s3-auth-sign-client", "com.netflix.s3authsigncde:s3-auth-sign-common", "netflix:gutenberg-common", "netflix:whitecastle_v2", "software.amazon.awssdk:s3-transfer-manager"]}, "software.amazon.awssdk:s3-transfer-manager": {"locked": "2.33.11", "transitive": ["netflix:gutenberg-common"]}, "software.amazon.awssdk:sdk-core": {"locked": "2.33.11", "transitive": ["com.netflix.spectator:spectator-ext-aws2", "software.amazon.awssdk:auth", "software.amazon.awssdk:aws-core", "software.amazon.awssdk:aws-query-protocol", "software.amazon.awssdk:aws-xml-protocol", "software.amazon.awssdk:protocol-core", "software.amazon.awssdk:regions", "software.amazon.awssdk:s3", "software.amazon.awssdk:s3-transfer-manager", "software.amazon.awssdk:sts"]}, "software.amazon.awssdk:sts": {"locked": "2.33.11", "transitive": ["com.netflix.s3authsigncde:s3-auth-sign-common", "netflix:cis-aws-sdk-java-instrumentor-core"]}, "software.amazon.awssdk:third-party-jackson-core": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:json-utils"]}, "software.amazon.awssdk:utils": {"locked": "2.33.11", "transitive": ["software.amazon.awssdk:apache-client", "software.amazon.awssdk:arns", "software.amazon.awssdk:auth", "software.amazon.awssdk:aws-core", "software.amazon.awssdk:aws-crt-client", "software.amazon.awssdk:aws-query-protocol", "software.amazon.awssdk:aws-xml-protocol", "software.amazon.awssdk:checksums", "software.amazon.awssdk:crt-core", "software.amazon.awssdk:http-auth", "software.amazon.awssdk:http-auth-aws", "software.amazon.awssdk:http-auth-spi", "software.amazon.awssdk:http-client-spi", "software.amazon.awssdk:identity-spi", "software.amazon.awssdk:json-utils", "software.amazon.awssdk:metrics-spi", "software.amazon.awssdk:netty-nio-client", "software.amazon.awssdk:profiles", "software.amazon.awssdk:protocol-core", "software.amazon.awssdk:regions", "software.amazon.awssdk:retries", "software.amazon.awssdk:retries-spi", "software.amazon.awssdk:s3", "software.amazon.awssdk:s3-transfer-manager", "software.amazon.awssdk:sdk-core", "software.amazon.awssdk:sts"]}, "software.amazon.cryptools:AmazonCorrettoCryptoProvider": {"locked": "1.6.2", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-corretto", "netflix:api", "netflix:evcache-nflx-client"]}, "software.amazon.eventstream:eventstream": {"locked": "1.0.1", "transitive": ["software.amazon.awssdk:auth", "software.amazon.awssdk:aws-core", "software.amazon.awssdk:http-auth-aws-eventstream"]}, "tools.profiler:async-profiler": {"locked": "2.9", "transitive": ["com.netflix.spring:spring-boot-netflix"]}, "xmlpull:xmlpull": {"locked": "1.1.3.1", "transitive": ["io.github.x-stream:mxparser"]}}, "resolutionRules": {"com.netflix.nebula:gradle-resolution-rules": {"locked": "0.83.0"}, "netflix.nebula.resolutionrules:resolution-rules": {"locked": "1.362.0"}}, "springBootNetflixRolloutRule": {"com.netflix.sbnfeaturerolloutrule:sbn-feature-rollout-rule": {"locked": "0.1.5890"}}, "testCompileClasspath": {"com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}}, "testRuntimeClasspath": {"com.atomicjar:testcontainers-cloud-provider-strategy": {"locked": "0.0.45", "transitive": ["com.netflix.nebula.testcontainers:testcontainers-cloud-configurer"]}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}, "com.netflix.nebula.testcontainers:testcontainers-cloud-configurer": {"locked": "1.20.0"}, "org.apiguardian:apiguardian-api": {"locked": "1.1.2", "transitive": ["org.junit.platform:junit-platform-commons", "org.junit.platform:junit-platform-engine", "org.junit.platform:junit-platform-launcher"]}, "org.junit.platform:junit-platform-commons": {"locked": "1.13.4", "transitive": ["org.junit.platform:junit-platform-engine"]}, "org.junit.platform:junit-platform-engine": {"locked": "1.13.4", "transitive": ["org.junit.platform:junit-platform-launcher"]}, "org.junit.platform:junit-platform-launcher": {"locked": "1.13.4"}, "org.opentest4j:opentest4j": {"locked": "1.3.0", "transitive": ["org.junit.platform:junit-platform-engine"]}}}