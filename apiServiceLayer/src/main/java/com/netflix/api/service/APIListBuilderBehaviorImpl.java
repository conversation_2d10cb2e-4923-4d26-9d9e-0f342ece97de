package com.netflix.api.service;

import java.util.ArrayList;
import java.util.List;
import rx.Observable;
import rx.Subscriber;
import rx.exceptions.CompositeException;

public abstract class APIListBuilderBehaviorImpl<Item, Reference>
    extends APIObservableBehaviorImpl<ListItem<Item>> implements APIListBuilderBehavior<Item> {

  public abstract Item loadItem(Reference reference);

  public Observable<List<Reference>> getReferencesAsObservable() {
    return Observable.defer(() -> Observable.just(getReferences())).cache();
  }

  protected List<Reference> getReferences() {
    return List.of();
  }

  @Override
  protected void internalSubscribe(final Subscriber<? super ListItem<Item>> subscriber) {
    // a list of exceptions to be kept track of (to be compiled and returned with onError after all
    // onNext's complete.
    getReferencesAsObservable()
        .flatMap(
            references -> {
              List<Throwable> errors = new ArrayList<>();
              List<ListItem<Item>> items = new ArrayList<>(references.size());
              for (int i = 0; i < references.size(); i++) {
                final Reference reference = references.get(i);
                try {
                  Item item = loadItem(reference);
                  if (item != null) {
                    items.add(createListItem(item, i));
                  }
                } catch (Exception e) {
                  errors.add(new APIListItemException(i, e));
                }
              }
              if (errors.isEmpty()) {
                return Observable.from(items);
              } else {
                return Observable.from(items).concatWith(Observable.error(processErrors(errors)));
              }
            })
        .subscribe(subscriber);
  }

  /**
   * The list of errors is gauranteed to be non-empty. Allows for special processing of errors in
   * the specialized sub classes
   *
   * @param errors From the execution of the internalSubscribe method
   * @return the single error that will be passed back to in the {@link
   *     Subscriber#onError(Throwable)} method
   */
  private Throwable processErrors(List<Throwable> errors) {
    if (errors.size() == 1) {
      return errors.getFirst();
    } else {
      return new CompositeException(errors);
    }
  }

  private ListItem<Item> createListItem(Item item, int index) {
    return new ListItem<>(item, index);
  }
}
