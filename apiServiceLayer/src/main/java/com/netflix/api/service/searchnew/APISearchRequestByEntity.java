package com.netflix.api.service.searchnew;

/**
 * Request for a search list that was previously computed and can be retrieved by its reference
 * (entityId of the list).
 */
public class APISearchRequestByEntity extends APISearchRequest {
  private final String entityId;

  public APISearchRequestByEntity(String entityId) {
    this.entityId = entityId;
  }

  public String getEntityId() {
    return entityId;
  }
}
