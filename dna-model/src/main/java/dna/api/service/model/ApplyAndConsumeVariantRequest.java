package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class ApplyAndConsumeVariantRequest {
  private String templateId;

  private String variantId;

  private String domainData;

  private List<String> domainLogs;

  public String getTemplateId() {
    return templateId;
  }

  public String getVariantId() {
    return variantId;
  }

  public String getDomainData() {
    return domainData;
  }

  public List<String> getDomainLogs() {
    return domainLogs;
  }

  public ApplyAndConsumeVariantRequest setTemplateId(String templateId) {
    this.templateId = templateId;
    return this;
  }

  public ApplyAndConsumeVariantRequest setVariantId(String variantId) {
    this.variantId = variantId;
    return this;
  }

  public ApplyAndConsumeVariantRequest setDomainData(String domainData) {
    this.domainData = domainData;
    return this;
  }

  public ApplyAndConsumeVariantRequest setDomainLogs(List<String> domainLogs) {
    this.domainLogs = domainLogs;
    return this;
  }
}
