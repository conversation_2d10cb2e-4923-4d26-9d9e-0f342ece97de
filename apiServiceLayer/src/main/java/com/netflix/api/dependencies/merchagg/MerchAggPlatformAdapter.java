package com.netflix.api.dependencies.merchagg;

import static com.netflix.api.service.batch.APILoloUtils.CLIENT_APP_CAPABILITY_KEY;

import com.netflix.api.service.APIClientCapabilitiesInternal;
import com.netflix.api.service.APIRequest;
import com.netflix.map.annotation.MapAnnotationConstants;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MerchAggPlatformAdapter {

  private static final Logger logger = LoggerFactory.getLogger(MerchAggPlatformAdapter.class);

  private MerchAggPlatformAdapter() {}

  public static void tryAddMultiLanguageCatalogAnnotation(Map<String, Object> annotations) {
    APIClientCapabilitiesInternal.get(APIRequest.getCurrentRequest())
        .ifPresent(
            clientCapabilitiesInternal -> {
              if (clientCapabilitiesInternal.clientAppCanHandleMultiLanguageCatalog()) {
                Map<Object, Object> capabilitiesMap = null;
                Object existingCapabilitiesMap = annotations.get(CLIENT_APP_CAPABILITY_KEY);
                if (existingCapabilitiesMap == null) {
                  capabilitiesMap = new LinkedHashMap<>(); // this is the expected type from MAP
                  annotations.put(CLIENT_APP_CAPABILITY_KEY, capabilitiesMap);
                } else if (existingCapabilitiesMap instanceof Map) {
                  //noinspection unchecked
                  capabilitiesMap = (Map<Object, Object>) existingCapabilitiesMap;
                }
                if (capabilitiesMap != null) {
                  capabilitiesMap.put(
                      APIClientCapabilitiesInternal.getMultiLangFeatureName(), true);
                  if (logger.isTraceEnabled()) {
                    logger.trace("Added MultiLanguage Feature Annotation");
                  }
                } else if (logger.isTraceEnabled()) {
                  logger.trace("Could not add expected MultiLanguage Feature Annotation.");
                }
              }
            });
  }

  public static boolean nonMemberRequest(Map<String, Object> annotations) {
    return Optional.ofNullable(annotations.get(MapAnnotationConstants.NON_MEMBER_REQUEST))
        .map(Object::toString)
        .map(Boolean::valueOf)
        .orElse(false);
  }
}
