package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaYearRangeFilter {
  private Integer startYear;

  private Integer endYear;

  public Integer getStartYear() {
    return startYear;
  }

  public Integer getEndYear() {
    return endYear;
  }

  public NapaYearRangeFilter setStartYear(Integer startYear) {
    this.startYear = startYear;
    return this;
  }

  public NapaYearRangeFilter setEndYear(Integer endYear) {
    this.endYear = endYear;
    return this;
  }
}
