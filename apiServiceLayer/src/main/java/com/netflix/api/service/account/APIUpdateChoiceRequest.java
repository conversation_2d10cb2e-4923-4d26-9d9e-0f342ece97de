package com.netflix.api.service.account;

import com.netflix.api.service.identity.APIUser;

public class APIUpdateChoiceRequest {
  private final APIUser user;
  private final long planId;
  private final String priceTier;
  private final String context;
  private final String messageGuid;
  private final boolean isChargedUpgrade;

  APIUpdateChoiceRequest(
      APIUser user,
      Long planId,
      String priceTier,
      String context,
      String messageGuid,
      boolean isChargedUpgrade) {
    this.user = user;
    this.planId = planId;
    this.priceTier = priceTier;
    this.context = context;
    this.messageGuid = messageGuid;
    this.isChargedUpgrade = isChargedUpgrade;
  }

  public APIUser getUser() {
    return user;
  }

  public long getPlanId() {
    return planId;
  }

  public String getPriceTier() {
    return priceTier;
  }

  public String getContext() {
    return context;
  }

  public String getMessageGuid() {
    return messageGuid;
  }

  public boolean isChargedUpgrade() {
    return isChargedUpgrade;
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    private APIUser user;
    private Long planId;
    private String priceTier;
    private String context;
    private String messageGuid;
    private boolean isChargedUpgrade;

    private Builder() {}

    public Builder user(APIUser user) {
      this.user = user;
      return this;
    }

    public Builder planId(Long planId) {
      this.planId = planId;
      return this;
    }

    public Builder priceTier(String priceTier) {
      this.priceTier = priceTier;
      return this;
    }

    public Builder context(String context) {
      this.context = context;
      return this;
    }

    public Builder messageGuid(String messageGuid) {
      this.messageGuid = messageGuid;
      return this;
    }

    public Builder isChargedUpgrade(boolean isChargedUpgrade) {
      this.isChargedUpgrade = isChargedUpgrade;
      return this;
    }

    public APIUpdateChoiceRequest build() {
      return new APIUpdateChoiceRequest(
          user, planId, priceTier, context, messageGuid, isChargedUpgrade);
    }

    public String toString() {
      return "APIUpdateChoiceRequest.Builder(planId="
          + this.planId
          + ", priceTier="
          + this.priceTier
          + ", context="
          + this.context
          + ", messageGuid="
          + this.messageGuid
          + ", isChargedUpgrade="
          + this.isChargedUpgrade
          + ")";
    }
  }
}
