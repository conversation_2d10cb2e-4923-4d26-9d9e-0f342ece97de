package com.netflix.api.service;

import com.google.common.base.Preconditions;

/**
 * An item with context information so that clients know which item in the subscription it belongs
 * to. For example, if asking for a list of items (returned as Observable emissions out of order),
 * context can be list index. When a service loads items for an array of references requested,
 * context can be the reference.
 *
 * <AUTHOR>
 */
public class ContextualItem<CONTEXT, ITEM> {
  protected final ITEM item;
  protected final CONTEXT context;

  /**
   * FIXME FIXME FIXME.
   *
   * @param w FIXME FIXME FIXME
   * @param c FIXME FIXME FIXME
   */
  public ContextualItem(ITEM w, CONTEXT c) {
    this.item = Preconditions.checkNotNull(w);
    this.context = Preconditions.checkNotNull(c);
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;

    ContextualItem that = (ContextualItem) o;

    return this.context.equals(that.context);
  }

  /**
   * FIXME FIXME FIXME.
   *
   * @return FIXME FIXME FIXME
   */
  public ITEM getItem() {
    return item;
  }

  /**
   * FIXME FIXME FIXME.
   *
   * @return FIXME FIXME FIXME
   */
  public CONTEXT getContext() {
    return context;
  }

  @Override
  public int hashCode() {
    return context.hashCode();
  }

  @Override
  public String toString() {
    return context.toString() + ":" + item.toString();
  }
}
