package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SearchPageEntityId {
  private Integer id;

  @Deprecated private String kind;

  private NapaEntityKind entityKind;

  private String unifiedEntityId;

  public Integer getId() {
    return id;
  }

  @Deprecated
  public String getKind() {
    return kind;
  }

  public NapaEntityKind getEntityKind() {
    return entityKind;
  }

  public String getUnifiedEntityId() {
    return unifiedEntityId;
  }

  public SearchPageEntityId setId(Integer id) {
    this.id = id;
    return this;
  }

  @Deprecated
  public SearchPageEntityId setKind(String kind) {
    this.kind = kind;
    return this;
  }

  public SearchPageEntityId setEntityKind(NapaEntityKind entityKind) {
    this.entityKind = entityKind;
    return this;
  }

  public SearchPageEntityId setUnifiedEntityId(String unifiedEntityId) {
    this.unifiedEntityId = unifiedEntityId;
    return this;
  }
}
