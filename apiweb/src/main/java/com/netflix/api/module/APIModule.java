package com.netflix.api.module;

import com.google.inject.AbstractModule;
import com.google.inject.Binder;
import com.google.inject.Module;
import com.google.inject.name.Names;
import com.netflix.api.dependencies.device.DeviceModule;
import com.netflix.api.grpc.module.Grpc2CurlModule;
import com.netflix.api.priming.APIStartupDependencyPriming;
import com.netflix.api.tomcat.RequestScopedGrpcGraphModule;
import com.netflix.api.tomcat.RequestScopedNiwsGraphModule;
import com.netflix.aws.sqs.SQSManager;
import com.netflix.group.attribute.GroupAttributeModule;
import com.netflix.group.inject.LiteGroupModule;
import com.netflix.group.proto.GroupProtoBridgeModule;
import com.netflix.gs2.client.extended.impl.Gs2ClientExtensionModule;
import com.netflix.i18n.NFi18nManager;
import com.netflix.lang.BindingContexts;
import com.netflix.membercommerce.client.configuration.guice.UpsellServiceClientModule;
import com.netflix.napa.client.configuration.guice.PrePostplayServicesFallbackModule;
import com.netflix.nts.deviceperfmetrics.reader.lifecycle.NtsDeviceperfmetricsReaderModule;
import com.netflix.peas.authevents.logging.module.PEASAuthEventsLoggerModule;
import com.netflix.spectator.nflx.SpectatorModule;
import com.netflix.streamsaccountingservice.client.configuration.guice.StreamsAccountingServiceServiceClientModule;
import com.netflix.thumbs.data.ThumbsDataModule;
import com.netflix.zuul.push.lifecycle.ZuulPushModule;
import jakarta.inject.Singleton;
import java.util.Set;
import java.util.concurrent.Executor;

public final class APIModule extends AbstractModule {

  @Override
  protected void configure() {
    installLegacyAutoBind(binder());

    bind(Executor.class)
        .annotatedWith(Names.named("startupExecutor"))
        .toProvider(() -> BindingContexts.propagate(APIStartupDependencyPriming.EXECUTOR))
        .in(Singleton.class);

    // install dependencies modules
    getDependencyModules().forEach(this::install);

    // bind adhoc eager singletons
    getEagerSingletons().forEach(cls -> bind(cls).asEagerSingleton());
  }

  // Init Non-conforming Singletons

  private static Set<Class<?>> getEagerSingletons() {
    return Set.of(
        SQSManager.class, // SQSManager.getSQSManager() in clients
        //        MapEslLibrary.class,
        // Map fallbacks break
        // add this to fix EDGE-3849 Future: LPE team will give us a guice module to add.
        NFi18nManager.class);
  }

  private static Set<Class<?>> legacyAutoBindingClasses() {
    return Set.of(
        com.netflix.launch.common.LaunchConfiguration.class,
        com.netflix.api.platform.util.PropertyRepositoryHolder.class);
  }

  private Iterable<? extends Module> getDependencyModules() {
    return Set.of(
        new StreamsAccountingServiceServiceClientModule(),
        new SpectatorModule(),
        new DeviceModule(),
        new ThumbsDataModule(),
        new ZuulPushModule(),
        new RequestScopedNiwsGraphModule(),
        new RequestScopedGrpcGraphModule(),
        new NtsDeviceperfmetricsReaderModule(),
        new Gs2ClientExtensionModule(),
        new GroupProtoBridgeModule(),
        new GroupAttributeModule(),
        new LiteGroupModule(),
        new PEASAuthEventsLoggerModule(),
        new APIServiceLayerModule.Dependencies(),
        new Grpc2CurlModule(),
        new APIServiceLayerModule.Bindings(),
        new UpsellServiceClientModule(),
        new PrePostplayServicesFallbackModule());
  }

  private void installLegacyAutoBind(Binder binder) {
    legacyAutoBindingClasses().forEach(cls -> binder.bind(cls).asEagerSingleton());
  }

  @Override
  public boolean equals(Object obj) {
    return obj != null && getClass().equals(obj.getClass());
  }

  @Override
  public int hashCode() {
    return getClass().hashCode();
  }
}
