package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SearchEntityMatchContext {
  private String matchedLocale;

  public String getMatchedLocale() {
    return matchedLocale;
  }

  public SearchEntityMatchContext setMatchedLocale(String matchedLocale) {
    this.matchedLocale = matchedLocale;
    return this;
  }
}
