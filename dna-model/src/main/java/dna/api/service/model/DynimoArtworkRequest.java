package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * all fields except type are optional country and locale are obtained from request context, will
 * not expose explicit setters for them until specific use-cases show up
 *
 * @dotnext com.netflix.api.service.video.APIDynimoArtworkRequest
 */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class DynimoArtworkRequest {
  private Integer width;

  private Integer height;

  private String type;

  private Boolean secure;

  private Map<String, Object> parameters;

  public Integer getWidth() {
    return width;
  }

  public Integer getHeight() {
    return height;
  }

  public String getType() {
    return type;
  }

  public Boolean getSecure() {
    return secure;
  }

  public Map<String, Object> getParameters() {
    return parameters;
  }

  public DynimoArtworkRequest setWidth(Integer width) {
    this.width = width;
    return this;
  }

  public DynimoArtworkRequest setHeight(Integer height) {
    this.height = height;
    return this;
  }

  public DynimoArtworkRequest setType(String type) {
    this.type = type;
    return this;
  }

  public DynimoArtworkRequest setSecure(Boolean secure) {
    this.secure = secure;
    return this;
  }

  public DynimoArtworkRequest setParameters(Map<String, Object> parameters) {
    this.parameters = parameters;
    return this;
  }
}
