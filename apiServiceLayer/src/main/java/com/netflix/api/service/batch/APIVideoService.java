package com.netflix.api.service.batch;

import com.netflix.api.service.batch.model.PromoVideo;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import rx.Observable;
import rx.Single;

/** */
public interface APIVideoService {

  /**
   * Get promotional videos (aka Video Merch videos) for the IDs (video or collection) and given
   * context (see below). If a subset of passed in ids are not valid or do not have promo videos,
   * they will not be in the reply map.
   *
   * @param ids Ids of contexts for which you want the {@link PromoVideo}'s
   * @param context context supported by MAS. see ContextValues enum in {@see
   *     https://stash.corp.netflix.com/projects/MAP/repos/mas/browse/mas-server/src/main/java/com/netflix/mas/promovideo/resolvers/request/criteria/RequestCriteria
   *     }
   * @param lolomoId lolomo id if one exists, null otherwise
   * @param annotations pass-through annotations to be sent to TrailerPark servicw
   * @return a map of integer (passed in ids) to their corresponding promo videos.
   */
  Single<Map<Long, PromoVideo>> getPromoVideos(
      Collection<Long> ids, String context, String lolomoId, Map<String, String> annotations);

  /**
   * Get supplemental videos (trailers, montages, etc.) for collections of videos IDs and trailer
   * types
   *
   * @param videoIds the videos for which supplementals are requested
   * @param subTypes the trailer subtypes requested
   * @param nonMember whether supplementals are needed for a member customer or anonymous
   *     visitor
   * @param uiContext the UI descriptor (flavor, platform, version) of the client application
   * @return a Single emitting a Map of video ID to list of supplemental video IDs
   */
  Single<Map<Integer, Set<Integer>>> getSupplementalVideos(
      Set<Integer> videoIds, Set<String> subTypes, boolean nonMember, String uiContext);

  Single<Map<Integer, Integer>> getEpisodeBatchParents(Collection<Integer> videoIds);

  Single<Map<Integer, Integer>> getUpcomingSeasons(Collection<Integer> videoIds);

  Observable<Map<Integer, String>> getNumSeasonsLabel(
      String labelType, Map<Integer, List<Integer>> showSeasonsMap);

  /**
   * Remove videos from Continue Watching list
   *
   * @param videoIds the videos to remove from Continue Watching list
   * @param uiVersion UI can use this parameter to send UI version for DSE
   * @param trackId trackId associated with this removal for DSE consumption
   * @return a Single emitting null value
   */
  Single<Void> removeFromContinueWatchingList(
      Collection<Integer> videoIds, String uiVersion, Integer trackId);
}
