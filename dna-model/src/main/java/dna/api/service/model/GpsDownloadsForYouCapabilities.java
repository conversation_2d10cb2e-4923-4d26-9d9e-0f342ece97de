package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class GpsDownloadsForYouCapabilities {
  private Boolean canHandle;

  public Boolean getCanHandle() {
    return canHandle;
  }

  public GpsDownloadsForYouCapabilities setCanHandle(Boolean canHandle) {
    this.canHandle = canHandle;
    return this;
  }
}
