package com.netflix.api.platform.identity;

import com.netflix.lang.RequestVariable;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.protobuf.Passport;

/**
 * Thread variable to contain current request's identity data parsed from DCR cookie.
 *
 * <AUTHOR>
 */
public class CurrentIdentityResult {
  private CurrentIdentityResult() {}

  private static final RequestVariable<IdentityResult<?>> RESULT = new RequestVariable<>();
  private static final RequestVariable<PassportIdentity> PASSPORT = new RequestVariable<>();
  private static final RequestVariable<String> VDID = new RequestVariable<>();

  public static IdentityResult<?> get() {
    return RESULT.get();
  }

  public static boolean has() {
    return RESULT.get() != null;
  }

  public static void set(IdentityResult<?> id) {
    RESULT.set(id);
  }

  public static Passport getPassport() {
    PassportIdentity identity = PASSPORT.get();
    return identity != null ? identity.toProto() : null;
  }

  // Downstream can use this to get access to what's in the passport.
  public static PassportIdentity getPassportIdentity() {
    return PASSPORT.get();
  }

  public static void setPassport(PassportIdentity passport) {
    PASSPORT.set(passport);
  }

  public static void setVisitorDeviceId(String visitorDeviceId) {
    VDID.set(visitorDeviceId);
  }

  public static String getVisitorDeviceId() {
    return VDID.get();
  }
}
