package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchVideoListSection extends SearchPageSection {
  private List<SearchPageVideo> videos;

  public List<SearchPageVideo> getVideos() {
    return videos;
  }

  public SearchVideoListSection setVideos(List<SearchPageVideo> videos) {
    this.videos = videos;
    return this;
  }
}
