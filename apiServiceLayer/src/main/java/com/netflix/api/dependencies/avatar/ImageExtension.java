package com.netflix.api.dependencies.avatar;

import static org.apache.commons.lang.StringUtils.substringBefore;

public enum ImageExtension {
  webp,
  jpg,
  png,
  gif,
  mp4,
  astc,
  avif;

  public static ImageExtension fromUrl(String url) {
    if (url != null && url.trim().length() > 4) {
      for (ImageExtension ie : values()) {
        if (substringBefore(url, "?").endsWith(ie.name())) {
          return ie;
        }
      }
    }
    return ImageExtension.jpg;
  }
}
