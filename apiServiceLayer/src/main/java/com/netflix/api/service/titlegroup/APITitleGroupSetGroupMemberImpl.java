package com.netflix.api.service.titlegroup;

public class APITitleGroupSetGroupMemberImpl implements APITitleGroupSetMember {

  private final int _parentGroup;
  private final int _group;

  public APITitleGroupSetGroupMemberImpl(int movieId, int parentMovieId) {
    this._parentGroup = parentMovieId;
    this._group = movieId;
  }

  @Override
  public Integer getMemberId() {
    return _group;
  }

  @Override
  public Integer getParentGroupId() {
    return _parentGroup;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + _group;
    result = prime * result + _parentGroup;
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    APITitleGroupSetGroupMemberImpl other = (APITitleGroupSetGroupMemberImpl) obj;
    if (_group != other._group) return false;
    if (_parentGroup != other._parentGroup) return false;
    return true;
  }
}
