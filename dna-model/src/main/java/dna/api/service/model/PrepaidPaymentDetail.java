package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PrepaidPaymentDetail {
  private String type;

  private Integer monthsRemaining;

  private Integer monthsPurchased;

  private String uiMessage;

  private String discountStatus;

  private Double amountRemaining;

  private Double purchaseAmount;

  private String periodType;

  private Integer periodsRemaining;

  private Integer periodsPurchased;

  public String getType() {
    return type;
  }

  public Integer getMonthsRemaining() {
    return monthsRemaining;
  }

  public Integer getMonthsPurchased() {
    return monthsPurchased;
  }

  public String getUiMessage() {
    return uiMessage;
  }

  public String getDiscountStatus() {
    return discountStatus;
  }

  public Double getAmountRemaining() {
    return amountRemaining;
  }

  public Double getPurchaseAmount() {
    return purchaseAmount;
  }

  public String getPeriodType() {
    return periodType;
  }

  public Integer getPeriodsRemaining() {
    return periodsRemaining;
  }

  public Integer getPeriodsPurchased() {
    return periodsPurchased;
  }

  public PrepaidPaymentDetail setType(String type) {
    this.type = type;
    return this;
  }

  public PrepaidPaymentDetail setMonthsRemaining(Integer monthsRemaining) {
    this.monthsRemaining = monthsRemaining;
    return this;
  }

  public PrepaidPaymentDetail setMonthsPurchased(Integer monthsPurchased) {
    this.monthsPurchased = monthsPurchased;
    return this;
  }

  public PrepaidPaymentDetail setUiMessage(String uiMessage) {
    this.uiMessage = uiMessage;
    return this;
  }

  public PrepaidPaymentDetail setDiscountStatus(String discountStatus) {
    this.discountStatus = discountStatus;
    return this;
  }

  public PrepaidPaymentDetail setAmountRemaining(Double amountRemaining) {
    this.amountRemaining = amountRemaining;
    return this;
  }

  public PrepaidPaymentDetail setPurchaseAmount(Double purchaseAmount) {
    this.purchaseAmount = purchaseAmount;
    return this;
  }

  public PrepaidPaymentDetail setPeriodType(String periodType) {
    this.periodType = periodType;
    return this;
  }

  public PrepaidPaymentDetail setPeriodsRemaining(Integer periodsRemaining) {
    this.periodsRemaining = periodsRemaining;
    return this;
  }

  public PrepaidPaymentDetail setPeriodsPurchased(Integer periodsPurchased) {
    this.periodsPurchased = periodsPurchased;
    return this;
  }
}
