package dna.api.service.model;

import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class TalentEvidence {
  private TalentEvidenceType evidenceType;

  private String evidenceKey;

  private List<TalentEvidenceData> evidenceData;

  private Map<String, String> annotations;

  public TalentEvidenceType getEvidenceType() {
    return evidenceType;
  }

  public String getEvidenceKey() {
    return evidenceKey;
  }

  public List<TalentEvidenceData> getEvidenceData() {
    return evidenceData;
  }

  public Map<String, String> getAnnotations() {
    return annotations;
  }

  public TalentEvidence setEvidenceType(TalentEvidenceType evidenceType) {
    this.evidenceType = evidenceType;
    return this;
  }

  public TalentEvidence setEvidenceKey(String evidenceKey) {
    this.evidenceKey = evidenceKey;
    return this;
  }

  public TalentEvidence setEvidenceData(List<TalentEvidenceData> evidenceData) {
    this.evidenceData = evidenceData;
    return this;
  }

  public TalentEvidence setAnnotations(Map<String, String> annotations) {
    this.annotations = annotations;
    return this;
  }
}
