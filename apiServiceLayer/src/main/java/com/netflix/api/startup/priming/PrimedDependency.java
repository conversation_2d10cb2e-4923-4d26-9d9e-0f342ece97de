package com.netflix.api.startup.priming;

import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;
import jakarta.annotation.PostConstruct;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

public class PrimedDependency<T> implements PrimedDependencyStage<T> {

  private CompletionStage<T> future;

  private volatile CompletionStage<Void> completionFuture;

  private Provider<T> nestedProvider;
  private Key<?> key;
  private PrimedDependencyManager manager;
  private Injector injector;

  @Inject
  protected PrimedDependency(
      Injector injector,
      TypeLiteral<T> nestedType,
      Provider<T> nestedProvider,
      PrimedDependencyManager manager) {
    this(injector, Key.get(TypeLiteral.get(nestedType.getType())), nestedProvider, manager);
  }

  protected PrimedDependency(
      Injector injector, Key<?> key, Provider<T> nestedProvider, PrimedDependencyManager manager) {
    this.nestedProvider = nestedProvider;
    this.key = key;
    this.manager = manager;
    this.injector = injector;
  }

  @PostConstruct
  protected void setup() {
    this.completionFuture = manager.schedule(injector, key, nestedProvider);
  }

  private CompletionStage<T> withValue() {
    CompletionStage<Void> completionFuture = this.completionFuture;
    if (completionFuture != null) {
      return completionFuture.thenApply((_Void) -> nestedProvider.get());
    }
    return future;
  }

  private PrimedDependency(CompletionStage<T> future) {
    this.future = future;
  }

  @Override
  public T get() {
    return withValue().toCompletableFuture().join();
  }

  private static <U> PrimedDependencyStage<U> withStage(
      PrimedDependency<?> current, CompletionStage<U> completionStage) {
    return new PrimedDependency<U>(completionStage);
  }

  @Override
  public <U, V> CompletionStage<V> thenCombine(
      CompletionStage<? extends U> other, BiFunction<? super T, ? super U, ? extends V> fn) {
    return withValue().thenCombine(other, fn);
  }

  @Override
  public <U, V> CompletionStage<V> thenCombineAsync(
      CompletionStage<? extends U> other, BiFunction<? super T, ? super U, ? extends V> fn) {
    return withValue().thenCombineAsync(other, fn);
  }

  @Override
  public <U, V> CompletionStage<V> thenCombineAsync(
      CompletionStage<? extends U> other,
      BiFunction<? super T, ? super U, ? extends V> fn,
      Executor executor) {
    return withValue().thenCombineAsync(other, fn);
  }

  @Override
  public <U> CompletionStage<Void> thenAcceptBoth(
      CompletionStage<? extends U> other, BiConsumer<? super T, ? super U> action) {
    return withValue().thenAcceptBoth(other, action);
  }

  @Override
  public <U> CompletionStage<Void> thenAcceptBothAsync(
      CompletionStage<? extends U> other, BiConsumer<? super T, ? super U> action) {
    return withValue().thenAcceptBoth(other, action);
  }

  @Override
  public <U> CompletionStage<Void> thenAcceptBothAsync(
      CompletionStage<? extends U> other,
      BiConsumer<? super T, ? super U> action,
      Executor executor) {
    return withValue().thenAcceptBothAsync(other, action);
  }

  @Override
  public CompletionStage<Void> runAfterBoth(CompletionStage<?> other, Runnable action) {
    return withValue().runAfterBoth(other, action);
  }

  @Override
  public CompletionStage<Void> runAfterBothAsync(CompletionStage<?> other, Runnable action) {
    return withValue().runAfterBoth(other, action);
  }

  @Override
  public CompletionStage<Void> runAfterBothAsync(
      CompletionStage<?> other, Runnable action, Executor executor) {
    return withValue().runAfterBothAsync(other, action, executor);
  }

  @Override
  public <U> CompletionStage<U> applyToEither(
      CompletionStage<? extends T> other, Function<? super T, U> fn) {
    return withValue().applyToEither(other, fn);
  }

  @Override
  public <U> CompletionStage<U> applyToEitherAsync(
      CompletionStage<? extends T> other, Function<? super T, U> fn) {
    return withValue().applyToEitherAsync(other, fn);
  }

  @Override
  public <U> CompletionStage<U> applyToEitherAsync(
      CompletionStage<? extends T> other, Function<? super T, U> fn, Executor executor) {
    return withValue().applyToEitherAsync(other, fn, executor);
  }

  @Override
  public CompletionStage<Void> acceptEither(
      CompletionStage<? extends T> other, Consumer<? super T> action) {
    return withValue().acceptEither(other, action);
  }

  @Override
  public CompletionStage<Void> acceptEitherAsync(
      CompletionStage<? extends T> other, Consumer<? super T> action) {
    return withValue().acceptEitherAsync(other, action);
  }

  @Override
  public CompletionStage<Void> acceptEitherAsync(
      CompletionStage<? extends T> other, Consumer<? super T> action, Executor executor) {
    return withValue().acceptEitherAsync(other, action, executor);
  }

  @Override
  public CompletionStage<Void> runAfterEither(CompletionStage<?> other, Runnable action) {
    return withValue().runAfterEither(other, action);
  }

  @Override
  public CompletionStage<Void> runAfterEitherAsync(CompletionStage<?> other, Runnable action) {
    return withValue().runAfterEitherAsync(other, action);
  }

  @Override
  public CompletionStage<Void> runAfterEitherAsync(
      CompletionStage<?> other, Runnable action, Executor executor) {
    return withValue().runAfterBothAsync(other, action, executor);
  }

  @Override
  public <U> PrimedDependencyStage<U> thenApply(Function<? super T, ? extends U> fn) {
    return withStage(this, withValue().thenApply(fn));
  }

  @Override
  public <U> PrimedDependencyStage<U> thenApplyAsync(Function<? super T, ? extends U> fn) {
    return withStage(this, withValue().thenApplyAsync(fn));
  }

  @Override
  public <U> PrimedDependencyStage<U> thenApplyAsync(
      Function<? super T, ? extends U> fn, Executor executor) {
    return withStage(this, withValue().thenApplyAsync(fn, executor));
  }

  @Override
  public PrimedDependencyStage<Void> thenAccept(Consumer<? super T> action) {
    return withStage(this, withValue().thenAccept(action));
  }

  @Override
  public PrimedDependencyStage<Void> thenAcceptAsync(Consumer<? super T> action) {
    return withStage(this, withValue().thenAcceptAsync(action));
  }

  @Override
  public PrimedDependencyStage<Void> thenAcceptAsync(
      Consumer<? super T> action, Executor executor) {
    return withStage(this, withValue().thenAcceptAsync(action, executor));
  }

  @Override
  public PrimedDependencyStage<Void> thenRun(Runnable action) {
    return withStage(this, withValue().thenRun(action));
  }

  @Override
  public PrimedDependencyStage<Void> thenRunAsync(Runnable action) {
    return withStage(this, withValue().thenRunAsync(action));
  }

  @Override
  public PrimedDependencyStage<Void> thenRunAsync(Runnable action, Executor executor) {
    return withStage(this, withValue().thenRunAsync(action, executor));
  }

  @Override
  public <U> PrimedDependencyStage<U> thenCompose(
      Function<? super T, ? extends CompletionStage<U>> fn) {
    return withStage(this, withValue().thenCompose(fn));
  }

  @Override
  public <U> PrimedDependencyStage<U> thenComposeAsync(
      Function<? super T, ? extends CompletionStage<U>> fn) {
    return withStage(this, withValue().thenComposeAsync(fn));
  }

  @Override
  public <U> PrimedDependencyStage<U> thenComposeAsync(
      Function<? super T, ? extends CompletionStage<U>> fn, Executor executor) {
    return withStage(this, withValue().thenComposeAsync(fn, executor));
  }

  @Override
  public PrimedDependencyStage<T> exceptionally(Function<Throwable, ? extends T> fn) {
    return withStage(this, withValue().exceptionally(fn));
  }

  @Override
  public PrimedDependencyStage<T> whenComplete(BiConsumer<? super T, ? super Throwable> action) {
    return withStage(this, withValue().whenComplete(action));
  }

  @Override
  public PrimedDependencyStage<T> whenCompleteAsync(
      BiConsumer<? super T, ? super Throwable> action) {
    return withStage(this, withValue().whenCompleteAsync(action));
  }

  @Override
  public PrimedDependencyStage<T> whenCompleteAsync(
      BiConsumer<? super T, ? super Throwable> action, Executor executor) {
    return withStage(this, withValue().whenCompleteAsync(action, executor));
  }

  @Override
  public <U> PrimedDependencyStage<U> handle(BiFunction<? super T, Throwable, ? extends U> fn) {
    return withStage(this, withValue().handle(fn));
  }

  @Override
  public <U> PrimedDependencyStage<U> handleAsync(
      BiFunction<? super T, Throwable, ? extends U> fn) {
    return withStage(this, withValue().handleAsync(fn));
  }

  @Override
  public <U> PrimedDependencyStage<U> handleAsync(
      BiFunction<? super T, Throwable, ? extends U> fn, Executor executor) {
    return withStage(this, withValue().handleAsync(fn, executor));
  }

  @Override
  public CompletableFuture<T> toCompletableFuture() {
    return withValue().toCompletableFuture();
  }
}
