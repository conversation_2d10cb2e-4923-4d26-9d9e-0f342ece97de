package com.netflix.api.service;

import java.util.Map;

public class APIInsufficientContentException extends APIAnnotatedException {

  private static final long serialVersionUID = -5663036062835547844L;

  public APIInsufficientContentException(String message) {
    super(message);
  }

  public APIInsufficientContentException(String message, Throwable cause) {
    super(message, cause);
  }

  public APIInsufficientContentException(String message, Map<String, Object> objAnnotations) {
    super(message, objAnnotations);
  }
}
