package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class RemoveFromContinueWatchingRequest {
  private List<Integer> videoIds;

  /** U<PERSON> can use this key to send UI version for DSE. */
  private String uiVersion;

  private Integer trackId;

  public List<Integer> getVideoIds() {
    return videoIds;
  }

  public String getUiVersion() {
    return uiVersion;
  }

  public Integer getTrackId() {
    return trackId;
  }

  public RemoveFromContinueWatchingRequest setVideoIds(List<Integer> videoIds) {
    this.videoIds = videoIds;
    return this;
  }

  public RemoveFromContinueWatchingRequest setUiVersion(String uiVersion) {
    this.uiVersion = uiVersion;
    return this;
  }

  public RemoveFromContinueWatchingRequest setTrackId(Integer trackId) {
    this.trackId = trackId;
    return this;
  }
}
