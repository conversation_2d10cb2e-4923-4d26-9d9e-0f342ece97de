package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class LinearScheduleItem {
  private Integer videoId;

  /** Scheduled start time of video (epoch time in seconds). */
  private Long startTime;

  /** Duration of video in seconds. */
  private Long duration;

  private Long trackId;

  public Integer getVideoId() {
    return videoId;
  }

  public Long getStartTime() {
    return startTime;
  }

  public Long getDuration() {
    return duration;
  }

  public Long getTrackId() {
    return trackId;
  }

  public LinearScheduleItem setVideoId(Integer videoId) {
    this.videoId = videoId;
    return this;
  }

  public LinearScheduleItem setStartTime(Long startTime) {
    this.startTime = startTime;
    return this;
  }

  public LinearScheduleItem setDuration(Long duration) {
    this.duration = duration;
    return this;
  }

  public LinearScheduleItem setTrackId(Long trackId) {
    this.trackId = trackId;
    return this;
  }
}
