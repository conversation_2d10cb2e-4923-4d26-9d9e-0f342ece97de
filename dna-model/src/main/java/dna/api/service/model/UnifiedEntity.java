package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class UnifiedEntity implements GraphData {
  /**
   * see
   * https://docs.google.com/document/d/1b_Teeve35tsg0X9x2A_sQ1tvXYsyHYkOFZzGcAvm_qE/edit#heading=h.dmdvppryv6en
   */
  public String id;

  public String getId() {
    return id;
  }

  public UnifiedEntity setId(String id) {
    this.id = id;
    return this;
  }
}
