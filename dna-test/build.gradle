apply plugin: 'nebula.facet'

facets {
    integTest {
        parentSourceSet = 'main'
        testTaskName = 'integrationTest'
        includeInCheckLifecycle = false
    }
}

tasks.named('integrationTest', Test).configure {
    develocity.predictiveTestSelection {
        enabled = false
    }
    if (project.hasProperty('target')) {
        systemProperty 'target', project.property('target')
    }
    if (project.hasProperty('hostname')) {
        systemProperty 'hostname', project.property('hostname')
    }
    if (project.hasProperty('cluster')) {
        systemProperty 'cluster', project.property('cluster')
    }
    if (project.hasProperty('vip')) {
        systemProperty 'vip', project.property('vip')
    }
    if (project.hasProperty('proxyEndpoint')) {
        systemProperty 'proxyEndpoint', project.property('proxyEndpoint')
    }
    if (project.hasProperty('isGraphql')) {
        systemProperty 'isGraphql', project.property('isGraphql')
    }

    if (project.hasProperty('environment')) {
        systemProperty 'netflix.environment', project.property('environment')
        systemProperty 'NETFLIX_ENVIRONMENT', project.property('environment')
        systemProperty 'environment', project.property('environment')
        systemProperty 'eureka.serviceUrl.default', 'http://discoveryreadonly.us-east-1.dyn' + project.property('environment') + '.netflix.net:7001/discovery/v2/'
    } else {
        systemProperty 'netflix.environment', 'test'
        systemProperty 'NETFLIX_ENVIRONMENT', 'test'
        systemProperty 'eureka.serviceUrl.default', 'http://discoveryreadonly.us-east-1.dyntest.netflix.net:7001/discovery/v2/'
    }

    systemProperty 'setupRule', 'com.netflix.api.test.service.TestDnaService'

    systemProperty 'java.net.preferIPv4Stack', 'true'

    systemProperty 'netflix.appid', 'api'

    systemProperty 'netflix.appinfo.region', 'us-east-1'
    systemProperty 'netflix.appinfo.doNotInitWithAmazonInfo', 'true'
    systemProperty 'netflix.datacenter', 'cloud'

    systemProperty 'netflix.discovery.registration.enabled', 'false'
    systemProperty 'eureka.preferSameZone', 'true'
    systemProperty 'eureka.shouldUseDns', 'false'

    systemProperty 'eureka.decoderName', 'JacksonJson'
    systemProperty 'log4j.logger.com.netflix.niws.client', 'ERROR'

    testLogging {
        // set options for log level LIFECYCLE
        events "passed", "skipped", "failed", "standardOut"
        showExceptions true
        exceptionFormat "full"
        showCauses true
        showStackTraces true
        showStandardStreams true

        // set options for log level DEBUG and INFO
        debug {
            events "started", "passed", "skipped", "failed", "standardOut", "standardError"
            exceptionFormat "full"
        }
        info.events = debug.events
        info.exceptionFormat = debug.exceptionFormat

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} successes, ${result.failedTestCount} failures, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}

apply from: file('../dependency-properties.gradle')
apply from: file('../dependency-overrides.gradle')

dependencies {
    // DO NOT ADD NON-PROJECT DEPENDENCIES HERE
    
    integTestImplementation project(':dna-client')
    integTestImplementation project(":dna-model")
    integTestImplementation project(':api-global-dependencies')
    integTestImplementation project(':api-service-layer')

    integTestImplementation "org.hamcrest:hamcrest-core:${hamcrestVersion}"
    integTestImplementation "org.hamcrest:hamcrest-library:${hamcrestVersion}"

    integTestImplementation(platform 'org.junit:junit-bom:${junitBomVersion}')
    integTestImplementation('org.junit.jupiter:junit-jupiter') {
        because 'allows to write and run Jupiter tests'
    }
    integTestImplementation('junit:junit')
    integTestRuntimeOnly('org.junit.platform:junit-platform-launcher') {
        because 'allows tests to run from IDEs that bundle older versions of launcher'
    }

    integTestImplementation "com.jayway.jsonpath:json-path:2.9.0"
    integTestImplementation 'com.netflix.passport.test:passport-test-core:latest.release'
}

tasks.withType(Test).configureEach {
    useJUnitPlatform()
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring:spring-boot-netflix-bom:latest.release'
}
