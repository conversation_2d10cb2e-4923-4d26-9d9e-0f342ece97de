package com.netflix.api.service.account;

import com.netflix.api.adapters.MaturityRatingsAdapter;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.annotations.ExcludeFromCoverage;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.context.ImmutableRequestContext;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.identity.CurrentIdentityResult;
import com.netflix.api.platform.identity.IdentityResult;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import com.netflix.api.service.identity.APIParentalControlMaturity;
import com.netflix.api.service.identity.APIProfileMaturity;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserFactory;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.api.service.plan.APIPlanInfoFactory;
import com.netflix.api.service.uipersonalization.APIUIPersonalizationImpl;
import com.netflix.api.service.uipersonalization.APIUIPersonalizationRequest;
import com.netflix.api.service.uipersonalization.APIUIPersonalizationService;
import com.netflix.archaius.api.Property;
import com.netflix.i18n.NFLocale;
import com.netflix.memberprice.adapter.MemberpriceResponseAdapter;
import com.netflix.patron.customersupport.protogen.AttributeScalar;
import com.netflix.patron.customersupport.protogen.AuthTokenProvidedInfo;
import com.netflix.patron.customersupport.protogen.CustomerSupportServiceGrpc.CustomerSupportServiceStub;
import com.netflix.patron.customersupport.protogen.GetAuthTokenRequest;
import com.netflix.patron.customersupport.protogen.GetAuthTokenResponse;
import com.netflix.patron.customersupport.protogen.GetPhoneNumberRequest;
import com.netflix.patron.customersupport.protogen.GetPhoneNumberResponse;
import com.netflix.patron.customersupport.protogen.GetServiceCodeRequest;
import com.netflix.patron.customersupport.protogen.GetServiceCodeResponse;
import com.netflix.patron.customersupport.protogen.GetVailAuthTokenRequest;
import com.netflix.patron.customersupport.protogen.RecordPostCallRequest;
import com.netflix.patron.partnermetadata.protogen.GetPartnerMetadataRequest;
import com.netflix.patron.partnermetadata.protogen.GetPartnerMetadataResponse;
import com.netflix.patron.partnermetadata.protogen.PartnerMetadataServiceGrpc.PartnerMetadataServiceStub;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.streaming.dts.client.DtsClient;
import com.netflix.streaming.dts.common.UnknownDeviceTypeException;
import com.netflix.type.ISOCountry;
import com.netflix.type.NFCountry;
import jakarta.annotation.Nonnull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@SuppressWarnings({"deprecation", "rawtypes"})
@EnableDeprecatedMetrics
@Component
public class APIAccountManagementServiceImpl implements APIAccountManagementService {
  private static final Logger logger =
      LoggerFactory.getLogger(APIAccountManagementServiceImpl.class);
  private static final Map<String, Object> REFERRAL_DEFAULT;
  private static final Property<Boolean> ENABLE_PIN_SIGNUP_COUNTRY =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("accountService.enablePinSignupCountry", Boolean.class)
          .orElse(false);

  static {
    REFERRAL_DEFAULT =
        Map.of(
            "isEligible",
            false,
            "memberIncentiveId",
            "NO_INCENTIVE",
            "friendIncentiveId",
            "NO_INCENTIVE");
  }

  private final Gateway gateway;
  private final APIUserFactory userFactory;
  private final DtsClient dtsClient;
  private final LegacyAccountAdapter legacyAccountAdapter;
  private final CustomerSupportServiceStub customerSupportServiceStub;
  private final APIPlanInfoFactory planInfoFactory;
  private final PartnerMetadataServiceStub partnerClientServiceStub;
  private final APIUIPersonalizationService apiUIPersonalizationService;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public APIAccountManagementServiceImpl(
      APIUserFactory userFactory,
      APIPlanInfoFactory planInfoFactory,
      DtsClient dtsClient,
      MaturityRatingsAdapter maturityRatingsAdapter,
      @GrpcSpringClient("patron") PartnerMetadataServiceStub partnerClientServiceStub,
      LegacyAccountAdapter legacyAccountAdapter,
      @GrpcSpringClient("patron") CustomerSupportServiceStub customerSupportServiceStub,
      APIUIPersonalizationService apiUIPersonalizationService) {
    this.gateway = new Prod(maturityRatingsAdapter);
    this.userFactory = userFactory;
    this.planInfoFactory = planInfoFactory;
    this.dtsClient = dtsClient;
    this.legacyAccountAdapter = legacyAccountAdapter;
    this.customerSupportServiceStub = customerSupportServiceStub;
    this.partnerClientServiceStub = partnerClientServiceStub;
    this.apiUIPersonalizationService = apiUIPersonalizationService;
  }

  @ExcludeFromCoverage
  interface Gateway {

    List<APIParentalControlMaturity> getParentalControlMaturityRating(String country);

    Observable<APIProfileMaturity> getAPIProfileMaturityRating(
        Integer maturityValue, String country);

    String getPinCountry(APIUser user);
  }

  private static class Prod implements Gateway {

    private final MaturityRatingsAdapter maturityRatingsAdapter;

    Prod(MaturityRatingsAdapter maturityRatingsAdapter) {
      this.maturityRatingsAdapter = maturityRatingsAdapter;
    }

    @Override
    public Observable<APIProfileMaturity> getAPIProfileMaturityRating(
        Integer maturityValue, String country) {
      Optional<APIProfileMaturity> profileMaturity =
          maturityRatingsAdapter.getAPIProfileMaturityRating(maturityValue, country);
      return profileMaturity.map(Observable::just).orElseGet(Observable::empty);
    }

    @Override
    public List<APIParentalControlMaturity> getParentalControlMaturityRating(String country) {
      return maturityRatingsAdapter.getParentalControlMaturityRatings(country);
    }

    @Override
    public String getPinCountry(APIUser user) {
      if (!ENABLE_PIN_SIGNUP_COUNTRY.get() || user == null || user.getCountryOfSignup() == null) {
        return RequestContextWrapper.get().getCountry().getId();
      }
      return user.getCountryOfSignup();
    }
  }

  @Override
  public List<APIParentalControlMaturity> getParentalControlMaturityRatings(APIUser apiUser) {
    String country = gateway.getPinCountry(apiUser);
    return gateway.getParentalControlMaturityRating(country);
  }

  @Override
  public Observable<APIProfileMaturity> getAPIProfileMaturityRating(APIUser user) {
    return user.getRawMaturityValue()
        .flatMap(
            rawMaturity -> {
              if (rawMaturity == null) {
                return Observable.empty();
              }
              return user.getCountryOfSignupObservable()
                  .flatMap(
                      signupCountry -> {
                        if (signupCountry == null) {
                          return Observable.empty();
                        }
                        return gateway.getAPIProfileMaturityRating(rawMaturity, signupCountry);
                      });
            });
  }

  @Override
  public Observable<APIProfileConfiguration> getProfileConfiguration(APIUser user) {
    if (user == null) {
      return Observable.error(new IllegalArgumentException("No current user"));
    }
    return this.apiUIPersonalizationService
        .getPersonalization(
            APIUIPersonalizationRequest.builder()
                .user(user)
                .retrieveVideoMerchDisabled(true)
                .consistentRead(true)
                .build())
        .map(uipResponse -> Optional.ofNullable(uipResponse.getVideoMerchDisabled()).orElse(false))
        .onErrorReturn(ignore -> false)
        .defaultIfEmpty(false)
        .map(APIProfileConfigurationImpl::new);
  }

  @Override
  public Observable<UpdateProfileConfigurationResponse> updateProfileConfiguration(
      UpdateProfileConfigurationRequest update) {
    if (update == null) {
      return Observable.error(new IllegalArgumentException("Must provide an update"));
    }
    APIUser user = update.getUser();
    if (user == null) {
      return Observable.error(new IllegalArgumentException("No user"));
    }
    return apiUIPersonalizationService
        .setPersonalization(
            APIUIPersonalizationImpl.merchDisabled(update.isDisableVideoMerch()),
            user.getCustomerGUID())
        .map(response -> new UpdateProfileConfigurationResponse());
  }

  @Override
  public Observable<List<APIUser>> getAddOnBeneficiaries() {
    APIUser user = APIRequest.getCurrentRequest().getUser();
    if (user == null) {
      return Observable.just(Collections.emptyList());
    }
    return userFactory
        .getAccountProfileHandler()
        .getMemberData(APIUserUtil.getAccountOwnerId(user))
        .map(
            memberData -> {
              ArrayList<APIUser> addOnUsers =
                  new ArrayList<>(memberData.getAddOnBeneficiaryIdsCount());
              List<Long> addOns = memberData.getAddOnBeneficiaryIdsList();
              for (Long addOn : addOns) {
                addOnUsers.add(userFactory.createUser(addOn));
              }
              return addOnUsers;
            });
  }

  @Override
  @Deprecated
  public Observable<Map<String, Object>> executeGet(String endpoint, Map<String, Object> args) {
    if (!Objects.equals(endpoint, "promotionEligibility")) {
      DynamicCounter.increment("api.accountService.executeGet.empty", "endpoint", endpoint);
      return Observable.just(Map.of());
    }
    DynamicCounter.increment("api.accountService.executeGet", "endpoint", endpoint);
    return legacyAccountAdapter.promoEligibility(args);
  }

  @Override
  public Observable<APIPlan> getPlan() {
    return getPlan(APIRequest.getCurrentRequest());
  }

  @Override
  public Observable<APIPlan> getPlan(APIRequest apiRequest) {
    return apiRequest
        .getObservableUser()
        .flatMap(APIUser::getPlanId)
        .filter(planId -> planId != null && planId > 0)
        .flatMap(planId -> planInfoFactory.getPlan(planId).map(APIPlanImpl::new));
  }

  @Override
  public Observable<String> getCSServiceCode(final APIUser user) {
    Long customerId = APIUserUtil.getCustomerId(user);
    return RxObservable.defer(
            customerSupportServiceStub::getServiceCode,
            GetServiceCodeRequest.newBuilder().setCustomerId(customerId).build())
        .map(GetServiceCodeResponse::getBoxedServiceCode);
  }

  @Override
  public Observable<String> getCSPhoneNumber(APIUser user) {
    try {
      Long customerId = APIUserUtil.getCustomerId(user);
      ImmutableRequestContext requestContext = RequestContextWrapper.get();
      NFCountry country = convertCountry(requestContext.getCountry());
      NFLocale locale = requestContext.getNfLocale();
      return RxObservable.defer(
              customerSupportServiceStub::getPhoneNumber,
              GetPhoneNumberRequest.newBuilder()
                  .setCustomerId(customerId)
                  .setCountryId(country.getId())
                  .setLocaleId(locale.getId())
                  .build())
          .map(GetPhoneNumberResponse::getBoxedPhoneNumber);
    } catch (Exception e) {
      return Observable.error(e);
    }
  }

  private @Nonnull NFCountry convertCountry(ISOCountry country) {
    if (country instanceof NFCountry nfCountry) {
      return nfCountry;
    }

    if (country != null) {
      return NFCountry.findInstance(country.getId());
    }

    return NFCountry.US;
  }

  @Override
  public Observable<APIVailToken> getVailAuthToken(Map<String, Object> attributes) {
    // attempt to get a token as member
    return callVailAuthToken(authInfo(attributes, true));
  }

  @Override
  public Observable<APITokenForCall> getAuthTokenForCall(Map<String, Object> attributes) {
    // attempt to get a token as member
    return RxObservable.defer(
            customerSupportServiceStub::getAuthToken,
            GetAuthTokenRequest.newBuilder().setInfo(authInfo(attributes, true)).build())
        .map(GetAuthTokenResponse::getInfo)
        .map(APITokenForCallImpl::new);
  }

  @Override
  public Observable<Void> recordPostCallInfo(String authToken, String callData) {
    return RxObservable.call(
            customerSupportServiceStub::recordPostCall,
            RecordPostCallRequest.newBuilder()
                .setAuthToken(authToken)
                .setCallData(callData)
                .build())
        .map(recordPostCallInfoResponse -> null);
  }

  @Override
  public Observable<APIVailToken> getVailNonMemberAuthToken(Map<String, Object> attributes) {
    return callVailAuthToken(authInfo(attributes, false));
  }

  private Observable<APIVailToken> callVailAuthToken(AuthTokenProvidedInfo info) {
    return RxObservable.defer(
            customerSupportServiceStub::getVailAuthToken,
            GetVailAuthTokenRequest.newBuilder().setInfo(info).build())
        .map(
            token ->
                new APIVailTokenImpl(
                    token.getInfo().getBoxedToken(),
                    token.getBoxedCredentials(),
                    token.getInfo().getBoxedTokenExpirationTs(),
                    token.getInfo().getAttributesMap()));
  }

  private AuthTokenProvidedInfo authInfo(Map<String, Object> attributes, boolean asMember) {
    AuthTokenProvidedInfo.Builder builder = AuthTokenProvidedInfo.newBuilder();

    final APIRequestContext requestContext = APIRequest.getCurrentRequest().getRequestContext();
    builder.setDeviceTypeName(
        requestContext.getDeviceType() != null
            ? requestContext.getDeviceType().getDescription()
            : null);
    builder.setDeviceTypeId(requestContext.getDeviceTypeId());
    builder.setEsn(requestContext.getESN());
    builder.putAllAttributes(convertAttributes(attributes));

    // can still be null if customer is non-member
    if (asMember) {
      builder.setCustomerId(
          APIRequest.getCurrentRequest().getUser() != null
              ? APIUserUtil.getCustomerId(APIRequest.getCurrentRequest().getUser())
              : null);
    }

    IdentityResult identityResult = CurrentIdentityResult.get();
    if (identityResult != null) {

      var status = identityResult.getMembershipStatus();
      if (status.isPresent()) {
        builder.setMembershipType(status.get().toString());
      }
    }

    return builder.build();
  }

  private Map<String, AttributeScalar> convertAttributes(Map<String, Object> attrs) {
    if (attrs == null) return Map.of();

    var m = new HashMap<String, AttributeScalar>();

    attrs.forEach(
        (key, value) -> {
          switch (value) {
            case String s -> m.put(key, AttributeScalar.newBuilder().setStringAttribute(s).build());
            case Boolean b -> m.put(key, AttributeScalar.newBuilder().setBoolAttribute(b).build());
            case Integer i -> m.put(key, AttributeScalar.newBuilder().setIntAttribute(i).build());
            case null -> logger.error("null value for attribute key={}", key);
            default -> logger.error("unexpected attribute key={} value={}", key, value);
          }
        });

    return m;
  }

  @Override
  @Deprecated
  // see https://jira.netflix.com/browse/TVUI-34214
  public Observable<Map<String, Object>> getPartnerMetadata(String esnPrefix) {
    if (esnPrefix == null) {
      return Observable.error(new NullPointerException("ESN prefix cannot be null"));
    }
    String temp = null;
    try {
      temp = dtsClient.getDeviceTypeFromESN(esnPrefix).getEsnStartsWith();
    } catch (UnknownDeviceTypeException ignore) {
      DynamicCounter.increment("api.partnermetadata", "result", "badesn");
    }
    final String esnStartsWith = temp;
    return RxObservable.defer(
            partnerClientServiceStub::getPartnerMetadata,
            GetPartnerMetadataRequest.newBuilder()
                .setEsnPrefix(esnPrefix)
                .setEsnStartsWith(esnStartsWith)
                .build())
        .map(GetPartnerMetadataResponse::getAttributesMap)
        .map(this::convertMap)
        .onErrorResumeNext(ignore -> Observable.empty())
        .defaultIfEmpty(null);
  }

  @Override
  @Deprecated
  public Observable<Map<String, Object>> getProductChoicesMap(Map<String, String> requestMap) {
    APIUser user = APIRequest.getCurrentRequest().getUser();
    if (user == null) {
      return Observable.error(new IllegalArgumentException("no user provided"));
    }
    if (user.isJustForKidsExperience()) {
      return Observable.just(MemberpriceResponseAdapter.getFallbackMap());
    }
    return legacyAccountAdapter.getProductChoiceMap(APIUserUtil.getCustomerId(user), requestMap);
  }

  @Override
  @Deprecated
  public Observable<Map<String, Object>> updateProductChoiceMap(Map<String, String> updateMap) {
    APIUser user = APIRequest.getCurrentRequest().getUser();
    if (user == null) {
      return Observable.error(new IllegalArgumentException("no user provided"));
    }
    final Long customerId = APIUserUtil.getCustomerId(user);
    return legacyAccountAdapter.updateProductChoiceMap(customerId, updateMap);
  }

  private Map<String, Object> convertMap(Map<String, String> patronResponse) {
    if (patronResponse == null) {
      return null;
    }
    return new HashMap<>(patronResponse);
  }

  @Override
  @Deprecated
  public Observable<Map<String, Object>> getReferralId(Map<String, String> requestMap) {
    DynamicCounter.increment(
        "api.referralid.call",
        "path",
        APIRequest.getCurrentRequest().getRequestContext().getEndpointPath());
    return Observable.just(REFERRAL_DEFAULT);
  }
}
