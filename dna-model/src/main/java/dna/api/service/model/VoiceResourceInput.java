package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.vps.VoiceResource */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class VoiceResourceInput {
  private VoiceResourceType resourceType;

  private String id;

  private String value;

  public VoiceResourceType getResourceType() {
    return resourceType;
  }

  public String getId() {
    return id;
  }

  public String getValue() {
    return value;
  }

  public VoiceResourceInput setResourceType(VoiceResourceType resourceType) {
    this.resourceType = resourceType;
    return this;
  }

  public VoiceResourceInput setId(String id) {
    this.id = id;
    return this;
  }

  public VoiceResourceInput setValue(String value) {
    this.value = value;
    return this;
  }
}
