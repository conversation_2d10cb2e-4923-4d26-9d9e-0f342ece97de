package com.netflix.api.service.ums;

import com.netflix.api.adapters.StreamingClientServiceAdapter;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.APIAnnotation;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.api.service.list.APIAsyncListImpl;
import com.netflix.api.service.list.ReferenceSource;
import com.netflix.evolutionprotodefinition.channel.protogen.NotificationReadStateFilter;
import com.netflix.i18n.NFLocale;
import com.netflix.tela.client.TelaClientUtil;
import com.netflix.tela.common.protogen.ReplyStatus;
import com.netflix.tela.notifications.protogen.NetflixInAppNotificationsReply;
import com.netflix.tela.notifications.protogen.NetflixInAppNotificationsRequest;
import com.netflix.tela.notifications.protogen.TelaNotificationsServiceGrpc;
import com.netflix.type.ISOCountry;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import rx.Observable;
import rx.Single;

public class APIAsyncFilterNotificationsTelaImpl
    extends APIAsyncListImpl<Map<String, Object>, Map<String, Object>>
    implements APIFilterNotifications {

  private static final NetflixInAppNotificationsReply FallbackReply;

  static {
    // default fallback response backported from hystrix command
    FallbackReply =
        NetflixInAppNotificationsReply.newBuilder()
            .addAllNotifications(List.of())
            .setEtag("d751713988987e9331980363e24189ce")
            .setLatestMessageTimestamp(0L)
            .setStatus(ReplyStatus.SUCCESS)
            .build();
  }

  private final Observable<List<Map<String, Object>>> cachedMessages;
  private final Observable<String> cachedEtag;
  private final Observable<Long> cachedLatestMessageTimestamp;

  public APIAsyncFilterNotificationsTelaImpl(
      TelaNotificationsServiceGrpc.TelaNotificationsServiceStub telaNotificationsServiceStub,
      StreamingClientServiceAdapter streamingClientServiceAdapter,
      APIFilterNotificationMessagesRequestImpl request) {

    Observable<NetflixInAppNotificationsReply> telaResponse =
        streamingClientServiceAdapter
            .getHostName()
            .toObservable()
            .flatMap(
                ocaHost -> {
                  NetflixInAppNotificationsRequest telaRequest = createFrom(ocaHost, request);
                  return RxObservable.defer(
                          telaNotificationsServiceStub::getNetflixInAppNotifications, telaRequest)
                      .onErrorReturn(e -> FallbackReply);
                })
            .cache();

    cachedMessages =
        telaResponse.map(
            response -> TelaClientUtil.convertProtoNotifications(response.getNotificationsList()));

    cachedEtag = telaResponse.map(NetflixInAppNotificationsReply::getEtag);

    cachedLatestMessageTimestamp =
        telaResponse.map(NetflixInAppNotificationsReply::getLatestMessageTimestamp);
  }

  private NetflixInAppNotificationsRequest createFrom(
      String ocaHost, APIFilterNotificationMessagesRequestImpl filterRequest) {
    ISOCountry country = RequestContextWrapper.get().getCountry();
    NFLocale locale = RequestContextWrapper.get().getNfLocale();
    APIUser user = APIRequest.getCurrentRequest().getUser();

    if (user == null) {
      throw new IllegalArgumentException("Must provide user");
    }

    NetflixInAppNotificationsRequest.Builder telaRequestBuilder =
        NetflixInAppNotificationsRequest.newBuilder()
            .setProfileId(APIUserUtil.getCustomerId(user))
            .setCountry(country.getId())
            .setEventuallyConsistent(true);

    if (locale != null) {
      telaRequestBuilder.setLocale(locale.getId());
    }
    if (filterRequest.getMaxCount() != null) {
      telaRequestBuilder.setMaxCount(filterRequest.getMaxCount());
    }
    if (filterRequest.getTimestamp() != null) {
      telaRequestBuilder.setReferenceTs(filterRequest.getTimestamp());
    }
    if (filterRequest.getRetrieveOlderThanRefTimestamp() != null) {
      telaRequestBuilder.setSearchOlder(filterRequest.getRetrieveOlderThanRefTimestamp());
    }
    if (StringUtils.isNotBlank(filterRequest.getEventGuid())) {
      telaRequestBuilder.setTargetEventGuid(filterRequest.getEventGuid());
    }

    if (StringUtils.isNotBlank(filterRequest.getUiPlatform())) {
      telaRequestBuilder.setUiPlatform(filterRequest.getUiPlatform());
    }
    if (StringUtils.isNotBlank(filterRequest.getAppVersion())) {
      telaRequestBuilder.setAppVersion(filterRequest.getAppVersion());
    }
    if (StringUtils.isNotBlank(ocaHost)) {
      telaRequestBuilder.setOcaHostname(ocaHost);
    }
    if (StringUtils.isNotBlank(filterRequest.getBoxartSize())) {
      telaRequestBuilder.setBoxArtSize(filterRequest.getBoxartSize());
    }
    if (filterRequest.getReadState() != null) {
      telaRequestBuilder.setReadStateFilter(
          NotificationReadStateFilter.valueOf(filterRequest.getReadState().name()));
    }

    return telaRequestBuilder.build();
  }

  @Override
  protected Observable<Map<String, Object>> loadReferences() {
    return cachedMessages
        .flatMap(
            r -> {
              ReferenceSource<Map<String, Object>> refSource = new ReferenceSource.ListSource<>(r);
              return Observable.from(refSource);
            })
        .onErrorResumeNext(e -> Observable.just(Collections.emptyMap()));
  }

  @Override
  protected Single<Map<String, Object>> loadItem(Map<String, Object> map) {
    return Single.just(map);
  }

  @Override
  public Observable<String> getListId() {
    return Observable.empty();
  }

  @Override
  public Observable<APIAnnotation<String, Object>> getAnnotations() {
    return Observable.empty();
  }

  @Override
  public Observable<String> getListName() {
    return Observable.empty();
  }

  @Override
  public Long getLatestMessageTimestamp() {
    return getLatestMessageTimestampObservable().toBlocking().first();
  }

  @Override
  public Observable<Long> getLatestMessageTimestampObservable() {
    return cachedLatestMessageTimestamp;
  }

  @Override
  public String getEtag() {
    return getEtagObservable().toBlocking().first();
  }

  @Override
  public Observable<String> getEtagObservable() {
    return cachedEtag;
  }
}
