package com.netflix.api.service.video;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.archaius.api.Property;
import com.netflix.lang.RequestVariable;
import com.netflix.servo.monitor.DynamicCounter;
import java.util.Collection;
import java.util.concurrent.PriorityBlockingQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class APIVideoMetadataCacheHelper {
  private static final Logger logger = LoggerFactory.getLogger(APIVideoMetadataCacheHelper.class);

  private static final Property<Integer> INITIAL_PRIORITY_QUEUE_SIZE =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.video.metadata.priority.queue.init.size", Integer.class)
          .orElse(10);
  private static final Property<Boolean> SMALLEST_BATCH =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.video.metadata.smallest.batch.enabled", Boolean.class)
          .orElse(true);
  private static final Property<Boolean> LOG_VIDEO_BATCHES =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.video.service.logVideoBatches", Boolean.class)
          .orElse(false);

  private static final RequestVariable<PriorityBlockingQueue<Collection<Integer>>>
      requestScopedVideoBatches =
          new RequestVariable<>() {
            @Override
            public PriorityBlockingQueue<Collection<Integer>> initialValue() {
              return new PriorityBlockingQueue<>(
                  INITIAL_PRIORITY_QUEUE_SIZE.get(),
                  (o1, o2) -> {
                    if (SMALLEST_BATCH.get()) {
                      // smallest size first
                      if (o1 == null) return -1;
                      if (o2 == null) return 1;
                      if (o1.size() > o2.size()) return 1;
                      return -1;
                    } else {
                      if (o1 == null) return 1;
                      if (o2 == null) return -1;
                      if (o1.size() > o2.size()) return -1;
                      return 1;
                    }
                  });
            }
          };

  public void registerBatch(Collection<Integer> idList) {
    // batches-of-1 do not help us
    if (idList == null || idList.size() <= 1) {
      return;
    }
    if (LOG_VIDEO_BATCHES.get()) {
      try {
        String path = APIRequest.getCurrentRequest().getRequestContext().getEndpointPath();
        DynamicCounter.increment(
            "api.video.metadata.batch.register",
            "path",
            path,
            "size",
            String.valueOf(idList.size()));
      } catch (Exception e) {
        logger.error("Problem logging registering metric", e);
      }
    }
    requestScopedVideoBatches.get().add(idList);
  }
}
