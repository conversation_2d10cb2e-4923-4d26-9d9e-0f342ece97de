package com.netflix.api.dependencies.avatar;

import ust.images.v1.Image;

public class ProfileAvatar {

  private final String id;
  private final String url;
  private final boolean inDefaultSet;
  private final Image image;

  public ProfileAvatar(String id, Image image, boolean secure, boolean inDefaultSet) {
    this.id = id;
    this.url = secure ? image.getBoxedSecureUrl() : image.getBoxedInsecureUrl();
    this.inDefaultSet = inDefaultSet;
    this.image = image;
  }

  public String getId() {
    return id;
  }

  public String getUrl() {
    return url;
  }

  public boolean isInDefaultSet() {
    return inDefaultSet;
  }

  public Image getImage() {
    return image;
  }
}
