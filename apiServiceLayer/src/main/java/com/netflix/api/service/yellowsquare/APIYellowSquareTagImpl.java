package com.netflix.api.service.yellowsquare;

import java.util.Objects;

public class APIYellowSquareTagImpl implements APIYellowSquareTag {

  private final String name;
  private final String value;
  private final Integer ttl;
  private final Long timestamp;

  protected APIYellowSquareTagImpl(String name, String value, Integer ttl, Long timestamp) {
    this.name = name;
    this.value = value;
    this.ttl = ttl;
    this.timestamp = timestamp;
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public String getValue() {
    return value;
  }

  @Override
  public int getTtl() {
    return ttl;
  }

  @Override
  public Long getUpdatedTimestamp() {
    return timestamp;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIYellowSquareTagImpl)) {
      return false;
    }
    APIYellowSquareTagImpl that = (APIYellowSquareTagImpl) o;
    return Objects.equals(name, that.name)
        && Objects.equals(value, that.value)
        && Objects.equals(timestamp, that.timestamp);
  }

  @Override
  public int hashCode() {
    return Objects.hash(name, value, ttl, timestamp);
  }

  @Override
  public String toString() {
    return "APIYellowSquareTag{"
        + "name='"
        + name
        + '\''
        + ", value='"
        + value
        + '\''
        + ", ttl="
        + ttl
        + ", timestamp="
        + timestamp
        + '}';
  }
}
