package com.netflix.api.service;

import com.netflix.api.service.identity.APIDeletedProfileException;
import com.netflix.api.service.identity.APIUser;
import com.netflix.lang.RequestVariable;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.NoopRegistry;
import com.netflix.spectator.api.Registry;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Objects;
import org.apache.commons.lang3.concurrent.ConcurrentException;
import org.apache.commons.lang3.concurrent.LazyInitializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.Observable;

/**
 * Objects and state for a single HTTP request/response loop to an API EndpointScript.
 *
 * <p>Each EndpointScript execution will receive an instance of this class containing request
 * specific information such as request URI variables and user identities.
 */
public class APIRequest {
  private static final Logger logger = LoggerFactory.getLogger(APIRequest.class);

  private final Observable<APIUser> userObservable;
  private final LazyInitializer<APIUser> userBlocking = new APIUserLazyInitializer();
  private final APIRequestContext requestContext;
  private final HttpServletRequest request;
  private final HttpServletResponse response;

  private final Counter okCount;
  private final Counter deletedCount;
  private final Counter nullCount;

  /* used to allow static access to the current request */
  private static final RequestVariable<APIRequest> currentRequest = new RequestVariable<>();

  public APIRequest(
      APIUser user,
      APIRequestContext requestContext,
      HttpServletRequest request,
      HttpServletResponse response,
      Registry registry) {
    this.userObservable = loadUser(user);
    this.requestContext = requestContext;
    this.request = request;
    this.response = response;

    //  Count metrics to understand how often we get requests from deleted users.
    if (registry == null) registry = new NoopRegistry();
    this.okCount = newCounter(registry, "ok");
    this.deletedCount = newCounter(registry, "deleted");
    this.nullCount = newCounter(registry, "null");
  }

  private static Counter newCounter(Registry reg, String result) {
    return reg.counter("apiRequest.loadUser", "result", result);
  }

  /**
   * User bound to this request. APIUser is an abstraction over a netflix user (subscriber).
   *
   * @return the user bound to this request. Null if user cannot be resolved (no netflix cookies,
   *     bad cookies, no user id, etc).
   */
  public APIUser getUser() {
    try {
      return userBlocking.get();
    } catch (ConcurrentException e) {
      logger.error("Could not get user", e);
    }
    return null;
  }

  private Observable<APIUser> loadUser(final APIUser user) {
    return Observable.defer(
            () ->
                Observable.just(user)
                    .map(
                        u -> {
                          if (u == null) {
                            nullCount.increment();
                            return null;
                          }

                          try {
                            u.getCustomerGUID();
                            okCount.increment();
                            return u;
                          } catch (APIDeletedProfileException e) {
                            deletedCount.increment();
                            return null;
                          }
                        }))
        .cache();
  }

  /**
   * An asynchronous user exposed to the scripts that contains only a fully-constructed APIUser
   * instance Scripts can take the output of this method and connect it to other methods expecting
   * an APIUser to get fully asynchronous scripts
   *
   * @return An Observable that has either a fully-constructed APIUser or a failure
   */
  public Observable<APIUser> getObservableUser() {
    // need to invoke getDateCreated() so that user can be fully constructed.
    return userObservable
        .filter(Objects::nonNull)
        .map(
            u -> {
              u.getDateCreated();
              return u;
            });
  }

  /**
   * APIRequestContext is a wrapper around request context that is passed along from service to
   * service. It contains information regarding the geo location, locales, etc. Any information
   * passed in the request parameters, headers or cookies related to geo, user or device is stored
   * here.
   *
   * @return a request context object. Not null.
   */
  public APIRequestContext getRequestContext() {
    return requestContext;
  }

  /**
   * HttpServletRequest bound to the request. Use this to get any information sent by clients in the
   * http(s) request.
   */
  public HttpServletRequest getServletRequest() {
    return request;
  }

  /**
   * HttpServletResponse bound to the request. Scripts can set all response attributes here, and
   * stream response back.
   */
  public HttpServletResponse getServletResponse() {
    return response;
  }

  /**
   * The APIRequest for the current HTTP request.
   *
   * @return the {@link APIRequest} object corresponding to the current HTTP request
   */
  public static APIRequest getCurrentRequest() {
    return currentRequest.get();
  }

  public static void setCurrentRequest(APIRequest apiRequest) {
    currentRequest.set(apiRequest);
  }

  private class APIUserLazyInitializer extends LazyInitializer<APIUser> {

    @Override
    protected APIUser initialize() {
      return userObservable.toBlocking().first();
    }
  }
}
