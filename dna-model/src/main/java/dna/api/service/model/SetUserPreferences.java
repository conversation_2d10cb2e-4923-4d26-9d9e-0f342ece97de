package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SetUserPreferences {
  private Boolean isReduceAnimationEffect;

  public Boolean getIsReduceAnimationEffect() {
    return isReduceAnimationEffect;
  }

  public SetUserPreferences setIsReduceAnimationEffect(Boolean isReduceAnimationEffect) {
    this.isReduceAnimationEffect = isReduceAnimationEffect;
    return this;
  }
}
