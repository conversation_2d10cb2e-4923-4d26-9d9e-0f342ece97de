package dna.api.service.model;

import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PreQueryOptions {
  private List<SearchResultType> searchResultTypes;

  private Map<String, String> options;

  public List<SearchResultType> getSearchResultTypes() {
    return searchResultTypes;
  }

  public Map<String, String> getOptions() {
    return options;
  }

  public PreQueryOptions setSearchResultTypes(List<SearchResultType> searchResultTypes) {
    this.searchResultTypes = searchResultTypes;
    return this;
  }

  public PreQueryOptions setOptions(Map<String, String> options) {
    this.options = options;
    return this;
  }
}
