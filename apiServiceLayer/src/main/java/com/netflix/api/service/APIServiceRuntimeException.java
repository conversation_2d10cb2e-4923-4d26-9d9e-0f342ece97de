package com.netflix.api.service;

import com.netflix.api.annotations.ExcludeFromCoverage;

@ExcludeFromCoverage
public class APIServiceRuntimeException extends RuntimeException {

  private static final long serialVersionUID = -8640838981865455399L;

  public static final Integer BAD_REQUEST = 400;
  public static final Integer INTERNAL_ERROR = 500;

  private final Integer statusCode;

  public APIServiceRuntimeException(String message) {
    this(message, null, null);
  }

  public APIServiceRuntimeException(String message, Throwable e) {
    this(message, e, null);
  }

  public APIServiceRuntimeException(String message, Integer statusCode) {
    this(message, null, statusCode);
  }

  public APIServiceRuntimeException(String message, Throwable e, Integer statusCode) {
    super(message, e);
    this.statusCode = statusCode == null ? INTERNAL_ERROR : statusCode;
  }

  public Integer getStatusCode() {
    return statusCode;
  }
}
