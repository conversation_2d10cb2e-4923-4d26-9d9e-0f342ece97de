package com.netflix.api.service.list;

import com.netflix.api.service.APIAnnotation;
import com.netflix.api.service.BasicAPIAnnotation;
import com.netflix.api.service.batch.APILoloUtils;
import com.netflix.map.datatypes.MapAnnotations;
import com.netflix.map.datatypes.MapList;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ProcessedMapAnnotations {

  private final MapAnnotations underlyingMapAnnotations;

  private ProcessedMapAnnotations(MapAnnotations underlyingMapAnnotations) {
    this.underlyingMapAnnotations = underlyingMapAnnotations;
  }

  public static ProcessedMapAnnotations from(MapList<?> mapList) {
    MapAnnotations underlyingMapAnnotations = mapList.getAnnotations();
    return new ProcessedMapAnnotations(underlyingMapAnnotations);
  }

  public Set<String> getNames() {
    if (underlyingMapAnnotations == null) {
      return null;
    }
    Set<String> result = new HashSet<>(underlyingMapAnnotations.getAllNames());

    for (String oldName : MapResponseUtils.MAP_ANNOTATIONS_DEPRECATED_TO_CURRENT.keySet()) {
      if (result.contains(MapResponseUtils.MAP_ANNOTATIONS_DEPRECATED_TO_CURRENT.get(oldName))
          && !result.contains(oldName)) result.add(oldName);
    }
    return result;
  }

  public List<APIAnnotation<String, Object>> getApiAnnotations() {
    List<APIAnnotation<String, Object>> apiAnnotations = new ArrayList<>();

    if (underlyingMapAnnotations != null) {
      for (String name : getNames()) {
        String newName = getNewAnnotationName(name);
        Object value = sanitize(underlyingMapAnnotations.get(newName));
        if (value != null) {
          apiAnnotations.add(new BasicAPIAnnotation<>(name, value));
        }
      }
    }
    return apiAnnotations;
  }

  private static String getNewAnnotationName(String name) {
    String newName = name;
    if (MapResponseUtils.MAP_ANNOTATIONS_DEPRECATED_TO_CURRENT.containsKey(name)) {
      newName = MapResponseUtils.MAP_ANNOTATIONS_DEPRECATED_TO_CURRENT.get(name);
    }
    return newName;
  }

  static Object sanitize(Object o) {
    return APILoloUtils.sanitize(o);
  }

  public <O> O getUnsanitizedAnnotation(String name, Class<O> asType) {
    return MapResponseUtils.getAnnotation(name, underlyingMapAnnotations, asType);
  }
}
