package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class EditorialPromotionPlacement {
  private String placementId;

  private List<EditorialPromotionTile> tiles;

  public String getPlacementId() {
    return placementId;
  }

  public List<EditorialPromotionTile> getTiles() {
    return tiles;
  }

  public EditorialPromotionPlacement setPlacementId(String placementId) {
    this.placementId = placementId;
    return this;
  }

  public EditorialPromotionPlacement setTiles(List<EditorialPromotionTile> tiles) {
    this.tiles = tiles;
    return this;
  }
}
