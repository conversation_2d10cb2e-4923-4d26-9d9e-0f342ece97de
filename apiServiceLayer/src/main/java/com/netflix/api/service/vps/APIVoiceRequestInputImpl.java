package com.netflix.api.service.vps;

import com.netflix.api.service.identity.APIMaturity;
import java.util.Map;

public class APIVoiceRequestInputImpl implements APIVoiceRequestInput {

  private String rawTextUtterance;
  private String languageCode;
  private APIMaturity profileMaturity;
  private String appName;
  private boolean odpAware;
  private Map<String, String> additionalContext;

  public APIVoiceRequestInputImpl(
      String rawTextUtterance,
      String languageCode,
      APIMaturity profileMaturity,
      String appName,
      boolean odpAware,
      Map<String, String> addditonalContext) {
    this.rawTextUtterance = rawTextUtterance;
    this.languageCode = languageCode;
    this.profileMaturity = profileMaturity;
    this.appName = appName;
    this.odpAware = odpAware;
    this.additionalContext = addditonalContext;
  }

  public String getRawTextUtterance() {
    return rawTextUtterance;
  }

  public String getLanguageCode() {
    return languageCode;
  }

  public APIMaturity getProfileMaturity() {
    return profileMaturity;
  }

  public String getAppName() {
    return appName;
  }

  public boolean isOdpAware() {
    return odpAware;
  }

  public Map<String, String> getAdditionalContext() {
    return additionalContext;
  }

  public void setRawTextUtterance(String rawTextUtterance) {
    this.rawTextUtterance = rawTextUtterance;
  }

  public void setLanguageCode(String languageCode) {
    this.languageCode = languageCode;
  }

  public void setProfileMaturity(APIMaturity profileMaturity) {
    this.profileMaturity = profileMaturity;
  }

  public void setAppName(String appName) {
    this.appName = appName;
  }

  public void setIsOdpAware(boolean isOdpAware) {
    this.odpAware = isOdpAware;
  }

  public void setAdditionalContext(Map<String, String> additionalContext) {
    this.additionalContext = additionalContext;
  }
}
