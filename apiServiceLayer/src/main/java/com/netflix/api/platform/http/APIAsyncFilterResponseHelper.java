package com.netflix.api.platform.http;

import com.netflix.lang.BindingContexts;
import com.netflix.lang.ThreadContext;
import jakarta.servlet.AsyncContext;
import jakarta.servlet.AsyncEvent;
import jakarta.servlet.AsyncListener;
import jakarta.servlet.ServletRequest;
import java.io.IOException;
import java.util.concurrent.TimeoutException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class APIAsyncFilterResponseHelper {
  private static final Logger logger = LoggerFactory.getLogger(APIAsyncFilterResponseHelper.class);

  @FunctionalInterface
  public interface AsyncRequestListener {
    void complete() throws IOException;
  }

  @FunctionalInterface
  public interface AsyncRequestWithErrorListener {
    void complete(Throwable t) throws IOException;
  }

  /**
   * Run a runnable once the request complete. If the request is synchronous the runnable is
   * executed immediately otherwise is scheduled for completion when the request complete
   *
   * <p>If an error or a timeout occur asynchronously the runnable is not executed
   *
   * @param request
   * @param finalizeReponse
   */
  public static void enqueueAfterRequest(
      ServletRequest request, AsyncRequestListener finalizeReponse) throws IOException {
    if (!request.isAsyncSupported() || !request.isAsyncStarted()) {
      finalizeReponse.complete();
    } else {
      AsyncContext context = request.getAsyncContext();
      context.addListener(new Runner(false, t -> finalizeReponse.complete()));
    }
  }

  /**
   * Run a closure once the request complete. If the request is synchronous the closure is executed
   * immediately otherwise scheduled for completion when the request complete
   *
   * <p>If an error occur or the request timeout the closure is executed with the Throwable as
   * argument
   *
   * @param request
   * @param finalizeReponse
   */
  public static void enqueueAfterRequestFinally(
      ServletRequest request, AsyncRequestWithErrorListener finalizeReponse) throws IOException {
    if (!request.isAsyncSupported() || !request.isAsyncStarted()) {
      finalizeReponse.complete(null);
    } else {
      AsyncContext context = request.getAsyncContext();
      context.addListener(new Runner(true, finalizeReponse));
    }
  }

  private static class Runner implements AsyncListener {
    final AsyncRequestWithErrorListener finalizeReponseConsumer;
    final boolean executeOnError;
    final ThreadContext threadContext;
    private volatile Throwable error;

    public Runner(boolean executeOnError, AsyncRequestWithErrorListener finalizeReponseConsumer) {
      this.executeOnError = executeOnError;
      this.finalizeReponseConsumer = finalizeReponseConsumer;
      this.threadContext = ThreadContext.capture();
    }

    private void run(Throwable th) throws IOException {
      BindingContexts.push(this.threadContext);
      try {
        finalizeReponseConsumer.complete(th);
      } catch (IOException e) {
        logger.error("Error while calling filter async", e);
        throw e;
      } finally {
        BindingContexts.pop();
      }
    }

    @Override
    public void onComplete(AsyncEvent event) throws IOException {
      if (error == null || executeOnError) {
        run(error);
      }
    }

    @Override
    public void onTimeout(AsyncEvent event) {
      Throwable th = event.getThrowable();
      if (th == null) {
        th = new TimeoutException("Request timeout");
      }
      error = th;
    }

    @Override
    public void onError(AsyncEvent event) {
      Throwable th = event.getThrowable();
      if (th == null) {
        th = new RuntimeException("Exception occured in filter");
      }
      error = th;
    }

    @Override
    public void onStartAsync(AsyncEvent event) {}
  }
}
