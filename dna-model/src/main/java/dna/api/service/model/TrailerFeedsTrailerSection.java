package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrailerFeedsTrailerSection extends SearchPageSection {
  private SearchPageVideo trailer;

  public SearchPageVideo getTrailer() {
    return trailer;
  }

  public TrailerFeedsTrailerSection setTrailer(SearchPageVideo trailer) {
    this.trailer = trailer;
    return this;
  }
}
