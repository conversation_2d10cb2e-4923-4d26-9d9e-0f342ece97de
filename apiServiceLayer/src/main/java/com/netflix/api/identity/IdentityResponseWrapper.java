package com.netflix.api.identity;

import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.introspect.PassportIdentityFactory;
import com.netflix.servo.monitor.DynamicCounter;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;
import org.apache.commons.lang.StringUtils;

public class IdentityResponseWrapper extends HttpServletResponseWrapper {

  private final String path;
  private final PassportIdentityFactory identityFactory;

  public IdentityResponseWrapper(
      HttpServletResponse response, String path, PassportIdentityFactory identityFactory) {
    super(response);
    this.path = path;
    this.identityFactory = identityFactory;
  }

  public PassportIdentity getModifiedPassportIdentity() {
    String passport = super.getHeader("X-Passport");
    if (StringUtils.isBlank(passport)) return null;

    try {
      return identityFactory.createPassportIdentity(passport);
    } catch (Exception e) {
      DynamicCounter.increment("COUNTER-IdentityFilter-invalidPassport", "path", path);
    }

    return null;
  }
}
