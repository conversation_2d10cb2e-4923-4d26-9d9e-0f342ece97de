package com.netflix.api.service.uipersonalization;

import com.netflix.uipersonalization.protogen.DisabledVotingTitles;
import com.netflix.uipersonalization.protogen.HiddenModules;
import com.netflix.uipersonalization.protogen.Personalization;
import com.netflix.uipersonalization.protogen.SecondaryLanguages;
import java.util.List;

public class APIUIPersonalizationImpl implements APIUIPersonalization {
  private final Personalization personalization;

  public static APIUIPersonalization merchDisabled(boolean merchDisabled) {
    return new APIUIPersonalizationImpl(
        Personalization.newBuilder().setVideoMerchDisabled(merchDisabled).build());
  }

  public static APIUIPersonalization secondaryLanguages(List<String> secondaryLanguages) {
    return new APIUIPersonalizationImpl(
        Personalization.newBuilder()
            .setSecondaryLanguages(
                SecondaryLanguages.newBuilder().addAllLanguage(secondaryLanguages).build())
            .build());
  }

  APIUIPersonalizationImpl(Personalization personalization) {
    this.personalization = personalization;
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Personalization.video_merch_disabled"
   */
  @Override
  public Boolean getVideoMerchDisabled() {
    return this.personalization.getBoxedVideoMerchDisabled();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Personalization.secondary_languages"
   */
  @Override
  public APIUIPersonalizationSecondaryLanguages getSecondaryLanguages() {
    SecondaryLanguages secondaryLanguages = this.personalization.getSecondaryLanguages();
    if (secondaryLanguages == null) {
      return null;
    }
    return new APIUIPersonalizationSecondaryLanguagesImpl(secondaryLanguages);
  }

  /**
   * 3: add next attribute here
   *
   * <p>Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Personalization.disabled_voting_titles"
   */
  @Override
  public APIUIPersonalizationDisabledVotingTitles getDisabledVotingTitles() {
    DisabledVotingTitles disabledVotingTitles = this.personalization.getDisabledVotingTitles();
    if (disabledVotingTitles == null) {
      return null;
    }
    return new APIUIPersonalizationDisabledVotingTitlesImpl(disabledVotingTitles);
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#Personalization.hidden_modules"
   */
  @Override
  public APIUIPersonalizationHiddenModules getHiddenModules() {
    HiddenModules hiddenModules = this.personalization.getHiddenModules();
    if (hiddenModules == null) {
      return null;
    }
    return new APIUIPersonalizationHiddenModulesImpl(hiddenModules);
  }

  private Personalization getPersonalizationProto() {
    return this.personalization;
  }

  protected static Personalization fromAPIPersonalization(APIUIPersonalization personalization) {
    return ((APIUIPersonalizationImpl) personalization).getPersonalizationProto();
  }
}
