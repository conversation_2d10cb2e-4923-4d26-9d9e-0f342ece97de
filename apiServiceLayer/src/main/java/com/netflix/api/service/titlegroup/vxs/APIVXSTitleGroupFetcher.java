package com.netflix.api.service.titlegroup.vxs;

import com.google.protobuf.util.Timestamps;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.platform.context.ImmutableRequestContext;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.titlegroup.APITitleGroupSetMember;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.type.Visitor;
import com.netflix.vxs.protogen.GetTitleGroupMembershipsRequest;
import com.netflix.vxs.protogen.GetTitleGroupRequest;
import com.netflix.vxs.protogen.GetTitleGroupSiblingsRequest;
import com.netflix.vxs.protogen.MemberKind;
import com.netflix.vxs.protogen.TitleGroup;
import com.netflix.vxs.protogen.TitleGroupMembership;
import com.netflix.vxs.protogen.TitleGroupMemberships;
import com.netflix.vxs.protogen.TitleGroupMembershipsResult;
import com.netflix.vxs.protogen.TitleGroupResult;
import com.netflix.vxs.protogen.TitleGroupSiblings;
import com.netflix.vxs.protogen.TitleGroupSiblingsResult;
import com.netflix.vxs.protogen.VxsRequest;
import com.netflix.vxs.protogen.VxsServiceGrpc;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import rx.Observable;

public class APIVXSTitleGroupFetcher {

  private final VxsServiceGrpc.VxsServiceStub vxsServiceClient;
  private final VxsRequest vxsRequest;

  public APIVXSTitleGroupFetcher(VxsServiceGrpc.VxsServiceStub vxsServiceClient) {
    this.vxsServiceClient = vxsServiceClient;
    ImmutableRequestContext requestContext = RequestContextWrapper.get();
    this.vxsRequest = createVxsRequest(requestContext);
  }

  private static VxsRequest createVxsRequest(ImmutableRequestContext requestContext) {
    return VxsRequest.newBuilder()
        .setTimestamp(Timestamps.fromMillis(System.currentTimeMillis()))
        .setCountry(requestContext.getCountry().getId())
        .setVisitorId(Optional.ofNullable(CurrentVisitor.get()).map(Visitor::getId))
        .build();
  }

  public Observable<TitleGroupSiblings> fetchVXSSiblings(
      Collection<APITitleGroupSetMember> members) {
    GetTitleGroupSiblingsRequest.Builder builder = GetTitleGroupSiblingsRequest.newBuilder();
    List<TitleGroupMembership> requests =
        members.stream()
            .map(
                member ->
                    TitleGroupMembership.newBuilder()
                        .setId(member.getMemberId())
                        .setKind(MemberKind.TITLE)
                        .setParentId(member.getParentGroupId())
                        .build())
            .toList();

    builder.addAllMembership(requests);
    builder.setVxsRequest(this.vxsRequest);

    return RxObservable.defer(vxsServiceClient::getTitleGroupSiblings, builder.build())
        .flatMap(
            response ->
                Observable.from(response.getResultList())
                    .filter(TitleGroupSiblingsResult::hasSiblings))
        .map(TitleGroupSiblingsResult::getSiblings);
  }

  public Observable<TitleGroupMemberships> fetchVXSMemberships(Collection<Integer> videos) {
    GetTitleGroupMembershipsRequest request =
        GetTitleGroupMembershipsRequest.newBuilder()
            .addAllId(videos)
            .setVxsRequest(vxsRequest)
            .build();

    return RxObservable.defer(vxsServiceClient::getTitleGroupMemberships, request)
        .flatMap(
            response ->
                Observable.from(response.getResultList())
                    .filter(TitleGroupMembershipsResult::hasMemberships))
        .map(TitleGroupMembershipsResult::getMemberships);
  }

  public Observable<TitleGroup> fetchVXSTitleGroups(
      Collection<Integer> groups, boolean includeMembers) {
    GetTitleGroupRequest request =
        GetTitleGroupRequest.newBuilder()
            .addAllId(groups)
            .setIncludeMembers(includeMembers)
            .setVxsRequest(vxsRequest)
            .build();

    return RxObservable.defer(vxsServiceClient::getTitleGroup, request)
        .flatMap(
            response ->
                Observable.from(response.getResultList()).filter(TitleGroupResult::hasGroup))
        .map(TitleGroupResult::getGroup);
  }
}
