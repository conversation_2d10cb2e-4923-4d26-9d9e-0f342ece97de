package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class TalentEvidenceData {
  private String text;

  private Map<String, String> annotations;

  private TalentEvidenceType evidenceType;

  public String getText() {
    return text;
  }

  public Map<String, String> getAnnotations() {
    return annotations;
  }

  public TalentEvidenceType getEvidenceType() {
    return evidenceType;
  }

  public TalentEvidenceData setText(String text) {
    this.text = text;
    return this;
  }

  public TalentEvidenceData setAnnotations(Map<String, String> annotations) {
    this.annotations = annotations;
    return this;
  }

  public TalentEvidenceData setEvidenceType(TalentEvidenceType evidenceType) {
    this.evidenceType = evidenceType;
    return this;
  }
}
