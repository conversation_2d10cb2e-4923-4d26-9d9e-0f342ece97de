package com.netflix.api.service;

import static com.netflix.api.platform.MicrocontextFlags.MICROCONTEXT_SHADOW_ENABLED;

import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;
import com.netflix.api.adapters.StreamingClientServiceAdapter;
import com.netflix.api.dependencies.device.DeviceTypeFactory;
import com.netflix.api.dependencies.device.DeviceTypeRequest;
import com.netflix.api.platform.NetflixAppId;
import com.netflix.api.platform.NetflixESN;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.context.RequestContextWrapper.Builder;
import com.netflix.api.platform.identity.CurrentIdentityResult;
import com.netflix.api.platform.util.APIEnvironment;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.device.APIDeviceType;
import com.netflix.api.service.device.APIDeviceTypeImpl;
import com.netflix.archaius.api.Property;
import com.netflix.geoclient.CurrentGeoData;
import com.netflix.geoclient.GeoData;
import com.netflix.i18n.LocaleContext;
import com.netflix.i18n.NFLocale;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.ContextUtils;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.init.resolvers.ClientResolvers;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Spectator;
import com.netflix.streaming.dts.common.model.DeviceType;
import com.netflix.type.ISOCountry;
import com.netflix.type.proto.Locales;
import com.netflix.type.protogen.BasicTypes.Locale;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import netflix.context.Context;
import netflix.context.client.flavor.ClientFlavor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.Observable;

public class APIRequestContextImpl implements APIRequestContextInternal {
  private static final Logger logger = LoggerFactory.getLogger(APIRequestContextImpl.class);

  private static final Property<String> DEFAULT_UIFLAVOR_VALUE =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.default.uiFlavor", String.class)
          .orElse("legacy");

  private final HttpServletRequest request;
  private final String endpointURI;
  private final String endpointGroup;
  private final RequestContextGateway nfRequestContext;
  private final Integer appId;
  private final NetflixESN esn;
  private final APIDeviceTypeImpl deviceType;
  private final Integer deviceTypeId;
  private final GeoData geoData;
  private final APICountryFactory countryFactory;
  private final APIClientCapabilitiesInternal clientCapabilitiesInternal =
      new APIClientCapabilitiesInternal();
  private final StreamingClientServiceAdapter streamingClientServiceAdapter;
  private APICountry country;
  private final AtomicReference<UIFlavor> uiFlavor;

  @Inject
  private APIRequestContextImpl(
      @Assisted HttpServletRequest request,
      @Assisted("uri") String endpointURI,
      @Assisted("group") String endpointGroup,
      APICountryFactory countryFactory,
      DeviceTypeFactory deviceTypeFactory,
      StreamingClientServiceAdapter streamingClientServiceAdapter) {
    this.request = request;
    this.endpointURI = endpointURI;
    this.endpointGroup = endpointGroup;
    this.countryFactory = countryFactory;
    this.nfRequestContext = new RequestContextGatewayImpl();
    var nfAppId = NetflixAppId.createFromRequest();
    this.appId = nfAppId == null ? null : nfAppId.getAppId();
    this.esn = NetflixESN.getCurrent();
    this.geoData = CurrentGeoData.get();

    this.deviceType = resolveDeviceType(esn, deviceTypeFactory);
    this.deviceTypeId = resolveDeviceTypeId(CurrentRequestContext.get(), deviceType);
    this.streamingClientServiceAdapter = streamingClientServiceAdapter;

    // force uiflavor to have a default value and set in RC
    this.uiFlavor = new AtomicReference<>(getDefaultUIFlavor(request));
    CurrentRequestContext.get().addContext("UIFlavor", uiFlavor.get().name());
  }

  @Override
  public Integer getAppId() {
    return appId;
  }

  @Override
  public void addClientCapabilities(String featureSet, Collection<String> clientCapabilities) {
    clientCapabilitiesInternal.addClientCapabilities(featureSet, clientCapabilities);
  }

  @Override
  public APIClientCapabilitiesInternal getClientCapabilitiesInternal() {
    return clientCapabilitiesInternal;
  }

  @Override
  public String getAppName() {
    if (request != null) {
      return request.getParameter("application_name");
    }
    return null;
  }

  @Override
  public String getEnvironment() {
    return APIEnvironment.getEnvironment();
  }

  @Override
  public String getStack() {
    return APIEnvironment.getStack();
  }

  @Override
  public APICountry getCountry() {
    if (country == null) {
      ISOCountry isoCountry = nfRequestContext.getCountry();
      if (isoCountry == null) {
        throw new APIServiceRuntimeException(
            "APICountry can not be created with a NULL ISOCountry.");
      }
      country = countryFactory.get(isoCountry);
    }
    return country;
  }

  @Override
  public String getESN() {
    if (esn == null) return null;
    return esn.getESN();
  }

  @Override
  public Integer getDeviceTypeId() {
    return deviceTypeId;
  }

  @Override
  public int getESNBucket() {
    return esn == null ? -1 : esn.getBucket();
  }

  @Override
  public APIDeviceType getDeviceType() {
    return deviceType;
  }

  @Override
  @Nullable
  public APILocale getLocale() {
    return Optional.ofNullable(getPreferredLocales().toBlocking().firstOrDefault(null))
        .map(ListItem::getItem)
        .orElse(null);
  }

  @Override
  public Observable<ListItem<APILocale>> getPreferredLocales() {
    class PreferredLocaleBehavior extends APIListBuilderBehaviorImpl<APILocale, NFLocale> {
      @Override
      public APILocale loadItem(NFLocale reference) {
        return new APILocaleImpl(reference);
      }

      @Override
      public List<NFLocale> getReferences() {
        List<NFLocale> references = new ArrayList<>();
        String localesString = nfRequestContext.getLocaleList();
        if (localesString == null) return references;
        localesString = localesString.trim();
        if (localesString.isEmpty()) return references;
        String[] localesStringArray = localesString.split(",");
        for (String localeString : localesStringArray) {
          references.add(NFLocale.createFromString(localeString));
        }
        return references;
      }
    }

    class PreferredLocale extends APIListBuilderImpl<APILocale, PreferredLocaleBehavior> {
      PreferredLocale() {
        super(new PreferredLocaleBehavior());
      }
    }
    return new PreferredLocale().build();
  }

  @Override
  @Deprecated
  public void setPreferredLocales(List<String> locales) {
    if (locales == null || locales.isEmpty()) return;
    // create a comma separated locale list strings
    StringBuilder sb = new StringBuilder();
    for (String locale : locales) {
      NFLocale nfLocale;
      try {
        nfLocale = NFLocale.createFromString(locale.trim());
      } catch (Exception e) {
        throw new APIServiceRuntimeException("Locale " + locale + " is not valid.", e);
      }
      if (nfLocale != null) sb.append(nfLocale.getId()).append(",");
    }

    if (sb.length() > 1) nfRequestContext.setLocaleList(sb.substring(0, sb.lastIndexOf(",")));
  }

  @Override
  public String getEndpointPath() {
    return endpointURI;
  }

  @Override
  public String getEndpointGroup() {
    return endpointGroup;
  }

  @Override
  public String getRequestId() {
    return nfRequestContext.getRequestId();
  }

  @Override
  public String getVisitorDeviceId() {
    return CurrentIdentityResult.getVisitorDeviceId();
  }

  private APIDeviceTypeImpl resolveDeviceType(NetflixESN esn, DeviceTypeFactory deviceTypeFactory) {
    if (esn == null) {
      return null;
    }

    DeviceType t = deviceTypeFactory.get(new DeviceTypeRequest().withEsn(esn.getESN()));
    if (t == null) {
      return null;
    }

    return new APIDeviceTypeImpl(t);
  }

  private static Integer resolveDeviceTypeId(RequestContext context, APIDeviceTypeImpl deviceType) {
    com.netflix.type.DeviceType deviceTypeFromRequestContext = context.getDeviceType();
    if (deviceTypeFromRequestContext != null) {
      return deviceTypeFromRequestContext.getId();
    }

    if (deviceType != null) {
      return deviceType.getId();
    }

    return null;
  }

  @Override
  public String toString() {
    return "APIRequestContext{}";
  }

  /*
   * RequestContext is a final class that cannot be mocked, so wrapping it
   */
  private interface RequestContextGateway {
    String getRequestId();

    ISOCountry getCountry();

    String getLocaleList();

    void setLocaleList(String localeList);
  }

  private static class RequestContextGatewayImpl implements RequestContextGateway {
    @Override
    public String getRequestId() {
      return RequestContextWrapper.get().getRequestId();
    }

    @Override
    public ISOCountry getCountry() {
      return RequestContextWrapper.get().getCountry();
    }

    @Override
    public String getLocaleList() {
      return RequestContextWrapper.get().getLocaleList();
    }

    @Override
    public void setLocaleList(String localeList) {
      RequestContextWrapper.createAndSet(
          Builder.of(RequestContextWrapper.get()).localeList(localeList));
      LocaleContext.setLocalContext(localeList);
      CurrentRequestContext.get().setLocaleList(localeList);
      if (MICROCONTEXT_SHADOW_ENABLED.get()) {
        List<Locale> real =
            RequestContextWrapper.convertLocales(localeList).stream()
                .map(NFLocale::getId)
                .map(Locales::toProtobuf)
                .toList();
        CurrentMicrocontext.set(
            ContextUtils.toBuilder(CurrentMicrocontext.get())
                .clearLocales()
                .addAllLocales(real)
                .build());
      }
    }
  }

  @Override
  public boolean isGeoOverriden() {
    if (geoData != null) {
      return geoData.isOverriden();
    }
    return false;
  }

  @Override
  public UIFlavor getUIFlavor() {
    return uiFlavor.get();
  }

  /**
   * Mid tier services, specifically MAP and Netflix Image Library, need a UI flavor to vary the
   * behavior across different UI platforms.
   *
   * <p>Note: UI flavor indicates the UI server side (groovy) codebase, not the UI experience. The
   * discrete set of values is defined by MAP team.
   */
  @Override
  public void setUIFlavor(UIFlavor uiFlavor) {
    if (uiFlavor != null && !Objects.equals(uiFlavor, this.uiFlavor.get())) {
      this.uiFlavor.set(uiFlavor);
      String name = uiFlavor.name();
      CurrentRequestContext.get().addContext("UIFlavor", name);
      ClientFlavor clientFlavor = ClientResolvers.clientFlavor(name);
      if (clientFlavor != ClientFlavor.UNSPECIFIED) {
        Microcontext microcontext = CurrentMicrocontext.get();
        if (microcontext.getClient().getClientFlavor() != clientFlavor) {
          Spectator.globalRegistry()
              .counter("microcontext.uiflavor.resolve", "result", "setter")
              .increment();
          Context.Builder builder = microcontext.toProto().toBuilder();
          ClientResolvers.updateBuilder(builder.getClientBuilder(), clientFlavor);
          CurrentMicrocontext.set(builder.build());
        }
      } else {
        logger.error("unrecognized uiflavor value {}", name);
      }
    }
  }

  @Override
  public boolean deviceSupportsLocalizedKidsProfile() {
    return clientCapabilitiesInternal.deviceSupportsLocalizedKidsProfile();
  }

  /**
   * Returns the OCA hostname (url segment) that's used by UI bootloaders to create js/css URIs that
   * point to OCA servers instead of Akamai (non-netflix CDNs).
   */
  @Override
  public Observable<String> getSteeredCdnHostname() {
    return streamingClientServiceAdapter.getHostName().toObservable();
  }

  private UIFlavor getDefaultUIFlavor(HttpServletRequest request) {
    // look at ui_trace_tag parameter
    String uiTrace = request.getParameter("ui_trace_tag");
    if (!Strings.isNullOrEmpty(uiTrace)) {
      if (uiTrace.equals("darwin-ql")) {
        return UIFlavor.darwin;
      } else if (uiTrace.equals("darwin")) {
        return UIFlavor.tv_other;
      }
    }

    String endpointPath = getEndpointPath();
    if (endpointPath != null) {
      if (endpointPath.startsWith("/tvui")) {
        return UIFlavor.tv_other;
      } else if (endpointPath.startsWith("/shakti") || endpointPath.startsWith("/cbp")) {
        return UIFlavor.akira;
      } else if (endpointPath.startsWith("/ios") || endpointPath.startsWith("/mobile")) {
        return UIFlavor.argo;
      } else if (endpointPath.startsWith("/android")) {
        return UIFlavor.android;
      }
    }

    // use property default
    return UIFlavor.lookup(DEFAULT_UIFLAVOR_VALUE.get());
  }
}
