package com.netflix.api.dependencies.device;

/**
 * Encapsulation of logic of how to lookup a device type - Device Type ID - ESN Prefix - ESN -
 * Consumer Key
 *
 * <p>This dependency command uses the builder pattern so you can add as many arguments as you want,
 * and the dep-cmd will handle the fallback logic appropriately.
 *
 * <p>You can also ask it to provide a synthetic fallback. This is useful when you don't need the
 * exact device type, but rather are handing it off to a different system and want a result from
 * that. The motivating example is the public API: There, we don't have a device ID or ESN, but we
 * still want to know if a video is available in HD or not. CUPS is a system which requires a device
 * type, so the synthetic device type is useful in this case.
 */
public class DeviceTypeRequest {
  private boolean useSyntheticDevice = false;
  private Integer deviceTypeId;
  private String esnPrefix;
  private String esn;
  private String consumerKey;

  public DeviceTypeRequest() {}

  protected String getCacheKey() {
    if (deviceTypeId == null
        && esn == null
        && esnPrefix == null
        && consumerKey == null
        && useSyntheticDevice) {
      return "SYNTHETIC";
    } else {
      String deviceIdStr = (deviceTypeId == null) ? ":" : deviceTypeId + ":";
      String esnStr = (esn == null) ? ":" : esn + ":";
      String esnPrefixStr = (esnPrefix == null) ? ":" : esnPrefix + ":";
      String consumerKeyStr = (consumerKey == null) ? ":" : consumerKey + ":";
      return deviceIdStr + esnStr + esnPrefixStr + consumerKeyStr;
    }
  }

  boolean isUseSyntheticDevice() {
    return useSyntheticDevice;
  }

  Integer getDeviceTypeId() {
    return deviceTypeId;
  }

  String getEsnPrefix() {
    return esnPrefix;
  }

  String getEsn() {
    return esn;
  }

  String getConsumerKey() {
    return consumerKey;
  }

  public DeviceTypeRequest withDeviceTypeId(Integer deviceTypeId) {
    this.deviceTypeId = deviceTypeId;
    return this;
  }

  public DeviceTypeRequest withEsn(String esn) {
    this.esn = esn;
    return this;
  }

  public DeviceTypeRequest withEsnPrefix(String esnPrefix) {
    this.esnPrefix = esnPrefix;
    return this;
  }

  public DeviceTypeRequest withConsumerKey(String consumerKey) {
    this.consumerKey = consumerKey;
    return this;
  }

  public boolean isEmpty() {
    if (deviceTypeId == null
        && esn == null
        && esnPrefix == null
        && consumerKey == null
        && useSyntheticDevice) {
      return true;
    } else {
      return false;
    }
  }

  public DeviceTypeRequest withSyntheticDeviceOnNullInput() {
    this.useSyntheticDevice = true;
    return this;
  }
}
