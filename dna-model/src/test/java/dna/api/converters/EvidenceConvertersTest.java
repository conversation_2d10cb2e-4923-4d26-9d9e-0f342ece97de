package dna.api.converters;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.netflix.aballocator.protogen.Alloc;
import com.netflix.aballocator.protogen.AllocResponse;
import com.netflix.api.platform.MockPropertyRepositoryConfig;
import com.netflix.api.service.UIFlavor;
import com.netflix.evidence.protogen.Classification;
import com.netflix.evidence.protogen.EvidenceType;
import com.netflix.evidence.protogen.GetContextualEvidenceReply;
import com.netflix.evidence.protogen.GetContextualEvidenceRequest;
import com.netflix.evidence.protogen.SocialProofEvidenceType;
import com.netflix.evidence.protogen.StaffPicksEvidenceTypes;
import com.netflix.evidence.protogen.UiContext;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.request.protogen.UiFlavor;
import com.netflix.type.Visitor;
import com.netflix.type.protogen.BasicTypes;
import dna.api.service.model.ContextualEvidence;
import dna.api.service.model.ContextualEvidenceCriteria;
import dna.api.service.model.ContextualEvidenceData;
import dna.api.service.model.ContextualEvidenceResponse;
import dna.api.service.model.GroupLocator;
import dna.api.service.model.SocialProofEvidenceTypes;
import dna.api.service.model.UIContext;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import netflix.context.geo.GeoContext;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class EvidenceConvertersTest {

  @BeforeAll
  static void beforeClass() {
    MockPropertyRepositoryConfig config = new MockPropertyRepositoryConfig();
    config.init();
  }

  @Test
  void convert_contextualEvidenceResponse() {
    final ContextualEvidenceResponse actual =
        EvidenceConverters.convert(
            GetContextualEvidenceReply.UiContextMap.newBuilder()
                .putAllEvidenceContextMap(
                    Map.of(
                        "some-ui-context-a",
                            GetContextualEvidenceReply.VideoEvidenceList.newBuilder()
                                .addEvidenceList(
                                    GetContextualEvidenceReply.VideoEvidence.newBuilder()
                                        .setEvidenceType(EvidenceType.EXPLANATION)
                                        .setEvidenceKey("my-evidence-key")
                                        .addSupportedUiContexts(UiContext.BobCategories)
                                        .putAnnotations(
                                            "toplevel-annotation-key", "toplevel-annotation-value")
                                        .addEvidence(
                                            GetContextualEvidenceReply.EvidenceData.newBuilder()
                                                .setText("my-evidence-text")
                                                .putAnnotations(
                                                    "my-annotation-key", "my-annotation-value")
                                                .setStaffPicksEvidenceType(
                                                    StaffPicksEvidenceTypes.BADGE)
                                                .setSocialProofEvidenceType(
                                                    SocialProofEvidenceType.MOST_LIKED)
                                                .setClassification(Classification.MOST_LIKED)
                                                .build())
                                        .build())
                                .build(),
                        "some-ui-context-b",
                            GetContextualEvidenceReply.VideoEvidenceList.newBuilder()
                                .addEvidenceList(
                                    GetContextualEvidenceReply.VideoEvidence.newBuilder()
                                        .setEvidenceType(EvidenceType.COMING_SOON)
                                        .setEvidenceKey("your-evidence-key")
                                        .addSupportedUiContexts(UiContext.Billboard)
                                        .addSupportedUiContexts(UiContext.BobComingSoonDeluxe)
                                        .addEvidence(
                                            GetContextualEvidenceReply.EvidenceData.newBuilder()
                                                .setText("your-evidence-text")
                                                .putAnnotations(
                                                    "your-annotation-key", "your-annotation-value")
                                                .setStaffPicksEvidenceType(
                                                    StaffPicksEvidenceTypes.STAFF_PICKS_NONE)
                                                .setSocialProofEvidenceType(
                                                    SocialProofEvidenceType.SOCIAL_PROOF_NONE)
                                                .setClassification(Classification.REGULAR)
                                                .setEvidenceBadge(
                                                    GetContextualEvidenceReply.EvidenceBadge
                                                        .newBuilder()
                                                        .setBadgePrefix("LIVE")
                                                        .setBadgeDate("Today!"))
                                                .build())
                                        .addEvidence(
                                            GetContextualEvidenceReply.EvidenceData.newBuilder()
                                                .build())
                                        .build())
                                .build()))
                .build());

    assertNotNull(actual);

    final Map<String, List<ContextualEvidence>> evidenceContexts = actual.getEvidenceContexts();
    assertNotNull(evidenceContexts);
    assertEquals(2, evidenceContexts.size());

    assertTrue(evidenceContexts.containsKey("some-ui-context-a"));

    {
      final List<ContextualEvidence> contextualEvidences =
          evidenceContexts.get("some-ui-context-a");
      assertNotNull(contextualEvidences);
      assertEquals(1, contextualEvidences.size());

      final ContextualEvidence contextualEvidence = contextualEvidences.getFirst();
      assertNotNull(contextualEvidence);
      assertEquals(
          dna.api.service.model.EvidenceType.EXPLANATION, contextualEvidence.getEvidenceType());
      assertEquals("my-evidence-key", contextualEvidence.getEvidenceKey());
      assertEquals(
          Map.of("toplevel-annotation-key", "toplevel-annotation-value"),
          contextualEvidence.getAnnotations());

      final List<UIContext> supportedUIContexts = contextualEvidence.getSupportedUIContexts();
      assertEquals(1, supportedUIContexts.size());

      final UIContext uiContext = supportedUIContexts.getFirst();
      assertEquals(UIContext.BobCategories, uiContext);

      final List<ContextualEvidenceData> evidence = contextualEvidence.getEvidence();
      assertNotNull(evidence);
      assertEquals(1, evidence.size());

      final ContextualEvidenceData contextualEvidenceData = evidence.getFirst();
      assertEquals("my-evidence-text", contextualEvidenceData.getText());

      final Map<String, String> annotations = contextualEvidenceData.getAnnotations();
      assertNotNull(annotations);

      assertTrue(annotations.containsKey("my-annotation-key"));

      final String annotationVal = annotations.get("my-annotation-key");
      assertEquals("my-annotation-value", annotationVal);

      final SocialProofEvidenceTypes socialProofEvidenceType =
          contextualEvidenceData.getSocialProofEvidenceType();
      assertNotNull(socialProofEvidenceType);
      assertEquals(SocialProofEvidenceTypes.MOST_LIKED, socialProofEvidenceType);

      final dna.api.service.model.StaffPicksEvidenceTypes staffPicksEvidenceType =
          contextualEvidenceData.getStaffPicksEvidenceType();
      assertNotNull(staffPicksEvidenceType);
      assertEquals(dna.api.service.model.StaffPicksEvidenceTypes.BADGE, staffPicksEvidenceType);

      final dna.api.service.model.TextMessageClassification classification =
          contextualEvidenceData.getClassification();
      assertNotNull(classification);
      assertEquals(dna.api.service.model.TextMessageClassification.MOST_LIKED, classification);
    }

    {
      final List<ContextualEvidence> contextualEvidences =
          evidenceContexts.get("some-ui-context-b");
      assertNotNull(contextualEvidences);
      assertEquals(1, contextualEvidences.size());

      final ContextualEvidence contextualEvidence = contextualEvidences.getFirst();
      assertNotNull(contextualEvidence);
      assertEquals(
          dna.api.service.model.EvidenceType.COMING_SOON, contextualEvidence.getEvidenceType());
      assertEquals("your-evidence-key", contextualEvidence.getEvidenceKey());

      final List<UIContext> supportedUIContexts = contextualEvidence.getSupportedUIContexts();
      assertEquals(2, supportedUIContexts.size());

      final UIContext uiContext1 = supportedUIContexts.get(0);
      assertEquals(UIContext.Billboard, uiContext1);

      final UIContext uiContext2 = supportedUIContexts.get(1);
      assertEquals(UIContext.BobComingSoonDeluxe, uiContext2);

      final List<ContextualEvidenceData> evidence = contextualEvidence.getEvidence();
      assertNotNull(evidence);
      assertEquals(2, evidence.size());

      final ContextualEvidenceData contextualEvidenceData = evidence.getFirst();
      assertEquals("your-evidence-text", contextualEvidenceData.getText());

      final Map<String, String> annotations = contextualEvidenceData.getAnnotations();
      assertNotNull(annotations);

      assertTrue(annotations.containsKey("your-annotation-key"));

      final String annotationVal = annotations.get("your-annotation-key");
      assertEquals("your-annotation-value", annotationVal);

      final SocialProofEvidenceTypes socialProofEvidenceType =
          contextualEvidenceData.getSocialProofEvidenceType();
      assertNotNull(socialProofEvidenceType);
      assertEquals(SocialProofEvidenceTypes.SOCIAL_PROOF_NONE, socialProofEvidenceType);

      final dna.api.service.model.StaffPicksEvidenceTypes staffPicksEvidenceType =
          contextualEvidenceData.getStaffPicksEvidenceType();
      assertNotNull(staffPicksEvidenceType);
      assertEquals(
          dna.api.service.model.StaffPicksEvidenceTypes.STAFF_PICKS_NONE, staffPicksEvidenceType);

      final dna.api.service.model.TextMessageClassification classification =
          contextualEvidenceData.getClassification();
      assertNotNull(classification);
      assertEquals(dna.api.service.model.TextMessageClassification.REGULAR, classification);

      final var badge = contextualEvidenceData.getBadge();
      assertNotNull(badge);
      assertEquals("LIVE", badge.getPrefix());
      assertEquals("Today!", badge.getDateText());
    }
  }

  @Test
  void convert_GetContextualEvidenceRequest() {
    final Microcontext microcontext = mock(Microcontext.class);
    when(microcontext.getLocale())
        .thenReturn(Optional.of(BasicTypes.Locale.newBuilder().setId("some-locale").build()));
    when(microcontext.getCountry())
        .thenReturn(BasicTypes.Country.newBuilder().setId("some-country-id").build());
    when(microcontext.getGeo())
        .thenReturn(GeoContext.newBuilder().setIanaTimezone("some-timezone").build());

    final Visitor visitor = mock(Visitor.class);
    when(visitor.getId()).thenReturn(9988L);

    final GetContextualEvidenceRequest actual =
        EvidenceConverters.convert(
            new ContextualEvidenceCriteria()
                .setTargetEvidenceTypes(
                    Arrays.asList(
                        dna.api.service.model.EvidenceType.EXPLANATION,
                        dna.api.service.model.EvidenceType.COMING_SOON))
                .setTargetUIContexts(Arrays.asList(UIContext.Billboard, UIContext.BobComingSoon))
                .setGroupLocator(GroupLocator.BASED_ON_RECENTLY_INTERACTED)
                .setAnnotations(Map.of("k", "v")),
            Set.of(1234, 5678),
            microcontext,
            visitor,
            UIFlavor.darwin,
            AllocResponse.newBuilder()
                .putAlloc(5, Alloc.newBuilder().setAllocPlanId("some-alloc-plan").build())
                .build());

    assertNotNull(actual);
    assertEquals(9988L, actual.getVisitorId());
    assertEquals("some-locale", actual.getLocaleId());
    assertEquals(2, actual.getVideoIdsCount());
    assertThat(actual.getVideoIdsList(), containsInAnyOrder(1234, 5678));
    assertEquals(1, actual.getAnnotationsCount());
    assertTrue(actual.getAnnotationsMap().containsKey("k"));
    assertEquals("v", actual.getAnnotationsMap().get("k"));
    assertEquals(UiFlavor.DARWIN, actual.getUiFlavor());
    assertEquals("some-country-id", actual.getCountryId());
    assertEquals("some-timezone", actual.getIanaTimezone());
    assertEquals(2, actual.getAcceptedEvidenceTypesCount());
    assertEquals(EvidenceType.EXPLANATION, actual.getAcceptedEvidenceTypes(0));
    assertEquals(EvidenceType.COMING_SOON, actual.getAcceptedEvidenceTypes(1));
    assertEquals(2, actual.getUiContextsCount());
    assertEquals(UiContext.Billboard, actual.getUiContexts(0));
    assertEquals(UiContext.BobComingSoon, actual.getUiContexts(1));
    assertEquals("basedonrecentlyinteracted", actual.getGroupLocator());
  }
}
