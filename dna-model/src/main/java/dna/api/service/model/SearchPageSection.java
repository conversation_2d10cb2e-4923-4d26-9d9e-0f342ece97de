package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SearchPageSection {
  private List<String> debugData;

  private String displayString;

  private String feature;

  @Deprecated private String kind;

  private SearchPageSectionKind sectionKind;

  private NapaPerformanceData performanceData;

  private String sectionId;

  /** @deprecated Track ID is an integer and not a string. Please use {@link trackerId}. */
  @Deprecated private String trackId;

  private Integer trackerId;

  private NapaSectionFeatureMetadata featureMetadata;

  /**
   * The info that can be used to acquire the same exact section subsequent to the initial creation.
   */
  private NapaSessionInfo sessionInfo;

  /** The session info from the previous creation of this section. Usually will be empty. */
  private NapaSessionInfo previousSessionInfo;

  /**
   * Context for ensuring images shown for titles in the section are congruent with the section
   * itself. It contains a comma separated list of Turbo collection IDs that have Identity type
   * associated with them.
   */
  private String imageCongruenceContext;

  public List<String> getDebugData() {
    return debugData;
  }

  public String getDisplayString() {
    return displayString;
  }

  public String getFeature() {
    return feature;
  }

  @Deprecated
  public String getKind() {
    return kind;
  }

  public SearchPageSectionKind getSectionKind() {
    return sectionKind;
  }

  public NapaPerformanceData getPerformanceData() {
    return performanceData;
  }

  public String getSectionId() {
    return sectionId;
  }

  @Deprecated
  public String getTrackId() {
    return trackId;
  }

  public Integer getTrackerId() {
    return trackerId;
  }

  public NapaSectionFeatureMetadata getFeatureMetadata() {
    return featureMetadata;
  }

  public NapaSessionInfo getSessionInfo() {
    return sessionInfo;
  }

  public NapaSessionInfo getPreviousSessionInfo() {
    return previousSessionInfo;
  }

  public String getImageCongruenceContext() {
    return imageCongruenceContext;
  }

  public SearchPageSection setDebugData(List<String> debugData) {
    this.debugData = debugData;
    return this;
  }

  public SearchPageSection setDisplayString(String displayString) {
    this.displayString = displayString;
    return this;
  }

  public SearchPageSection setFeature(String feature) {
    this.feature = feature;
    return this;
  }

  @Deprecated
  public SearchPageSection setKind(String kind) {
    this.kind = kind;
    return this;
  }

  public SearchPageSection setSectionKind(SearchPageSectionKind sectionKind) {
    this.sectionKind = sectionKind;
    return this;
  }

  public SearchPageSection setPerformanceData(NapaPerformanceData performanceData) {
    this.performanceData = performanceData;
    return this;
  }

  public SearchPageSection setSectionId(String sectionId) {
    this.sectionId = sectionId;
    return this;
  }

  @Deprecated
  public SearchPageSection setTrackId(String trackId) {
    this.trackId = trackId;
    return this;
  }

  public SearchPageSection setTrackerId(Integer trackerId) {
    this.trackerId = trackerId;
    return this;
  }

  public SearchPageSection setFeatureMetadata(NapaSectionFeatureMetadata featureMetadata) {
    this.featureMetadata = featureMetadata;
    return this;
  }

  public SearchPageSection setSessionInfo(NapaSessionInfo sessionInfo) {
    this.sessionInfo = sessionInfo;
    return this;
  }

  public SearchPageSection setPreviousSessionInfo(NapaSessionInfo previousSessionInfo) {
    this.previousSessionInfo = previousSessionInfo;
    return this;
  }

  public SearchPageSection setImageCongruenceContext(String imageCongruenceContext) {
    this.imageCongruenceContext = imageCongruenceContext;
    return this;
  }
}
