package com.netflix.api.service.batch;

import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonGenerator.Feature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.fasterxml.jackson.datatype.guava.GuavaModule;
import com.fasterxml.jackson.module.afterburner.AfterburnerModule;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.netflix.map.annotation.MapAnnotationConstants;
import com.netflix.spectator.impl.Preconditions;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class USTListUtils {
  private USTListUtils() {}

  private static final Set<String> emptyProperties = new HashSet<>();

  public static final TypeReference<List<Integer>> LIST_OF_INT_TYPE_REF = new TypeReference<>() {};

  public static final TypeReference<List<Map<String, Object>>> LIST_OF_MAP_OF_OBJ_TYPE_REF =
      new TypeReference<>() {};

  public static final TypeReference<Map<String, Integer>> MAP_OF_INT_TYPE_REF =
      new TypeReference<>() {};

  private static final ObjectMapper mapper =
      new ObjectMapper()
          .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
          .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
          .disable(Feature.AUTO_CLOSE_TARGET)
          .disable(Feature.FLUSH_PASSED_TO_STREAM)
          .registerModule(new AfterburnerModule())
          .registerModule(new GuavaModule())
          .setSerializationInclusion(Include.NON_NULL)
          .setVisibility(PropertyAccessor.FIELD, Visibility.PUBLIC_ONLY)
          .setFilterProvider(
              new SimpleFilterProvider()
                  .addFilter(
                      "EmptyMapAnnotationFilter",
                      SimpleBeanPropertyFilter.serializeAllExcept(emptyProperties))
                  .setFailOnUnknownId(false));

  public static String toJson(Object obj) {
    try {
      if (obj instanceof MessageOrBuilder msg) {
        return JsonFormat.printer().print(msg);
      }
      return mapper.writeValueAsString(obj);
    } catch (IOException e) {
      return "<error>";
    }
  }

  static <T> T toObject(String value, Class<T> toValueType) {
    if (value == null) {
      return null;
    }
    try {
      return mapper.readValue(value, toValueType);
    } catch (JsonProcessingException e) {
      throw new IllegalArgumentException(e);
    }
  }

  static <T> T toObject(String value, TypeReference<T> toValueType) {
    if (value == null) {
      return null;
    }
    try {
      return mapper.readValue(value, toValueType);
    } catch (JsonProcessingException e) {
      throw new IllegalArgumentException(e);
    }
  }

  static String extractUuid(Map<String, Object> annotations) {
    Object uuid = annotations.get(MapAnnotationConstants.UUID);
    Preconditions.checkArg(uuid instanceof String, "Missing uuid annotations");
    return (String) uuid;
  }
}
