package com.netflix.api.service.searchnew;

/** Holds all the useful constants related to search search service */
public interface APISearchConstants {
  /** Type of search item - consult with search team for details on each type. */
  enum SearchItemType {
    video,
    person,
    genre,
    character,
    string,
    collection,
    merch,
    topic,

    /**
     * See <a href="http://go/abtest-10025-spec">Android Search Lolomo Test Spec</a> and <a
     * href="https://jira.netflix.com/browse/DNA-367">DNA-367</a>
     */
    videoGroup
  }

  /** Type of search list - consult with search team for details on each type. */
  enum SearchListType {
    titles,
    people,
    suggestions,
    searchHistory,
    popularSearches,
    trendingSearches,
    empty
  }
}
