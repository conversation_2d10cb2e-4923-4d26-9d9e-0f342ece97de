package com.netflix.api.service.video;

import com.netflix.api.service.profiles.ProfileSize;
import java.util.Collection;
import java.util.Map;

/**
 * Encapsulates an image - width, height and url of the image.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIArtwork.class.png">
 */
public interface APIImage extends ProfileSize {

  /** Enumeration set of extension of an image -- image format. */
  enum ImageExtension {
    webp,
    jpg,
    png,
    gif,
    mp4,
    astc,
    avif
  }

  String getUrl();

  /**
   * Image's file extension - jpg, webp, png are the 3 known extensions in Netflix image catalog.
   *
   * @return the image's file extension - an enumeration representing the file extension
   */
  ImageExtension getExtension();

  /**
   * @return image's actual source file ID.
   */
  String getSourceFileId();

  /**
   * Location of the image.
   *
   * @param secure set this to <code>true</code> if you want the HTTPS URL for the image, or <code>
   *     false</code> if you want the HTTP URL
   * @return the URL of the image
   */
  String getUrl(Boolean secure);

  Map<String, String> getAttributes();

  Map<String, Collection<String>> getMultiValueAttributes();

  /**
   * Uniquely identifies the image type. This is used by data science team to analyze performance of
   * different images. This field must be used by clients solely to report back (via consolidated
   * logging or such) an impression of this image or an action on this image.
   *
   * @return String identifying the image type (for logging).
   */
  String getImageTypeIdentifier();

  /**
   * Is the image in a language (locale) that reads right-to-left?
   *
   * @return always returns false
   */
  boolean isRightToLeft();

  boolean isSmoky();
}
