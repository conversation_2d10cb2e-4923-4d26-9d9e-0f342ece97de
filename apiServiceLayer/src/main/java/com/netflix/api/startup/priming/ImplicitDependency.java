package com.netflix.api.startup.priming;

import com.google.inject.Key;
import com.google.inject.Provider;
import com.google.inject.TypeLiteral;
import com.google.inject.matcher.AbstractMatcher;
import com.google.inject.matcher.Matcher;
import com.google.inject.matcher.Matchers;
import com.google.inject.util.Providers;
import java.util.Set;

@SuppressWarnings("rawtypes")
public final class ImplicitDependency {

  private final Matcher<Class> classMatcher;
  private final Matcher typeLiteralMatcher;

  private final Provider<Set<Key<?>>> targets;

  private ImplicitDependency(
      Matcher<Class> classMatcher,
      Matcher<Object> typeLiteralMatcher,
      Provider<Set<Key<?>>> targets) {
    super();
    this.classMatcher = classMatcher;
    this.typeLiteralMatcher = typeLiteralMatcher;
    this.targets = targets;
  }

  public Matcher<Class> getClassMatcher() {
    return classMatcher == null ? Matchers.not(Matchers.any()) : classMatcher;
  }

  @SuppressWarnings("unchecked")
  public Matcher<Object> getTypeLiteralMatcher() {
    return typeLiteralMatcher == null ? Matchers.not(Matchers.any()) : typeLiteralMatcher;
  }

  public Set<Key<?>> getTargets() {
    return targets.get();
  }

  public static ImplicitDependency betweenType(TypeLiteral<?> literal, Key<?>... keys) {
    return betweenType(literal, Providers.of(Set.of(keys)));
  }

  @SuppressWarnings("unchecked")
  public static ImplicitDependency betweenType(
      TypeLiteral<?> literal, Provider<Set<Key<?>>> provider) {
    Matcher matcher =
        new AbstractMatcher<TypeLiteral<?>>() {
          @Override
          public boolean matches(TypeLiteral<?> t) {
            return t.equals(literal);
          }
        };
    return new ImplicitDependency(null, matcher, provider);
  }

  public static ImplicitDependency betweenClass(Matcher<Class> matcher, Key<?>... keys) {
    return betweenClass(matcher, Providers.of(Set.of(keys)));
  }

  public static ImplicitDependency betweenClass(
      Matcher<Class> matcher, Provider<Set<Key<?>>> provider) {
    return new ImplicitDependency(matcher, null, provider);
  }
}
