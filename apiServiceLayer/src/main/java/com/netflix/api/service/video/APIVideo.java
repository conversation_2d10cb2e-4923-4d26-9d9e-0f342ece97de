package com.netflix.api.service.video;

import com.netflix.api.annotations.ExcludeFromCoverage;
import com.netflix.api.service.list.APIEntity;

/**
 * Interface representing various varieties of Netflix video titles.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.varieties.png"
 * alt="varieties of APIVideo-implementing objects">
 *
 * <p>This interface has several extensions, representing different types of Netflix titles: A show
 * has one or more seasons, and one or more episodes, one of which is the current episode. A season
 * may has one show parent, and one or more episodes, one of which is the current episode. A series
 * has one or more episodes, one of which is the current episode. An episode has either a series
 * parent, or a show parent and a season parent, and also may have next and previous episodes. An
 * original is associated with videos and trailers that are implemented as videos.
 *
 * <p>Episodes and movies are "viewable" varieties of <code>APIVideo</code> and have a number of
 * additional methods relating to their viewable content. Shows, seasons, and series are
 * non-viewable collections of viewable titles.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.class.png"
 * alt="APIVideo class diagram"> <img
 * src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.cluster2.png"
 * alt="Relationships in the APIVideo class cluster">
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.cluster.png"
 * alt="Methods that enact relationships in the APIVideo class cluster">
 *
 * <p>There are many other API.Next classes and interfaces that are referenced by methods of the
 * <code>APIVideo</code> interface. For some examples of how to use these methods to retrieve
 * additional information about a video, see <a
 * href="https://confluence.netflix.com/display/API/5.+An+example+of+problem+analysis+and+script+design">API.Next
 * Programmer's Guide: An example of problem analysis and script design</a>.
 */
public interface APIVideo extends APIEntity, APIResolutions {

  @Override
  Integer getId();

  /**
   * @return <code>true</code> if this video is a viewable, <code>false</code> otherwise
   */
  // @MigrationStatus(status = MigrationStatus.Status.Done)
  boolean isViewable();

  /**
   * If this video is a show, return this video as a show.
   *
   * @return this video as an object that implements {@link APIShow}. Null if this video is not a
   *     show.
   */
  // @MigrationStatus(status = MigrationStatus.Status.WontDo)
  APIShow asShow();

  /**
   * If this video is a supplemental video, return this video as a {@link
   * com.netflix.api.service.video.APISupplementalVideo}.
   *
   * @return this video as an object that implements {@link APISupplementalVideo}, or <code>null
   *     </code> if this video is not a supplemental video
   */
  // @MigrationStatus(status = MigrationStatus.Status.WontDo)
  APISupplementalVideo asSupplementalVideo();

  @Deprecated
  @Override
  String getName();

  /**
   * Is this video available in HD? HD availability may vary based on the client -- it may differ
   * for PC/Mac and set-top boxes (devices). This method uses the request's device type based on the
   * ESN to determine HD availability.
   *
   * <p>User's with SD only plans will influence this method
   *
   * @return <code>true</code> if this video is available in HD
   */
  @Deprecated
  boolean isHD();

  /**
   * Is this video available in super HD? SuperHD availability may vary based on the client -- it
   * may differ for PC/Mac and set-top boxes (devices). This method uses the request's device type
   * based on the ESN to determine SuperHD availability.
   *
   * @deprecated user {@link #isHD()}
   * @return <code>true</code> if this video is available in super HD
   */
  @Deprecated
  boolean isSuperHD();

  /**
   * Is this video available in Ultra HD? UltraHD availability may vary based on the client -- it
   * may differ for PC/Mac and set-top boxes (devices). This method uses the request's device type
   * based on the ESN to determine UltraHD availability.
   *
   * @return <code>true</code> if this video is available in Ultra HD
   */
  @Deprecated
  boolean isUltraHD();

  /**
   * An <code>APIPerson</code> is a class that we use to represent actors, directors, and creators
   * of videos. This enum assists us in getting <code>APIPerson</code> objects of particular types
   * within methods
   */
  @ExcludeFromCoverage
  enum PersonRole {
    actor,
    director,
    creator,
    writer,
    producer,
    crew,
    story_by,
    screenwriter,
    guest,
    host
  }

  /**
   * Badge type to be shown for the video. NONE - Signifies no applicable badge REGULAR - Applied to
   * a show to indicate there is new content (i.e. "Has New Episodes") SMART - Applies "smart" logic
   * to rolling episode shows indicating what day of week the New episodes are coming (1 - 7 Day of
   * Weeek index) NEW - for use with Non-Serialized Rolling Episode shows (Chelsea Handler talk
   * show) to signify a "New" badge on episodes less than one week old NEW_TODAY - Added Oct 2016
   * for test 7608. Used to add the "New Today" badge in the ROAR for Trenchcoat boosted videos that
   * launched today. NEW_THIS_WEEK - Added Oct 2016 for test 7608. Used to add the "New This Week"
   * badge in the ROAR for Trenchcoat boosted videos that launched in the last 7 days.
   * RECENTLY_ADDED - Added Oct 2016 for test 7608. Used to add the "Recently Added" badge in the
   * ROAR for Trenchcoat boosted videos that launched in the last 14 days.
   */
  enum BadgeType {
    NONE,
    REGULAR,
    SMART,
    NEW,
    NEW_TODAY,
    NEW_THIS_WEEK,
    RECENTLY_ADDED
  }
}
