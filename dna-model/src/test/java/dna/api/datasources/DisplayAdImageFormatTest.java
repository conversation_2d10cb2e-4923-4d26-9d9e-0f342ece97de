package dna.api.datasources;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.params.provider.Arguments.arguments;

import com.netflix.adsnativeadarbiter.protogen.ImageFormat;
import dna.api.service.model.DisplayAdImageRecipeFormat;
import java.util.List;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.FieldSource;

class DisplayAdImageFormatTest {

  @ParameterizedTest
  @FieldSource
  void getAdsNativeImageFormat(DisplayAdImageRecipeFormat input, ImageFormat expected) {
    var wrapper = new DisplayAdImageFormat(input);
    assertEquals(expected, wrapper.getAdsNativeImageFormat());
  }

  static final List<Arguments> getAdsNativeImageFormat =
      List.of(
          arguments(DisplayAdImageRecipeFormat.JPG, ImageFormat.FORMAT_JPG),
          arguments(DisplayAdImageRecipeFormat.ASTC, ImageFormat.FORMAT_ASTC));
}
