package com.netflix.api.service.gallery;

import com.netflix.api.service.APIRequest;
import java.util.HashMap;
import java.util.Map;

/** A service for retrieving gallery views */
public interface APIGalleryService {

  class GalleryRequestBuilder {

    private final Map<String, Object> values;

    public GalleryRequestBuilder(String listContext) {
      values = new HashMap<>();
      values.put("listContext", listContext);
    }

    /**
     * Adds an individual attribute to a request
     *
     * @param k the key
     * @param v the value
     * @return the same instance using the builder pattern
     */
    public GalleryRequestBuilder put(String k, Object v) {
      values.put(k, v);
      return this;
    }

    /**
     * Adds all the attribute from the values parameter into the builder
     *
     * @param values the map
     * @return the same instance using the builder pattern
     */
    public GalleryRequestBuilder putAll(Map<String, Object> values) {
      this.values.putAll(values);
      return this;
    }

    /**
     * @return the fully formed gallery request
     */
    public Map<String, Object> build() {
      Map<String, Object> map = new HashMap<>();
      APIRequest request = APIRequest.getCurrentRequest();
      map.put("country", request.getRequestContext().getCountry().getId());
      map.put("esn", request.getRequestContext().getESN());
      map.put("deviceId", request.getRequestContext().getESN());
      map.put("locale", request.getRequestContext().getLocale().getId());
      map.put("galleryOrder", "natural");
      if (this.values != null) map.putAll(this.values); // override defaults
      return map;
    }
  }
}
