package com.netflix.api.service.titlegroup;

import java.util.List;

/** Title group with a set of the members that compose the group */
public interface APITitleGroupSet {

  /** Title group information */
  APITitleGroup getGroup();

  /**
   * Get all members within this groups
   *
   * @return group member, either APITitlesGroupSetVideoMember or APITitlesGroupSetGroupMember
   */
  List<APITitleGroupSetMember> getMembers();

  /**
   * Get the group member that was used to retrieve this group set The sorting order of the group
   * members may depend on member used to retrieve the APITitleGroupSet.
   *
   * @return group member
   */
  APITitleGroupSetMember getRelativeToMember();
}
