package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class CreditCardPaymentDetail {
  private String lastFourDigits;

  private String expiryMonth;

  private String expiryYear;

  private String billingZip;

  private String type;

  private String identificationData;

  public String getLastFourDigits() {
    return lastFourDigits;
  }

  public String getExpiryMonth() {
    return expiryMonth;
  }

  public String getExpiryYear() {
    return expiryYear;
  }

  public String getBillingZip() {
    return billingZip;
  }

  public String getType() {
    return type;
  }

  public String getIdentificationData() {
    return identificationData;
  }

  public CreditCardPaymentDetail setLastFourDigits(String lastFourDigits) {
    this.lastFourDigits = lastFourDigits;
    return this;
  }

  public CreditCardPaymentDetail setExpiryMonth(String expiryMonth) {
    this.expiryMonth = expiryMonth;
    return this;
  }

  public CreditCardPaymentDetail setExpiryYear(String expiryYear) {
    this.expiryYear = expiryYear;
    return this;
  }

  public CreditCardPaymentDetail setBillingZip(String billingZip) {
    this.billingZip = billingZip;
    return this;
  }

  public CreditCardPaymentDetail setType(String type) {
    this.type = type;
    return this;
  }

  public CreditCardPaymentDetail setIdentificationData(String identificationData) {
    this.identificationData = identificationData;
    return this;
  }
}
