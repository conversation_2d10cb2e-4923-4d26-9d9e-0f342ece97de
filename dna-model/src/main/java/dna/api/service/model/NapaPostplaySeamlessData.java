package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaPostplaySeamlessData {
  private Integer startSeconds;

  private Long startMillis;

  private Integer endSeconds;

  private Long endMillis;

  public Integer getStartSeconds() {
    return startSeconds;
  }

  public Long getStartMillis() {
    return startMillis;
  }

  public Integer getEndSeconds() {
    return endSeconds;
  }

  public Long getEndMillis() {
    return endMillis;
  }

  public NapaPostplaySeamlessData setStartSeconds(Integer startSeconds) {
    this.startSeconds = startSeconds;
    return this;
  }

  public NapaPostplaySeamlessData setStartMillis(Long startMillis) {
    this.startMillis = startMillis;
    return this;
  }

  public NapaPostplaySeamlessData setEndSeconds(Integer endSeconds) {
    this.endSeconds = endSeconds;
    return this;
  }

  public NapaPostplaySeamlessData setEndMillis(Long endMillis) {
    this.endMillis = endMillis;
    return this;
  }
}
