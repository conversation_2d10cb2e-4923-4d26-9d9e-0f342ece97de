package com.netflix.api.service.video;

import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.vms.provider.VideoFormatDescriptorProvider;

public class APICachedEpisodeBatchImpl extends APICachedVideoImpl {
  APICachedEpisodeBatchImpl(
      CompleteVideo v,
      APIVideoFactory videoFactory,
      USTVideoMetadataAdapter videoMetadataAdapter,
      VideoFormatDescriptorProvider videoFormatDescriptorProvider) {
    super(v, videoFactory, videoMetadataAdapter, videoFormatDescriptorProvider);
  }
}
