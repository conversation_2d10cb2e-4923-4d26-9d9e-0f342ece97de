package com.netflix.api.service.ums;

import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.ums.APIMessage.APIMessageState;
import com.netflix.api.service.ums.APIMessage.APISocialMessageType;

/**
 * FIXME FIXME FIXME.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIUMS.class.png"
 * alt="Class Diagram: APIUniversalMessagingService">
 */
public interface APIFilterNotificationMessagesRequest {

  /**
   * FIXME FIXME FIXME.
   *
   * @param readState FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationMessagesRequest setReadState(APIMessageState readState);

  /**
   * FIXME FIXME FIXME.
   *
   * @param maxCount FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationMessagesRequest setMaxCount(int maxCount);

  /**
   * Return only messages that are newer or older than this timestamp
   *
   * @param timestamp FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationMessagesRequest setReferenceTimestamp(long timestamp);

  /**
   * If true, return only messages that are older than this timestamp, optional
   *
   * @param retrieveOlderThanRefTimestamp FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationMessagesRequest setMessagesOlderThanRefTimestamp(
      boolean retrieveOlderThanRefTimestamp);

  /**
   * Only return this type of social messages, optional
   *
   * @param socialMessageType FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationMessagesRequest setSocialMessageType(APISocialMessageType socialMessageType);

  /**
   * The requested size of the boxart
   *
   * @param size FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationMessagesRequest setBoxArtSize(String size);

  /**
   * The requested size of the boxart for NSA
   *
   * @param size FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationMessagesRequest setNsaBoxArtSize(String size);

  /**
   * FIXME FIXME FIXME.
   *
   * @param secure FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationMessagesRequest setIsSecure(boolean secure);

  /**
   * FIXME FIXME FIXME.
   *
   * @param eTag FIXME FIXME FIXME
   * @return
   */
  APIFilterNotificationMessagesRequest setETag(String eTag);

  /**
   * FIXME FIXME FIXME.
   *
   * @return
   */
  APIUser getUser();

  /**
   * The requested boxart format, optional
   *
   * @param artWorkFormat
   * @return
   */
  APIFilterNotificationMessagesRequest setArtWorkFormat(String artWorkFormat);

  /**
   * The uiPlatform making the request, optional
   *
   * @param uiPlatform
   * @return
   */
  APIFilterNotificationMessagesRequest setUIPlatform(String uiPlatform);

  /**
   * Whether to fetch the template for this message, optional
   *
   * @param fetchTemplate
   * @return
   */
  APIFilterNotificationMessagesRequest setFetchTemplate(Boolean fetchTemplate);

  /**
   * HTML (default), TEXT, or OBELIX_KEY
   *
   * @param templateStringFormat
   * @return
   */
  APIFilterNotificationMessagesRequest setTemplateStringFormat(String templateStringFormat);

  /**
   * the app version of the uiPlatform making the request, optional
   *
   * @param appVersion
   * @return
   */
  APIFilterNotificationMessagesRequest setAppVersion(String appVersion);

  /**
   * the eventGuid of the message to return, optional
   *
   * @return
   */
  APIFilterNotificationMessagesRequest setEventGuid(String eventGuid);
}
