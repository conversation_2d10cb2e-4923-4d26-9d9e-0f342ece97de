package com.netflix.api.service.batch.model;

import java.util.Map;

/** Author: sgudiboina Date: 11/14/17 Time: 2:23 PM */
public class PromoVideoImpl implements PromoVideo {
  private final int id;
  private final String type;
  private final long offsetInMs;
  private final String computeId;
  private final boolean playIntoMDP;
  private final Map<String, String> annotations;

  public PromoVideoImpl(
      int id,
      String type,
      long offsetInMs,
      String computeId,
      boolean playIntoMDP,
      Map<String, String> annotations) {
    this.id = id;
    this.type = type;
    this.offsetInMs = offsetInMs;
    this.computeId = computeId;
    this.playIntoMDP = playIntoMDP;
    this.annotations = annotations;
  }

  @Override
  public int getId() {
    return id;
  }

  @Override
  public String getType() {
    return type;
  }

  @Override
  public long getOffsetInMs() {
    return offsetInMs;
  }

  @Override
  public String getComputeId() {
    return computeId;
  }

  @Override
  public boolean isPlayIntoMDP() {
    return playIntoMDP;
  }

  public Map<String, String> getAnnotations() {
    return annotations;
  }
}
