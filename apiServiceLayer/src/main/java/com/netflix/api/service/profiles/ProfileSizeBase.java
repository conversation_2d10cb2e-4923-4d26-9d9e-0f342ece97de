package com.netflix.api.service.profiles;

import java.util.Objects;

public class ProfileSizeBase implements ProfileSize {

  private final int height;
  private final int width;

  protected ProfileSizeBase(int width, int height) {
    this.height = height;
    this.width = width;
  }

  @Override
  public int getHeight() {
    return height;
  }

  @Override
  public int getWidth() {
    return width;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof ProfileSizeBase profileSizeBase)) {
      return false;
    }

    return height == profileSizeBase.height && width == profileSizeBase.width;
  }

  @Override
  public int hashCode() {
    return Objects.hash(height, width);
  }

  @Override
  public final String toString() {
    return this.height + "x" + this.width;
  }
}
