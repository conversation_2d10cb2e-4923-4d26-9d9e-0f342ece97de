package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaSectionFeatureMetadata {
  private NapaFilterSectionMetadata filterMetadata;

  public NapaFilterSectionMetadata getFilterMetadata() {
    return filterMetadata;
  }

  public NapaSectionFeatureMetadata setFilterMetadata(NapaFilterSectionMetadata filterMetadata) {
    this.filterMetadata = filterMetadata;
    return this;
  }
}
