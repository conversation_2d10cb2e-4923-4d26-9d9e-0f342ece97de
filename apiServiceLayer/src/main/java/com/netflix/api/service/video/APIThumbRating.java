package com.netflix.api.service.video;

public enum APIThumbRating {
  UNRATED(0),
  THUMBS_DOWN(1),
  THUMBS_UP(2);

  private final int mValue;

  APIThumbRating(int value) {
    mValue = value;
  }

  public int getValue() {
    return mValue;
  }

  public static APIThumbRating valueOf(int value) {
    return switch (value) {
      case 2 -> THUMBS_UP;
      case 1 -> THUMBS_DOWN;
      default -> UNRATED;
    };
  }
}
