package com.netflix.api.service.device;

import static com.netflix.api.grpc.GrpcCallHelpers.RxObservable.defer;
import static com.netflix.api.service.APIRequest.getCurrentRequest;
import static java.util.Collections.emptyMap;

import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.dcms.models.protogen.PropertyValue;
import com.netflix.dcms.protogen.DcmsRtServiceGrpc.DcmsRtServiceStub;
import com.netflix.dcms.protogen.GetDeviceTypePropertiesReq;
import com.netflix.servo.DefaultMonitorRegistry;
import com.netflix.servo.MonitorRegistry;
import com.netflix.servo.monitor.Counter;
import com.netflix.servo.monitor.Monitors;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
public class APIDeviceManagementServiceImpl implements APIDeviceManagementService {

  private static final Counter HAS_DEVICES =
      Monitors.newCounter("deprecated.apiDeviceManagementService.hasDevices");
  private static final Counter ESN_PREFIX =
      Monitors.newCounter("deprecated.apiDeviceManagementService.getDeviceTypeFromESNPrefix");
  private static final Counter LOCALIZED_DESCRIPTION =
      Monitors.newCounter("deprecated.apiDeviceManagementService.getLocalizedDescription");

  private final DcmsRtServiceStub dcmsService;

  static {
    MonitorRegistry registry = DefaultMonitorRegistry.getInstance();
    registry.register(HAS_DEVICES);
    registry.register(ESN_PREFIX);
    registry.register(LOCALIZED_DESCRIPTION);
  }

  @Autowired
  public APIDeviceManagementServiceImpl(@GrpcSpringClient("dcmsrt") DcmsRtServiceStub dcmsService) {
    this.dcmsService = dcmsService;
  }

  private Observable<Map<String, String>> getDcmsDeviceProperties(
      Integer deviceTypeId, @Nullable String nrdAppVersion, Collection<String> propertyNames) {
    GetDeviceTypePropertiesReq.Builder builder =
        GetDeviceTypePropertiesReq.newBuilder()
            .setDeviceTypeId(deviceTypeId)
            .addAllProperties(propertyNames);
    if (nrdAppVersion != null) {
      builder.setNrdAppVersion(nrdAppVersion);
    }
    return defer(dcmsService::getDeviceTypeProperties, builder.build())
        .map(
            res -> {
              Map<String, String> properties = HashMap.newHashMap(res.getPropertiesCount());
              for (Entry<String, PropertyValue> property : res.getPropertiesMap().entrySet()) {
                properties.put(property.getKey(), property.getValue().getPropertyValue());
              }
              return properties;
            });
  }

  @Override
  public Observable<Map<String, String>> getCurrentDeviceProperties(
      Collection<String> propertyNames, String nrdAppVersion) {
    Integer deviceTypeId = getCurrentRequest().getRequestContext().getDeviceTypeId();
    if (deviceTypeId == null) {
      return Observable.just(emptyMap());
    }
    return getDcmsDeviceProperties(deviceTypeId, nrdAppVersion, propertyNames)
        .onErrorResumeNext(Observable.just(emptyMap()));
  }
}
