package com.netflix.api.service.video;

import java.util.Objects;

/** A Content Warning -- for DNA-202 Content Warning for 13 Reasons Why */
public class APIContentWarning {

  private final String url;
  private final String message;
  private final String urlSubstitutionString;

  public APIContentWarning(String url, String message, String urlSubstitutionString) {
    this.url = url;
    this.message = message;
    this.urlSubstitutionString = urlSubstitutionString;
  }

  public String getUrl() {
    return url;
  }

  public String getMessage() {
    return message;
  }

  public String getUrlSubstitutionString() {
    return urlSubstitutionString;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIContentWarning that)) {
      return false;
    }
    return Objects.equals(getUrl(), that.getUrl())
        && Objects.equals(getMessage(), that.getMessage())
        && Objects.equals(getUrlSubstitutionString(), that.getUrlSubstitutionString());
  }

  @Override
  public int hashCode() {
    return Objects.hash(getUrl(), getMessage(), getUrlSubstitutionString());
  }

  @Override
  public String toString() {
    return "APIContentWarning{"
        + "url='"
        + url
        + '\''
        + ", message='"
        + message
        + '\''
        + ", urlSubstitutionString='"
        + urlSubstitutionString
        + '\''
        + '}';
  }
}
