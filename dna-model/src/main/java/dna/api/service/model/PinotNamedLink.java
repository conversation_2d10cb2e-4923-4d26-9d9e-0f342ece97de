package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class PinotNamedLink {
  private String name;

  private String url;

  public String getName() {
    return name;
  }

  public String getUrl() {
    return url;
  }

  public PinotNamedLink setName(String name) {
    this.name = name;
    return this;
  }

  public PinotNamedLink setUrl(String url) {
    this.url = url;
    return this;
  }
}
