package com.netflix.api.platform.stats;

import com.google.common.annotations.VisibleForTesting;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.migration.DeviceMigration;
import com.netflix.spectator.api.BasicTag;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Registry;
import com.netflix.spectator.api.Tag;
import com.netflix.type.protogen.BasicTypes.DeviceType;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import netflix.context.device.DeviceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** Used to capture statistics and publish them to Atlas. */
@Component
public class APIStats {
  private static final Logger logger = LoggerFactory.getLogger(APIStats.class);
  private static final String DEV_RET_METRIC_NAME = "API-EndpointExecution";

  private static final Property<Integer> DEVICE_RETIREMENTS_SAMPLE_RATE =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("stats.deviceRetirementsSampleRate", Integer.class)
          .orElse(0);

  private final Registry registry;
  private final Id devRetMetricId;

  @Autowired
  public APIStats(Registry registry) {
    this.registry = registry;
    this.devRetMetricId = registry.createId(DEV_RET_METRIC_NAME);
  }

  /** Record response with duration and statusCode so it can be classified. Once DNA traffic sufficiently decreases, we may want to move this to AtlasMetricsFilter and attach to every Falcor path.*/
  public void emitDeviceRetirementMetrics(
      int statusCode, HttpServletRequest request, Microcontext microcontext) {
    try {
      final DeviceContext device = microcontext.getDevice();
      if (isSubjectToSampler(
          device.hasEsn() ? device.getEsn().getValue() : null,
          DEVICE_RETIREMENTS_SAMPLE_RATE.get())) {

        logger.debug("microcontext: {}", microcontext.toProto().toString());

        final Optional<DeviceType> deviceType = DeviceMigration.deviceType(microcontext.toProto());
        final String method = request != null ? request.getMethod() : "null";

        String appVersion = CurrentMicrocontext.get().getClient().getAppVersion().getVersion();

        List<Tag> devRetTags = new ArrayList<>();
        devRetTags.add(
            new BasicTag(
                "requestPath", request.getRequestURI() != null ? request.getRequestURI() : "n/a"));
        devRetTags.add(new BasicTag("retired", Boolean.toString(device.getRetired())));
        devRetTags.add(
            new BasicTag(
                "supportLevel",
                device.getSupportLevel() != null
                    ? device.getSupportLevel().name()
                    : "UNSPECIFIED"));
        devRetTags.add(
            new BasicTag("deviceCategory", microcontext.getClient().getClientCategory().name()));
        devRetTags.add(new BasicTag("status", Integer.toString(statusCode)));
        devRetTags.add(new BasicTag("method", method));
        devRetTags.add(
            new BasicTag(
                "deviceType",
                deviceType.map(DeviceType::getId).map(d -> Integer.toString(d)).orElse("none")));
        devRetTags.add(new BasicTag("appVersion", appVersion.isBlank() ? "unknown" : appVersion));

        Counter counter = registry.counter(devRetMetricId.withTags(devRetTags));
        counter.increment();
      }

    } catch (Exception e) {
      logger.debug("exception emitting dev ret metric", e);
    }
  }

  @VisibleForTesting
  static boolean isSubjectToSampler(String esn, int threshold) {
    if (threshold <= 0) {
      return false;
    } else if (threshold >= 100) {
      return true;
    } else {
      int bucket = getBucket(esn);
      return bucket < threshold;
    }
  }

  @VisibleForTesting
  static int getBucket(String esn) {
    if (esn == null) {
      return 0; // subject to retirement at >0 dial value
    }
    int hash = esn.hashCode();
    int modulo = hash % 100;
    return Math.abs(modulo);
  }
}
