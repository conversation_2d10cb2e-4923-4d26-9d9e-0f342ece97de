package com.netflix.api.service.account;

import com.netflix.patron.customersupport.protogen.AuthTokenInfo;
import java.util.Map;
import java.util.Objects;

public class APITokenForCallImpl implements APITokenForCall {

  private final String token;
  private final long tokenExpirationTS;
  private final Map<String, String> resultAttributes;

  public APITokenForCallImpl(AuthTokenInfo response) {
    this.token = response.getBoxedToken();
    this.tokenExpirationTS = response.getBoxedTokenExpirationTs();
    this.resultAttributes = response.getAttributesMap();
  }

  public Map<String, String> getResultAttributes() {
    return resultAttributes;
  }

  @Override
  public String getToken() {
    return token;
  }

  @Override
  public long getTokenExpirationTS() {
    return tokenExpirationTS;
  }

  @Override
  public String toString() {
    return "APITokenForCall{"
        + "token='"
        + token
        + '\''
        + ", tokenExpirationTS="
        + tokenExpirationTS
        + ", resultAttributes="
        + resultAttributes
        + '}';
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APITokenForCallImpl)) {
      return false;
    }
    APITokenForCallImpl that = (APITokenForCallImpl) o;
    return getTokenExpirationTS() == that.getTokenExpirationTS()
        && Objects.equals(getToken(), that.getToken())
        && Objects.equals(getResultAttributes(), that.getResultAttributes());
  }

  @Override
  public int hashCode() {
    return Objects.hash(getToken(), getTokenExpirationTS(), getResultAttributes());
  }
}
