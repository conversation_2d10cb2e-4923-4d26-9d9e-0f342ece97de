package dna.api.service.model;

import javax.annotation.Generated;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
public enum SearchPageSectionKind {
  UNKNOWN,

  DBG_ALL,

  ChipCarousel,

  CreatorHome,

  EntityCarousel,

  EntityGallery,

  EntityNameList,

  DvdNameList,

  FilterLanguages,

  GameCarousel,

  GameCarouselWide,

  GameGallery,

  GameList,

  Hero,

  MerchVideoPlayer,

  OocVideoCarousel,

  PillCarousel,

  PostplayHardwire,

  PostplayLegacyOriginalsPostplay,

  PostplayLegacyOriginalsPostplayPostTrailer,

  PostplayLegacyOriginalsPostplayRecommendations,

  PostplayLegacyRecommendations,

  PostplayNextEpisode,

  PostplayNextEpisodeSeamless,

  PostplayOriginalsBackgroundAutoplayTrailer,

  PostplayPlaySomething,

  PostplayPreview3,

  PostplayPreview3Characters,

  PostplayTwoUpChoicepoint,

  PreplayRecap,

  PreplayTutorial,

  Promo,

  PulseEntities,

  SearchHints,

  SimsVideoCarousel,

  SupplementalVideo,

  TrailerFeed,

  TrailerFeedsTrailer,

  TrailerFeedsVideo,

  TrailerFeedsGame,

  VideoCarousel,

  VideoCarouselList,

  VideoGallery,

  VideoList
}
