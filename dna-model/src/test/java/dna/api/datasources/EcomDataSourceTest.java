package dna.api.datasources;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;

import com.netflix.api.platform.MockPropertyRepositoryConfig;
import com.netflix.demograph.Context;
import com.netflix.growth.planspricing.protogen.GrowthPlansPricingServiceGrpc.GrowthPlansPricingServiceStub;
import com.netflix.paymentexperiences.protogen.PaymentExperiencesServiceGrpc.PaymentExperiencesServiceStub;
import com.netflix.paymentexperiences.protogen.RetryPaymentRequest;
import com.netflix.paymentexperiences.protogen.RetryPaymentResponse;
import com.netflix.plex.member.protogen.PlexMemberServiceGrpc.PlexMemberServiceStub;
import io.grpc.stub.StreamObserver;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

class EcomDataSourceTest {

  @BeforeAll
  static void setupClass() {
    MockPropertyRepositoryConfig config = new MockPropertyRepositoryConfig();
    config.init();
  }

  @ParameterizedTest
  @ValueSource(booleans = {true, false})
  void retryPayment(boolean isSuccessful) {
    var stub = mock(PaymentExperiencesServiceStub.class);
    var mockMembers = mock(PlexMemberServiceStub.class);
    var mockPlans = mock(GrowthPlansPricingServiceStub.class);
    var datasource = new EcomDataSource(mockMembers, stub, mockPlans);

    doAnswer(
            invocation -> {
              StreamObserver<RetryPaymentResponse> observer = invocation.getArgument(1);
              if (isSuccessful) {
                observer.onNext(RetryPaymentResponse.getDefaultInstance());
                observer.onCompleted();
              } else {
                observer.onError(new RuntimeException("failed"));
              }
              return null;
            })
        .when(stub)
        .retryPayment(any(RetryPaymentRequest.class), any());

    var ctx = Context.builder().withAttribute("foo", "bar").build();
    var stage = datasource.retryPayment().lookup(ctx);
    var future = stage.toCompletableFuture();

    assertEquals(isSuccessful, future.join());
  }
}
