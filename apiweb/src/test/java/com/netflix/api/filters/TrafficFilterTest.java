package com.netflix.api.filters;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.netflix.api.platform.MockPropertyRepositoryConfig;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TrafficFilterTest {

  HttpServletRequest request;
  HttpServletResponse response;

  @BeforeAll
  static void beforeClass() {
    var config = new MockPropertyRepositoryConfig();
    config.init();
  }

  @BeforeEach
  void before() {
    request = mock(HttpServletRequest.class);
    response = mock(HttpServletResponse.class);
  }

  @Test
  void testRootBlock() {
    TrafficFilter filter = new TrafficFilter();
    when(request.getRequestURI()).thenReturn("/");
    boolean proceed = filter.filterRootPath(request, response, true);
    assertFalse(proceed);
    verify(response).setStatus(404);
  }

  @Test
  void testRootBlockDisabled() {
    TrafficFilter filter = new TrafficFilter();
    when(request.getRequestURI()).thenReturn("/");
    boolean proceed = filter.filterRootPath(request, response, false);
    assertTrue(proceed);
    verify(response, never()).setStatus(anyInt());
  }

  @Test
  void testNext() {
    TrafficFilter filter = new TrafficFilter();
    when(request.getRequestURI()).thenReturn("/mr/foo");
    boolean proceed = filter.filterDenylistTraffic(request, response, true, List.of("/mr/"));
    assertFalse(proceed);
    verify(response).setStatus(421);
  }

  @Test
  void testWhitelist() {
    TrafficFilter filter = new TrafficFilter();
    when(request.getRequestURI()).thenReturn("/mr/foo");
    boolean proceed = filter.filterAllowlistTraffic(request, response, true, List.of("/nrdjs/"));
    assertFalse(proceed);
    verify(response).setStatus(421);
  }

  @Test
  void testWhitelistPass() {
    TrafficFilter filter = new TrafficFilter();
    when(request.getRequestURI()).thenReturn("/nrdjs/foo");
    boolean proceed = filter.filterAllowlistTraffic(request, response, true, List.of("/nrdjs/"));
    assertTrue(proceed);
    verify(response, never()).setStatus(anyInt());
  }
}
