package com.netflix.api.service.i18n;

import java.util.Collection;
import java.util.Map;
import rx.Observable;
import rx.Single;

public interface APIShakespeareService {

  /**
   * Retrieve a message for giving messageID and request information.
   *
   * @deprecated add a batched version if needed
   * @param messageID UI message tag/ID
   * @param request details about the call to make
   * @return Matched message
   */
  @Deprecated
  Observable<String> getTestCopy(String messageID, APIShakespeareRequest request);

  /**
   * Retrieve a test copy resolved by Obelix for giving messageID.
   *
   * @deprecated use {@link #getAllTestCopiesObelix(String, String, APIShakespeareRequest)}
   * @param namespace Obelix namesapce
   * @param bundle Obelix bundle
   * @param key Obelix key
   * @param request details about the call to make
   * @return Matched message from Obelix, or an empty Observable
   */
  @Deprecated
  Observable<String> getTestCopyObelix(
      String namespace, String bundle, String key, APIShakespeareRequest request);

  /**
   * Get a map of Obelix key and test copy belongs to given profile ID, specified language Obelix
   * namespace and Obelix bundles.
   *
   * @param namespace Obelix namespace
   * @param bundle Obelix bundle
   * @param request details about the call to make
   * @return A map of &lt;Obelix Key, Test Copy&gt;
   */
  Single<Map<String, String>> getAllTestCopiesObelix(
      String namespace, String bundle, APIShakespeareRequest request);

  /**
   * Get a map of maps of Obelix key and test copy belongs to given profile ID, specified language
   * Obelix namespace and Obelix bundles.
   *
   * @param namespace Obelix namespace
   * @param bundles Obelix bundles
   * @param request details about the call to make
   * @return A map of &lt;Obelix Bundle, map of &lt;Obelix Key, Test Copy&gt;&gt;
   */
  Single<Map<String, Map<String, String>>> getBatchedTestCopiesObelix(
      String namespace, Collection<String> bundles, APIShakespeareRequest request);

  /**
   * Get a map of test copies based on AB ID.
   *
   * @param testIds A collection of AB Test ID
   * @param request details about the call to make
   * @return A map of &lt;obelix-namespace____obelix-bundle____obelix-key, Test Copy&gt;
   */
  Single<Map<String, String>> getAllTestCopiesByABTestIds(
      Collection<Integer> testIds, APIShakespeareRequest request);
}
