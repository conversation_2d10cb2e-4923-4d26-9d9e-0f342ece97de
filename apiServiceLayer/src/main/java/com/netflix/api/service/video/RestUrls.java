package com.netflix.api.service.video;

import com.netflix.type.Video;
import com.netflix.videometadata.bean.VideoEpisode;
import com.netflix.videometadata.type.CompleteVideo;
import jakarta.annotation.Nonnull;
import ust.common.v1.VideoType;
import ust.common.v1.VideoType.Type;
import ust.video.v1.EpisodeDetail;
import ust.video.v1.VideoCore;

public class RestUrls {

  private static final String BASE_URI = "http://api.netflix.com/";
  private static final String CATALOG_TITLE_URI = BASE_URI + "catalog/titles/";

  /**
   * Legacy REST URL of the video. This is primarily used during migration of clients from API2.0
   * (or less) to API.Next.
   *
   * <p>Example: Homepage built using API.Next needs to get a rest URL for movie detail page that is
   * still being built using API 2.0.
   *
   * @return this video's REST URL
   */
  public static String getRestUrl(CompleteVideo completeVideo) {
    if (completeVideo == null || completeVideo.getId() < 1) {
      return null;
    }

    String restUrl;
    if (completeVideo.isMovie()) {
      restUrl = getUrl(completeVideo, IdentityType.MOVIE);
    } else if (completeVideo.isShow()) {
      restUrl = getUrl(completeVideo, IdentityType.SERIES);
    } else if (completeVideo.isSeason()) {
      restUrl = getUrl(completeVideo, IdentityType.SEASON);
    } else if (completeVideo.isEpisode()) {
      restUrl = getUrl(completeVideo.getEpisodeDetails());
    } else { // default to a movie url
      restUrl = getUrl(completeVideo, IdentityType.MOVIE);
    }

    return restUrl;
  }

  public static String getRestUrl(@Nonnull VideoCore videoCore) {
    VideoType videoType = videoCore.getVideoBase().getVideoType();
    int id = videoType.getVideo().getId();
    if (id < 1) {
      return null;
    }

    Type type = videoType.getType();
    if (type == Type.TYPE_SHOW) {
      return getMovieSeriesUrl(id, IdentityType.SERIES);
    } else if (type == Type.TYPE_SEASON) {
      return getSeasonUrl(id, videoCore.getVideoBase().getBoxedTopNodeId());
    } else if (type == Type.TYPE_EPISODE) {
      return getEpisodeUrl(id, videoCore.getEpisode());
    } else { // default to a movie url
      return getMovieSeriesUrl(id, IdentityType.MOVIE);
    }
  }

  private static String getEpisodeUrl(int videoId, EpisodeDetail episode) {
    if (episode != null) {
      return String.format(
          "%sprograms/%d/%d", CATALOG_TITLE_URI, videoId, episode.getDeliverableVideo().getId());
    }
    return CATALOG_TITLE_URI;
  }

  private static String getUrl(VideoEpisode episode) {
    if (episode != null) {
      return String.format(
          "%sprograms/%d/%d",
          CATALOG_TITLE_URI, episode.getId(), episode.getDeliverableVideo().getId());
    }
    return CATALOG_TITLE_URI;
  }

  private enum IdentityType {
    MOVIE("movies"),
    SERIES("series"),
    SEASON("seasons"),
    EPISODE("episode");

    private final String id;

    IdentityType(String id) {
      this.id = id;
    }

    @Override
    public String toString() {
      return id;
    }
  }

  private static String getMovieSeriesUrl(int videoId, IdentityType type) {
    return CATALOG_TITLE_URI + type + "/" + videoId;
  }

  private static String getSeasonUrl(int videoId, Integer parentVideo) {
    StringBuilder sb = new StringBuilder();
    if (parentVideo != null) {
      sb.append(CATALOG_TITLE_URI).append("series/");
      sb.append(parentVideo).append("/");
      sb.append(IdentityType.SEASON).append("/");
      sb.append(videoId);
    }
    return sb.toString();
  }

  private static String getUrl(CompleteVideo completeVideo, IdentityType type) {
    StringBuilder sb = new StringBuilder();
    switch (type) {
      case MOVIE:
      case SERIES:
        {
          sb.append(CATALOG_TITLE_URI).append(type).append("/").append(completeVideo.getId());
          break;
        }
      case SEASON:
        {
          Integer parentVideo = getParent(completeVideo);
          if (parentVideo != null) {
            sb.append(CATALOG_TITLE_URI).append("series/");
            sb.append(parentVideo).append("/");
            sb.append(type).append("/");
            sb.append(completeVideo.getId());
          }
          break;
        }
      case EPISODE:
        {
          break;
        }
      default:
        // do nothing
    }

    return sb.toString();
  }

  private static Integer getParent(CompleteVideo pVideo) {
    Video video = pVideo.getTopNode();
    if (video != null) {
      return video.getId();
    }
    return null;
  }
}
