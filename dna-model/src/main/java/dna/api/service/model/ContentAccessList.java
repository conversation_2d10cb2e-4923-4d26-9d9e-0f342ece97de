package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class ContentAccessList {
  private List<ContentAccess> list;

  public List<ContentAccess> getList() {
    return list;
  }

  public ContentAccessList setList(List<ContentAccess> list) {
    this.list = list;
    return this;
  }
}
