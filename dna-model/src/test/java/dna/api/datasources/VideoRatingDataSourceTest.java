package dna.api.datasources;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;

import com.google.protobuf.util.Timestamps;
import com.netflix.api.platform.MockPropertyRepositoryConfig;
import com.netflix.demograph.Context;
import com.netflix.demograph.datasource.AsyncDataSource;
import io.grpc.stub.StreamObserver;
import java.util.List;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import ust.rating.v1.GetAllStarRatingsResponse;
import ust.rating.v1.RatingAccess;
import ust.rating.v1.StarRating;
import ust.rating.v2.GetAllThumbRatingsResponse;
import ust.rating.v2.Rating;
import ust.rating.v2.ThumbRating;

class VideoRatingDataSourceTest {
  private final RatingAccess ratingAccessV1 = mock(RatingAccess.class);
  private final ust.rating.v2.RatingAccess ratingAccessV2 = mock(ust.rating.v2.RatingAccess.class);
  private final VideoRatingDataSource dataSource =
      new VideoRatingDataSource(null, ratingAccessV1, ratingAccessV2, null);

  @BeforeAll
  static void beforeClass() {
    MockPropertyRepositoryConfig config = new MockPropertyRepositoryConfig();
    config.init();
  }

  @BeforeEach
  void before() {
    doAnswer(
            args -> {
              StreamObserver<GetAllStarRatingsResponse> obs = args.getArgument(1);
              obs.onNext(
                  GetAllStarRatingsResponse.newBuilder()
                      .addStarRatings(
                          StarRating.newBuilder()
                              .setRating(3f)
                              .setTimestamp(Timestamps.fromSeconds(1))
                              .build())
                      .build());
              obs.onCompleted();
              return null;
            })
        .when(ratingAccessV1)
        .getAllStarRatings(any(), any());
    doAnswer(
            args -> {
              StreamObserver<GetAllThumbRatingsResponse> obs = args.getArgument(1);
              obs.onNext(
                  GetAllThumbRatingsResponse.newBuilder()
                      .addThumbRatings(
                          ThumbRating.newBuilder()
                              .setRating(Rating.THUMBS_UP)
                              .setTimestamp(Timestamps.fromSeconds(2)))
                      .build());
              obs.onCompleted();
              return null;
            })
        .when(ratingAccessV2)
        .getAllThumbRatings(any(), any());
  }

  @Test
  void allRatingsByUser() {
    AsyncDataSource<Long, List<StarThumbRating>> asyncDS = dataSource.allRatingsByUser();
    assertNotNull(asyncDS);

    var ctx = Context.builder().withUserAuthenticated().build();

    var promise = asyncDS.lookup(ctx, 12345L);

    var starThumbRatings = promise.toCompletableFuture().join();
    assertEquals(2, starThumbRatings.size());

    assertNotNull(starThumbRatings.getFirst().thumbRating());
    assertEquals(Rating.THUMBS_UP, starThumbRatings.getFirst().thumbRating().getRating());

    assertNotNull(starThumbRatings.get(1).starRating());
    assertEquals(3f, starThumbRatings.get(1).starRating().getRating(), 0);
  }
}
