package com.netflix.api.service.vhs;

import com.google.common.base.Preconditions;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;
import com.netflix.api.facade.service.VideoLookup;
import com.netflix.type.Video;
import com.netflix.videometadata.type.CompleteVideo;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import rx.Observable;

public class APIViewingImpl implements APIViewing {

  private final ViewingHolder viewing;
  private final VideoLookup videoLookup;

  @Inject
  APIViewingImpl(@Assisted ViewingHolder viewing, VideoLookup videoLookup) {
    this.viewing = Preconditions.checkNotNull(viewing, "Viewing must be provided");
    this.videoLookup = videoLookup;
  }

  @Override
  public String getEsn() {
    return viewing.activeSessionInfo.getEsn();
  }

  @Override
  @Deprecated
  public Observable<Integer> getTopNode() {
    // make async
    var id = viewing.activeSessionInfo.getMovieId();
    Map<Integer, Optional<CompleteVideo>> videos = videoLookup.getVideos(Set.of(id));

    var video = videos.get(id).orElse(null);
    if (video == null) {
      return Observable.empty();
    }
    return Observable.just(video).map(CompleteVideo::getTopNode).map(Video::getId);
  }

  @Override
  public int getDeviceTypeId() {
    return viewing.deviceTypeId;
  }

  @Override
  public long getCustomerId() {
    return viewing.activeSessionInfo.getProfileId();
  }

  @Override
  public boolean isHidden() {
    return false;
  }
}
