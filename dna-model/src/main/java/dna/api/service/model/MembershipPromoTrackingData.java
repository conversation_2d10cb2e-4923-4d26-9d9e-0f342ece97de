package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class MembershipPromoTrackingData {
  private String promoName;

  private Map<String, Integer> actionCounts;

  public String getPromoName() {
    return promoName;
  }

  public Map<String, Integer> getActionCounts() {
    return actionCounts;
  }

  public MembershipPromoTrackingData setPromoName(String promoName) {
    this.promoName = promoName;
    return this;
  }

  public MembershipPromoTrackingData setActionCounts(Map<String, Integer> actionCounts) {
    this.actionCounts = actionCounts;
    return this;
  }
}
