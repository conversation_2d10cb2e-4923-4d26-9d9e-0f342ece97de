package dna.api.service.model;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.netflix.demograph.graph.Atom;
import java.util.HashMap;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class ProfileImage implements GraphData {
  /**
   * The actual image for the lolopi
   *
   * @deprecated Fetch via ImageDetails instead for perf reasons. (jira: DNA-2215)
   * @dotnext com.netflix.api.service.profiles.APIProfileIcon#getId
   * @dotnext com.netflix.api.service.profiles.APIProfileIcon#getImage
   * @dotnext com.netflix.api.service.profiles.APIProfileIcon#getUrl#startsWith("https")
   */
  public Atom<Image> image;

  /**
   * uuid of profileImage
   *
   * @deprecated Fetch via ImageDetails instead for perf reasons. (jira: DNA-2215)
   * @dotnext com.netflix.api.service.profiles.APIProfileIcon#getAnnotationValue
   */
  public String uuid;

  /**
   * title of profileImage
   *
   * @deprecated Fetch via ImageDetails instead for perf reasons. (jira: DNA-2215)
   * @dotnext com.netflix.api.service.profiles.APIProfileIcon#getAnnotationValue
   */
  public String title;

  /** Image details of ProfileImage */
  public Atom<ImageDetails> details;

  private HashMap<String, Object> annotationToValue;

  public Atom<Image> getImage() {
    return image;
  }

  public String getUuid() {
    return uuid;
  }

  public String getTitle() {
    return title;
  }

  public Atom<ImageDetails> getDetails() {
    return details;
  }

  public ProfileImage setImage(Atom<Image> image) {
    this.image = image;
    return this;
  }

  public ProfileImage setUuid(String uuid) {
    this.uuid = uuid;
    return this;
  }

  public ProfileImage setTitle(String title) {
    this.title = title;
    return this;
  }

  public ProfileImage setDetails(Atom<ImageDetails> details) {
    this.details = details;
    return this;
  }

  @JsonAnyGetter
  public HashMap<String, Object> annotationToValue() {
    return this.annotationToValue;
  }

  public Object getAnnotation(String annotation) {
    return this.annotationToValue != null ? this.annotationToValue.get(annotation) : null;
  }

  @JsonAnySetter
  public void setAnnotation(String key, Object value) {
    if (annotationToValue == null) {
      annotationToValue = new HashMap<>();
    }
    this.annotationToValue.put(key, value);
  }
}
