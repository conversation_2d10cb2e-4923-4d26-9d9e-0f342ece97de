package com.netflix.api.service.identity;

import com.netflix.api.service.APIListBuilderImpl;
import com.netflix.servo.monitor.DynamicCounter;

public abstract class APIUserManagementListBuilder<Behavior extends APIUserManagementBehavior>
    extends APIL<PERSON><PERSON><PERSON>erImpl<APIUser, Behavior> implements APIUserProfileBuilder {

  private final String context;

  public APIUserManagementListBuilder(String context, Behavior behavior) {
    super(behavior);
    this.context = context;
  }

  @Override
  public APIUserManagementListBuilder setProfileName(String s) {
    DynamicCounter.increment(
        "UserManagementFieldAccess", "context", context, "field", "profileName");
    getBehavior().setProfileName(s);
    return this;
  }

  @Override
  public APIUserManagementListBuilder setPreferredExperience(APIUser.Experience experience) {
    DynamicCounter.increment(
        "UserManagementFieldAccess", "context", context, "field", "experience");
    getBehavior().setExperience(experience);
    return this;
  }

  @Override
  public APIUserManagementListBuilder setAvatarName(String s) {
    DynamicCounter.increment(
        "UserManagementFieldAccess", "context", context, "field", "avatarName");
    getBehavior().setAvatarName(s);
    return this;
  }

  @Override
  public APIUserManagementListBuilder setMaturity(APIMaturity apiMaturity) {
    DynamicCounter.increment("UserManagementFieldAccess", "context", context, "field", "maturity");
    getBehavior().setMaturity(apiMaturity);
    return this;
  }

  @Override
  public APIUserManagementListBuilder setRawMaturityValue(Integer rawMaturityValue) {
    DynamicCounter.increment(
        "UserManagementFieldAccess", "context", context, "field", "rawMaturityValue");
    getBehavior().setMaturity(rawMaturityValue);
    return this;
  }

  @Override
  public APIUserProfileBuilder setAutoplayEnabled(Boolean autoplayEnabled) {
    DynamicCounter.increment(
        "UserManagementFieldAccess", "context", context, "field", "autoPlayEnabled");
    getBehavior().setAutoplayEnabled(autoplayEnabled);
    return this;
  }

  @Override
  public APIUserProfileBuilder setCookieDisclosureAccepted(Boolean cookieDisclosureAccepted) {
    getBehavior().setCookieDisclosureAccepted(cookieDisclosureAccepted);
    return this;
  }

  @Override
  public APIUserManagementListBuilder setFirstUse(boolean isFirstUse) {
    DynamicCounter.increment("UserManagementFieldAccess", "context", context, "field", "firstUse");
    getBehavior().setFirstUse(isFirstUse);
    return this;
  }

  @Override
  public APIUserManagementListBuilder setLanguage(String s) {
    DynamicCounter.increment("UserManagementFieldAccess", "context", context, "field", "language");
    getBehavior().setLanguage(s);
    return this;
  }

  @Override
  public APIUserManagementListBuilder setProfileLockPin(String profileLockPin) {
    DynamicCounter.increment(
        "UserManagementFieldAccess", "context", context, "field", "profileLockPin");
    getBehavior().setProfileLockPin(profileLockPin);
    return this;
  }

  @Override
  public APIUserManagementListBuilder removeProfileLockPin(Boolean removeLockPin) {
    DynamicCounter.increment(
        "UserManagementFieldAccess", "context", context, "field", "removeProfileLockPin");
    getBehavior().removeProfileLockPin(removeLockPin);
    return this;
  }

  @Override
  public APIUserProfileBuilder setProfileCreationLocked(Boolean locked) {
    DynamicCounter.increment(
        "UserManagementFieldAccess", "context", context, "field", "profileCreationLocked");
    getBehavior().setProfileCreationLocked(locked);
    return this;
  }
}
