package com.netflix.api.service.plan;

import com.google.common.base.Preconditions;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.platform.deprecated.DeprecatedMethodTracker;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIPlanInfo;
import java.util.List;

@EnableDeprecatedMetrics
public class APIPlanInfoImpl implements APIPlanInfo {

  private final PlanData planData;

  APIPlanInfoImpl(Long subscriptionId, PlanData planData) {
    Preconditions.checkNotNull(subscriptionId);
    this.planData = planData;
  }

  @Override
  @Deprecated
  public boolean isHDEnabled() {
    DeprecatedMethodTracker.incrementDeprecatedMetrics(
        APIPlanInfoImpl.class.getName(),
        "isHDEnabled",
        List.of(),
        APIRequest.getCurrentRequest().getRequestContext().getEndpointPath());
    return planData.isHDEnabled();
  }

  @Override
  @Deprecated
  public boolean isUHDEnabled() {
    DeprecatedMethodTracker.incrementDeprecatedMetrics(
        APIPlanInfoImpl.class.getName(),
        "isUHDEnabled",
        List.of(),
        APIRequest.getCurrentRequest().getRequestContext().getEndpointPath());
    return planData.isUHDEnabled();
  }
}
