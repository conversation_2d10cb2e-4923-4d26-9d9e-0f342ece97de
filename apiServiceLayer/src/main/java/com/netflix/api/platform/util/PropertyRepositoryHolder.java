package com.netflix.api.platform.util;

import com.netflix.archaius.api.PropertyRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Hack that allows keeping archaius2 properties static, when migrating from archaius1, in places where it's hard to make them non-static.
 */
@Component
public class PropertyRepositoryHolder {

  private static PropertyRepository propertyRepository;

  @Autowired
  public PropertyRepositoryHolder(PropertyRepository propertyRepository) {
    PropertyRepositoryHolder.propertyRepository = propertyRepository;
  }

  public static PropertyRepository getPropertyRepository() {
    return propertyRepository;
  }
}
