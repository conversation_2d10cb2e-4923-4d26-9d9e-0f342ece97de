package com.netflix.api.service.video;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/** Encapsulates request to get a dynimo artwork url. Use the Builder to build the request. */
public class APIDynimoArtworkRequest {
  private final int width;
  private final int height;
  private final String type;
  private final boolean secure;
  private final int videoId;
  private final Map<String, Object> optionalParameters;
  private final String country;
  private final String locale;
  private final boolean countrySet;
  private final boolean localeSet;

  private APIDynimoArtworkRequest(
      int videoId,
      String type,
      int width,
      int height,
      boolean secure,
      Map<String, Object> optionalParameters,
      String country,
      String locale,
      boolean countrySet,
      boolean localeSet) {
    this.height = height;
    this.type = type;
    this.secure = secure;
    this.videoId = videoId;
    this.width = width;
    this.optionalParameters = optionalParameters;
    this.country = country;
    this.locale = locale;
    this.countrySet = countrySet;
    this.localeSet = localeSet;
  }

  public int getWidth() {
    return width;
  }

  public int getHeight() {
    return height;
  }

  public String getType() {
    return type;
  }

  public boolean isSecure() {
    return secure;
  }

  public int getVideoId() {
    return videoId;
  }

  public String getCountry() {
    return country;
  }

  public String getLocale() {
    return locale;
  }

  public boolean isCountrySet() {
    return countrySet;
  }

  public boolean isLocaleSet() {
    return localeSet;
  }

  public Map<String, Object> getParameters() {
    return optionalParameters;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIDynimoArtworkRequest that)) {
      return false;
    }
    return getWidth() == that.getWidth()
        && getHeight() == that.getHeight()
        && isSecure() == that.isSecure()
        && getVideoId() == that.getVideoId()
        && isCountrySet() == that.isCountrySet()
        && isLocaleSet() == that.isLocaleSet()
        && Objects.equals(getType(), that.getType())
        && Objects.equals(optionalParameters, that.optionalParameters)
        && Objects.equals(getCountry(), that.getCountry())
        && Objects.equals(getLocale(), that.getLocale());
  }

  @Override
  public int hashCode() {
    return Objects.hash(
        getWidth(),
        getHeight(),
        getType(),
        isSecure(),
        getVideoId(),
        optionalParameters,
        getCountry(),
        getLocale(),
        isCountrySet(),
        isLocaleSet());
  }

  @Override
  public String toString() {
    return "APIDynimoArtworkRequest{"
        + "width="
        + width
        + ", height="
        + height
        + ", type='"
        + type
        + '\''
        + ", secure="
        + secure
        + ", videoId="
        + videoId
        + ", optionalParameters="
        + optionalParameters
        + ", country='"
        + country
        + '\''
        + ", locale='"
        + locale
        + '\''
        + ", countrySet="
        + countrySet
        + ", localeSet="
        + localeSet
        + '}';
  }

  public static class Builder {
    private int width;
    private int height;
    private String type;
    private boolean secure;
    private int videoId;
    private Map<String, Object> optionalParameters;
    private String country;
    private String locale;
    private boolean countrySet;
    private boolean localeSet;

    public Builder setWidth(int width) {
      this.width = width;
      return this;
    }

    public Builder setHeight(int height) {
      this.height = height;
      return this;
    }

    /**
     * Set artwork type
     *
     * @param imageType
     * @return
     */
    public Builder setImageType(String imageType) {
      this.type = imageType;
      return this;
    }

    public Builder setSecure(boolean secure) {
      this.secure = secure;
      return this;
    }

    public Builder setVideoId(int videoId) {
      this.videoId = videoId;
      return this;
    }

    public Builder addParameter(String key, Object value) {
      if (key == null) return this;
      if (optionalParameters == null) optionalParameters = new HashMap<>();
      optionalParameters.put(key, value);
      return this;
    }

    public void setCountry(String country) {
      this.country = country;
      this.countrySet = true;
    }

    public void setLocale(String locale) {
      this.locale = locale;
      this.localeSet = true;
    }

    public APIDynimoArtworkRequest build() {
      return new APIDynimoArtworkRequest(
          videoId,
          type,
          width,
          height,
          secure,
          optionalParameters,
          country,
          locale,
          countrySet,
          localeSet);
    }
  }
}
