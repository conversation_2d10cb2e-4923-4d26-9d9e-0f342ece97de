package com.netflix.api.service;

import com.google.common.base.Preconditions;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.ibm.icu.util.Calendar;
import com.ibm.icu.util.GregorianCalendar;
import com.ibm.icu.util.LocaleData;
import com.ibm.icu.util.TimeZone;
import com.ibm.icu.util.ULocale;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.i18n.NFFormattingHelper;
import com.netflix.i18n.NFLocale;
import com.netflix.type.ISOCountry;
import java.text.DateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class APILocaleImpl implements APILocale {
  // Do NOT leak the NFLocale from this class
  private final NFLocale locale;

  private static final LoadingCache<ULocale, LocaleData> localeDataCache =
      CacheBuilder.newBuilder()
          .build(
              new CacheLoader<>() {
                @Override
                public LocaleData load(ULocale key) {
                  return LocaleData.getInstance(key);
                }
              });

  public APILocaleImpl(NFLocale locale) {
    this.locale = Preconditions.checkNotNull(locale, "Locale cannot be null");
  }

  @Override
  public String getId() {
    return locale.getId();
  }

  @Override
  public String getCountry() {
    return locale.getCountry();
  }

  @Override
  public String getLanguage() {
    return locale.getLanguage();
  }

  @Override
  public String toLanguageTag() {
    return locale.toLanguageTag();
  }

  @Override
  public String getName() {
    return locale.getName();
  }

  @Override
  public String getDisplayName() {
    return locale.getDisplayName();
  }

  @Override
  public String getDisplayName(APILocale locale) {
    return this.locale.getDisplayName(((APILocaleImpl) locale).locale);
  }

  @Override
  public String getDisplayNameWithDialect() {
    return locale.getDisplayNameWithDialect();
  }

  @Override
  public APILocale getFallback() {
    return new APILocaleImpl(locale.getFallback());
  }

  @Override
  public List<APILocale> getFallbacks() {
    var locales = locale.getFallbacks();
    if (locales == null) return List.of();

    return locales.stream().map(APILocaleImpl::new).map(APILocale.class::cast).toList();
  }

  @Override
  public String getDisplayNameWithDialect(boolean standAlone) {
    return locale.getDisplayNameWithDialect(standAlone);
  }

  @Override
  public String getDisplayNameWithDialect(APILocale locale) {
    return this.locale.getDisplayNameWithDialect(((APILocaleImpl) locale).locale);
  }

  @Override
  public String getDisplayNameWithDialect(APILocale locale, boolean standAlone) {
    return this.locale.getDisplayNameWithDialect(((APILocaleImpl) locale).locale, standAlone);
  }

  @Override
  public boolean isRightToLeft() {
    return locale.toULocale().isRightToLeft();
  }

  @Deprecated
  @Override
  public DateFormat getDateInstance(int style) {
    ISOCountry country = RequestContextWrapper.get().getCountry();
    if (country != null) {
      return DateFormat.getDateInstance(style, toLocale(country.getId()));
    } else {
      return DateFormat.getDateInstance(style, toFormattingLocale());
    }
  }

  @Override
  public String formatDate(int style, Date date) {
    return formatDate(style, date, null);
  }

  @Override
  public String formatDate(int style, Date date, String timeZone) {
    return formatDate(style, date, timeZone, true);
  }

  @Override
  public String formatDate(int style, Date date, String timeZone, boolean useGregorian) {
    return formatDateTime(style, com.ibm.icu.text.DateFormat.NONE, date, timeZone, useGregorian);
  }

  @Override
  public String formatDateTime(int dateStyle, int timeStyle, Date date) {
    return formatDateTime(dateStyle, timeStyle, date, null);
  }

  @Override
  public String formatDateTime(int dateStyle, int timeStyle, Date date, String timeZone) {
    return formatDateTime(dateStyle, timeStyle, date, timeZone, true);
  }

  @Override
  public String formatDateTime(
      int dateStyle, int timeStyle, Date date, String timeZone, boolean useGregorian) {
    com.ibm.icu.text.DateFormat dateFormat;
    Locale loc;

    TimeZone userTimeZone = TimeZone.getDefault();
    if (timeZone != null) {
      userTimeZone = TimeZone.getTimeZone(timeZone);
    }

    ISOCountry country = RequestContextWrapper.get().getCountry();
    if (country != null) {
      loc = toLocale(country.getId());
    } else {
      loc = toFormattingLocale();
    }

    dateFormat = com.ibm.icu.text.DateFormat.getDateTimeInstance(dateStyle, timeStyle, loc);

    dateFormat = NFFormattingHelper.formatOverrides(dateFormat);

    if (useGregorian) {
      Calendar gc = new GregorianCalendar(userTimeZone, ULocale.forLocale(loc));
      dateFormat.setCalendar(gc);
    } else {
      dateFormat.setTimeZone(userTimeZone);
    }

    return dateFormat.format(date);
  }

  @Override
  public Locale toLocale() {
    return locale.toLocale();
  }

  @Override
  public String getLocaleSeparator() {
    return localeDataCache.getUnchecked(locale.toULocale()).getLocaleSeparator();
  }

  @Override
  public Locale toFormattingLocale() {
    return locale.toFormattingLocale();
  }

  @Override
  public Locale toLocale(String defaultCountry) {
    Locale newlocale = locale.toFormattingLocale();
    String country = newlocale.getCountry();
    if (country == null || country.isEmpty()) {
      if (defaultCountry != null) {
        newlocale =
            NFLocale.getLocale(locale.getLanguage(), locale.getScript(), defaultCountry)
                .toFormattingLocale();
      }
    }
    return newlocale;
  }

  @Override
  public String toString() {
    return locale.toLanguageTag();
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((locale == null) ? 0 : locale.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    APILocaleImpl other = (APILocaleImpl) obj;
    if (locale == null) {
      if (other.locale != null) return false;
    } else if (!locale.equals(other.locale)) return false;
    return true;
  }

  public NFLocale getNFLocale() {
    return locale;
  }
}
