package com.netflix.api.endpoint;

import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.netflix.lang.BindingContexts;
import com.netflix.servo.DefaultMonitorRegistry;
import com.netflix.servo.monitor.CompositeMonitor;
import com.netflix.servo.monitor.Counter;
import com.netflix.servo.monitor.Monitors;
import com.netflix.servo.monitor.Stopwatch;
import com.netflix.servo.monitor.Timer;
import com.netflix.servo.tag.BasicTagList;
import io.netty.util.HashedWheelTimer;
import io.netty.util.Timeout;
import java.util.Optional;
import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class EndpointThreadTask {

  private static final Counter requestTaskTimeoutCount =
      Monitors.newCounter(
          "api.endpoint.request.task.status", () -> BasicTagList.of("status", "timeout"));
  private static final Counter requestTaskWaitingCount =
      Monitors.newCounter(
          "api.endpoint.request.task.status", () -> BasicTagList.of("status", "waiting"));
  private static final Counter requestTaskCompleteCount =
      Monitors.newCounter(
          "api.endpoint.request.task.status", () -> BasicTagList.of("status", "completed"));
  private static final Counter requestTaskCreatedCount =
      Monitors.newCounter(
          "api.endpoint.request.task.status", () -> BasicTagList.of("status", "created"));

  private static final Counter requestTaskCancelAttempt =
      Monitors.newCounter("api.endpoint.request.task.cancelattempt");

  private static final Counter requestTaskCancelFailed =
      Monitors.newCounter("api.endpoint.request.task.taskcancelfailed");

  private static final Timer requestTaskTimer =
      Monitors.newTimer("api.endpoint.request.task.timer", TimeUnit.MILLISECONDS);

  private static final HashedWheelTimer timeoutTimer = new HashedWheelTimer();

  private static final ThreadFactory THREAD_FACTORY =
      new ThreadFactoryBuilder().setNameFormat("endpoint-timeout-thread-%d").build();

  private static final Integer THREADPOOL_SIZE_MIN = 5;

  private static final Integer THREADPOOL_SIZE_MAX = 20;

  private static final Long CANCELLATION_RETRY_WAIT = 1000l;

  private static final Boolean CANCELLATION_RETRY_ENABLE = true;

  private static final ThreadPoolExecutor timeoutExecutor =
      new ThreadPoolExecutor(
          THREADPOOL_SIZE_MIN,
          THREADPOOL_SIZE_MAX,
          60L,
          TimeUnit.SECONDS,
          new LinkedBlockingQueue<>(),
          THREAD_FACTORY);

  private static final CompositeMonitor<?> threadPoolMonitor =
      Monitors.newThreadPoolMonitor("api.endpoint.request.task.timeout.pool", timeoutExecutor);

  static {
    DefaultMonitorRegistry.getInstance().register(requestTaskTimeoutCount);
    DefaultMonitorRegistry.getInstance().register(requestTaskCompleteCount);
    DefaultMonitorRegistry.getInstance().register(requestTaskCreatedCount);
    DefaultMonitorRegistry.getInstance().register(threadPoolMonitor);
    DefaultMonitorRegistry.getInstance().register(requestTaskTimer);
  }

  /** Implements the required functionality to handle timeouts of a task */
  interface TimeoutTaskHandler {
    /**
     * A timeout occurred
     *
     * @param timeoutMillis how long was the time period
     * @throws TimeoutException if a specific exception must be generated on timeout
     */
    String reportTimeout(long timeoutMillis) throws TimeoutException;

    /**
     * Get the amount of time to way before timeout out. This method is called right before
     * scheduling
     *
     * @return long period in milliseconds
     */
    long getTimeoutMillis();

    /**
     * Cleanup method to release or cancel any resources or future.
     *
     * @return true if we should follow up on the cancellation
     */
    boolean requestCancel();

    /**
     * Try to cancel / interrupt the task again if needed
     *
     * @return true if we did manage to cancel (interrupt) the task or we've decided to give up.
     *     false means to we failed to interrupt and we should retry.
     */
    boolean tryCancel();
  }

  private final CompletableFuture<Void> future;
  private final TimeoutTaskHandler timeoutHandler;
  private final Stopwatch taskTimer;

  private EndpointThreadTask(CompletableFuture<Void> future, TimeoutTaskHandler timeoutHandler) {
    super();
    taskTimer = requestTaskTimer.start();
    this.future = future.whenComplete((_Void, e) -> complete());
    this.timeoutHandler = timeoutHandler;
  }

  private void timeout(long timeoutMillis) {
    Throwable reportException = null;
    String message = null;
    try {
      try {
        message = this.timeoutHandler.reportTimeout(timeoutMillis);
      } finally {
        requestTaskTimeoutCount.increment();
      }
    } catch (TimeoutException e) {
      reportException = e;
    } finally {
      this.future.completeExceptionally(
          reportException == null
              ? new TimeoutException(
                  message == null ? ("Expired after " + timeoutMillis + "ms") : message)
              : reportException);
    }
  }

  public boolean isDone() {
    return this.future.isDone();
  }

  private void complete() {
    taskTimer.stop();
    requestTaskCompleteCount.increment();
  }

  /**
   * Create a timeout handler that cancel the a future when it expired after `timeout`
   *
   * @param future future to cancel on timeout
   * @param timeout time period before the task time out
   * @param unit unit of time for the timeout value
   * @return handler
   */
  public static TimeoutTaskHandler cancelFuture(
      Future<?> future, String context, long timeout, TimeUnit unit) {
    return new TimeoutTaskHandler() {

      @Override
      public String reportTimeout(long timeoutMillis) {
        return "Expired after " + timeoutMillis + "ms (" + context + ")";
      }

      public boolean requestCancel() {
        future.cancel(true);
        // not need to retry to cancel latter for future.
        return false;
      }

      @Override
      public long getTimeoutMillis() {
        return unit.toMillis(timeout);
      }

      @Override
      public boolean tryCancel() {
        return future.isDone();
      }
    };
  }

  public long getTimeoutMillis() {
    return timeoutHandler.getTimeoutMillis();
  }

  /**
   * Create a new CompletableFuture that augment the task completable future to complete
   * exceptionally if it's not completed within a time period
   *
   * @return CompletableFuture that complete exceptionally if not completed in time
   */
  @SuppressWarnings({"deprecation", "OptionalUsedAsFieldOrParameterType"})
  public CompletableFuture<Void> futureWithTimeout(Optional<Long> timeLeftMillis) {
    requestTaskWaitingCount.increment();
    long timeout = Math.min(getTimeoutMillis(), timeLeftMillis.orElse(Long.MAX_VALUE));
    Runnable timeoutRunnable = BindingContexts.makeClosure(() -> timeout(timeout));
    final Timeout timerHandle =
        timeoutTimer.newTimeout(
            timeoutTask -> timeoutExecutor.execute(timeoutRunnable),
            timeout,
            TimeUnit.MILLISECONDS);
    return future.whenComplete(
        (_Void, e) -> {
          if (!timerHandle.cancel()) {
            // weren't able to cancel the task, the task is either cancelled or expired
            // we should cancel the underlying future
            if (this.timeoutHandler.requestCancel()) {
              if (CANCELLATION_RETRY_ENABLE) {
                scheduleCancellationFollowUp(true, this.timeoutHandler);
              }
            }
          }
        });
  }

  private void scheduleCancellationFollowUp(
      final boolean firstTry, final TimeoutTaskHandler timeoutHandler) {
    // The task did cancel and we've requested a cancellation
    // Let make sure we did cancel as expected
    timeoutTimer.newTimeout(
        timeoutTask -> {
          if (!timeoutHandler.tryCancel()) {
            // The cancellation failed, lets retry again later.
            if (firstTry) {
              requestTaskCancelFailed.increment();
            }
            scheduleCancellationFollowUp(false, timeoutHandler);
            requestTaskCancelAttempt.increment();
          }
        },
        CANCELLATION_RETRY_WAIT,
        TimeUnit.MILLISECONDS);
  }

  /**
   * Create a new EndpointThreadTask from an existing CompletableFuture and TimeoutHandler
   *
   * <p>CompletableFuture that complete exceptionally will fail the entire request
   *
   * @param task CompletableFuture task monitoring a task completion
   * @param timeoutHandler that handle the timeout
   * @return EndpointThreadTask
   */
  public static EndpointThreadTask create(
      CompletableFuture<Void> task, TimeoutTaskHandler timeoutHandler) {
    requestTaskCreatedCount.increment();
    return new EndpointThreadTask(task, timeoutHandler);
  }

  /**
   * Create EndpointThreadTask from a ListenableFuture that cancel the future when the task is
   * timing out
   *
   * <p>ListenableFuture that throws an exception will fail the entire request
   *
   * @return EndpointThreadTask
   */
  public static EndpointThreadTask create(ListenableFuture<?> future, String context) {
    CompletableFuture<Void> completableFuture = new CompletableFuture<>();
    future.addListener(
        () -> {
          try {
            future.get();
            completableFuture.complete(null);
          } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            completableFuture.completeExceptionally(e);
          } catch (ExecutionException | CancellationException e) {
            completableFuture.completeExceptionally(e);
          }
        },
        MoreExecutors.directExecutor());

    return EndpointThreadTask.create(
        completableFuture,
        EndpointThreadTask.cancelFuture(
            future,
            context,
            EndpointThreadCommandBase.defaultExecutionTimeoutInMilliseconds.get(),
            TimeUnit.MILLISECONDS));
  }

  public static EndpointThreadTask create(
      CompletableFuture<Void> completableFuture, String context) {
    return EndpointThreadTask.create(
        completableFuture,
        EndpointThreadTask.cancelFuture(
            completableFuture,
            context,
            EndpointThreadCommandBase.defaultExecutionTimeoutInMilliseconds.get(),
            TimeUnit.MILLISECONDS));
  }

  public Throwable getException() {
    if (this.future.isCompletedExceptionally()) {
      try {
        this.future.get();
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        return e;
      } catch (ExecutionException e) {
        Throwable cause = e.getCause();
        if (cause == null) return e;
        return cause;
      }
    }
    return null;
  }
}
