package com.netflix.api.adapters;

import com.netflix.api.platform.util.CompletionStageAdapter;
import com.netflix.api.platform.util.RequestScopedSingle;
import com.netflix.geoclient.GeoDataImpl;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.init.resolvers.geo.Geos;
import com.netflix.streaming.cdnsteer.CdnUrlParamsFactory;
import com.netflix.streaming.cdnsteer.CdnUrlSteeringService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Single;

@Component
public class StreamingClientServiceAdapter {
  private static final Logger logger = LoggerFactory.getLogger(StreamingClientServiceAdapter.class);
  private static final RequestScopedSingle<String> cachedHostName = new RequestScopedSingle<>();

  private final CdnUrlSteeringService cdnUrlSteeringService;
  private final CdnUrlParamsFactory cdnUrlParamsFactory;

  @Autowired
  public StreamingClientServiceAdapter(
      final CdnUrlSteeringService cdnUrlSteeringService,
      final CdnUrlParamsFactory cdnUrlParamsFactory) {
    this.cdnUrlSteeringService = cdnUrlSteeringService;
    this.cdnUrlParamsFactory = cdnUrlParamsFactory;
  }

  public Single<String> getHostName() {
    Single<String> response = cachedHostName.get().get();
    if (response != null) {
      logger.trace("got hostname from req scoped cache");
      return response;
    }

    response = getHostnameFromCodaGrpc();

    return cachedHostName.get().compareAndSet(null, response)
        ? response
        : cachedHostName.get().get();
  }

  private Single<String> getHostnameFromCodaGrpc() {
    final var geo = new GeoDataImpl(Geos.attributeMap(CurrentMicrocontext.get().getGeo()));
    final var cdnURLParams = cdnUrlParamsFactory.fromMicrocontext();

    return CompletionStageAdapter.toSingle(
            cdnUrlSteeringService.getSodHostnameAsync(cdnURLParams, geo, false))
        .onErrorReturn(
            (th) -> {
              logger.error("failed to get hostname from coda grpc", th);
              return "";
            })
        .doOnSuccess(hostname -> logger.debug("got hostname {} from coda", hostname));
  }
}
