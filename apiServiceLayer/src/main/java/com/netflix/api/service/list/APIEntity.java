package com.netflix.api.service.list;

/**
 * A base class for all business entities like {@link com.netflix.api.service.video.APIVideo}
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIEntity.class.png"
 * alt="APIEntity class diagram">
 */
public interface APIEntity {

  /**
   * Get the name (or display title) for this entity.
   *
   * @return the name of this entity as a String
   */
  String getName();

  /**
   * Get the ID of this entity type.
   *
   * @return the ID of this content type of object. Can be {@code null} if the entity cannot be
   *     looked up by ID. You should not cache IDs as they change and migrate often.
   */
  Object getId();
}
