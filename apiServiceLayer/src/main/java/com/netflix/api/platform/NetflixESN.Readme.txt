#
# The NetflixESN prefix grouping tries to groups things for better display and consumption.
#
# It uses the data from NRD Portal and just hardcodes it into code (bad) to get the job done for now.
#
# This really should be a service that exists somewhere at Netflix to do this for us.

# JSON comes from: https://portal.nrd.netflix.com/nrdportal/deviceModel/jsonList?_search=false&nd=1346445643008&rows=10000&page=1&sidx=brand.name&sord=asc&cb=1346445643010

# Python script for generating code that gets copy/pasted into the bottom of NetflixESN.java is: 

########################################################################

#!/usr/bin/env python

import json

if __name__ == "__main__":
    f = open("./esn.json", "r")
    json_text = f.read()
    #print json_text
    esn = json.loads(json_text)

    for rowData in esn["rows"]:
        print "esnPrefixGrouping.put(\"" + str(rowData['deviceType']['esnPrefix']).replace("\-", "-") + "\", \"" + str(rowData['deviceCategory']['name']).replace(" ", "_") +"\");"
    

########################################################################
