package com.netflix.api.service.interactive;

public class APIPlaybackImpressionContextImpl implements APIPlaybackImpressionContext {

  protected APIPlaybackImpressionData data;
  protected String impressionType;

  @Override
  public APIPlaybackImpressionContext setPlaybackImpressionData(APIPlaybackImpressionData data) {
    this.data = data;
    return this;
  }

  @Override
  public APIPlaybackImpressionContext setImpressionType(String impressionType) {
    this.impressionType = impressionType;
    return this;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((data == null) ? 0 : data.hashCode());
    result = prime * result + ((impressionType == null) ? 0 : impressionType.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    APIPlaybackImpressionContextImpl other = (APIPlaybackImpressionContextImpl) obj;
    if (data == null) {
      if (other.data != null) return false;
    } else if (!data.equals(other.data)) return false;
    if (impressionType == null) {
      if (other.impressionType != null) return false;
    } else if (!impressionType.equals(other.impressionType)) return false;
    return true;
  }

  @Override
  public String toString() {
    return "APIPlaybackImpressionContextImpl [data="
        + data
        + ", impressionType="
        + impressionType
        + "]";
  }

  @Override
  public APIPlaybackImpressionData getData() {
    return data;
  }

  @Override
  public String getImpressionType() {
    return impressionType;
  }
}
