package com.netflix.api.service.batch;

import com.netflix.api.service.batch.model.APIAvatarKey;
import com.netflix.api.service.batch.model.APIProfileIconCriteria;
import com.netflix.api.service.video.APIImage;
import java.util.Collection;
import java.util.Map;
import rx.Single;

/**
 * This service sources personalized images displayed on the Netflix site, including
 * video/person/character artwork, interesting stills, and hero images (a mix of artwork, studio
 * stills and interesting stills).
 *
 * <p>Interfaces are exposed
 */
public interface APIImageService {

  /**
   * Returns the icons for the input, the resulting map will be indexed by the iconNames from the
   * parameters, if an icon is not found a fallback icon will be emitted in it's place
   *
   * @param keys the keys of the icons to fetch
   * @param critera the specification of the images to be returned
   * @return the map of profile icons in a {@link Single}
   */
  Single<Map<APIAvatarKey, APIImage>> getProfileIconsByKey(
      Collection<APIAvatarKey> keys, APIProfileIconCriteria critera);
}
