package com.netflix.api.endpoint;

import static com.netflix.api.platform.MicrocontextFlags.MICROCONTEXT_SHADOW_ENABLED;
import static com.netflix.servo.monitor.DynamicCounter.increment;

import com.google.common.annotations.VisibleForTesting;
import com.netflix.api.platform.context.ImmutableRequestContext;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.context.RequestContextWrapper.Builder;
import com.netflix.api.platform.util.ParamConstants;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APILocale;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.service.ListItem;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.util.ServiceUtils;
import com.netflix.archaius.api.Property;
import com.netflix.geoclient.CurrentGeoData;
import com.netflix.geoclient.GeoData;
import com.netflix.i18n.LocaleContext;
import com.netflix.i18n.NFLocale;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.ContextUtils;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.type.DeviceType;
import com.netflix.type.ISOCountry;
import com.netflix.type.NFCountry;
import com.netflix.type.proto.Locales;
import com.netflix.type.protogen.BasicTypes;
import com.netflix.type.protogen.BasicTypes.Locale;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import net.jcip.annotations.ThreadSafe;
import netflix.context.Context;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import rx.Observable;

/**
 * Provides functionality for each request to initialize contexts that are required by the Netflix
 * stack.
 *
 * <p>For example, this sets up CurrentRequestContext and ContentRegionContext.
 *
 * <p>This should be invoked before every EndpointScript execution.
 */
@ThreadSafe
@Component
public class EndpointContextInitializerImpl implements EndpointContextInitializer {
  public static final String LANGUAGE_COUNTRY_SEPARATOR = "-";
  private static final Logger logger =
      LoggerFactory.getLogger(EndpointContextInitializerImpl.class);

  // This denotes a request that we track separately in terms of our metrics
  // and that downstream dependencies may or may not handle differently.
  private static final String BULK_REQUEST = "API_Bulk_Request";

  private static final String COUNTRY_PARAM = "country";

  public static final String BOT_LOCALE_HEADER = "x-zuul-seo-prefix";
  public static final String BOT_LEGACY_COUNTRY = "x-zuul-seo-country";
  public static final String BOT_LEGACY_LANGUAGE = "x-zuul-seo-language";
  private static final Property<Boolean> ENABLE_LOCALE_METRICS =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.locales.metrics.enabled", Boolean.class)
          .orElse(false);

  private final LocaleLookup localeLookup;

  EndpointContextInitializerImpl() {
    this(new ProdLocaleLookup());
  }

  @VisibleForTesting
  EndpointContextInitializerImpl(LocaleLookup localeLookup) {
    this.localeLookup = localeLookup;
  }

  /** Initialize or re-initialize the locales in the request context. */
  public void initializeContextLocales(HttpServletRequest request, APIUser user) {
    ImmutableRequestContext ctx = RequestContextWrapper.get();

    final String locale = getLocale(request, ctx.getCountry(), user, ctx.getLocaleList());

    // if the resolved locale already matches the current locale, don't bother setting
    if (locale != null && !locale.equals(ctx.getLocaleList())) {
      if (MICROCONTEXT_SHADOW_ENABLED.get()) {
        try {
          Microcontext microcontext = CurrentMicrocontext.get();
          final String micro =
              microcontext.getLocales().stream()
                  .map(Locale::getId)
                  .collect(Collectors.joining(","));
          if (Objects.equals(locale, micro)) {
            increment("api.localeresolution.shadow", "result", "true");
          } else {
            List<Locale> collect =
                RequestContextWrapper.convertLocales(locale).stream()
                    .map(NFLocale::getId)
                    .map(Locales::toProtobuf)
                    .toList();
            increment("api.localeresolution.shadow", "result", "false");
            Context.Builder builder =
                ContextUtils.toBuilder(microcontext).clearLocales().addAllLocales(collect);
            CurrentMicrocontext.set(builder.build());
          }
        } catch (Exception e) {
          logger.error("Could not initialize microcontext", e);
        }
      }
      RequestContextWrapper.createAndSet(Builder.of(ctx).localeList(locale));
      LocaleContext.setLocalContext(locale);
      CurrentRequestContext.get().setLocaleList(locale);
    }
  }

  /**
   * Initializations that underlying dependencies require which use CurrentRequestContext. If null
   * user is provided we will derive the locales solely from request and the request context.
   * Otherwise, the account provide will be used as input parameter to resolve the locales
   */
  @Override
  public void initializeContext(HttpServletRequest request, APIUser user) {
    final GeoData geoData = CurrentGeoData.get();

    // set the country in the CurrentRequestContext
    final ISOCountry isoCountry =
        getCountry(request, geoData, CurrentRequestContext.get().getCountry());

    final String locale =
        getLocale(request, isoCountry, user, CurrentRequestContext.get().getLocaleList());

    // request id
    final String requestId = CurrentRequestContext.get().getRequestId();

    // device information
    final String deviceId = CurrentRequestContext.get().getDeviceId();
    final DeviceType deviceType = CurrentRequestContext.get().getDeviceType();

    // create and set request context wrapper for this request (note this will also set
    // CurrentRequestContext for compatibility with clients
    ImmutableRequestContext newcontext =
        RequestContextWrapper.createAndSet(
            Builder.newInstance()
                .country(isoCountry)
                .localeList(locale)
                .requestId(requestId)
                .deviceId(deviceId)
                .deviceType(deviceType));

    // compare microcontext to the newly resolved requestcontextwrapper
    compare(CurrentMicrocontext.get(), newcontext);

    // also set country and locale in their respective caches
    CurrentRequestContext.get().setCountry(isoCountry);
    LocaleContext.setLocalContext(locale);
    CurrentRequestContext.get().setLocaleList(locale);

    // Set context for bulk request if appropriate
    if (isBulkRequest(request)) {
      CurrentRequestContext.get().setAppName(BULK_REQUEST);
    }
  }

  private void compare(Microcontext micro, ImmutableRequestContext wrapper) {
    try {
      boolean country =
          Objects.equals(
              micro.getCountry() != null ? micro.getCountry().getId() : null,
              wrapper.getCountry() != null ? wrapper.getCountry().getId() : null);
      increment(
          "api.contexts.compare.detail", "field", "country", "result", Boolean.toString(country));
      List<String> shadow = micro.getLocales().stream().map(Locale::getId).toList();
      List<String> real =
          RequestContextWrapper.convertLocales(wrapper.getLocaleList()).stream()
              .map(NFLocale::getId)
              .toList();
      boolean locales = Objects.equals(shadow, real);
      increment(
          "api.contexts.compare.detail", "field", "locales", "result", Boolean.toString(locales));
      if (!locales) {
        logger.debug("Micro shadow locale mismatch real {} shadow {}", real, shadow);
      }
      boolean esn = Objects.equals(micro.getEsn().orElse(null), wrapper.getDeviceId());
      if (!esn) {
        logger.debug(
            "Micro shadow esn mismatch real {} shadow {}", wrapper.getDeviceId(), micro.getEsn());
      }
      increment("api.contexts.compare.detail", "field", "esn", "result", Boolean.toString(esn));
      boolean deviceType =
          Objects.equals(
              micro.getDeviceType().map(BasicTypes.DeviceType::getId).orElse(0),
              wrapper.getDeviceType() != null ? wrapper.getDeviceType().getId() : 0);
      if (!deviceType) {
        logger.debug(
            "Micro shadow deviceType mismatch real {} shadow {}",
            wrapper.getDeviceType(),
            micro.getDeviceType());
      }
      increment(
          "api.contexts.compare.detail",
          "field",
          "deviceType",
          "result",
          Boolean.toString(deviceType));
      boolean overall = country && locales && esn && deviceType;
      increment("api.contexts.compare", "result", Boolean.toString(overall));
    } catch (Exception e) {
      increment("api.contexts.compare", "result", "fail");
      logger.info("Could not compare contexts", e);
    }
  }

  /**
   * Algorithm to choose country of request 1. Use Geo-location 2. Use HTTP query param. Requires
   * parsing, so may fail 3. Use the country from the RequestContext that was already set elsewhere
   * 4. Default to US
   *
   * @param request incoming HTTP request
   * @param geoData geo-location that happened upstream
   * @param reqContextCountry country from request context
   * @return country we tie request to, according to above algorithm
   */
  static ISOCountry getCountry(
      final HttpServletRequest request, final GeoData geoData, ISOCountry reqContextCountry) {
    // for bot based requests, set country to the code passed in the header (website is responsible
    // for validating a
    // bot request)
    if (ServiceUtils.isBot(request)) {
      ISOCountry seoCountry = getSeoCountry(request);
      if (seoCountry != null) return seoCountry;
    }
    /*Temporary support for LG Korea: DNA-1160*/
    if (ServiceUtils.isDETRequest(request)) {
      ISOCountry detCountry = getCountryFromHTTPParam(request);
      if (detCountry != null) return detCountry;
    }

    if (geoData != null) {
      ISOCountry countryFromGeo = geoData.getCountry();
      if (countryFromGeo != null) {
        return countryFromGeo;
      }
    }

    // country will only be null if geo lookup fails, and we do not have a default country
    // configured for the stack
    ISOCountry validCountryResolved = getCountryFromHTTPParam(request);

    if (validCountryResolved != null) {
      return validCountryResolved;
    } else {
      if (reqContextCountry != null) {
        return reqContextCountry;
      } else {
        return NFCountry.US;
      }
    }
  }

  private static ISOCountry getCountryFromHTTPParam(final HttpServletRequest request) {
    String countryFromHttp = getParam(request, COUNTRY_PARAM);
    if (countryFromHttp != null) {
      try {
        return NFCountry.findInstance(countryFromHttp);
      } catch (IllegalArgumentException iae) {
        if (logger.isWarnEnabled()) logger.warn("Invalid country parameter: {}", countryFromHttp);
      }
    }
    return null;
  }

  private static ISOCountry getSeoCountry(HttpServletRequest request) {
    NFCountry result;
    try {
      // try legacy first
      String countryCode = request.getHeader(BOT_LEGACY_COUNTRY);
      if (countryCode != null) {
        result = NFCountry.findInstance(countryCode);
        if (Objects.equals(result, NFCountry.AA))
          throw new APIServiceRuntimeException("AA is not an allowed country in bot headers!");
        return result;
      }

      // facebook bot code -- get seo prefix which is of the format language-country (e.g., en-US)
      String localeString = request.getHeader(BOT_LOCALE_HEADER);
      if (localeString != null) {
        NFLocale locale = NFLocale.findInstance(localeString);
        if (locale != null && locale.getCountry() != null) {
          result = NFCountry.findInstance(locale.getCountry());
          if (Objects.equals(result, NFCountry.AA))
            throw new APIServiceRuntimeException("AA is not an allowed country in bot headers!");
          return result;
        }
      }
    } catch (Exception e) {
      logger.error("Error getting seo country, rejecting request", e);
      throw e;
    }
    return null;
  }

  /**
   * Algorithm for choosing locale of an incoming HTTP request 1. Use the HTTP query param for key
   * "languages" 2. Find the user's preferred locales and use those 3. Use
   * NFLocale.getMatchingLocales on the previously looked-up country 4. Use
   * NFLocale.getSUpportedLocales on the previously looked-up country 5. Use the locales already in
   * the RequestContext
   *
   * @param request incoming HTTP request
   * @param resolvedCountry country of HTTP request {@see getCountry()}
   * @param user user in HTTP request
   * @param reqContextLocaleList locale list already set in RequestContext
   * @return locale of the HTTP request, according to above algorithm
   */
  String getLocale(
      HttpServletRequest request,
      ISOCountry resolvedCountry,
      APIUser user,
      String reqContextLocaleList) {
    // for bot requests, set locale to the locale passed by the bot
    if (ServiceUtils.isBot(request)) {
      NFLocale seoLocale = getSeoLocale(request);
      if (seoLocale != null) return seoLocale.getId();
    }
    // now try the request parameter
    String localeFromHttp = getParam(request, ParamConstants.LANGUAGES);
    if (localeFromHttp != null) {
      emitLocaleMetrics("source", "param");
      return localeFromHttp;
    } else {
      List<NFLocale> userLocales = getUserLocales(user);
      if (userLocales != null && !userLocales.isEmpty()) {
        emitLocaleMetrics("source", "user");
        return formattedLocaleList(userLocales);
      } else {
        List<NFLocale> matchingLocales = localeLookup.getMatchingLocales(resolvedCountry);
        if (matchingLocales != null) {
          emitLocaleMetrics("source", "country", "endpoint", "matching");
          return formattedLocaleList(matchingLocales);
        } else {
          List<NFLocale> supportedLocales = localeLookup.getSupportedLocales(resolvedCountry);
          if (supportedLocales != null) {
            emitLocaleMetrics("source", "country", "endpoint", "supported");
            return formattedLocaleList(supportedLocales);
          } else {
            emitLocaleMetrics("source", "none_found");
          }
        }
      }
      return reqContextLocaleList;
    }
  }

  private void emitLocaleMetrics(String tag1, String tag2) {
    if (ENABLE_LOCALE_METRICS.get()) {
      DynamicCounter.increment("api.locale.init", tag1, tag2);
    }
  }

  private void emitLocaleMetrics(String tag1, String tag2, String tag3, String tag4) {
    if (ENABLE_LOCALE_METRICS.get()) {
      DynamicCounter.increment("api.locale.init", tag1, tag2, tag3, tag4);
    }
  }

  private NFLocale getSeoLocale(HttpServletRequest request) {
    try {
      // try legacy first
      String countryCode = request.getHeader(BOT_LEGACY_COUNTRY);
      String language = request.getHeader(BOT_LEGACY_LANGUAGE);
      if (countryCode != null && language != null) {
        return NFLocale.findInstance(language + LANGUAGE_COUNTRY_SEPARATOR + countryCode);
      }

      // facebook bot code -- get seo prefix which is of the format language-country (e.g., en-US)
      String localeString = request.getHeader(BOT_LOCALE_HEADER);
      if (localeString != null) {
        return NFLocale.findInstance(localeString);
      }
    } catch (Exception e) {
      logger.warn("Error getting seo locale, resorting to default country", e);
    }
    return null;
  }

  private static List<NFLocale> getUserLocales(APIUser user) {
    if (user != null) {
      Observable<ListItem<APILocale>> userPreferences;
      try {
        userPreferences = user.getPreferredLocales();
      } catch (Exception t) {
        return List.of();
      }
      return userPreferences
          .map(ListItem::getItem)
          .map(apiLocale -> NFLocale.findInstance(apiLocale.getId()))
          .filter(Objects::nonNull)
          .toList()
          .onErrorReturn(t1 -> Collections.emptyList())
          .toBlocking()
          .first();
    }

    return List.of();
  }

  private static String formattedLocaleList(List<NFLocale> localeList) {
    StringBuilder sb = new StringBuilder();
    if (localeList != null && !localeList.isEmpty()) {
      for (NFLocale locale : localeList) {
        if (!sb.isEmpty()) {
          sb.append(',');
        }
        sb.append(locale.getId());
      }
    }
    return sb.toString();
  }

  private static boolean isBulkRequest(HttpServletRequest request) {
    String ret = request.getParameter("bulk");
    if (ret == null || ret.isEmpty()) {
      return false;
    } else {
      return Boolean.parseBoolean(ret);
    }
  }

  private static String getParam(HttpServletRequest request, String paramName) {
    String paramValue = request.getParameter(paramName);
    if (paramValue == null || paramValue.isEmpty()) {
      return null;
    } else {
      return paramValue;
    }
  }

  interface LocaleLookup {
    List<NFLocale> getSupportedLocales(ISOCountry country);

    List<NFLocale> getMatchingLocales(ISOCountry country);
  }

  private static class ProdLocaleLookup implements LocaleLookup {

    @Override
    public List<NFLocale> getSupportedLocales(ISOCountry country) {
      return NFLocale.getSupportedLocales(country);
    }

    @Override
    public List<NFLocale> getMatchingLocales(ISOCountry country) {
      return NFLocale.getMatchingLocaleList(
          new ArrayList<>(), null, country.getId(), NFLocale.MATCH_UI_AND_MESSAGES);
    }
  }
}
