package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CreatorHomeSection extends SearchPageSection {
  private Long creatorHomeId;

  private List<SearchPageVideo> videos;

  public Long getCreatorHomeId() {
    return creatorHomeId;
  }

  public List<SearchPageVideo> getVideos() {
    return videos;
  }

  public CreatorHomeSection setCreatorHomeId(Long creatorHomeId) {
    this.creatorHomeId = creatorHomeId;
    return this;
  }

  public CreatorHomeSection setVideos(List<SearchPageVideo> videos) {
    this.videos = videos;
    return this;
  }
}
