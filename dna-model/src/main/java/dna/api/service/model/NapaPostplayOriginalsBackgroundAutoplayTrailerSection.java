package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NapaPostplayOriginalsBackgroundAutoplayTrailerSection
    extends NapaPostplayCommonSection {
  private SearchPageEntity entity;

  public SearchPageEntity getEntity() {
    return entity;
  }

  public NapaPostplayOriginalsBackgroundAutoplayTrailerSection setEntity(SearchPageEntity entity) {
    this.entity = entity;
    return this;
  }
}
