package com.netflix.api.service.uipersonalization;

import com.netflix.uipersonalization.protogen.HiddenModule;
import com.netflix.uipersonalization.protogen.HiddenModules;
import com.netflix.uipersonalization.protogen.MergeStrategy;
import java.util.List;

public class APIUIPersonalizationHiddenModulesImpl implements APIUIPersonalizationHiddenModules {
  private final HiddenModules hiddenModules;

  APIUIPersonalizationHiddenModulesImpl(HiddenModules hiddenModules) {
    this.hiddenModules = hiddenModules;
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#HiddenModules.hidden_module"
   */
  @Override
  public List<APIUIPersonalizationHiddenModule> getHiddenModules() {
    List<HiddenModule> list = this.hiddenModules.getHiddenModuleList();
    if (list == null) {
      return null;
    }
    return list.stream()
        .map(APIUIPersonalizationHiddenModuleImpl::new)
        .map(APIUIPersonalizationHiddenModule.class::cast)
        .toList();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "uipersonalization.proto#HiddenModules.merge_strategy"
   */
  @Override
  public APIUIPersonalizationMergeStrategy getMergeStrategy() {
    MergeStrategy mergeStrategy = this.hiddenModules.getMergeStrategy();
    if (mergeStrategy == null) {
      return null;
    }
    if (mergeStrategy == MergeStrategy.UNRECOGNIZED) {
      return APIUIPersonalizationMergeStrategy.UNRECOGNIZED;
    } else {
      try {
        return Enum.valueOf(APIUIPersonalizationMergeStrategy.class, mergeStrategy.name());
      } catch (IllegalArgumentException e) {
        return APIUIPersonalizationMergeStrategy.UNRECOGNIZED;
      }
    }
  }
}
