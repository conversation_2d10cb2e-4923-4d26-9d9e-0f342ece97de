package com.netflix.api.service.interactive;

import com.netflix.api.service.interactive.APIInteractiveExperienceService.APIPrePlayAnnotation;

public class APIPrePlayAnnotationImpl implements APIPrePlayAnnotation {

  private final boolean supportsTutorial;
  private final String interactiveDeviceType;

  public APIPrePlayAnnotationImpl(boolean supportsTutorial, String interactiveDeviceType) {
    super();
    this.supportsTutorial = supportsTutorial;
    this.interactiveDeviceType = interactiveDeviceType;
  }

  @Override
  public boolean supportsTutorial() {
    return supportsTutorial;
  }

  @Override
  public String getInteractiveDeviceType() {
    return interactiveDeviceType;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result =
        prime * result + ((interactiveDeviceType == null) ? 0 : interactiveDeviceType.hashCode());
    result = prime * result + (supportsTutorial ? 1231 : 1237);
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    APIPrePlayAnnotationImpl other = (APIPrePlayAnnotationImpl) obj;
    if (interactiveDeviceType == null) {
      if (other.interactiveDeviceType != null) return false;
    } else if (!interactiveDeviceType.equals(other.interactiveDeviceType)) return false;
    if (supportsTutorial != other.supportsTutorial) return false;
    return true;
  }

  @Override
  public String toString() {
    return "APIPrePlayAnnotationImpl [supportsTutorial="
        + supportsTutorial
        + ", interactiveDeviceType="
        + interactiveDeviceType
        + "]";
  }
}
