package com.netflix.api.service.searchnew;

import com.google.common.base.Preconditions;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/** Base search request */
public abstract class APISearchRequest {
  private Integer count = null;
  private final Map<String, String> otherParams = new HashMap<>();

  public Integer getCount() {
    return count;
  }

  /**
   * Specify how many items you want in the resulting search list.
   *
   * @param count - a positive integer (0 or more).
   * @return A {@link APISearchRequest} that can be further decorated with other search request
   *     parameters, or passed into a search service method.
   */
  public APISearchRequest withCount(Integer count) {
    Preconditions.checkState(count != null && count >= 0, "Count should be greater or equal 0");
    this.count = count;
    return this;
  }

  public Map<String, String> getOtherParams() {
    return otherParams;
  }

  /**
   * Specify key-value parameters per the contract established between a device and the search
   * service.
   *
   * <p>Known key-value pairs are:
   *
   * <ul>
   *   <li>includeDvdInfo=true|false ** note: this is only used by website to allow them to
   *       cross-sell DVDCo product.
   *   <li>includeSourceInfo=true|false ** note: includes additional info about the title.
   *   <li>odpAware=true|false
   * </ul>
   *
   * @param key - parameter name.
   * @param value - parameter value.
   * @return A {@link APISearchRequest} that can be further decorated with other search request
   *     parameters, or passed into a search service method.
   */
  public APISearchRequest withOtherParam(String key, String value) {
    Preconditions.checkNotNull(key);
    Preconditions.checkNotNull(value);

    this.otherParams.put(key, value);
    return this;
  }

  /**
   * Returns {@link APISearchRequestByEntity} that can be further decorated with other search
   * request parameters.
   *
   * @param entityId - A non-null search list reference id.
   * @return A {@link APISearchRequestByEntity} that can be further decorated with other search
   *     request parameters, or passed into a search service method.
   */
  public static APISearchRequest newSearchRequestByEntityId(String entityId) {
    return new APISearchRequestByEntity(entityId);
  }

  /**
   * Returns {@link APISearchRequestByQuery} that can be further decorated with other search request
   * parameters.
   *
   * @param term - A non-null search term for which search results are needed.
   * @param listType - A non-null list type of which search results are needed..
   * @return A {@link APISearchRequestByQuery} that can be further decorated with other search
   *     request parameters, or passed into a search service method.
   */
  public static APISearchRequest newSearchRequestByQuery(
      String term, APISearchConstants.SearchListType listType) {
    return new APISearchRequestByQuery(term, listType);
  }

  /**
   * Returns {@link APISearchRequestByQuery} that can be further decorated with other search request
   * parameters.
   *
   * @param listType - A non-null list type of which pre-query search results are needed.
   * @return A {@link APISearchPreQueryRequest} that can be further decorated with other search
   *     request parameters, or passed into a search service method.
   */
  public static APISearchRequest newSearchPreQueryRequest(
      APISearchConstants.SearchListType listType) {
    return new APISearchPreQueryRequest(listType, Set.of());
  }

  public static APISearchRequest newSearchPreQueryRequest() {
    return new APISearchPreQueryRequest();
  }
}
