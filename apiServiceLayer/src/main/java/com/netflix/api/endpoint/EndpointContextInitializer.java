package com.netflix.api.endpoint;

import com.netflix.api.service.identity.APIUser;
import jakarta.servlet.http.HttpServletRequest;

/**
 * Provides functionality for each request to initialize contexts that are required by the Netflix
 * stack.
 *
 * <p>For example, this sets up CurrentRequestContext and ContentRegionContext.
 *
 * <p>This should be invoked before every EndpointScript execution.
 */
public interface EndpointContextInitializer {

  /**
   * Initializations that underlying dependencies require which use CurrentRequestContext.
   *
   * @param request
   * @param user
   */
  void initializeContext(HttpServletRequest request, APIUser user);

  /**
   * Initializations of locales for the request context.
   *
   * @param request
   * @param user
   */
  void initializeContextLocales(HttpServletRequest request, APIUser user);
}
