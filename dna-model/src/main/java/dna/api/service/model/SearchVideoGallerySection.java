package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchVideoGallerySection extends SearchPageSection {
  private Integer suggestedNumberOfVideosToShow;

  private List<SearchPageVideo> videos;

  private OocMessageData oocMessageData;

  public Integer getSuggestedNumberOfVideosToShow() {
    return suggestedNumberOfVideosToShow;
  }

  public List<SearchPageVideo> getVideos() {
    return videos;
  }

  public OocMessageData getOocMessageData() {
    return oocMessageData;
  }

  public SearchVideoGallerySection setSuggestedNumberOfVideosToShow(
      Integer suggestedNumberOfVideosToShow) {
    this.suggestedNumberOfVideosToShow = suggestedNumberOfVideosToShow;
    return this;
  }

  public SearchVideoGallerySection setVideos(List<SearchPageVideo> videos) {
    this.videos = videos;
    return this;
  }

  public SearchVideoGallerySection setOocMessageData(OocMessageData oocMessageData) {
    this.oocMessageData = oocMessageData;
    return this;
  }
}
