package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class VerifyPinRequest {
  private String enteredPin;

  private Boolean clientAgeVerificationCapable;

  public String getEnteredPin() {
    return enteredPin;
  }

  public Boolean getClientAgeVerificationCapable() {
    return clientAgeVerificationCapable;
  }

  public VerifyPinRequest setEnteredPin(String enteredPin) {
    this.enteredPin = enteredPin;
    return this;
  }

  public VerifyPinRequest setClientAgeVerificationCapable(Boolean clientAgeVerificationCapable) {
    this.clientAgeVerificationCapable = clientAgeVerificationCapable;
    return this;
  }
}
