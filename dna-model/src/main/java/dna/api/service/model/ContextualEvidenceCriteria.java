package dna.api.service.model;

import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class ContextualEvidenceCriteria {
  /** Desired evidence types */
  private List<EvidenceType> targetEvidenceTypes;

  /** Metadata to forward to Text Evidence */
  private Map<String, String> annotations;

  /**
   * Value that indicates to evidence systems which page/context the UI is requesting this for. For
   * example, ODP is used to indicate that this value will be displayed on a details page (ODP =
   * original display page).
   *
   * <p>Consult with Evidence Engineering (#evengers) for more info.
   */
  private List<UIContext> targetUIContexts;

  /** Context about which row to provide evidence for (ie: populartitles) */
  private GroupLocator groupLocator;

  public List<EvidenceType> getTargetEvidenceTypes() {
    return targetEvidenceTypes;
  }

  public Map<String, String> getAnnotations() {
    return annotations;
  }

  public List<UIContext> getTargetUIContexts() {
    return targetUIContexts;
  }

  public GroupLocator getGroupLocator() {
    return groupLocator;
  }

  public ContextualEvidenceCriteria setTargetEvidenceTypes(List<EvidenceType> targetEvidenceTypes) {
    this.targetEvidenceTypes = targetEvidenceTypes;
    return this;
  }

  public ContextualEvidenceCriteria setAnnotations(Map<String, String> annotations) {
    this.annotations = annotations;
    return this;
  }

  public ContextualEvidenceCriteria setTargetUIContexts(List<UIContext> targetUIContexts) {
    this.targetUIContexts = targetUIContexts;
    return this;
  }

  public ContextualEvidenceCriteria setGroupLocator(GroupLocator groupLocator) {
    this.groupLocator = groupLocator;
    return this;
  }
}
