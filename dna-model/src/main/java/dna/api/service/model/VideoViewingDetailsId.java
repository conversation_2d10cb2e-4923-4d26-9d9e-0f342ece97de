package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class VideoViewingDetailsId {
  private String videoViewingDetailsId;

  /** all | most_recent */
  private String type;

  /** applies only to "all" */
  private Integer index;

  public String getVideoViewingDetailsId() {
    return videoViewingDetailsId;
  }

  public String getType() {
    return type;
  }

  public Integer getIndex() {
    return index;
  }

  public VideoViewingDetailsId setVideoViewingDetailsId(String videoViewingDetailsId) {
    this.videoViewingDetailsId = videoViewingDetailsId;
    return this;
  }

  public VideoViewingDetailsId setType(String type) {
    this.type = type;
    return this;
  }

  public VideoViewingDetailsId setIndex(Integer index) {
    this.index = index;
    return this;
  }
}
