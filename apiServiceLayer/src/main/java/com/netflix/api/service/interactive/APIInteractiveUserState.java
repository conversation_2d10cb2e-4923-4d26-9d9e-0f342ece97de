package com.netflix.api.service.interactive;

import java.util.Objects;

public class APIInteractiveUserState {
  private final byte[] currentUserState;
  private final byte[] previousStateSnapshots;
  private final Long playbackPositionMs;

  public static Builder builder() {
    return new Builder();
  }

  private APIInteractiveUserState(Builder builder) {
    this.currentUserState = builder.currentUserState;
    this.previousStateSnapshots = builder.previousStateSnapshots;
    this.playbackPositionMs = builder.playbackPositionMs;
  }

  public byte[] getCurrentUserState() {
    return currentUserState;
  }

  public byte[] getPreviousStateSnapshots() {
    return previousStateSnapshots;
  }

  public Long getPlaybackPositionMs() {
    return playbackPositionMs;
  }

  public static class Builder {
    private byte[] currentUserState;
    private byte[] previousStateSnapshots;
    private Long playbackPositionMs;

    private Builder() {}

    public Builder setCurrentUserState(byte[] currentUserState) {
      Objects.requireNonNull(currentUserState, "currentUserState must not be null");
      this.currentUserState = currentUserState;
      return this;
    }

    public Builder setPreviousStateSnapshots(byte[] previousStateSnapshots) {
      this.previousStateSnapshots = previousStateSnapshots;
      return this;
    }

    public Builder setPlaybackPositionMs(Long playbackPositionMs) {
      this.playbackPositionMs = playbackPositionMs;
      return this;
    }

    public APIInteractiveUserState build() {
      return new APIInteractiveUserState(this);
    }
  }
}
