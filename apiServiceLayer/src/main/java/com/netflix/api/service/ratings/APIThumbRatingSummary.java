package com.netflix.api.service.ratings;

import com.netflix.myratings.protogen.ThumbRatingSummary;

public class APIThumbRatingSummary {
  private final ThumbRatingSummary thumbRatingSummary;

  APIThumbRatingSummary(ThumbRatingSummary thumbRatingSummary) {
    this.thumbRatingSummary = thumbRatingSummary;
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "myratings.proto#ThumbRatingSummary.countTotal"
   */
  public Long getCountTotal() {
    return this.thumbRatingSummary.getCountTotal();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "myratings.proto#ThumbRatingSummary.countThumbsUp"
   */
  public Long getCountThumbsUp() {
    return this.thumbRatingSummary.getCountThumbsUp();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "myratings.proto#ThumbRatingSummary.countThumbsDown"
   */
  public Long getCountThumbsDown() {
    return this.thumbRatingSummary.getCountThumbsDown();
  }

  /**
   * Auto-generated from gRPC proto
   *
   * @see "myratings.proto#ThumbRatingSummary.percentThumbsUp"
   */
  public Float getPercentThumbsUp() {
    return this.thumbRatingSummary.getPercentThumbsUp();
  }

  @Override
  public boolean equals(Object otherObj) {
    if (otherObj == null) return false;
    if (!(otherObj instanceof APIThumbRatingSummary)) return false;
    final APIThumbRatingSummary otherSummary = (APIThumbRatingSummary) otherObj;
    return thumbRatingSummary.equals(otherSummary.thumbRatingSummary);
  }

  @Override
  public String toString() {
    return thumbRatingSummary.toString();
  }
}
