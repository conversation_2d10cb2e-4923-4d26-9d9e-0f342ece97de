package dna.api.datasources;

import static com.netflix.myratings.protogen.ThumbRatingV2Value.THUMBS_V2_DOWN;
import static com.netflix.myratings.protogen.ThumbRatingV2Value.THUMBS_V2_UNRATED;
import static com.netflix.myratings.protogen.ThumbRatingV2Value.THUMBS_V2_UP;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.params.provider.Arguments.arguments;

import com.netflix.api.service.video.APIThumbRating;
import com.netflix.myratings.protogen.ThumbRatingV2Value;
import dna.api.service.model.Thumbs;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.FieldSource;

class EntityThumbRatingTest {
  @Test
  void videoRating_whenMyRatingEntityIsNull() {
    assertNull(new EntityThumbRating(null).videoRating());
  }

  @ParameterizedTest
  @FieldSource("videoRatingTestCases")
  void videoRating_returnsVideoRating(ThumbRatingV2Value input, String expected) {
    var data =
        com.netflix.myratings.protogen.EntityThumbRating.newBuilder()
            .setRating(input)
            .setTimestamp(1010L)
            .build();

    var rating = new EntityThumbRating(data).videoRating();
    assertNotNull(rating);

    var value = rating.getRating();
    assertEquals(expected, value);

    var timestamp = rating.getTimestamp();
    assertNotNull(timestamp);
    assertEquals(1010L, timestamp.longValue());
  }

  static final List<Arguments> videoRatingTestCases =
      List.of(
          arguments(THUMBS_V2_UNRATED, APIThumbRating.UNRATED.name()),
          arguments(THUMBS_V2_DOWN, APIThumbRating.THUMBS_DOWN.name()),
          arguments(THUMBS_V2_UP, APIThumbRating.THUMBS_UP.name()));

  @Test
  void thumbRatingData_whenMyRatingEntityIsNull() {
    assertNull(new EntityThumbRating(null).thumbRatingData());
  }

  @ParameterizedTest
  @FieldSource("thumbRatingDataTestCases")
  void thumbRatingData_returnsVideoRating(ThumbRatingV2Value input, Thumbs expected) {
    var data =
        com.netflix.myratings.protogen.EntityThumbRating.newBuilder()
            .setRating(input)
            .setTimestamp(2020L)
            .build();

    var rating = new EntityThumbRating(data).thumbRatingData();
    assertNotNull(rating);

    var thumbs = rating.getThumbs();
    assertEquals(expected, thumbs);

    var timestamp = rating.getTimestamp();
    assertNotNull(timestamp);
    assertEquals(2020L, timestamp.longValue());
  }

  static final List<Arguments> thumbRatingDataTestCases =
      List.of(
          arguments(THUMBS_V2_UNRATED, Thumbs.THUMBS_UNRATED),
          arguments(THUMBS_V2_DOWN, Thumbs.THUMBS_DOWN),
          arguments(THUMBS_V2_UP, Thumbs.THUMBS_UP));
}
