package com.netflix.api.service;

import com.netflix.api.annotations.ExcludeFromCoverage;
import com.netflix.api.service.video.APIImageFormat;
import com.netflix.i18n.NFLocale;
import com.netflix.type.ISOCountry;
import com.netflix.videometadata.image.TrickPlayFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * A generic API enumeration to dependency enumeration resolver
 *
 * @see
 */
public class APIToDependencyObjectMapper {
  private static final APIToDependencyObjectMapper instance = new APIToDependencyObjectMapper();

  private final Map<Class, Class> resolutionTypeMap;
  private final Map<Class, Resolver> apiEnumToResolverMap;

  public interface Resolver<A, D> {
    D resolve(A source);
  }

  public static class APIImageFormatResolver implements Resolver<APIImageFormat, TrickPlayFormat> {
    @Override
    public TrickPlayFormat resolve(APIImageFormat type) {
      for (TrickPlayFormat f : TrickPlayFormat.values())
        if (f.getWidth() == type.getWidth()) return f;
      return TrickPlayFormat.UNKNOWN;
    }
  }

  public static class APILocaleResolver implements Resolver<APILocale, NFLocale> {

    @Override
    public NFLocale resolve(APILocale source) {
      return ((APILocaleImpl) source).getNFLocale();
    }
  }

  public static class APICountryResolver implements Resolver<APICountry, ISOCountry> {

    @Override
    public ISOCountry resolve(APICountry source) {
      return source.getISOCountry();
    }
  }

  @ExcludeFromCoverage
  public static APIToDependencyObjectMapper getInstance() {
    return instance;
  }

  @ExcludeFromCoverage
  APIToDependencyObjectMapper() {
    Map<Class, Class> typeMap = new HashMap<>();
    typeMap.put(APILocale.class, NFLocale.class);
    typeMap.put(APICountry.class, ISOCountry.class);
    typeMap.put(APIImageFormat.class, TrickPlayFormat.class);
    this.resolutionTypeMap = typeMap;

    Map<Class, Resolver> map = new HashMap<>();
    map.put(APILocale.class, new APILocaleResolver());
    map.put(APICountry.class, new APICountryResolver());
    map.put(APIImageFormat.class, new APIImageFormatResolver());
    apiEnumToResolverMap = map;
  }

  public <A, D> D resolve(A apiEnumValue, Class sourceType, Class<? extends D> targetType) {
    if (apiEnumValue == null || sourceType == null || targetType == null) return null;
    Class resolvedType = resolutionTypeMap.get(sourceType);
    if (resolvedType != null && targetType.isAssignableFrom(resolvedType)) {
      if (apiEnumToResolverMap.containsKey(sourceType)) {
        return targetType.cast(apiEnumToResolverMap.get(sourceType).resolve(apiEnumValue));
      }
    }
    return null;
  }
}
