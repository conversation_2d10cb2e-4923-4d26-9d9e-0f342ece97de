package com.netflix.api.service.profiles;

import com.netflix.map.datatypes.MapItem;
import com.netflix.map.datatypes.MapList;
import com.netflix.map.jackson.serializable.MapSerializableImage;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class APILolopiListImpl implements APILolopiList {

  private final List<APIProfileIcon> items;
  private final Map<String, Object> annotations;

  APILolopiListImpl(MapList<MapItem<?>> mapList) {
    this.annotations = mapList.getAnnotations().getAnnotationsAsMap();
    List<APIProfileIcon> list = new ArrayList<>(mapList.size());
    for (MapItem<?> item : mapList) {
      list.add(
          new APIProfileIconImpl(
              item.getAnnotations().getAnnotationsAsMap(), (MapSerializableImage) item.getItem()));
    }
    this.items = list;
  }

  @Override
  public List<APIProfileIcon> getItems() {
    return items;
  }

  @Override
  public Map<String, Object> getAnnotations() {
    return annotations;
  }

  @Override
  public Object getAnnotationValue(String key) {
    return annotations.get(key);
  }
}
