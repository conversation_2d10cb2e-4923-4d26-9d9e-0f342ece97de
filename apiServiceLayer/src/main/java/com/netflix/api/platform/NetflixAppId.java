package com.netflix.api.platform;

/** Type representing a Netflix AppId */
public class NetflixAppId {
  private final Integer appId;

  private NetflixAppId(Integer appId) {
    this.appId = appId;
  }

  public Integer getAppId() {
    return appId;
  }

  /**
   * Get an AppId from a Request.
   *
   * @return NetflixAppId containing the appId or 0 if the ESN doesn't match an appID, NULL if there
   *     is no ESN in this request.
   */
  public static NetflixAppId createFromRequest() {
    return createFromESN(NetflixESN.getCurrent());
  }

  /**
   * Get an AppId from a NetflixESN.
   *
   * @param esn an ESN instance
   * @return NetflixAppId containing the appId or 0 if the ESN doesn't match an appID, NULL if the
   *     ESN passed in was NULL.
   */
  public static NetflixAppId createFromESN(NetflixESN esn) {
    return new NetflixAppId(0);
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((appId == null) ? 0 : appId.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    NetflixAppId other = (NetflixAppId) obj;
    if (appId == null) {
      if (other.appId != null) return false;
    } else if (!appId.equals(other.appId)) return false;
    return true;
  }
}
