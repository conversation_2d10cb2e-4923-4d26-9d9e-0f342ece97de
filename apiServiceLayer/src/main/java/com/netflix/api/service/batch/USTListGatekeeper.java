package com.netflix.api.service.batch;

import com.netflix.gps.client.grps.page.ext.session.common.SessionKey;
import com.netflix.gps.client.grps.page.ext.session.common.SessionKey.KeyFormat;
import com.netflix.gps.client.grps.page.ext.session.common.SessionKeyParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class USTListGatekeeper {

  private final SessionKeyParser sessionKeyParser;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public USTListGatekeeper(SessionKeyParser sessionKeyParser) {
    this.sessionKeyParser = sessionKeyParser;
  }

  public boolean shouldCallUstForList(String listId) {
    if (listId == null || listId.isEmpty()) {
      return false;
    }
    return isNesSessionKey(listId);
  }

  private boolean isNesSessionKey(String listId) {
    if (sessionKeyParser.isSessionKey(listId)) {
      SessionKey key = sessionKeyParser.parse(listId);
      KeyFormat format = key.getKeyFormat();
      return format == KeyFormat.NES;
    }
    return false;
  }
}
