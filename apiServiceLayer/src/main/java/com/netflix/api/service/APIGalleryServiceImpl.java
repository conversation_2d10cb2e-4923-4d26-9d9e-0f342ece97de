package com.netflix.api.service;

import com.google.common.base.Preconditions;
import com.netflix.api.annotations.EnableDeprecatedMetrics;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.exception.APIRuntimeException;
import com.netflix.api.service.batch.USTListAdapter;
import com.netflix.api.service.gallery.APIGalleryService;
import com.netflix.api.service.gallery.APIGallerySubGenresRequest;
import com.netflix.api.service.identity.APIUser;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.map.annotation.MapAnnotationConstants;
import com.netflix.map.annotation.MapAnnotationConstants.ListContexts;
import com.netflix.map.datatypes.MapItem;
import com.netflix.map.datatypes.MapList;
import com.netflix.map.datatypes.MapResponse;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.type.ISOCountry;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;

@Component
@EnableDeprecatedMetrics
public class APIGalleryServiceImpl implements APIGalleryService {

  private final USTListAdapter listAdapter;

  @Autowired
  public APIGalleryServiceImpl(USTListAdapter listAdapter) {
    this.listAdapter = listAdapter;
  }

  // If no "visitor" were provided, add "visitor" -> apiRequest.user.customerId.
  // If caller included "visitor" -> apiUser into request, we must replace it with "visitor" ->
  // apiUser.customerId.
  // In all other cases keep request as is.
  private Map<String, Object> transform(Map<String, Object> request) {
    Map<String, Object> transformed = new HashMap<>(request);
    APIRequest apiRequest = APIRequest.getCurrentRequest();
    Object value = transformed.get("visitor");
    if (value == null) {
      transformed.put("visitor", APIUserUtil.getCustomerId(apiRequest.getUser()));
    } else if (value instanceof APIUser user) {
      transformed.put("visitor", APIUserUtil.getCustomerId(user));
    }
    APIClientCapabilitiesInternal.get(apiRequest)
        .ifPresent(
            apiClientCapabilitiesInternal -> {
              if (apiClientCapabilitiesInternal.currentInteractiveTitlesPresent()) {
                transformed.put(
                    APIClientCapabilitiesInternal.INTERACTIVE_VIDEOS_FEATURE,
                    apiClientCapabilitiesInternal.getAllInteractiveTitlesImmutable());
              }
            });

    return transformed;
  }

  public Observable<MapResponse> _getMemberGalleryRow(Map<String, Object> request) {
    if (request == null) {
      return Observable.error(new APIRuntimeException("Must provide request parameter"));
    }
    Map<String, Object> annotations = transform(request);
    return listAdapter.getGallery(annotations).toObservable().cache();
  }

  public Observable<MapResponse> _getNonMemberGalleryRow(Map<String, Object> request) {
    Map<String, Object> annotations;
    try {
      if (request == null) {
        throw new APIRuntimeException("Must provide a non-null request");
      }
      final Object galleryType = request.get("galleryType");
      if (galleryType == null) {
        throw new APIRuntimeException("Must provide request key 'galleryType'");
      }
      final ISOCountry country =
          Preconditions.checkNotNull(RequestContextWrapper.get().getCountry());
      request.put("country", country.getId());

      // add visitor if it is not included in request
      request.computeIfAbsent(
          "visitor", key -> APIUserUtil.getCustomerId(APIRequest.getCurrentRequest().getUser()));
      annotations = transform(request);
    } catch (Exception e) {
      return Observable.error(e);
    }

    annotations.put(MapAnnotationConstants.LISTCONTEXT, ListContexts.nonMember.name());
    return listAdapter.getGallery(annotations).toObservable().cache();
  }

  @SuppressWarnings("unchecked")
  public Observable<MapList<MapItem<?>>> getGallerySubGenreIdsRaw(
      APIGallerySubGenresRequest request, APIUser user) {
    Map<String, Object> annotations = buildSubgenreIdsRequest(request, user);
    return listAdapter
        .getMenuNavigationSubGenres(annotations)
        .toObservable()
        .filter(mapResponse -> mapResponse != null && mapResponse.size() > 0)
        .map(
            mapResponse ->
                // in the returned data structure, only the first map list has the data
                (MapList<MapItem<?>>) mapResponse.getAllLists().getFirst())
        .cache();
  }

  private Map<String, Object> buildSubgenreIdsRequest(
      APIGallerySubGenresRequest request, APIUser user) {
    final Map<String, Object> annotations =
        USTListAdapter.getAccountProfileInfoToAnnotations(
            APIUserUtil.getAccountProfileRemote(user));

    annotations.put(
        "country", Preconditions.checkNotNull(RequestContextWrapper.get().getCountry()).getId());
    annotations.put("visitor", Preconditions.checkNotNull(CurrentVisitor.get()).getId());
    annotations.put("kidsonly", request.getKids());
    annotations.put("locale", request.getLocale());
    annotations.put("supportsCulturalMomentPages", request.getSupportsCulturalMomentPages());

    if (request.getGenreId() != null) {
      annotations.put("genreId", request.getGenreId());
    }

    if (request.getNavId() != null) {
      annotations.put("navId", request.getNavId());
    }

    if (!request.getIncludeDownloadsSubGenre()) {
      annotations.put("allowDownloadableSubGenre", request.getIncludeDownloadsSubGenre());
    }
    return annotations;
  }
}
