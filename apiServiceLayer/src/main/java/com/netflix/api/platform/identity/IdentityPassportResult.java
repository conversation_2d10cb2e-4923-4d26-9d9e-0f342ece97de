package com.netflix.api.platform.identity;

import com.google.protobuf.InvalidProtocolBufferException;
import com.netflix.membership.MembershipStatus;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.protobuf.Source;
import com.netflix.passport.protobuf.UserAction;
import com.netflix.subscriberservice.protogen.SubscriberFallback;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IdentityPassportResult extends IdentityResult<PassportIdentity> {

  private static final Logger logger = LoggerFactory.getLogger(IdentityPassportResult.class);

  // allowed identity sources to be verified against the passport.
  private static final Set<Source> DEFAULT_SOURCES =
      Set.of(
          Source.COOKIE,
          Source.MSL,
          Source.MSL_WEAK_AUTHENTICATION,
          Source.COOKIE_INSECURE,
          Source.ASSERTION,
          Source.USER_CREDENTIALS,
          Source.AUTO_LOGIN_TOKEN);

  private final PassportIdentity identity;
  private final Set<Source> allowedSources;
  private final boolean success;

  public IdentityPassportResult(PassportIdentity identity, Set<Source> allowedSources) {
    this.identity = identity;
    this.allowedSources = allowedSources;
    this.success = getSuccess();
  }

  public IdentityPassportResult(PassportIdentity identity) {
    this.identity = identity;
    this.allowedSources = DEFAULT_SOURCES;
    this.success = getSuccess();
  }

  @Override
  public PassportIdentity getData() {
    return identity;
  }

  @Override
  public boolean isSuccess() {
    return success;
  }

  // The source of user information, i.e. COOKIE.
  private Source user() {
    return identity.getUserClaims().getUserInformationSource();
  }

  // The source of device information, i.e. DET_REQUEST_BODY.
  private Source device() {
    return identity.getDeviceClaims().getDeviceInformationSource();
  }

  @Override
  public Long getCustomerId() {
    Source src = user();
    return canAccessUserData(src) ? identity.getProfileId().orElse(null) : null;
  }

  @Override
  public Long getAccountOwnerId() {
    Source src = user();
    return canAccessUserData(src) ? identity.getAccountId().orElse(null) : null;
  }

  @Override
  public String getSignupCountry() {
    return null;
  }

  @Override
  public String getRegistrationCountry() {
    return null; // TBD
  }

  @Override
  public String getESN() {
    Source src = device();
    return canAccessUserData(src) ? identity.getEsn().orElse(null) : null;
  }

  @Override
  // NOTE: The value from this call should be considered stale since it is derived from a token and
  // not from live data
  public Optional<MembershipStatus> getMembershipStatus() {
    return identity
        .getUserClaims()
        .getSubscriberFallback()
        .flatMap(
            bytes -> {
              try {
                SubscriberFallback subscriberFallback = SubscriberFallback.parseFrom(bytes);
                if (subscriberFallback.hasMembershipStatus()) {
                  return subscriberFallback.getOptionalMembershipStatus();
                }
              } catch (InvalidProtocolBufferException e) {
                logger.warn("Invalid Subscriber fallback in passport", e);
              }
              return Optional.empty();
            })
        .map(MembershipStatus::getMembershipStatus);
  }

  @Override
  public String getCustomerGuid() {
    Source src = user();
    return canAccessUserData(src) ? identity.getProfileGuid().orElse(null) : null;
  }

  @Override
  public Integer getDeviceType() {
    Source src = device();
    return canAccessUserData(src) ? identity.getDeviceTypeId().orElse(null) : null;
  }

  @Override
  public IdentitySource getUserInformationSource() {
    return convertSource(identity.getUserClaims().getUserInformationSource());
  }

  @Override
  public IdentitySource getDeviceInformationSource() {
    return convertSource(identity.getDeviceClaims().getDeviceInformationSource());
  }

  private boolean getSuccess() {
    List<UserAction> userActions = identity.getUserClaims().getUserActions();
    if (userActions.isEmpty()) return true;

    Optional<UserAction> forceLoginAction =
        userActions.stream()
            .filter(UserAction::hasForceLogin)
            .findAny(); // denotes bad incoming tokens.

    if (forceLoginAction.isEmpty()) return true;

    // It's possible that we are processing a user login or a user registration in the same
    // request which has bad incoming tokens (e.g. cookies or MSL tokens). We should allow such
    // requests.

    Long customerId = getCustomerId();
    return customerId != null
        && userActions.stream()
            .anyMatch(action -> action.hasUserLogin() || action.hasUserRegistration());
  }

  private static IdentitySource convertSource(Source source) {
    if (source == null) return IdentitySource.NONE;

    return switch (source) {
      case NONE -> IdentitySource.NONE;
      case COOKIE -> IdentitySource.COOKIES;
      case COOKIE_INSECURE -> IdentitySource.COOKIES_INSECURE;
      case MSL -> IdentitySource.MSL;
      case MSL_WEAK_AUTHENTICATION -> IdentitySource.MSL_WEAK_AUTHENTICATION;
      case PARTNER_COOKIE -> IdentitySource.PARTNER_COOKIE;
      case USER_CREDENTIALS -> IdentitySource.CREDENTIALS;
      case AUTO_LOGIN_TOKEN -> IdentitySource.ADC_IDENTITY_TOKEN;
      case ASSERTION -> IdentitySource.ASSERTION;
      case PARTNER_TOKEN -> IdentitySource.PARTNER_TOKEN;
      case ZUUL_PUSH_TOKEN -> IdentitySource.ZUUL_PUSH_TOKEN;
      default -> IdentitySource.UNKNOWN;
    };
  }

  public static Set<Source> getAllowedIdentitySourcesForPassport(boolean allowPartnerCookies) {
    if (!allowPartnerCookies) return DEFAULT_SOURCES;

    var withPartnerCookies = new HashSet<>(DEFAULT_SOURCES);
    withPartnerCookies.add(Source.PARTNER_COOKIE);
    withPartnerCookies.add(Source.PARTNER_TOKEN);
    withPartnerCookies.add(Source.ZUUL_PUSH_TOKEN);
    return Set.copyOf(withPartnerCookies);
  }

  // This code is here to replication the functionality that was present in PassportIntrospector.
  // This predates PACS and the team does not have confidence that we can simply remove this check
  // here at the time of the migration to PassportIdentity.
  // TODO(tony): remove in favor of PACS handling authorization.
  private boolean canAccessUserData(Source src) {
    return allowedSources == null || allowedSources.contains(src);
  }
}
