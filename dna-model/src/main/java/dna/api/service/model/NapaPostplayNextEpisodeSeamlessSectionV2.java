package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NapaPostplayNextEpisodeSeamlessSectionV2 extends NapaPostplayCommonSection {
  private SearchPageEntity entity;

  public SearchPageEntity getEntity() {
    return entity;
  }

  public NapaPostplayNextEpisodeSeamlessSectionV2 setEntity(SearchPageEntity entity) {
    this.entity = entity;
    return this;
  }
}
