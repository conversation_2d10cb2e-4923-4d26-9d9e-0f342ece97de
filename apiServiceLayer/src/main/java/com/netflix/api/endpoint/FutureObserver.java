package com.netflix.api.endpoint;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.lang.BindingContexts;
import com.netflix.lang.ThreadContext;
import com.netflix.util.concurrent.MeasuredFuture;
import com.netflix.util.concurrent.NFFutureHook;
import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

class FutureObserver implements NFFutureHook {
  protected static final Property<Boolean> failRequestIfTaskCompleteExceptionally =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.endpoint.async.command.failrequestonexception", Boolean.class)
          .orElse(true);

  private final CompletableFuture<Void> completableFuture = new CompletableFuture<>();

  // hooks are called outside the ContextCallable used by the future
  private final ThreadContext threadContext = ThreadContext.capture();

  @Override
  public void noteInitialized(MeasuredFuture future) {}

  @Override
  public void noteGet(MeasuredFuture future, long numberOfGets) {}

  // this method is called only if java.util.concurrent.FutureTask::run
  // throw an exception, but it's quite unlikely because FutureTask::run
  // catches all Throwable
  @Override
  public void noteException(MeasuredFuture future, Exception e) {
    BindingContexts.runWithContext(() -> completableFuture.completeExceptionally(e), threadContext);
  }

  @Override
  public void noteStart(MeasuredFuture future, Executor executor) {}

  @Override
  public void noteRejected(MeasuredFuture future, Executor executor) {
    BindingContexts.runWithContext(
        () -> {
          completableFuture.completeExceptionally(new RejectedExecutionException());
        },
        threadContext);
  }

  @Override
  public void noteCallBegin(MeasuredFuture future) {}

  @Override
  public void noteCallEnd(MeasuredFuture future) {
    BindingContexts.runWithContext(
        () -> {
          if (failRequestIfTaskCompleteExceptionally.get() && future.isDone()) {
            try {
              future.get(0, TimeUnit.NANOSECONDS);
            } catch (InterruptedException e) {
              Thread.currentThread().interrupt();
              completableFuture.completeExceptionally(e);
              return;
            } catch (ExecutionException e) {
              Throwable cause = e.getCause();
              completableFuture.completeExceptionally(cause == null ? e : cause);
              return;
            } catch (CancellationException e) {
              completableFuture.completeExceptionally(e);
              return;
            } catch (TimeoutException e) {
            }
          }
          completableFuture.complete(null);
        },
        threadContext);
  }

  public CompletableFuture<Void> getCompletableFuture() {
    return this.completableFuture;
  }

  @Override
  public void noteGetDefault(MeasuredFuture future) {}
}
