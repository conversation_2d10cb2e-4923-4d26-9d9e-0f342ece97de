package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Any video from the Netflix catalog with a VMS node type. This message is useful when the type of
 * a video is unknown or unnecessary to supply. This type will never be returned by the backend but
 * is instead a convenience for callers since they need not inform the service what type of video
 * they are requesting
 */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NesVideoInput {
  private Integer id;

  public Integer getId() {
    return id;
  }

  public NesVideoInput setId(Integer id) {
    this.id = id;
    return this;
  }
}
