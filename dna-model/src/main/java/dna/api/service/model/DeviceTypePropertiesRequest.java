package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class DeviceTypePropertiesRequest {
  private String nrdAppVersion;

  private String validationVersion;

  private List<String> properties;

  public String getNrdAppVersion() {
    return nrdAppVersion;
  }

  public String getValidationVersion() {
    return validationVersion;
  }

  public List<String> getProperties() {
    return properties;
  }

  public DeviceTypePropertiesRequest setNrdAppVersion(String nrdAppVersion) {
    this.nrdAppVersion = nrdAppVersion;
    return this;
  }

  public DeviceTypePropertiesRequest setValidationVersion(String validationVersion) {
    this.validationVersion = validationVersion;
    return this;
  }

  public DeviceTypePropertiesRequest setProperties(List<String> properties) {
    this.properties = properties;
    return this;
  }
}
