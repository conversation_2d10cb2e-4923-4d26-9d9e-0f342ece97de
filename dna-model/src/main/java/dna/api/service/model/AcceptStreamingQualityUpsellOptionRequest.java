package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class AcceptStreamingQualityUpsellOptionRequest {
  /** UUID of the upsell offer to accept */
  private String upsellUUID;

  /** ID of plan that is being accepted for the upsell offer */
  private Long planID;

  /** Price tier of the accepted upsell offer */
  private String priceTier;

  /** Device platform category that is accepting the upsell. */
  private UpsellDevicePlatformCategory devicePlatformCategory;

  /** (Optional) Given message GUID. For logging/events. */
  private String messageGUID;

  public String getUpsellUUID() {
    return upsellUUID;
  }

  public Long getPlanID() {
    return planID;
  }

  public String getPriceTier() {
    return priceTier;
  }

  public UpsellDevicePlatformCategory getDevicePlatformCategory() {
    return devicePlatformCategory;
  }

  public String getMessageGUID() {
    return messageGUID;
  }

  public AcceptStreamingQualityUpsellOptionRequest setUpsellUUID(String upsellUUID) {
    this.upsellUUID = upsellUUID;
    return this;
  }

  public AcceptStreamingQualityUpsellOptionRequest setPlanID(Long planID) {
    this.planID = planID;
    return this;
  }

  public AcceptStreamingQualityUpsellOptionRequest setPriceTier(String priceTier) {
    this.priceTier = priceTier;
    return this;
  }

  public AcceptStreamingQualityUpsellOptionRequest setDevicePlatformCategory(
      UpsellDevicePlatformCategory devicePlatformCategory) {
    this.devicePlatformCategory = devicePlatformCategory;
    return this;
  }

  public AcceptStreamingQualityUpsellOptionRequest setMessageGUID(String messageGUID) {
    this.messageGUID = messageGUID;
    return this;
  }
}
