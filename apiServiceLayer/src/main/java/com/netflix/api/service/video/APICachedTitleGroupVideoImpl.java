package com.netflix.api.service.video;

import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.vms.provider.VideoFormatDescriptorProvider;

/** Generic groups of title exposing the APIVideo's methods family */
public class APICachedTitleGroupVideoImpl extends APICachedVideoImpl {

  protected APICachedTitleGroupVideoImpl(
      CompleteVideo video,
      APIVideoFactory videoFactory,
      USTVideoMetadataAdapter videoMetadataAdapter,
      VideoFormatDescriptorProvider videoFormatDescriptorProvider) {
    super(video, videoFactory, videoMetadataAdapter, videoFormatDescriptorProvider);
  }
}
