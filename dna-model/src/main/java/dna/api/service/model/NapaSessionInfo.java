package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaSessionInfo {
  /** The point in time at which the session will expire. */
  private Long expires;

  private Boolean fromSessionCache;

  private String pageId;

  /**
   * The ID that can be used to acquire the same exact subject subsequent to the initial creation.
   */
  private String sessionId;

  /**
   * ETag or entity tag, based on the concept of an HTTP etag
   * (https://en.wikipedia.org/wiki/HTTP_ETag), is used for session validation and formatted
   * according to RFC 7232 (https://tools.ietf.org/html/rfc7232#section-2.3).
   */
  private String etag;

  /** The recommended amount of time between sequential refreshes of the element. */
  private NapaDuration refreshInterval;

  public Long getExpires() {
    return expires;
  }

  public Boolean getFromSessionCache() {
    return fromSessionCache;
  }

  public String getPageId() {
    return pageId;
  }

  public String getSessionId() {
    return sessionId;
  }

  public String getEtag() {
    return etag;
  }

  public NapaDuration getRefreshInterval() {
    return refreshInterval;
  }

  public NapaSessionInfo setExpires(Long expires) {
    this.expires = expires;
    return this;
  }

  public NapaSessionInfo setFromSessionCache(Boolean fromSessionCache) {
    this.fromSessionCache = fromSessionCache;
    return this;
  }

  public NapaSessionInfo setPageId(String pageId) {
    this.pageId = pageId;
    return this;
  }

  public NapaSessionInfo setSessionId(String sessionId) {
    this.sessionId = sessionId;
    return this;
  }

  public NapaSessionInfo setEtag(String etag) {
    this.etag = etag;
    return this;
  }

  public NapaSessionInfo setRefreshInterval(NapaDuration refreshInterval) {
    this.refreshInterval = refreshInterval;
    return this;
  }
}
