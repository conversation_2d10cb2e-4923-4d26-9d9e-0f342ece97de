package com.netflix.api.service.vhs.async;

import com.netflix.playapi.events.streaming.PlaybackEvent;

public class PlaybackEventTopicProvider {

  public PlaybackEventTopic getPlaybackEventTopicCrossRegion(PlaybackEvent playbackEvent) {
    if (playbackEvent.getType() == PlaybackEvent.Type.KEEPALIVE) {
      return createPlaybackTopicForKeepaliveEvent(playbackEvent);
    }

    return createPlaybackTopicForEvent(playbackEvent);
  }

  public PlaybackEventTopic getSpilloverTopic(PlaybackEvent playbackEvent) {
    if (playbackEvent.getType() == PlaybackEvent.Type.KEEPALIVE) {
      return PlaybackEventTopic.PLAYBACK_EVENT_KEEP_ALIVE_SPILLOVER;
    }

    return PlaybackEventTopic.PLAYBACK_EVENT_SPILLOVER;
  }

  private static PlaybackEventTopic createPlaybackTopicForEvent(PlaybackEvent playbackEvent) {
    return switch (playbackEvent.getSessionInfo().getRegion()) {
      case US_WEST_2 -> PlaybackEventTopic.PLAYBACK_EVENT_US_WEST_2;
      case EU_WEST_1 -> PlaybackEventTopic.PLAYBACK_EVENT_EU_WEST_1;
      case US_EAST_2 -> PlaybackEventTopic.PLAYBACK_EVENT_US_EAST_2;
      default -> PlaybackEventTopic.PLAYBACK_EVENT_US_EAST_1;
    };
  }

  private static PlaybackEventTopic createPlaybackTopicForKeepaliveEvent(
      PlaybackEvent playbackEvent) {
    return switch (playbackEvent.getSessionInfo().getRegion()) {
      case US_WEST_2 -> PlaybackEventTopic.PLAYBACK_EVENT_KEEP_ALIVE_US_WEST_2;
      case EU_WEST_1 -> PlaybackEventTopic.PLAYBACK_EVENT_KEEP_ALIVE_EU_WEST_1;
      case US_EAST_2 -> PlaybackEventTopic.PLAYBACK_EVENT_KEEP_ALIVE_US_EAST_2;
      default -> PlaybackEventTopic.PLAYBACK_EVENT_KEEP_ALIVE_US_EAST_1;
    };
  }
}
