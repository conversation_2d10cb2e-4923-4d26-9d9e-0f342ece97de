package com.netflix.api.service.identity;

import java.util.EnumSet;

/** FIXME FIXME FIXME. */
public enum APIMembershipStatus {
  NEVER_MEMBER,
  FORMER_MEMBER,
  CURRENT_MEMBER,
  USER_HOLD_MEMBER,
  ACCOUNT_HOLD_MEMBER,
  NON_REGISTERED_MEMBER,
  FE_HOLD_MEMBER;

  static final EnumSet<APIMembershipStatus> ACTIVE_OR_ON_HOLD =
      EnumSet.of(CURRENT_MEMBER, USER_HOLD_MEMBER, ACCOUNT_HOLD_MEMBER, FE_HOLD_MEMBER);

  static final EnumSet<APIMembershipStatus> NON_MEMBER =
      EnumSet.of(NEVER_MEMBER, FORMER_MEMBER, NON_REGISTERED_MEMBER);

  /**
   * Does this status represent an active or an on-hold member?
   *
   * @return <code>true</code> if this status is either <code>CURRENT_MEMBER</code>, <code>
   *     USER_HOLD_MEMBER</code>, <code>ACCOUNT_HOLD_MEMBER</code>, or <code>FE_HOLD_MEMBER</code>
   */
  public boolean isActiveOrOnHold() {
    return ACTIVE_OR_ON_HOLD.contains(this);
  }

  /**
   * FIXME FIXME FIXME.
   *
   * @return <code>true</code> if this status is either <code>CURRENT_MEMBER</code>, <code>
   *     USER_HOLD_MEMBER</code>, <code>ACCOUNT_HOLD_MEMBER</code>, or <code>NON_REGISTERED_MEMBER
   *     </code>
   */
  public boolean isAuthorized() {
    return isActiveOrOnHold() || this == NON_REGISTERED_MEMBER;
  }

  /**
   * Copied over from MembershipStatus, to avoid using CurrentIdentity.
   *
   * @return true if status is non-member, false otherwise
   */
  public boolean isNonMember() {
    return NON_MEMBER.contains(this);
  }
}
