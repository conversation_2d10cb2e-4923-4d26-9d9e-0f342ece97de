package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class LocaleService implements GraphData {
  public Map<String, String> unsupportedLanguageMessagingImage;

  public Map<String, Atom<List<String>>> productPreferredLanguageTags;

  public Map<String, Map<String, Locale>> productSupportedLanguages;

  public Map<String, Locale> secondaryLanguages;
}
