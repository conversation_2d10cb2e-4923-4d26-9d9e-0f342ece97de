package dna.api.service.routes.impl;

import static com.netflix.api.platform.exception.Exceptions.chainedSneakyThrow;
import static dna.api.service.routes.util.VideoRoutesUtils.isAvailable;
import static dna.api.service.routes.util.VideoRoutesUtils.isEpisode;

import com.google.protobuf.Duration;
import com.netflix.api.platform.util.Properties;
import com.netflix.api.service.prepostplay.ThreePAdapter;
import com.netflix.api.service.vhs.IxBookmarkInfo;
import com.netflix.api.service.video.APIPostPlayRequest.ClientCapabilityOption;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.demograph.Context;
import com.netflix.demograph.datasource.batch.BatchPromise;
import com.netflix.demograph.graph.Atom;
import com.netflix.demograph.handler.CallRequest;
import com.netflix.demograph.handler.CallResponse;
import com.netflix.gps.maplogginglib.datatypes.MapResponseFactory;
import com.netflix.map.datatypes.MapList;
import com.netflix.map.datatypes.MapResponse;
import com.netflix.spectator.api.Registry;
import dna.api.datasources.ABDataSource;
import dna.api.datasources.CmsDataSource;
import dna.api.datasources.GamesDataSource;
import dna.api.datasources.InteractiveDataSource;
import dna.api.datasources.MapDataSource;
import dna.api.datasources.PostPlayRequest;
import dna.api.datasources.PrePlayRequest;
import dna.api.datasources.PrePostPlayDataSource;
import dna.api.datasources.VideosDataSource;
import dna.api.datasources.VxsDataSource;
import dna.api.service.model.BookmarkWithOptionsRequest;
import dna.api.service.model.BookmarkWithOptionsResponse;
import dna.api.service.model.IxPrePlayAnnotation;
import dna.api.service.model.PostPlayClientCapabilityOption;
import dna.api.service.model.PostPlayLolomoRequest;
import dna.api.service.model.PrePlayLolomoRequest;
import dna.api.service.model.SkipContentType;
import dna.api.service.model.TimeCode;
import dna.api.service.model.UiMetadataRequest;
import dna.api.service.routes.AnnotatedListReference;
import dna.api.service.routes.ViewableRoutes;
import dna.api.service.routes.util.CallResponses;
import dna.api.service.routes.util.VideoRoutesUtils;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ust.common.v1.VideoType.Type;
import ust.video.v1.EpisodeDetail;
import ust.video.v1.TimecodeType;
import ust.video.v1.VideoBase;
import ust.video.v1.VideoCore;
import ust.video.v1.ViewableInformation;

@Component
public class ViewableRoutesImpl extends BaseViewableRoutes implements ViewableRoutes {
  private static final Logger logger = LoggerFactory.getLogger(ViewableRoutesImpl.class);

  private final MapDataSource mapDataSource;
  private final PrePostPlayDataSource prePostPlayDataSource;
  private final CmsDataSource cmsDataSource;
  private final ThreePAdapter threePAdapter;
  private final Registry registry;

  // Always play recap for any season/episode of these shows
  private final Property<Set<Integer>> forceRecapTitles;

  @Autowired
  ViewableRoutesImpl(
      VideosDataSource videosDataSource,
      InteractiveDataSource interactiveDataSource,
      MapDataSource mapDataSource,
      VxsDataSource vxsDataSource,
      PrePostPlayDataSource prePostPlayDataSource,
      CmsDataSource cmsDataSource,
      GamesDataSource gamesDataSource,
      ThreePAdapter threePAdapter,
      ABDataSource abDataSource,
      Registry registry,
      PropertyRepository properties) {
    super(videosDataSource, vxsDataSource, interactiveDataSource, gamesDataSource, abDataSource);
    this.mapDataSource = mapDataSource;
    this.prePostPlayDataSource = prePostPlayDataSource;
    this.cmsDataSource = cmsDataSource;
    this.threePAdapter = threePAdapter;
    this.registry = registry;
    this.forceRecapTitles =
        Properties.intSetProperty(properties, "api.force.recap.titles").orElse(Set.of());
  }

  @Override
  @Deprecated
  public CompletionStage<AnnotatedListReference> postPlayLolomo(
      Context ctx, Integer videoId, PostPlayLolomoRequest inputs) {
    PostPlayRequest request = buildPostPlayRequest(inputs, videoId);
    return getViewableBase(ctx, videoId)
        .thenCompose(ignore -> prePostPlayDataSource.postPlayPage().lookup(ctx, request))
        .thenApply(response -> handlePrePostPlayLolomo(ctx, response));
  }

  private AnnotatedListReference handlePrePostPlayLolomo(Context ctx, MapResponse response) {
    String lolomoId =
        MapDataSource.MAGIC_PREFIX_FOR_PREPOST_ROOT
            + response.getAnnotation(MapDataSource.UUID, String.class)
            + MapDataSource.MAGIC_SUFFIX_FOR_ROOT;
    mapDataSource.cachedLolomo().stuffCache(ctx, lolomoId, response);
    for (MapList<?> list : response.getAllLists()) {
      mapDataSource
          .list()
          .stuffCache(ctx, list.getAnnotation(MapDataSource.UUID, String.class), list);
    }
    return new AnnotatedListReference(lolomoId);
  }

  private static PrePlayRequest buildPrePlayRequest(PrePlayLolomoRequest inputs, Integer videoId) {
    return new PrePlayRequest(inputs.getMapAnnotations(), inputs.getDebug(), videoId);
  }

  private static PostPlayRequest buildPostPlayRequest(
      PostPlayLolomoRequest inputs, Integer videoId) {
    Set<ClientCapabilityOption> clientCapabilityOptions =
        buildPostplayCapabilityOptions(inputs.getClientCapabilityOptions());
    return new PostPlayRequest(
        clientCapabilityOptions,
        inputs.getAnnotations(),
        inputs.getLimit(),
        inputs.getDebug(),
        videoId);
  }

  private static Set<ClientCapabilityOption> buildPostplayCapabilityOptions(
      List<PostPlayClientCapabilityOption> list) {
    Set<ClientCapabilityOption> opt = new HashSet<>();
    if (list != null) {
      for (PostPlayClientCapabilityOption item : list) {
        opt.add(ClientCapabilityOption.valueOf(item.name()));
      }
    }
    return opt;
  }

  private static final MapResponse EMPTY_MAPRESPONSE = MapResponseFactory.newInstance();

  @Override
  public CompletionStage<AnnotatedListReference> prePlayLolomo(
      Context ctx, Integer videoId, PrePlayLolomoRequest inputs) {

    CompletableFuture<AnnotatedListReference> defaultResponse =
        CompletableFuture.completedFuture(handlePrePostPlayLolomo(ctx, EMPTY_MAPRESPONSE));

    BatchPromise<Integer, VideoCore> corePromise = videosDataSource.getVideoCore().willCall(ctx);
    BatchPromise<Integer, Integer> numberPromise = vxsDataSource.episodeNumber().willCall(ctx);
    BatchPromise<Integer, Boolean> watchedPromise =
        vxsDataSource.hasBeenWatchedByUser().willCall(ctx);

    return VideoRoutesUtils.checkViewable(corePromise, videoId)
        .thenCompose(
            videoCore -> {
              var videoType = videoCore.getVideoBase().getVideoType().getType();
              var isEpisode = videoType == Type.TYPE_EPISODE;
              var isMovie = videoType == Type.TYPE_MOVIE;

              registry
                  .counter(
                      "dna.api.viewableRoutesImpl.prePlayLolomo", "videoType", videoType.toString())
                  .increment();

              // As part of the move to Mosaic, the team wants us to send all movies to preplay.
              // The gating done here was meant to prevent extraneous requests to them. Moving
              // forward, there will be more films using preplay and managing FPs to toggle that
              // behavior is more tedious than simply manging it on their side in preplay.
              // https://netflix.atlassian.net/browse/TLI-441
              if (isMovie) {
                numberPromise.cancel();
                watchedPromise.cancel();
                return getPrePlayLolomo(ctx, videoId, inputs);
              }

              // EAD-280: Always play recap for shows listed in api.force.recap.titles list. Or let
              // the backend decide what and whether to play
              if (isEpisode
                  && forceRecapTitles
                      .get()
                      .contains(videoCore.getVideoBase().getBoxedTopNodeId())) {
                numberPromise.cancel();
                watchedPromise.cancel();
                return getPrePlayLolomo(ctx, videoId, inputs);
              }
              /*
               * DNA-2875: Adding preplay gating logic: call the preplay service for eligible
               * viewable only -- the first episode of the original show, of any season other than
               * the first, unless customer started to watch it already. Otherwise, return an empty
               * MapResponse equivalent.
               * DNA-3043: Also call the preplay service in case the episode has an episode batch
               * parent
               */
              else if (isEpisode && videoCore.getOriginal()) {

                CompletableFuture<Boolean> e1S2Plus =
                    isFirstEpisodeOfSequelSeason(
                        videoId, videoCore.getEpisode(), numberPromise, watchedPromise);

                CompletableFuture<Boolean> batchMember =
                    isEpisodeBatchMember(videoCore.getEpisode());

                return CompletableFuture.allOf(e1S2Plus, batchMember)
                    .thenCompose(
                        ignore -> {
                          try {
                            if (e1S2Plus.get() || batchMember.get()) {
                              return watchedPromise
                                  .call(videoId)
                                  .thenCompose(
                                      hasBeenWatched -> {
                                        if (threePAdapter.ignoreBookmarksForPreplay()
                                            || hasBeenWatched == null
                                            || !hasBeenWatched) {
                                          return getPrePlayLolomo(ctx, videoId, inputs);
                                        } else {
                                          return defaultResponse;
                                        }
                                      });
                            } else {
                              watchedPromise.cancel();
                              return defaultResponse;
                            }
                          } catch (Exception e) {
                            watchedPromise.cancel();
                            logger.error("Error checking the preplay conditions on {}", videoId, e);
                            return defaultResponse;
                          }
                        });
              } else {
                numberPromise.cancel();
                watchedPromise.cancel();
                return defaultResponse;
              }
            });
  }

  private CompletionStage<AnnotatedListReference> getPrePlayLolomo(
      Context ctx, Integer videoId, PrePlayLolomoRequest inputs) {
    PrePlayRequest request = buildPrePlayRequest(inputs, videoId);
    return prePostPlayDataSource
        .prePlayPage()
        .lookup(ctx, request)
        .thenApply(response -> handlePrePostPlayLolomo(ctx, response));
  }

  private CompletableFuture<Boolean> isFirstEpisodeOfSequelSeason(
      int videoId,
      EpisodeDetail episode,
      BatchPromise<Integer, Integer> episodeNumberPromise,
      BatchPromise<Integer, Boolean> watchedPromise) {
    if (episode.getSeasonSequenceNumber().getValue() == 1) {
      episodeNumberPromise.cancel();
      return CompletableFuture.completedFuture(false);
    }
    return episodeNumberPromise
        .call(videoId)
        .exceptionally(
            err -> {
              watchedPromise.cancel();
              return chainedSneakyThrow(err);
            })
        .thenApply(episodeNumber -> episodeNumber == 1)
        .toCompletableFuture();
  }

  private CompletableFuture<Boolean> isEpisodeBatchMember(EpisodeDetail episode) {
    return CompletableFuture.completedFuture(episode.hasEpisodeBatchParent());
  }

  @Override
  public CompletionStage<Atom<List<TimeCode>>> clipOffsets(Context ctx, Integer videoId) {
    return timeCodes(ctx, videoId)
        .thenApply(map -> map.get(TimecodeType.TIMECODE_TYPE_CLIPS))
        .thenApply(tc -> tc == null ? List.<TimeCode>of() : List.of(tc))
        .thenApply(Atom::new);
  }

  private CompletionStage<Map<TimecodeType, TimeCode>> timeCodes(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoBase> basePromise = videosDataSource.getVideoBase().willCall(ctx);
    BatchPromise<Integer, Map<TimecodeType, TimeCode>> promise =
        videosDataSource.timeCodes().willCall(ctx);
    return VideoRoutesUtils.checkViewable(basePromise, videoId)
        .thenCompose(ignore -> promise.call(videoId));
  }

  @Override
  public CompletionStage<Integer> displayRuntimeInSeconds(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoBase> basePromise = videosDataSource.getVideoBase().willCall(ctx);
    BatchPromise<Integer, Integer> runtimePromise = videosDataSource.displayRuntime().willCall(ctx);
    return VideoRoutesUtils.checkViewable(basePromise, videoId)
        .thenCompose(ignore -> runtimePromise.call(videoId))
        .thenApply(runtime -> runtime != null ? runtime : 0);
  }

  @Override
  public CompletionStage<Long> endCreditOffset(Context ctx, Integer videoId) {
    return timeCodes(ctx, videoId)
        .thenApply(map -> map.get(TimecodeType.TIMECODE_TYPE_SKIP_CREDIT))
        .thenApply(TimeCode::getEnd);
  }

  @Override
  public CompletionStage<Long> endRecapOffset(Context ctx, Integer videoId) {
    return timeCodes(ctx, videoId)
        .thenApply(map -> map.get(TimecodeType.TIMECODE_TYPE_RECAP))
        .thenApply(TimeCode::getEnd);
  }

  @Override
  public CompletionStage<Atom<List<TimeCode>>> skipContent(
      Context ctx, Integer videoId, List<SkipContentType> types) {
    final Set<String> typeSet = types.stream()
        .map(SkipContentType::name)
        .collect(Collectors.toCollection(() -> new HashSet<>(types.size())));

    return timeCodes(ctx, videoId)
        .thenApply(
            map -> map.values().stream()
                .filter(tc -> typeSet.contains(tc.getType()))
                .collect(Collectors.toCollection(() -> new ArrayList<>(map.size()))))
        .thenApply(Atom::new);
  }

  @Override
  public CompletionStage<Long> hookMomentOffset(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoBase> basePromise = videosDataSource.getVideoBase().willCall(ctx);
    BatchPromise<Integer, Long> promise = vxsDataSource.logicalStartOffset().willCall(ctx);
    return VideoRoutesUtils.checkViewable(basePromise, videoId)
        .thenCompose(ignore -> promise.call(videoId));
  }

  @Override
  public CompletionStage<Boolean> isAgeVerificationProtected(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoBase> basePromise = videosDataSource.getVideoBase().willCall(ctx);
    BatchPromise<Integer, Boolean> ageVerificationProtectedPromise =
        videosDataSource.isAgeVerificationProtected().willCall(ctx);
    // only top nodes are protected
    return VideoRoutesUtils.checkViewable(basePromise, videoId)
        .thenApply(base -> base.getOptionalTopNodeId().orElse(videoId))
        .thenCompose(ageVerificationProtectedPromise::call);
  }

  @Override
  public CompletionStage<Boolean> isAutoplayEnabled(Context ctx, Integer videoId) {
    return getViewableInfo(ctx, videoId).thenApply(ViewableInformation::getAutoPlayEnabled);
  }

  @Override
  public CompletionStage<Boolean> isPinProtected(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoBase> promise = videosDataSource.getVideoBase().willCall(ctx);
    return VideoRoutesUtils.checkViewable(promise, videoId)
        .thenCompose(
            videoBase ->
                cmsDataSource
                    .legacyPinEnabled()
                    .lookup(
                        ctx,
                        isEpisode(videoBase.getVideoType().getType())
                            ? videoBase.getOptionalTopNodeId().orElse(videoId)
                            : videoId));
  }

  @Override
  public CompletionStage<Boolean> isR21PinProtected(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoBase> basePromise = videosDataSource.getVideoBase().willCall(ctx);
    BatchPromise<Integer, Boolean> r21Promise = cmsDataSource.r21PinEnabled().willCall(ctx);
    return VideoRoutesUtils.checkViewable(basePromise, videoId)
        .thenCompose(
            base ->
                r21Promise.call(
                    isEpisode(base.getVideoType().getType())
                        ? base.getOptionalTopNodeId().orElse(videoId)
                        : videoId));
  }

  @Override
  public CompletionStage<Boolean> isPreReleasePinProtected(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoCore> corePromise = videosDataSource.getVideoCore().willCall(ctx);
    return VideoRoutesUtils.checkViewable(corePromise, videoId)
        .thenApply(
            core ->
                core.getVideoInfo().getAvailability().getUnlaunchedContentPreview()
                    && core.getVideoBase().getVideoType().getType() != Type.TYPE_SUPPLEMENTAL);
  }

  @Override
  public CompletionStage<Long> logicalEndOffset(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoBase> basePromise = videosDataSource.getVideoBase().willCall(ctx);
    BatchPromise<Integer, Long> promise = vxsDataSource.logicalEndOffset().willCall(ctx);
    return VideoRoutesUtils.checkViewable(basePromise, videoId)
        .thenCompose(ignore -> promise.call(videoId));
  }

  @Override
  public CompletionStage<Long> logicalStartOffset(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoBase> basePromise = videosDataSource.getVideoBase().willCall(ctx);
    BatchPromise<Integer, Long> promise = vxsDataSource.logicalStartOffset().willCall(ctx);
    return VideoRoutesUtils.checkViewable(basePromise, videoId)
        .thenCompose(ignore -> promise.call(videoId));
  }

  @Override
  public CompletionStage<Long> logicalEndOffsetMs(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoBase> basePromise = videosDataSource.getVideoBase().willCall(ctx);
    BatchPromise<Integer, Long> promise = vxsDataSource.logicalEndOffsetMs().willCall(ctx);
    return VideoRoutesUtils.checkViewable(basePromise, videoId)
        .thenCompose(ignore -> promise.call(videoId));
  }

  @Override
  public CompletionStage<Long> logicalStartOffsetMs(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoBase> basePromise = videosDataSource.getVideoBase().willCall(ctx);
    BatchPromise<Integer, Long> promise = vxsDataSource.logicalStartOffsetMs().willCall(ctx);
    return VideoRoutesUtils.checkViewable(basePromise, videoId)
        .thenCompose(ignore -> promise.call(videoId));
  }

  @Override
  public CompletionStage<Long> runtime(Context ctx, Integer videoId) {
    return getViewableInfo(ctx, videoId)
        .thenApply(ViewableInformation::getRuntime)
        .thenApply(Duration::getSeconds);
  }

  @Override
  public CompletionStage<Long> startCreditOffset(Context ctx, Integer videoId) {
    return timeCodes(ctx, videoId)
        .thenApply(map -> map.get(TimecodeType.TIMECODE_TYPE_SKIP_CREDIT))
        .thenApply(TimeCode::getStart);
  }

  @Override
  public CompletionStage<Long> startRecapOffset(Context ctx, Integer videoId) {
    return timeCodes(ctx, videoId)
        .thenApply(map -> map.get(TimecodeType.TIMECODE_TYPE_RECAP))
        .thenApply(TimeCode::getStart);
  }

  @Override
  public CompletionStage<Long> watchedToEndOffset(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoBase> basePromise = videosDataSource.getVideoBase().willCall(ctx);
    BatchPromise<Integer, Long> promise = vxsDataSource.watchedToEndOffset().willCall(ctx);
    return VideoRoutesUtils.checkViewable(basePromise, videoId)
        .thenCompose(ignore -> promise.call(videoId));
  }

  @Override
  public CompletionStage<Long> watchedToEndOffsetMs(Context ctx, Integer videoId) {
    BatchPromise<Integer, VideoBase> basePromise = videosDataSource.getVideoBase().willCall(ctx);
    BatchPromise<Integer, Long> promise = vxsDataSource.watchedToEndOffsetMs().willCall(ctx);
    return VideoRoutesUtils.checkViewable(basePromise, videoId)
        .thenCompose(ignore -> promise.call(videoId));
  }

  @Override
  public CompletionStage<Atom<BookmarkWithOptionsResponse>> bookmarkWithOptions(
      Context ctx, Integer videoId, BookmarkWithOptionsRequest request) {
    final BatchPromise<Integer, VideoCore> corePromise =
        videosDataSource.getVideoCore().willCall(ctx);
    final BatchPromise<dna.api.datasources.bookmark.BookmarkWithOptionsRequest, IxBookmarkInfo>
        bookmarkPromise = interactiveDataSource.bookmarkWithOptions().willCall(ctx);

    return VideoRoutesUtils.checkViewable(corePromise, videoId)
        .thenCompose(
            core -> {
              if (isAvailable(core)) {
                return bookmarkPromise.call(
                    new dna.api.datasources.bookmark.BookmarkWithOptionsRequest(
                        videoId, request.getProfileGUID()));
              }
              // title is inactive -- no bookmark call
              bookmarkPromise.cancel();
              return CompletableFuture.completedFuture(null);
            })
        .thenApply(
            (bookmark) -> {
              if (bookmark == null) {
                return null;
              }

              return new Atom<>(
                  new BookmarkWithOptionsResponse()
                      .setLastModified(bookmark.getLastModified())
                      .setPosition(bookmark.getBookmarkPositionAsDouble())
                      .setNextPlaygraphSegmentId(bookmark.getNextPlaygraphSegmentId()));
            });
  }

  @Override
  @Deprecated
  public CompletionStage<Atom<Map<String, Object>>> ixVideoMoments(
      Context ctx, Integer videoId, UiMetadataRequest request) {
    return CompletableFuture.completedFuture(new Atom<>(Map.of()));
  }

  @Override
  public CompletionStage<Atom<IxPrePlayAnnotation>> ixPrePlayAnnotation(
      Context ctx, Integer videoId) {
    return CompletableFuture.completedFuture(Atom.emptyAtom());
  }

  @Override
  public CompletionStage<CallResponse<Boolean>> logIxPlaybackImpression(
      Context ctx, Integer videoId, CallRequest callRequest) {
    return CompletableFuture.completedFuture(CallResponses.withResult(false));
  }
}
