package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class AcceptConcurrentStreamsUpsellOptionResponse {
  /** ID of account owner */
  private Long customerID;

  public Long getCustomerID() {
    return customerID;
  }

  public AcceptConcurrentStreamsUpsellOptionResponse setCustomerID(Long customerID) {
    this.customerID = customerID;
    return this;
  }
}
