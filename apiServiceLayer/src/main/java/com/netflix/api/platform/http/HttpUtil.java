package com.netflix.api.platform.http;

import static java.util.function.Predicate.not;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Optional;

public class HttpUtil {

  private HttpUtil() {}

  public static String getPath(HttpServletRequest req) {
    String pathInfo = req.getPathInfo();
    if (pathInfo == null) {
      pathInfo = req.getServletPath();
    }
    return pathInfo;
  }

  public static Optional<String> originHeader(HttpServletRequest req) {
    return Optional.ofNullable(req.getHeader("Origin")).filter(not(String::isEmpty));
  }

  public static boolean isOptionsRequest(HttpServletRequest req) {
    return "OPTIONS".equals(req.getMethod());
  }
}
