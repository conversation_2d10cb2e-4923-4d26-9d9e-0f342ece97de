package com.netflix.api.service.titlegroup;

public enum APITitleGroupCategory {

  /**
   *
   *
   * <pre>
   * A group which contains a storyline that unfolds over multiple film or TV installments.
   * </pre>
   *
   * <code>CONTINUATIONS = 0;</code>
   */
  CONTINUATIONS(0),
  /**
   *
   *
   * <pre>
   * A group which contains all titles created by a performer playing himself/herself, or unscripted franchises that
   * feature standalone titles that fall under a common brand that are meant to be seen as a part of a larger set.
   * </pre>
   *
   * <code>COMPILATIONS = 2;</code>
   */
  COMPILATIONS(2),
  /**
   *
   *
   * <pre>
   * A group which contains titles that are a part of an overall story universe that branch out independently of one
   * another.
   * </pre>
   *
   * <code>BRANCHES_AND_NETWORKS = 3;</code>
   */
  BRANCHES_AND_NETWORKS(3),
  /**
   *
   *
   * <pre>
   * A group that contains titles that were successful in one market and then adapted for other local markets.
   * </pre>
   *
   * <code>RETELLINGS = 4;</code>
   */
  RETELLINGS(4),

  UNRECOGNIZED(-1),
  COMPANION_PIECES(5);

  private final int value;

  APITitleGroupCategory(int value) {
    this.value = value;
  }

  public int getValue() {
    return this.value;
  }
}
