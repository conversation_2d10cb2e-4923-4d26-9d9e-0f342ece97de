package com.netflix.api.service.titlegroup;

public class APITitleGroupSetVideoMemberImpl implements APITitleGroupSetMember {

  private final int _movieId;
  private final int _parentGroup;

  public APITitleGroupSetVideoMemberImpl(int movieId, int parent) {
    this._movieId = movieId;
    this._parentGroup = parent;
  }

  @Override
  public Integer getMemberId() {
    return _movieId;
  }

  @Override
  public Integer getParentGroupId() {
    return _parentGroup;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + _movieId;
    result = prime * result + _parentGroup;
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    APITitleGroupSetVideoMemberImpl other = (APITitleGroupSetVideoMemberImpl) obj;
    if (_movieId != other._movieId) return false;
    if (_parentGroup != other._parentGroup) return false;
    return true;
  }
}
