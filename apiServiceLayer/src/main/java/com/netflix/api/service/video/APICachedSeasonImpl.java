package com.netflix.api.service.video;

import com.netflix.api.service.batch.USTVideoMetadataAdapter;
import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.vms.provider.VideoFormatDescriptorProvider;

/**
 * This object extends the {@link APIVideo} object and provides the operations expected from a
 * "Season" type of object in the Netflix catalog.
 *
 * <p><img
 * src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.varieties.png">
 *
 * <p>A show has one or more seasons, and one or more episodes, one of which is the current episode.
 * A season has one show parent and has one or more episodes, one of which is the current episode.
 * An episode has either a series parent, or a show parent and a season parent.
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.class.png">
 * <img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.cluster2.png">
 *
 * <p><img src="https://confluence.netflix.com/download/attachments/52216234/APIVideo.cluster.png">
 */
public class APICachedSeasonImpl extends APICachedVideoImpl {

  public APICachedSeasonImpl(
      CompleteVideo v,
      APIVideoFactory videoFactory,
      USTVideoMetadataAdapter videoMetadataAdapter,
      VideoFormatDescriptorProvider videoFormatDescriptorProvider) {
    super(v, videoFactory, videoMetadataAdapter, videoFormatDescriptorProvider);
  }
}
