package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class ThumbSummary {
  private Long countTotal;

  private Long countThumbsUp;

  private Long countThumbsDown;

  private Float percentThumbsUp;

  /** time to live in seconds example: 86400 */
  private Long ttl;

  public Long getCountTotal() {
    return countTotal;
  }

  public Long getCountThumbsUp() {
    return countThumbsUp;
  }

  public Long getCountThumbsDown() {
    return countThumbsDown;
  }

  public Float getPercentThumbsUp() {
    return percentThumbsUp;
  }

  public Long getTtl() {
    return ttl;
  }

  public ThumbSummary setCountTotal(Long countTotal) {
    this.countTotal = countTotal;
    return this;
  }

  public ThumbSummary setCountThumbsUp(Long countThumbsUp) {
    this.countThumbsUp = countThumbsUp;
    return this;
  }

  public ThumbSummary setCountThumbsDown(Long countThumbsDown) {
    this.countThumbsDown = countThumbsDown;
    return this;
  }

  public ThumbSummary setPercentThumbsUp(Float percentThumbsUp) {
    this.percentThumbsUp = percentThumbsUp;
    return this;
  }

  public ThumbSummary setTtl(Long ttl) {
    this.ttl = ttl;
    return this;
  }
}
