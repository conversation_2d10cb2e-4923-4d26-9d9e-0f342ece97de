package com.netflix.api.platform.util;

import com.netflix.atlas.plugin.util.NetflixEnvironment;

/** Utility methods for determining what environment we're running in. */
public class APIEnvironment {
  private APIEnvironment() {}

  private static final String ENV = NetflixEnvironment.env();
  private static final String STACK = NetflixEnvironment.stack();
  private static final String CLUSTER = NetflixEnvironment.cluster();
  private static final String ASG = NetflixEnvironment.asg();
  private static final String INSTANCE_ID = NetflixEnvironment.instanceId();
  private static final boolean IS_TEST =
      "test".equalsIgnoreCase(ENV) || "dev".equalsIgnoreCase(ENV);
  private static final boolean IS_PROD = "prod".equalsIgnoreCase(ENV);

  public static boolean isTestOrDev() {
    return IS_TEST;
  }

  public static boolean isProd() {
    return IS_PROD;
  }

  public static String getEnvironment() {
    return ENV;
  }

  public static String getCluster() {
    return CLUSTER;
  }

  public static String getAsg() {
    return ASG;
  }

  public static String getStack() {
    return STACK;
  }

  public static String getInstanceId() {
    return INSTANCE_ID;
  }
}
