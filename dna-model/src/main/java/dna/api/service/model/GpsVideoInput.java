package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class GpsVideoInput {
  private List<Integer> dedupeVideoIds;

  private List<Integer> interactivePackageVideoIds;

  public List<Integer> getDedupeVideoIds() {
    return dedupeVideoIds;
  }

  public List<Integer> getInteractivePackageVideoIds() {
    return interactivePackageVideoIds;
  }

  public GpsVideoInput setDedupeVideoIds(List<Integer> dedupeVideoIds) {
    this.dedupeVideoIds = dedupeVideoIds;
    return this;
  }

  public GpsVideoInput setInteractivePackageVideoIds(List<Integer> interactivePackageVideoIds) {
    this.interactivePackageVideoIds = interactivePackageVideoIds;
    return this;
  }
}
