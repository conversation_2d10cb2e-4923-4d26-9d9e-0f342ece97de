package dna.api.util;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Test;

class JsonUtilTest {
  public record SomeObject(String method, String route, String description, Set<String> tags) {}

  @Test
  void serializeDeserializeList() {
    var sourceList =
        List.of(
            new SomeObject("set", "route1", null, Set.of("tag1", "tag2")),
            new SomeObject("set", "route2", null, Set.of("tag12", "tag22")));

    var jsonNode = JsonUtil.objToJson(sourceList);

    var definitions = JsonUtil.asListOfObjects(jsonNode, SomeObject.class);
    assertThat(definitions, equalTo(sourceList));
  }

  @Test
  void testIt() throws Exception {
    var ref = new TypeReference<List<String>>() {};
    var mapper = new ObjectMapper();

    var list = mapper.readValue("[\"foo\",\"bar\"]", ref);

    assertThat(list, equalTo(List.of("foo", "bar")));
  }
}
