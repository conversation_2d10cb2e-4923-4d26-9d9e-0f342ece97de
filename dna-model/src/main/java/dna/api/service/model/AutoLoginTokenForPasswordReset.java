package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** Contains fields related to token used for password reset. */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class AutoLoginTokenForPasswordReset {
  /** The auto login token. This token is only scoped for CHANGE_PASSWORD ability. */
  private String token;

  public String getToken() {
    return token;
  }

  public AutoLoginTokenForPasswordReset setToken(String token) {
    this.token = token;
    return this;
  }
}
