package com.netflix.api.startup.priming;

import com.fasterxml.jackson.core.util.MinimalPrettyPrinter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.google.inject.Binding;
import com.google.inject.Inject;
import com.google.inject.Key;
import com.google.inject.ProvisionException;
import com.google.inject.spi.Dependency;
import com.google.inject.spi.HasDependencies;
import com.google.inject.spi.ProvisionListener;
import com.netflix.governator.LifecycleManager;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PrimingMetricsLogger {
  private static final String LOGGING_HOME = "logging.home";
  private static final Logger LOGGER = LoggerFactory.getLogger(PrimingMetricsLogger.class);

  enum Type {
    VISIT_START,
    VISIT_END,
    PROV_START,
    PROV_END,
    PROV_FAILED
  }

  static class Event {
    final Key<?> key;
    final Set<Key<?>> dependencies;
    final Type type;
    final long ts;
    final String threadId;

    public Event(Key<?> key, Type type, long ts, String threadId, Set<Key<?>> dependencies) {
      super();
      this.key = key;
      this.type = type;
      this.ts = ts;
      this.threadId = threadId;
      this.dependencies = dependencies;
    }
  }

  private static final ConcurrentLinkedQueue<Event> events = new ConcurrentLinkedQueue<>();

  private static final AtomicBoolean isStartingUp = new AtomicBoolean(true);

  public static void visitStart(Key<?> key, Set<Key<?>> dependencies) {
    events.add(
        new Event(
            key,
            Type.VISIT_START,
            System.nanoTime(),
            Thread.currentThread().getName(),
            dependencies));
  }

  public static void visitEnd(Key<?> key, Set<Key<?>> dependencies) {
    events.add(
        new Event(
            key,
            Type.VISIT_END,
            System.nanoTime(),
            Thread.currentThread().getName(),
            dependencies));
  }

  private static void provisionBegin(Key<?> key, Set<Key<?>> dependencies) {
    events.add(
        new Event(
            key,
            Type.PROV_START,
            System.nanoTime(),
            Thread.currentThread().getName(),
            dependencies));
  }

  private static void provisionEnd(Key<?> key, Set<Key<?>> dependencies) {
    events.add(
        new Event(
            key, Type.PROV_END, System.nanoTime(), Thread.currentThread().getName(), dependencies));
  }

  private static void provisionFailed(Key<?> key) {
    events.add(
        new Event(
            key,
            Type.PROV_FAILED,
            System.currentTimeMillis(),
            Thread.currentThread().getName(),
            Set.of()));
  }

  public static void dump() throws IOException {

    String loggingHome = System.getProperty(LOGGING_HOME);
    File loggingFile = null;
    if (loggingHome != null) {
      File loggingHomeFile = new File(loggingHome);
      if (loggingHomeFile.exists() && loggingHomeFile.isDirectory()) {
        loggingFile = new File(loggingHomeFile, "startup-priming-trace.json.log");
      }
    }

    if (loggingFile == null) {
      loggingFile = File.createTempFile("startup-priming-trace", ".json.log");
    }

    List<Map<String, Object>> traceEvents = new ArrayList<>();
    while (!events.isEmpty()) {
      Event event = events.poll();
      Map<String, Object> eventOut = new HashMap<>();
      eventOut.put("name", event.key.toString());
      if (event.type == Type.VISIT_END || event.type == Type.VISIT_START) {
        eventOut.put("cat", "visit");
        eventOut.put("pid", 0);
      } else {
        eventOut.put("cat", "provision");
        eventOut.put("pid", 1);
      }

      eventOut.put("ts", TimeUnit.NANOSECONDS.toMicros(event.ts));
      eventOut.put("tid", event.threadId);

      eventOut.put("args", event.dependencies.stream().map(Key::toString).toList());

      switch (event.type) {
        case VISIT_END, PROV_END -> eventOut.put("ph", "E");
        case PROV_START, VISIT_START -> eventOut.put("ph", "B");
        case PROV_FAILED -> eventOut.put("ph", "n");
      }

      traceEvents.add(eventOut);
    }

    Map<String, Object> doc = new HashMap<>();
    doc.put("traceEvents", traceEvents);
    doc.put("displayTimeUnit", "ms");

    if (traceEvents.isEmpty()) return;

    ObjectMapper mapper = new ObjectMapper();
    ObjectWriter writer = mapper.writer(new MinimalPrettyPrinter());
    writer.writeValue(loggingFile, doc);

    LOGGER.info("Priming statup trace: {}", loggingFile);
  }

  public static class PrimingProvisioningListener
      implements ProvisionListener, com.netflix.governator.spi.LifecycleListener {

    @Inject
    protected void initialize(LifecycleManager manager) {
      manager.addListener(this);
    }

    @Override
    public <T> void onProvision(ProvisionInvocation<T> provision) {
      if (!isStartingUp.get()) {
        provision.provision();
        return;
      }

      Binding<?> binding = provision.getBinding();
      Set<Dependency<?>> dependencies =
          (binding instanceof HasDependencies b) ? b.getDependencies() : Set.of();

      if (dependencies == null) {
        dependencies = Set.of();
      }

      Set<Key<?>> keys = dependencies.stream().map(Dependency::getKey).collect(Collectors.toSet());

      try {
        provisionBegin(binding.getKey(), keys);
        provision.provision();
      } catch (ProvisionException e) {
        provisionFailed(binding.getKey());
        throw e;
      } finally {
        provisionEnd(binding.getKey(), keys);
      }
    }

    @Override
    public void onStarted() {
      if (isStartingUp.getAndSet(false)) {
        try {
          dump();
        } catch (IOException e) {
          LOGGER.warn("Can't generate the priming tracing logs", e);
        } finally {
          events.clear();
        }
      }
    }

    @Override
    public void onStopped(Throwable error) {}
  }
}
