package com.netflix.api.service.identity;

import static com.netflix.subscriber.types.protogen.Membership.Status.CURRENT_MEMBER;

import com.netflix.api.platform.avatar.LegacyIconNameMapper;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.archaius.api.Property;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.membership.MembershipStatus;
import com.netflix.subscriber.types.protogen.Membership;
import com.netflix.subscriberservice.client.model.AccountProfile;
import com.netflix.subscriberservice.client.model.AccountProfileImpl;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import jakarta.annotation.Nullable;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings("deprecation")
public class APIUserUtil {
  public static final Logger LOGGER = LoggerFactory.getLogger(APIUserUtil.class);

  private static final Property<Boolean> ENABLE_MAPPED_PROFILE_AVATAR =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.enableMappedProfileAvatar", Boolean.class)
          .orElse(false);

  public static Long currentAccountId() {
    return Optional.ofNullable(APIRequest.getCurrentRequest())
        .map(APIRequest::getUser)
        .map(APIUserUtil::getAccountOwnerId)
        .orElse(null);
  }

  public static Long getCustomerId(APIUser user) {
    if (user == null) {
      return null;
    }

    // should always be the case
    if (user instanceof APIUserInternal internal) {
      return internal.getRealCustomerId();
    }

    logUnexpectedUsage("unexpected user instance: " + user.getClass());

    // should never happen
    return -1L;
  }

  public static Long getAccountOwnerId(APIUser user) {
    if (user == null) {
      return null;
    }
    return extractRealAccountOwnerId(user);
  }

  private static long extractRealAccountOwnerId(APIUser user) {
    if (user instanceof APIUserInternal internal) {
      return internal.getRealAccountOwnerId();
    }

    logUnexpectedUsage("unexpected user instance: " + user.getClass());

    return -1L;
  }

  private static void logUnexpectedUsage(String reason) {
    LOGGER.debug("netflix.api.customer.id.unexpected.usage: {}", reason);
    MantisPublishContext mantis = MantisPublishContext.getCurrent();
    mantis.add("netflix.api.customer.id.unexpected.usage", reason);
  }

  @Deprecated
  public static @Nullable AccountProfile getAccountProfileLegacy() {
    return currentAccountProfile().map(AccountProfileImpl::fromRemoteObject).orElse(null);
  }

  /**
   * Transforms a profile icon into the new avatar name, have this in place until subscriber fork
   * lifts their datastore to not include legacy names
   *
   * @return the mapped avatar image value
   */
  public static String getAvatarImage(AccountProfileRemote accountProfile) {
    if (accountProfile == null) return null;
    if (ENABLE_MAPPED_PROFILE_AVATAR.get()) {
      return LegacyIconNameMapper.from(accountProfile.getBoxedAvatarImg());
    } else {
      return accountProfile.getBoxedAvatarImg();
    }
  }

  /**
   * @return current account profile (if available)
   * @see AccountProfile
   */
  public static Optional<AccountProfileRemote> currentAccountProfile() {
    return Optional.ofNullable(APIRequest.getCurrentRequest())
        .map(APIRequest::getUser)
        .map(APIUserInternal.class::cast)
        .map(APIUserInternal::getAccountProfile);
  }

  public static boolean isActiveOrOnHold(AccountProfileRemote accountProfile) {
    return MembershipStatus.fromProto(accountProfile.getMembershipStatusEnum()).isActiveOrOnHold();
  }

  public static boolean isNotCurrentMember(AccountProfileRemote account) {
    return CURRENT_MEMBER != account.getMembershipStatusEnum();
  }

  public static Membership.Status getMembershipStatus(AccountProfileRemote accountProfile) {
    String membershipStatus = accountProfile.getOptionalMembershipStatus().orElse(null);
    if (membershipStatus == null || membershipStatus.equals(MembershipStatus.UNRECOGNIZED.name())) {
      return Membership.Status.UNRECOGNIZED;
    }
    try {
      return Membership.Status.valueOf(membershipStatus);
    } catch (IllegalArgumentException e) {
      return Membership.Status.UNRECOGNIZED;
    }
  }

  public static @Nullable AccountProfileRemote getAccountProfileRemote(APIUser user) {
    if (user instanceof APIUserInternal internal) {
      return internal.getAccountProfile();
    }
    return null;
  }
}
