package com.netflix.api.service.account;

import com.netflix.api.service.identity.APIUser;

public class UpdateProfileConfigurationRequest {

  private final boolean disableVideoMerch;
  private final APIUser user;

  private UpdateProfileConfigurationRequest(boolean disableVideoMerch, APIUser user) {
    this.disableVideoMerch = disableVideoMerch;
    this.user = user;
  }

  public boolean isDisableVideoMerch() {
    return disableVideoMerch;
  }

  public APIUser getUser() {
    return user;
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {

    private boolean disableVideoMerch;
    private APIUser user;

    private Builder() {}

    public Builder setDisableVideoMerch(boolean disableVideoMerch) {
      this.disableVideoMerch = disableVideoMerch;
      return this;
    }

    public Builder setUser(APIUser user) {
      this.user = user;
      return this;
    }

    public UpdateProfileConfigurationRequest build() {
      return new UpdateProfileConfigurationRequest(disableVideoMerch, user);
    }

    @Override
    public String toString() {
      return "UpdateProfileConfigurationRequest.Builder{"
          + "disableVideoMerch="
          + disableVideoMerch
          + ", user="
          + user
          + '}';
    }
  }
}
