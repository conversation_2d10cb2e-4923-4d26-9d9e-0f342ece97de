package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class BeneficiaryProfile implements GraphData {
  public String name;

  public Map<String, Atom<Image>> avatar;

  public String getName() {
    return name;
  }

  public BeneficiaryProfile setName(String name) {
    this.name = name;
    return this;
  }
}
