package com.netflix.api.service.vhs;

import java.util.List;

/**
 * Contains a video's viewing history.
 *
 * <p>Clients generally only have a use case for getting the most recent viewing activity to display
 * the bookmark.
 */
public interface APIVideoViewingRecord {
  /**
   * Get the video for this record, which will always be an <code>APIViewable</code>.
   *
   * @return the video for this record
   */
  Integer getVideo();

  APIVideoViewingDetails getMostRecentViewingDetails();

  List<APIVideoViewingDetails> getAllViewingDetails();

  long getMostRecentDate();
}
