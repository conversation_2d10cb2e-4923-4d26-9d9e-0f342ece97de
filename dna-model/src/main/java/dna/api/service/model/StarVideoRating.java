package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class StarVideoRating implements GraphData {
  public Atom<StarRatingData> rating;

  public Video video;

  public Atom<StarRatingData> getRating() {
    return rating;
  }

  public StarVideoRating setRating(Atom<StarRatingData> rating) {
    this.rating = rating;
    return this;
  }
}
