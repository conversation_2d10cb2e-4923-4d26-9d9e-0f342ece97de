package com.netflix.api.insights;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.lang.RequestVariable;
import com.netflix.mantis.publish.api.MantisPublishContext;
import java.util.concurrent.ThreadLocalRandom;

public final class Insights {
  private static final int SAMPLING_UPPER_BOUND_EXCLUSIVE = 10000;

  private final boolean canLog;

  private static final Property<Integer> INSIGHTS_SAMPLING =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.insights.sampleFrom10000", Integer.class)
          .orElse(100);

  private static final RequestVariable<Insights> INSIGHTS_REQUEST_VARIABLE =
      new RequestVariable<>() {
        @Override
        protected Insights initialValue() {
          return new Insights();
        }
      };

  public static Insights getRequestScopedInstance() {
    return INSIGHTS_REQUEST_VARIABLE.get();
  }

  private Insights() {
    Integer samplingValue = INSIGHTS_SAMPLING.get();
    if (samplingValue >= SAMPLING_UPPER_BOUND_EXCLUSIVE) {
      this.canLog = true;
    } else if (samplingValue <= 0) {
      this.canLog = false;
    } else {
      int randomNumber = ThreadLocalRandom.current().nextInt(0, SAMPLING_UPPER_BOUND_EXCLUSIVE);
      MantisPublishContext mantis = MantisPublishContext.getCurrent();
      mantis.add("api_insights_sampleFrom10000", randomNumber);
      this.canLog = randomNumber < samplingValue;
    }
  }

  // This function helps centralize the logic if certain key-value pairs should be logged to
  // MantisContext or not.
  // In general, key-val pairs can be logged to MantisContext without needing sampling. But in some
  // cases, logging itself
  // can be expensive. In that case, this function can help with sampling.
  public boolean canLog() {
    return this.canLog;
  }
}
