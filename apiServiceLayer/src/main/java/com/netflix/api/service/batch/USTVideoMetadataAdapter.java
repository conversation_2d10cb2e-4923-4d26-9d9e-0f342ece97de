package com.netflix.api.service.batch;

import static com.netflix.api.context.USTContexts.personalizedVideoContext;

import com.google.protobuf.Duration;
import com.google.protobuf.util.Durations;
import com.netflix.api.context.USTContexts;
import com.netflix.api.grpc.GrpcAsyncCall;
import com.netflix.api.grpc.GrpcCallHelpers.Future;
import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.grpc.GrpcCallHelpers.RxSingle;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.identity.APIUserUtil;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.cms.protogen.TagsRecipe;
import com.netflix.evidence.protogen.ClientCapabilities;
import com.netflix.evidence.protogen.LabelType;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.pacs.protogen.ContentGrants;
import com.netflix.textevidence.protogen.SynopsisType;
import com.netflix.textevidence.protogen.TitleType;
import com.netflix.textevidence.protogen.VideoType;
import com.netflix.type.protogen.BasicTypes.Video;
import com.netflix.ust.adapters.USTCustomers;
import com.netflix.ust.adapters.USTStatus;
import com.netflix.ust.adapters.USTVideos;
import com.netflix.vxs.protogen.OffsetType;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Single;
import rx.functions.Func1;
import ust.playback.history.v1.GetHasTitleBeenWatchedRequest;
import ust.playback.history.v1.HasTitleBeenWatchedResult;
import ust.playback.history.v1.PlaybackHistoryAccess;
import ust.video.personalized.v1.GetAvailabilityDateMessagingRequest;
import ust.video.personalized.v1.GetAvailabilityDateMessagingResponse;
import ust.video.personalized.v1.GetBadgeRequest;
import ust.video.personalized.v1.GetBadgeResponse;
import ust.video.personalized.v1.GetCharacterVideoCountRequest;
import ust.video.personalized.v1.GetEpisodeListRequest;
import ust.video.personalized.v1.GetEpisodeNumberRequest;
import ust.video.personalized.v1.GetEpisodeRequest;
import ust.video.personalized.v1.GetEpisodeResponse;
import ust.video.personalized.v1.GetOffsetRequest;
import ust.video.personalized.v1.GetSeasonLabelRequest;
import ust.video.personalized.v1.GetSeasonLabelResponse;
import ust.video.personalized.v1.GetSeasonsRequest;
import ust.video.personalized.v1.GetSeasonsResponse;
import ust.video.personalized.v1.GetSynopsisRequest;
import ust.video.personalized.v1.GetSynopsisResponse;
import ust.video.personalized.v1.GetTaglineRequest;
import ust.video.personalized.v1.GetTaglineResponse;
import ust.video.personalized.v1.GetTagsRequest;
import ust.video.personalized.v1.GetTagsResponse;
import ust.video.personalized.v1.GetTitleBehaviorsRequest;
import ust.video.personalized.v1.GetTitleRequest;
import ust.video.personalized.v1.GetTitleResponse;
import ust.video.personalized.v1.GetUpcomingSeasonRequest;
import ust.video.personalized.v1.GetUpcomingSeasonResponse;
import ust.video.personalized.v1.SeasonResult;
import ust.video.personalized.v1.TaglineResult;
import ust.video.personalized.v1.TagsResult;
import ust.video.personalized.v1.VideoPersonalizedAccess;
import ust.video.v1.EpisodeData;
import ust.video.v1.SeasonData;
import ust.video.v1.TagData;
import ust.video.v1.TaglineData;
import ust.video.v1.TitleBehaviorData;

@Component
public class USTVideoMetadataAdapter {
  public static final Property<Boolean> ENABLE_VXS_DEBUG =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.vxs.enableDebug", Boolean.class)
          .orElse(false);
  public static final Property<Boolean> ENABLE_TE_DEBUG =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.text.evidence.enableDebug", Boolean.class)
          .orElse(false);
  public static final Property<Boolean> ENABLE_CMS_DEBUG =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.cms.enableDebug", Boolean.class)
          .orElse(false);

  private static final Property<Boolean> ENABLE_MANTIS =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.ust.video.mantis.enabled", Boolean.class)
          .orElse(false);

  private final VideoPersonalizedAccess videoPersonalizedAccess;
  private final PlaybackHistoryAccess playbackHistoryAccess;
  private final Property<Boolean> enableVXSReturnEmptyForUnauthorized;

  @Autowired
  USTVideoMetadataAdapter(
      VideoPersonalizedAccess videoPersonalizedAccess,
      PlaybackHistoryAccess playbackHistoryAccess,
      final PropertyRepository propertyRepository) {
    this.videoPersonalizedAccess = videoPersonalizedAccess;
    this.playbackHistoryAccess = playbackHistoryAccess;

    enableVXSReturnEmptyForUnauthorized =
        propertyRepository
            .get("api.enableVXSReturnEmptyForUnauthorized", Boolean.class)
            .orElse(true);
  }

  ////////////////////////////// CMS //////////////////////////////////////////////

  public CompletionStage<Map<Integer, String>> getBadges(Collection<Integer> ids) {
    return Future.call(videoPersonalizedAccess::getBadge, buildBadgeRequest(ids))
        .thenApply(USTVideoMetadataAdapter::parseBadgeReply);
  }

  private static GetBadgeRequest buildBadgeRequest(Collection<Integer> ids) {
    return GetBadgeRequest.newBuilder()
        .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_CMS_DEBUG.get()))
        .setVideoRequest(USTContexts.videoRequestBuilder(ids))
        .build();
    // TODO set up a FP and send AB allocations if there's ever badge-related AB tests
  }

  private static Map<Integer, String> parseBadgeReply(GetBadgeResponse response) {
    return response.getResultsList().stream()
        .filter(badgeResult -> USTStatus.isOk(badgeResult.getStatus()))
        .collect(
            Collectors.toMap(
                badgeResult -> badgeResult.getKey().getId(),
                badgeResult -> badgeResult.getResult().getBadgeType()));
  }

  //////////////// TEXT EVIDENCE //////////////////////////////////////////////////

  public CompletionStage<Map<Integer, String>> getNumSeasonsLabel(
      Collection<Integer> ids, LabelType labelType) {
    return getNumSeasonsLabel(ids, labelType, Collections.emptySet());
  }

  public CompletionStage<Map<Integer, String>> getNumSeasonsLabel(
      Collection<Integer> ids,
      LabelType labelType,
      Collection<ClientCapabilities> clientCapabilities) {
    return Future.call(
            videoPersonalizedAccess::getNumSeasonsLabel,
            buildSeasonLabelRequest(ids, labelType, clientCapabilities))
        .thenApply(USTVideoMetadataAdapter::parseSeasonLabelReply);
  }

  public CompletionStage<Map<Integer, String>> getSeasonSequenceNumberLabel(
      Collection<Integer> ids, LabelType labelType) {
    return getSeasonSequenceNumberLabel(ids, labelType, Collections.emptySet());
  }

  public CompletionStage<Map<Integer, String>> getSeasonSequenceNumberLabel(
      Collection<Integer> ids,
      LabelType labelType,
      Collection<ClientCapabilities> clientCapabilities) {
    return Future.call(
            videoPersonalizedAccess::getSeasonSequenceNumberLabel,
            buildSeasonLabelRequest(ids, labelType, clientCapabilities))
        .thenApply(USTVideoMetadataAdapter::parseSeasonLabelReply);
  }

  private static GetSeasonLabelRequest buildSeasonLabelRequest(
      Collection<Integer> ids,
      LabelType labelType,
      Collection<ClientCapabilities> clientCapabilities) {
    return GetSeasonLabelRequest.newBuilder()
        .setPersonalizedVideoContext(
            personalizedVideoContext(ENABLE_TE_DEBUG.get(), clientCapabilities))
        .setVideoRequest(USTContexts.videoRequestBuilder(ids))
        .setLabelType(labelType)
        .build();
  }

  private static Map<Integer, String> parseSeasonLabelReply(GetSeasonLabelResponse response) {
    if (ENABLE_MANTIS.get()) {
      MantisPublishContext.getCurrent()
          .add("ust.VideoPersonalizedAccess.SeasonLabel.response", USTListUtils.toJson(response));
    }
    return response.getResultsList().stream()
        .filter(result -> USTStatus.isOk(result.getStatus()))
        .collect(
            Collectors.toMap(
                result -> result.getKey().getId(), result -> result.getResult().getLabel()));
  }

  public Single<String> getAvailabilityDateMessaging(Integer id) {
    return RxSingle.defer(
            videoPersonalizedAccess::getAvailabilityDateMessaging,
            buildAvailabilityRequest(Collections.singleton(id)))
        .map(USTVideoMetadataAdapter::parseAvailabilityReply)
        .map(lookupById(id));
  }

  public CompletionStage<Map<Integer, String>> getAvailabilityDateMessaging(
      Collection<Integer> ids) {
    return Future.call(
            videoPersonalizedAccess::getAvailabilityDateMessaging, buildAvailabilityRequest(ids))
        .thenApply(USTVideoMetadataAdapter::parseAvailabilityReply);
  }

  private static GetAvailabilityDateMessagingRequest buildAvailabilityRequest(
      Collection<Integer> ids) {
    return GetAvailabilityDateMessagingRequest.newBuilder()
        .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_TE_DEBUG.get()))
        .setVideoRequest(USTContexts.videoRequestBuilder(ids))
        .build();
  }

  private static Map<Integer, String> parseAvailabilityReply(
      GetAvailabilityDateMessagingResponse response) {
    return response.getResultsList().stream()
        .filter(result -> USTStatus.isOk(result.getStatus()))
        .filter(result -> result.getResult().isInitialized())
        .collect(
            Collectors.toMap(
                result -> result.getKey().getId(), result -> result.getResult().getValue()));
  }

  public CompletionStage<Map<Integer, List<TagData>>> getTags(
      Collection<Integer> ids,
      Map<String, String> customContext,
      TagsRecipe tagsRecipe,
      boolean disableTagSelection) {
    return Future.call(
            videoPersonalizedAccess::getTags,
            buildTagsRequest(ids, customContext, tagsRecipe, disableTagSelection))
        .thenApply(USTVideoMetadataAdapter::parseTagsReply);
  }

  public CompletionStage<Map<Integer, TaglineData>> getTagline(
      Collection<Integer> ids, Map<String, String> customContext, @Nullable ContentGrants grants) {
    return Future.call(
            videoPersonalizedAccess::getTagline, buildTaglineRequest(ids, customContext, grants))
        .thenApply(USTVideoMetadataAdapter::parseTaglineReply);
  }

  public CompletionStage<Map<Integer, List<TaglineData>>> getTaglines(
      Collection<Integer> ids,
      String uiContext,
      Boolean withExplanationMessage,
      @Nullable Map<String, String> annotations,
      @Nullable ContentGrants grants) {
    return Future.call(
            videoPersonalizedAccess::getTagline,
            buildTaglineRequest(ids, uiContext, withExplanationMessage, annotations, grants))
        .thenApply(USTVideoMetadataAdapter::parseTaglines);
  }

  private static GetTagsRequest buildTagsRequest(
      Collection<Integer> ids,
      Map<String, String> customContext,
      TagsRecipe tagsRecipe,
      boolean disableTagSelection) {
    GetTagsRequest.Builder builder =
        GetTagsRequest.newBuilder()
            .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_TE_DEBUG.get()))
            .setVideoRequest(USTContexts.videoRequestBuilder(ids))
            .setUiContext(uiContextFromCustomContext(customContext)) // oy
            .setDisableTagSelection(disableTagSelection)
            .setUiFlavor(USTContexts.uiFlavor());
    if (tagsRecipe != null) {
      builder.setTagsRecipe(tagsRecipe);
    }
    if (customContext != null) {
      builder.putAllAnnotations(customContext);
    }
    return builder.build();
  }

  private static GetTaglineRequest buildTaglineRequest(
      Collection<Integer> ids, Map<String, String> customContext, @Nullable ContentGrants grants) {
    GetTaglineRequest.Builder builder =
        GetTaglineRequest.newBuilder()
            .setPersonalizedVideoContext(
                personalizedVideoContext(ENABLE_TE_DEBUG.get())) // ab allocations
            .setVideoRequest(USTContexts.videoRequestBuilder(ids))
            .setUiContext(uiContextFromCustomContext(customContext)) // oy
            .putAllAnnotations(customContext); // eek
    Optional.ofNullable(grants).ifPresent(builder::setContentGrants);
    return builder.build();
  }

  private static GetTaglineRequest buildTaglineRequest(
      Collection<Integer> ids,
      String uiContext,
      Boolean withExplanationMessage,
      @Nullable Map<String, String> annotations,
      @Nullable ContentGrants grants) {
    GetTaglineRequest.Builder builder =
        GetTaglineRequest.newBuilder()
            .setPersonalizedVideoContext(
                personalizedVideoContext(ENABLE_TE_DEBUG.get())) // ab allocations
            .setVideoRequest(USTContexts.videoRequestBuilder(ids))
            .setUiContext(uiContext)
            .setWithExplanationMessage(withExplanationMessage);
    Optional.ofNullable(annotations).ifPresent(builder::putAllAnnotations);
    Optional.ofNullable(grants).ifPresent(builder::setContentGrants);
    return builder.build();
  }

  private static String uiContextFromCustomContext(Map<String, String> customContext) {
    return Optional.ofNullable(customContext)
        .map(ignore -> customContext.get("uiContext"))
        .orElse(null);
  }

  private static Map<Integer, TaglineData> parseTaglineReply(GetTaglineResponse response) {
    return response.getResultsList().stream()
        .filter(taglineResult -> USTStatus.isOk(taglineResult.getStatus()))
        .collect(
            Collectors.toMap(
                taglineResult -> taglineResult.getKey().getId(),
                taglineResult ->
                    taglineResult.getTaglineDataList().isEmpty()
                        ? TaglineData.getDefaultInstance()
                        : taglineResult.getTaglineDataList().getFirst()));
  }

  private static Map<Integer, List<TaglineData>> parseTaglines(GetTaglineResponse response) {
    return response.getResultsList().stream()
        .filter(taglineResult -> USTStatus.isOk(taglineResult.getStatus()))
        .collect(
            Collectors.toMap(
                taglineResult -> taglineResult.getKey().getId(),
                TaglineResult::getTaglineDataList));
  }

  private static Map<Integer, List<TagData>> parseTagsReply(GetTagsResponse response) {
    return response.getResultsList().stream()
        .filter(tagsResult -> USTStatus.isOk(tagsResult.getStatus()))
        .collect(
            Collectors.toMap(
                tagsResult -> tagsResult.getKey().getId(), TagsResult::getResultsList));
  }

  public Observable<String> getTitle(TitleType titleType, VideoType videoType, Integer id) {
    return RxObservable.defer(
            videoPersonalizedAccess::getTitle,
            buildTitleRequest(titleType, videoType, Collections.singleton(id)))
        .map(USTVideoMetadataAdapter::parseTitleReply)
        .map(lookupById(id));
  }

  public CompletionStage<Map<Integer, String>> getTitles(
      TitleType titleType, VideoType videoType, Collection<Integer> ids) {
    return Future.call(
            videoPersonalizedAccess::getTitle, buildTitleRequest(titleType, videoType, ids))
        .thenApply(USTVideoMetadataAdapter::parseTitleReply);
  }

  private static GetTitleRequest buildTitleRequest(
      TitleType titleType, VideoType videoType, Collection<Integer> ids) {
    return GetTitleRequest.newBuilder()
        .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_TE_DEBUG.get()))
        .setVideoRequest(USTContexts.videoRequestBuilder(ids))
        .setTitleType(titleType)
        .setVideoType(videoType)
        .build();
    // TODO set up a FP and send AB allocations if there's ever title-related AB tests
  }

  private static Map<Integer, String> parseTitleReply(GetTitleResponse response) {
    return response.getResultsList().stream()
        .filter(titleResult -> USTStatus.isOk(titleResult.getStatus()))
        .collect(
            Collectors.toMap(
                titleResult -> titleResult.getKey().getId(),
                titleResult -> titleResult.getResult().getValue()));
  }

  public CompletionStage<Map<Integer, String>> getSynopsis(
      SynopsisType synopsisType, Collection<Integer> ids) {
    return Future.call(
            videoPersonalizedAccess::getSynopsis, buildSynopsisRequest(synopsisType, ids))
        .thenApply(USTVideoMetadataAdapter::parseSynopsisReply);
  }

  private static GetSynopsisRequest buildSynopsisRequest(
      SynopsisType synopsisType, Collection<Integer> ids) {
    return GetSynopsisRequest.newBuilder()
        .setPersonalizedVideoContext(
            personalizedVideoContext(ENABLE_TE_DEBUG.get())) // ab allocations
        .setVideoRequest(USTContexts.videoRequestBuilder(ids))
        .setSynopsisType(synopsisType)
        .build();
  }

  private static Map<Integer, String> parseSynopsisReply(GetSynopsisResponse response) {
    return response.getResultsList().stream()
        .filter(synopsisResult -> USTStatus.isOk(synopsisResult.getStatus()))
        .collect(
            Collectors.toMap(
                synopsisResult -> synopsisResult.getKey().getId(),
                synopsisResult -> synopsisResult.getResult().getValue()));
  }

  ///////////////////////// VXS ///////////////////////////////////////////////////////////////

  public CompletionStage<Map<Integer, Boolean>> hasBeenWatchedByUser(Collection<Integer> videoIds) {
    Long customerId = APIUserUtil.getCustomerId(APIRequest.getCurrentRequest().getUser());
    if (customerId == null || customerId == 0) {
      if (enableVXSReturnEmptyForUnauthorized.get()) {
        // kkelani 7/26: customer ID can be -1 too (see getCustomerId). -1 doesn't seem to be an
        // issue right now though.
        return CompletableFuture.completedFuture(Map.of());
      }
    }

    if (customerId == null) {
      throw new IllegalArgumentException("User / customerId must be non-null");
    }
    if (videoIds == null || videoIds.isEmpty()) {
      return CompletableFuture.completedFuture(Map.of());
    }
    return Future.call(
            playbackHistoryAccess::getHasTitleBeenWatched,
            GetHasTitleBeenWatchedRequest.newBuilder()
                .setCustomer(USTCustomers.fromId(customerId))
                .addAllVideos(USTVideos.toVideos(videoIds))
                .build())
        .thenApply(
            response ->
                response.getHasTitleBeenWatchedResultsList().stream()
                    .filter(result -> USTStatus.isOk(result.getStatus()))
                    .collect(
                        Collectors.toMap(
                            result -> result.getKey().getId(),
                            HasTitleBeenWatchedResult::getResult)));
  }

  public CompletionStage<Map<Integer, Integer>> getCurrentEpisode(Collection<Integer> videoIds) {
    return getEpisode(videoPersonalizedAccess::getCurrentEpisode, videoIds);
  }

  public CompletionStage<Map<Integer, Integer>> getNextEpisode(Collection<Integer> videoIds) {
    return getEpisode(videoPersonalizedAccess::getNextEpisode, videoIds);
  }

  public CompletionStage<Map<Integer, Integer>> getPreviousEpisode(Collection<Integer> videoIds) {
    return getEpisode(videoPersonalizedAccess::getPreviousEpisode, videoIds);
  }

  private CompletionStage<Map<Integer, Integer>> getEpisode(
      GrpcAsyncCall<GetEpisodeRequest, GetEpisodeResponse> serviceCall,
      Collection<Integer> videoIds) {
    final var customerId = APIUserUtil.getCustomerId(APIRequest.getCurrentRequest().getUser());
    if (customerId == null || customerId == 0) {
      if (enableVXSReturnEmptyForUnauthorized.get()) {
        // kkelani 7/26: customer ID can be -1 too (see getCustomerId). -1 doesn't seem to be an
        // issue right now though.
        return CompletableFuture.completedFuture(Map.of());
      }
    }

    return Future.call(
            serviceCall,
            GetEpisodeRequest.newBuilder()
                .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_VXS_DEBUG.get()))
                .setVideoRequest(USTContexts.videoRequestBuilder(videoIds))
                .build())
        .thenApply(
            response ->
                response.getResultsList().stream()
                    .filter(result -> USTStatus.isOk(result.getStatus()))
                    .collect(
                        Collectors.toMap(
                            result -> result.getKey().getId(),
                            result -> result.getResult().getEpisode().getId())));
  }

  public CompletionStage<Map<Integer, Long>> getOffset(
      OffsetType offsetType, boolean ms, Collection<Integer> videoIds) {
    return Future.call(
            videoPersonalizedAccess::getOffset,
            GetOffsetRequest.newBuilder()
                .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_VXS_DEBUG.get()))
                .setVideoRequest(USTContexts.videoRequestBuilder(videoIds))
                .setType(offsetType)
                .build())
        .thenApply(
            response ->
                response.getResultsList().stream()
                    .filter(result -> USTStatus.isOk(result.getStatus()))
                    .collect(
                        Collectors.toMap(
                            result -> result.getKey().getId(),
                            result -> parseOffset(result.getResult().getOffset(), ms))));
  }

  private static Long parseOffset(Duration offset, boolean ms) {
    return ms ? Durations.toMillis(offset) : Durations.toSeconds(offset);
  }

  public CompletionStage<Map<Integer, Integer>> getEpisodeNumber(Collection<Integer> videoIds) {
    return Future.call(
            videoPersonalizedAccess::getEpisodeNumber,
            GetEpisodeNumberRequest.newBuilder()
                .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_VXS_DEBUG.get()))
                .setVideoRequest(USTContexts.videoRequestBuilder(videoIds))
                .build())
        .thenApply(
            response ->
                response.getResultsList().stream()
                    .filter(result -> USTStatus.isOk(result.getStatus()))
                    .collect(
                        Collectors.toMap(
                            result -> result.getKey().getId(),
                            result -> result.getResult().getEpisodeNumber())));
  }

  public CompletionStage<Map<Integer, List<Integer>>> getEpisodeList(
      Collection<Integer> videoIds, boolean includeDABEpisodes) {
    return Future.call(
            videoPersonalizedAccess::getEpisodeList,
            GetEpisodeListRequest.newBuilder()
                .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_VXS_DEBUG.get()))
                .setVideoRequest(USTContexts.videoRequestBuilder(videoIds))
                .setIncludeDABEpisodes(includeDABEpisodes)
                .build())
        .thenApply(
            response ->
                response.getResultsList().stream()
                    .filter(result -> USTStatus.isOk(result.getStatus()))
                    .collect(
                        Collectors.toMap(
                            result -> result.getKey().getId(),
                            result -> parseEpisodeList(result.getResultsList()))));
  }

  public CompletionStage<Map<Integer, Integer>> getUpcomingSeason(Collection<Integer> videoIds) {
    return Future.call(
            videoPersonalizedAccess::getUpcomingSeason,
            GetUpcomingSeasonRequest.newBuilder()
                .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_VXS_DEBUG.get()))
                .setVideoRequest(USTContexts.videoRequestBuilder(videoIds))
                .build())
        .thenApply(USTVideoMetadataAdapter::parseUpcomingSeason);
  }

  public CompletionStage<Map<Integer, List<Integer>>> getSeasons(
      Collection<Integer> videoIds, boolean includeUnavailable) {
    return Future.call(
            videoPersonalizedAccess::getSeasons,
            GetSeasonsRequest.newBuilder()
                .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_VXS_DEBUG.get()))
                .setVideoRequest(USTContexts.videoRequestBuilder(videoIds))
                .setIncludeUnavailable(includeUnavailable)
                .build())
        .thenApply(USTVideoMetadataAdapter::parseSeasons);
  }

  public Single<List<Integer>> getSeasons(Integer id, boolean includeUnavailable) {
    return RxSingle.defer(
            videoPersonalizedAccess::getSeasons,
            GetSeasonsRequest.newBuilder()
                .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_VXS_DEBUG.get()))
                .setVideoRequest(USTContexts.videoRequestBuilder(Collections.singleton(id)))
                .setIncludeUnavailable(includeUnavailable)
                .build())
        .map(USTVideoMetadataAdapter::parseSeasons)
        .map(lookupById(id));
  }

  public CompletionStage<Map<Integer, Collection<String>>> getTitleBehaviors(
      Collection<Integer> videoIds) {
    if (videoIds == null || videoIds.isEmpty()) {
      return CompletableFuture.completedFuture(Map.of());
    }
    return Future.call(
            videoPersonalizedAccess::getTitleBehaviors,
            GetTitleBehaviorsRequest.newBuilder()
                .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_VXS_DEBUG.get()))
                .setVideoRequest(USTContexts.videoRequestBuilder(videoIds))
                .build())
        .thenApply(
            response ->
                response.getResultsList().stream()
                    .filter(result -> USTStatus.isOk(result.getStatus()))
                    .collect(
                        Collectors.toMap(
                            result -> result.getKey().getId(),
                            result -> parseTitleBehaviors(result.getResultsList()))));
  }

  private static Collection<String> parseTitleBehaviors(List<TitleBehaviorData> resultsList) {
    return resultsList.stream().map(TitleBehaviorData::getBehavior).collect(Collectors.toSet());
  }

  private static Map<Integer, List<Integer>> parseSeasons(GetSeasonsResponse response) {
    return response.getResultsList().stream()
        .filter(seasonsResult -> USTStatus.isOk(seasonsResult.getStatus()))
        .collect(
            Collectors.toMap(
                seasonsResult -> seasonsResult.getKey().getId(),
                seasonsResult -> parseSeasons(seasonsResult.getResultsList())));
  }

  private static Map<Integer, Integer> parseUpcomingSeason(GetUpcomingSeasonResponse response) {
    return response.getResultsList().stream()
        .filter(seasonResult -> USTStatus.isOk(seasonResult.getStatus()))
        .filter(USTVideoMetadataAdapter::isInitialized)
        .collect(
            Collectors.toMap(
                seasonResult -> seasonResult.getKey().getId(),
                seasonResult -> seasonResult.getResult().getSeason().getId()));
  }

  private static boolean isInitialized(SeasonResult seasonResult) {
    SeasonData result = seasonResult.getResult();
    Video season = result.getSeason();
    return result.isInitialized() && season.isInitialized() && season.getId() > 0;
  }

  public CompletionStage<Map<Long, Integer>> getCharacterVideoCount(
      Collection<Long> characterIds, ust.common.v1.VideoType videoType) {
    return Future.call(
            videoPersonalizedAccess::getCharacterVideoCount,
            GetCharacterVideoCountRequest.newBuilder()
                .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_VXS_DEBUG.get()))
                .setCharacterRequest(USTContexts.characterRequestBuilder(characterIds))
                .setVideoType(videoType)
                .build())
        .thenApply(
            response ->
                response.getResultsList().stream()
                    .filter(result -> USTStatus.isOk(result.getStatus()))
                    .collect(
                        Collectors.toMap(
                            result -> result.getKey().getId(),
                            result -> result.getResult().getVideoCount())));
  }

  private static List<Integer> parseEpisodeList(List<EpisodeData> resultsList) {
    return resultsList.stream().map(e -> e.getEpisode().getId()).toList();
  }

  private static List<Integer> parseSeasons(List<SeasonData> resultsList) {
    return Optional.ofNullable(resultsList)
        .map(list -> list.stream().map(e -> e.getSeason().getId()).toList())
        .orElse(Collections.emptyList());
  }

  private static GetEpisodeRequest buildEpisodeRequest(Integer id) {
    return GetEpisodeRequest.newBuilder()
        .setPersonalizedVideoContext(personalizedVideoContext(ENABLE_VXS_DEBUG.get()))
        .setVideoRequest(USTContexts.videoRequestBuilder(Collections.singleton(id)))
        .build();
  }

  public Observable<Integer> getCurrentEpisode(Integer id) {
    return getEpisode(videoPersonalizedAccess::getCurrentEpisode, id);
  }

  private static Observable<Integer> getEpisode(
      GrpcAsyncCall<GetEpisodeRequest, GetEpisodeResponse> serviceCall, Integer id) {
    return RxObservable.call(serviceCall, buildEpisodeRequest(id))
        .map(USTVideoMetadataAdapter::parseEpisodeReply)
        .map(lookupById(id));
  }

  private static Map<Integer, Integer> parseEpisodeReply(GetEpisodeResponse response) {
    return response.getResultsList().stream()
        .filter(result -> USTStatus.isOk(result.getStatus()))
        .collect(
            Collectors.toMap(
                result -> result.getKey().getId(),
                result -> result.getResult().getEpisode().getId()));
  }

  private static <K, V> Func1<Map<K, V>, V> lookupById(K id) {
    // The following assumption is brittle and might cause issues like
    // https://jira.netflix.net/browse/DNA-2906)
    return map -> map.get(id); // we should always get fallback data from UST
  }
}
