package com.netflix.api.service.video;

import com.netflix.api.facade.service.collection.CollectionData;
import com.netflix.api.service.batch.USTListAdapter;
import com.netflix.videometadata.l10n.VMSL10NProviderDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class APIGenreFactory {

  private final APIVideoFactory videoFactory;
  private final VMSL10NProviderDelegate vmsl10NProviderDelegate;
  private final USTListAdapter listAdapter;

  @Autowired
  APIGenreFactory(
      APIVideoFactory videoFactory,
      VMSL10NProviderDelegate vmsl10NProviderDelegate,
      USTListAdapter listAdapter) {
    this.videoFactory = videoFactory;
    this.vmsl10NProviderDelegate = vmsl10NProviderDelegate;
    this.listAdapter = listAdapter;
  }

  APIGenre getInstance(CollectionData collectionGenre) {
    return new APIGenreImpl(collectionGenre, videoFactory, vmsl10NProviderDelegate, listAdapter);
  }
}
