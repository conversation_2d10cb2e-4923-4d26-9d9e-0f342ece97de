package com.netflix.api.service.vps;

public class APIVoiceRequestHeaderImpl implements APIVoiceRequestHeader {

  private APIVoiceCommandType commandDirective;
  private String payloadVersion;

  public APIVoiceRequestHeaderImpl(APIVoiceCommandType commandDirective, String payloadVersion) {
    this.commandDirective = commandDirective;
    this.payloadVersion = payloadVersion;
  }

  public APIVoiceCommandType getCommandDirective() {
    return commandDirective;
  }

  public String getPayloadVersion() {
    return payloadVersion;
  }

  public void setCommandDirective(APIVoiceCommandType commandDirective) {
    this.commandDirective = commandDirective;
  }

  public void setPayloadVersion(String payloadVersion) {
    this.payloadVersion = payloadVersion;
  }
}
