package dna.api.service.model;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.netflix.demograph.graph.Atom;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

/** @dotnext com.netflix.api.service.video.device.APIDeviceType */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class DeviceType implements GraphData {
  /**
   * E.g. "iOS"
   *
   * @see {@link https://confluence.netflix.net/display/DSE/Device+Hierarchy+Orthogonal+Attributes}
   */
  public String clientCategory;

  public String clientPlatformCategory;

  /** E.g. "Apple iPhone 11 Pro" */
  public String description;

  public Integer deviceTypeId;

  public String esnPrefix;

  /** E.g. "NFAPPL-D2-", "NFAPPL-02-IPHONE12=3-" */
  public String esnStartsWith;

  /** E.g. "NFAPPL-D1-" */
  public String fileSuffix;

  /**
   * E.g. "Phone", "Tablet"
   *
   * @see {@link https://confluence.netflix.com/display/DSE/Device+Hierarchy+Orthogonal+Attributes}
   */
  public String hardwareCategory;

  public String hardwareMajorCategory;

  /**
   * @dotnext com.netflix.api.service.device.APIDeviceType#getLocalizedCategory
   * @dotnext com.netflix.api.service.device.APIDeviceManagementService#getLocalizedCategory
   */
  public String localizedCategory;

  /** E.g. "Apple" */
  public String manufacturer;

  public String model;

  public String operationalName;

  public String reportingName;

  public Map<String, Atom<Map<String, String>>> properties;

  public Map<String, Atom<Map<String, Object>>> devicePerformanceEntries;

  private HashMap<String, Object> propertyToValue;

  public String getClientCategory() {
    return clientCategory;
  }

  public String getClientPlatformCategory() {
    return clientPlatformCategory;
  }

  public String getDescription() {
    return description;
  }

  public Integer getDeviceTypeId() {
    return deviceTypeId;
  }

  public String getEsnPrefix() {
    return esnPrefix;
  }

  public String getEsnStartsWith() {
    return esnStartsWith;
  }

  public String getFileSuffix() {
    return fileSuffix;
  }

  public String getHardwareCategory() {
    return hardwareCategory;
  }

  public String getHardwareMajorCategory() {
    return hardwareMajorCategory;
  }

  public String getLocalizedCategory() {
    return localizedCategory;
  }

  public String getManufacturer() {
    return manufacturer;
  }

  public String getModel() {
    return model;
  }

  public String getOperationalName() {
    return operationalName;
  }

  public String getReportingName() {
    return reportingName;
  }

  public DeviceType setClientCategory(String clientCategory) {
    this.clientCategory = clientCategory;
    return this;
  }

  public DeviceType setClientPlatformCategory(String clientPlatformCategory) {
    this.clientPlatformCategory = clientPlatformCategory;
    return this;
  }

  public DeviceType setDescription(String description) {
    this.description = description;
    return this;
  }

  public DeviceType setDeviceTypeId(Integer deviceTypeId) {
    this.deviceTypeId = deviceTypeId;
    return this;
  }

  public DeviceType setEsnPrefix(String esnPrefix) {
    this.esnPrefix = esnPrefix;
    return this;
  }

  public DeviceType setEsnStartsWith(String esnStartsWith) {
    this.esnStartsWith = esnStartsWith;
    return this;
  }

  public DeviceType setFileSuffix(String fileSuffix) {
    this.fileSuffix = fileSuffix;
    return this;
  }

  public DeviceType setHardwareCategory(String hardwareCategory) {
    this.hardwareCategory = hardwareCategory;
    return this;
  }

  public DeviceType setHardwareMajorCategory(String hardwareMajorCategory) {
    this.hardwareMajorCategory = hardwareMajorCategory;
    return this;
  }

  public DeviceType setLocalizedCategory(String localizedCategory) {
    this.localizedCategory = localizedCategory;
    return this;
  }

  public DeviceType setManufacturer(String manufacturer) {
    this.manufacturer = manufacturer;
    return this;
  }

  public DeviceType setModel(String model) {
    this.model = model;
    return this;
  }

  public DeviceType setOperationalName(String operationalName) {
    this.operationalName = operationalName;
    return this;
  }

  public DeviceType setReportingName(String reportingName) {
    this.reportingName = reportingName;
    return this;
  }

  @JsonAnyGetter
  public HashMap<String, Object> propertyToValue() {
    return this.propertyToValue;
  }

  public Object getProperty(String property) {
    return this.propertyToValue != null ? this.propertyToValue.get(property) : null;
  }

  @JsonAnySetter
  public void setProperty(String key, Object value) {
    if (propertyToValue == null) {
      propertyToValue = new HashMap<>();
    }
    this.propertyToValue.put(key, value);
  }
}
