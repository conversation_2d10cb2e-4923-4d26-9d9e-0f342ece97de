package com.netflix.api.service.video;

import java.util.Objects;
import org.jetbrains.annotations.NotNull;

public class APIColorImpl implements APIColor {

  private final com.netflix.videocolor.protogen.Color color;
  private String hex;

  APIColorImpl(@NotNull com.netflix.videocolor.protogen.Color color) {
    this.color = color;
  }

  @Override
  public int getRed() {
    return color.getRed().getValue();
  }

  @Override
  public int getGreen() {
    return color.getGreen().getValue();
  }

  @Override
  public int getBlue() {
    return color.getBlue().getValue();
  }

  @Override
  public String hexString() {
    if (hex == null) {
      hex = String.format("%02x%02x%02x", getRed(), getGreen(), getBlue());
    }
    return hex;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof APIColorImpl)) {
      return false;
    }
    APIColorImpl apiColor = (APIColorImpl) o;
    return Objects.equals(color, apiColor.color);
  }

  @Override
  public int hashCode() {
    return Objects.hash(color);
  }

  @Override
  public String toString() {
    return "APIColor{" + "red=" + getRed() + ", green=" + getGreen() + ", blue=" + getBlue() + '}';
  }
}
