package com.netflix.api.service;

/** Provides access to tester account (user) objects (for functional/integration testing. */
public interface APITestUserService {
  /**
   * Get S&amp;D Api overrides for device under certification.
   *
   * @param deviceID Identifier for a device under certificaiton eg.esn, vdid etc.
   * @param lookUpKey A key specifying name of S&amp;D resource, which needs to be overridden for
   *     certification.
   */
  @Deprecated
  String getCertificationSDApiOverrides(String deviceID, String lookUpKey);
}
