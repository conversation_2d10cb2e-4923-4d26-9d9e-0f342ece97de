package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SearchVideoCarouselListSection extends SearchPageSection {
  private List<SearchVideoCarousel> carousels;

  public List<SearchVideoCarousel> getCarousels() {
    return carousels;
  }

  public SearchVideoCarouselListSection setCarousels(List<SearchVideoCarousel> carousels) {
    this.carousels = carousels;
    return this;
  }
}
