package com.netflix.api.service.identity;

import java.util.concurrent.CompletionStage;

/**
 * Package-private holder of all logic for reading a profile. Used by old/new Service Layer methods
 * to standardize behavior.
 */
public interface ProfileReader {
  Long getCustomerId();

  String getCustomerGuid();

  boolean isAccountOwner();

  CompletionStage<String> getProfileName();

  CompletionStage<String> getHtmlDecodedProfileName();

  APIUser.Experience getExperience();

  boolean isFirstUse();

  APIMaturity getMaturity();

  long getDateCreated();

  String getIconName();
}
