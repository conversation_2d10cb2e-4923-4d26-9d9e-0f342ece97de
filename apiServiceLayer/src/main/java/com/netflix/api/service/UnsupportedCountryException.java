package com.netflix.api.service;

import com.netflix.api.annotations.ExcludeFromCoverage;

/**
 * Thrown when the script tries to access metadata for the country in REJECT status or
 * FRIENDS_AND_FAMILY status but the user has not signed up for F&amp;F.
 */
@ExcludeFromCoverage
public class UnsupportedCountryException extends APIServiceRuntimeException {

  private static final long serialVersionUID = -5985658345092462357L;

  public UnsupportedCountryException(String message) {
    super(message);
  }
}
