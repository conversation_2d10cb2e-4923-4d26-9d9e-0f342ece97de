package com.netflix.api.endpoint;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.springboot.scheduling.DefaultExecutor;
import jakarta.annotation.Nonnull;
import java.util.concurrent.Executor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/** An {@link Executor} that executes tasks on the EndpointThreadCommand thread pool. */
@Component
public class EndpointThreadCommandExecutor extends EndpointThreadCommandBase<Void, Runnable>
    implements Executor {
  /**
   * This is only specified for {@code EndpointController}'s use. It needs to use an {@link
   * Executor}. When it calls {@link EndpointThreadCommandExecutor#execute(Runnable)} it cannot
   * provide a timeout because it is using the {@link Executor} interface. former FP,
   */
  private final Property<Integer> endpointControllerTimeoutInMillis =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.endpointscript.timeoutms", Integer.class)
          .orElse(30_000);

  @Autowired
  public EndpointThreadCommandExecutor(@DefaultExecutor final Executor executor) {
    super(executor);
  }

  /**
   * This is an implementation of abstract method in EndpointThreadCommandBase. {@link
   * EndpointThreadCommandBase#queue(Object, Property)} will call this method on an endpoint thread.
   *
   * @param command to be run on the endpoint thread.
   * @return nothing.
   */
  @Override
  protected Void invoke(final Runnable command) {
    command.run();
    return null;
  }

  /**
   * Executor interface implementation - this submits the {@param command} to thread pool. This is
   * meant to be used by EndpointController. It queues the task with a relatively long timeout
   * compared to shorter executions such as gRPC client calls, Hystrix commands, etc.
   *
   * @param command command to run.
   */
  @Override
  public void execute(@Nonnull Runnable command) {
    queue(command, endpointControllerTimeoutInMillis);
  }

  public void execute(@Nonnull Runnable command, Property<Integer> timeout) {
    queue(command, timeout);
  }
}
