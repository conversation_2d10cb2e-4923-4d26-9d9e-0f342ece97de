package com.netflix.api.service;

import com.google.common.base.Preconditions;
import java.util.Objects;

/**
 * An item with context information so that clients know which item in the subscription it belongs
 * to. For example, if asking for a list of items (returned as Observable items out of order),
 * context can be list index. When a service loads items for an array of references requested,
 * context can be the reference.
 *
 * <AUTHOR>
 */
public class ListItem<ITEM> extends ContextualItem<Integer, ITEM>
    implements Comparable<ListItem<ITEM>> {
  private Integer index;

  public ListItem(ITEM item, Integer index) {
    super(item, index);
    this.index = Preconditions.checkNotNull(index);
  }

  public void setIndex(Integer index) {
    this.index = index;
  }

  /**
   * The requested index that corresponds with item in this ListItem.
   *
   * @return the index
   */
  public Integer getIndex() {
    return index;
  }

  /**
   * Comparison of list item to another list item returns comparison of index to the other list
   * item's index.
   *
   * @param that the comparee
   * @return positive if that &lt; this, negative if this &lt; that, 0 if they are equal.
   */
  @Override
  public int compareTo(ListItem<ITEM> that) {
    return this.getIndex().compareTo(that.getIndex());
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (!(o instanceof ListItem listItem)) return false;
    if (!super.equals(o)) return false;
    return Objects.equals(index, listItem.index);
  }

  @Override
  public int hashCode() {
    return index;
  }
}
