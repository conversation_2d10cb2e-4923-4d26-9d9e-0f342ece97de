package dna.api.service.model;

import javax.annotation.Generated;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
public enum PacsDetailCode {
  UNRECOGNIZED,

  NONE,

  PAYMENT_HOLD,

  ACCOUNT_SHARE,

  UNSUPPORTED_COUNTRY,

  VPN_USE_DETECTED,

  B<PERSON>C<PERSON><PERSON><PERSON>HT_FRAUD_DETECTED,

  UNSUPPORTED_DEVICE,

  APP_UPDATE_REQUIRED,

  BLOCKED_USER,

  MATURITY_RATING_CHECK_FAILED,

  NEEDS_SIGNUP,

  UNSUPPORTED_CLIENT_APP_VERSION,

  UNAVAILABLE_VIEWABLE,

  INACTIVE_MEMBERSHIP,

  <PERSON>LL<PERSON>_MISSING,

  KIDS_PROFILE,

  BAD_MOC,

  PROFILE_HAS_PIN,

  HAS_FREE_PLAN,

  HAS_DVD,

  NO_CONSENT,

  INVALID_PLAN_OTHER,

  DVD_ONLY_USER,

  B<PERSON><PERSON><PERSON>,

  <PERSON>ER_<PERSON>ELIGIBLE,

  PAID_LOCATION,

  UNPAID_LOCATION_ACTIVE_GRACEPERIOD,

  UNCLUSTERED_DEVICE,

  UNPAID_LOCATION_GRACEPERIOD_EXPIRED,

  UNPAID_LOCATION_GRACEPERIOD_ELIGIBLE,

  INCLUDED_LOCATION_UNASSIGNED,

  UNPAID_LOCATION_GRACEPERIOD_INELIGIBLE,

  INVALID_CONTENT_USAGE,

  EBI,

  MHU3,

  MISCLASSIFIED_HOUSEHOLD,

  PROFILE_DELETED,

  PRIMARYLOCATION_WITH_EM,

  PRIMARYLOCATION_WITHOUT_EM,

  UPPER_LIMIT_REACHED,

  INVALID_ACCOUNT_DEVICE_STATE,

  INVALID_RESOURCE_ACCESS,

  INVALID_RESOURCE_STATE,

  RESOURCE_NOT_FOUND,

  MAA_CLUSTERING,

  INVALID_PLAYER_ACCESS,

  INVALID_NONPLAYER_READ_ACCESS,

  NON_DOMESTIC_STREAMING,

  INVALID_VMS_GAME,

  GANDALF_CHECK_FAILED,

  INVALID_ENTERPIRSE_ACCESS,

  PROTECTORATE_CHECK_FAILED,

  AUTHORIZED_LOCATION,

  VIDEO_DISABLED,

  PASSPORT_NOT_ALLOWED,

  PASSPORT_NOT_PRESENT,

  UNSUPPORTED_LIVE_DEVICE,

  UNSUPPORTED_LIVE_CLIENT_APP_VERSION,

  NEEDS_MFA,

  APP_VERSION_RETIRED,

  DEVICE_RETIRED,

  GPAS_TEMPORARY_BAN,

  GPAS_PERMANENT_BAN,

  GAME_SUNSET,

  PRE_RETIRED,

  API_ACTION_POLICY_NOT_FOUND,

  UNAVAILABLE_VIEWABLE_OUT_OF_WINDOW,

  UNAVAILABLE_VIEWABLE_ON_PLAN,

  VOD_ONLY,

  VOD_AND_LINEAR,

  VOTE_LIMIT_EXCEEDED
}
