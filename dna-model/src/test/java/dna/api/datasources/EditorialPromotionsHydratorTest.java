package dna.api.datasources;

import static java.util.concurrent.CompletableFuture.completedFuture;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.anEmptyMap;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.netflix.api.facade.service.LookupContext;
import com.netflix.api.facade.service.VideoLookup;
import com.netflix.api.platform.MockPropertyRepositoryConfig;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.editorialpromotion.protogen.EditorialPromotion;
import com.netflix.editorialpromotion.protogen.EditorialPromotionPlacement;
import com.netflix.editorialpromotion.protogen.EditorialPromotionTile;
import com.netflix.editorialpromotion.protogen.EditorialPromotionsResponse;
import com.netflix.spectator.api.Registry;
import com.netflix.textevidence.protogen.SynopsisType;
import com.netflix.textevidence.protogen.TitleType;
import com.netflix.textevidence.protogen.VideoType;
import com.netflix.type.proto.Videos;
import com.netflix.videometadata.type.CompleteVideo;
import dna.api.TextEvidenceService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class EditorialPromotionsHydratorTest {
  private final TextEvidenceService textEvidenceService = mock(TextEvidenceService.class);
  private final VideoLookup videoLookup = mock(VideoLookup.class);
  private EditorialPromotionsHydrator hydrator;

  @BeforeAll
  static void beforeClass() {
    MockPropertyRepositoryConfig config = new MockPropertyRepositoryConfig();
    config.init();
  }

  @BeforeEach
  void before() {
    var falseProp = mock(Property.class);
    when(falseProp.get()).thenReturn(false);

    var orElseProp = mock(Property.class);
    when(orElseProp.orElse(any())).thenReturn(falseProp);

    var propertyRepository = mock(PropertyRepository.class);
    when(propertyRepository.get(anyString(), any())).thenReturn(orElseProp);

    var registry = mock(Registry.class);
    hydrator =
        new EditorialPromotionsHydrator(
            textEvidenceService, videoLookup, propertyRepository, registry);
  }

  @Test
  void getEvidenceForAssetIDs_returnsEvidenceForAssetsInTheEditorialResponse()
      throws ExecutionException, InterruptedException {
    var completeVideo1 = mock(CompleteVideo.class);
    when(completeVideo1.isTopNode()).thenReturn(true);
    when(completeVideo1.getId()).thenReturn(1);

    var completeVideo3 = mock(CompleteVideo.class);
    when(completeVideo3.isTopNode()).thenReturn(false);
    when(completeVideo3.getTopNode()).thenReturn(Videos.toBasicType(33));

    var completeVideo4 = mock(CompleteVideo.class);
    when(completeVideo4.isTopNode()).thenReturn(false);

    var completeVideo5 = mock(CompleteVideo.class);
    when(completeVideo5.isTopNode()).thenReturn(true);
    when(completeVideo5.getId()).thenReturn(5);

    when(videoLookup.getVideos(any(), any()))
        .thenReturn(
            Map.of(
                1, Optional.of(completeVideo1),
                2, Optional.empty(),
                3, Optional.of(completeVideo3),
                4, Optional.of(completeVideo4),
                5, Optional.of(completeVideo5)));

    var titles = new HashMap<Integer, String>();
    titles.put(1, "title 1");
    titles.put(33, "title 33");
    titles.put(5, null);

    when(textEvidenceService.getTitles(any(), any(), any(), anyString(), any()))
        .thenReturn(completedFuture(titles));

    var synopses = new HashMap<Integer, String>();
    synopses.put(1, "synopsis 1");
    synopses.put(33, "synopsis 33");
    synopses.put(5, null);

    when(textEvidenceService.getSynopsis(any(), any(), anyString()))
        .thenReturn(completedFuture(synopses));

    var editorialResp =
        EditorialPromotionsResponse.newBuilder()
            .addEditorialPromotions(
                EditorialPromotion.newBuilder()
                    .addPromotionPlacements(
                        EditorialPromotionPlacement.newBuilder()
                            .addEditorialPromotionTiles(
                                EditorialPromotionTile.newBuilder().setAssetId(1))))
            .addEditorialPromotions(
                EditorialPromotion.newBuilder()
                    .addPromotionPlacements(
                        EditorialPromotionPlacement.newBuilder()
                            .addEditorialPromotionTiles(
                                EditorialPromotionTile.newBuilder().setAssetId(2))))
            .addEditorialPromotions(
                EditorialPromotion.newBuilder()
                    .addPromotionPlacements(
                        EditorialPromotionPlacement.newBuilder()
                            .addEditorialPromotionTiles(
                                EditorialPromotionTile.newBuilder().setAssetId(3))))
            .addEditorialPromotions(
                EditorialPromotion.newBuilder()
                    .addPromotionPlacements(
                        EditorialPromotionPlacement.newBuilder()
                            .addEditorialPromotionTiles(
                                EditorialPromotionTile.newBuilder().setAssetId(4))))
            .addEditorialPromotions(
                EditorialPromotion.newBuilder()
                    .addPromotionPlacements(
                        EditorialPromotionPlacement.newBuilder()
                            .addEditorialPromotionTiles(
                                EditorialPromotionTile.newBuilder().setAssetId(5))))
            .build();

    var future = hydrator.getEvidenceForAssetIDs(editorialResp, "FR");
    assertNotNull(future);

    var actual = future.toCompletableFuture().get();

    assertEquals(3, actual.size());

    assertTrue(actual.containsKey(1));
    var evidence1 = actual.get(1);
    assertNotNull(evidence1);
    assertEquals("title 1", evidence1.title());
    assertEquals("synopsis 1", evidence1.synopsis());

    assertTrue(actual.containsKey(3));
    var evidence3 = actual.get(3);
    assertNotNull(evidence3);
    assertEquals("title 33", evidence3.title());
    assertEquals("synopsis 33", evidence3.synopsis());

    assertTrue(actual.containsKey(5));
    var evidence5 = actual.get(5);
    assertNotNull(evidence5);
    assertEquals("", evidence5.title());
    assertEquals("", evidence5.synopsis());

    var lookupContextCaptor = ArgumentCaptor.forClass(LookupContext.class);
    verify(videoLookup).getVideos(eq(List.of(1, 2, 3, 4, 5)), lookupContextCaptor.capture());

    assertEquals("FR", lookupContextCaptor.getValue().country().getId());

    ArgumentCaptor<Iterable<Integer>> assetIDCaptor = ArgumentCaptor.forClass(Iterable.class);
    verify(textEvidenceService)
        .getTitles(
            assetIDCaptor.capture(),
            eq(TitleType.REGULAR),
            eq(VideoType.COMPLETE_VIDEO),
            eq("FR"),
            eq(null));

    verify(textEvidenceService)
        .getSynopsis(assetIDCaptor.capture(), eq(SynopsisType.INFORMATIVE), eq("FR"));

    assertThat(assetIDCaptor.getValue(), Matchers.contains(1, 33, 5));
  }

  @Test
  void
      getEvidenceForAssetIDs_whenNoAssetsInEditorialResponse_doesNotMakeRequestToTextEvidence_returnsEmptyMap()
          throws ExecutionException, InterruptedException {
    var future =
        hydrator.getEvidenceForAssetIDs(
            EditorialPromotionsResponse.getDefaultInstance(), "does not matter");
    assertNotNull(future);

    var result = future.toCompletableFuture().get();
    assertThat(result, anEmptyMap());

    verifyNoInteractions(videoLookup);
    verifyNoInteractions(textEvidenceService);
  }
}
