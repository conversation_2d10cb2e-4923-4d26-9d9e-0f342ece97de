package com.netflix.api.platform.identity;

import com.netflix.membership.MembershipStatus;
import java.util.Optional;

/** Holds identity data parsed from DCR cookie. */
public abstract class IdentityResult<R> {

  private String modifiedPassportString;

  protected IdentityResult() {}

  public abstract R getData();

  public enum IdentitySource {
    COOKIES_INSECURE,
    COOKIES,
    MSL,
    MSL_WEAK_AUTHENTICATION,
    PARTNER_COOKIE,
    ADC_IDENTITY_TOKEN,
    ADC_MASQUERADER,
    USER_REGISTRATION,
    UNKNOWN,
    CREDENTIALS,
    COOKIES_WITH_PROFILE_ASSERTION,
    COOKIES_WITH_ESN,
    NONE,
    PARTNER_TOKEN,
    ASSERTION,
    AUTO_LOGIN_TOKEN,
    ZUUL_PUSH_TOKEN
  }

  public abstract boolean isSuccess();

  public Exception getException() {
    return null;
  }

  public boolean isDeviceBound() {
    return getCustomerId() == null && getESN() != null;
  }

  public boolean isUserBound() {
    return getCustomerId() != null && getESN() == null;
  }

  public abstract Long getCustomerId();

  public abstract Long getAccountOwnerId();

  /**
   * @deprecated with passport backed identity data, this will never be populated
   */
  @Deprecated
  public abstract String getSignupCountry();

  /**
   * @deprecated with passport backed identity data, this will never be populated
   */
  @Deprecated
  public abstract String getRegistrationCountry();

  public abstract String getESN();

  // NOTE: The value from this call should be considered stale since it is derived from a token and
  // not from live data
  public abstract Optional<MembershipStatus> getMembershipStatus();

  public abstract String getCustomerGuid();

  public abstract Integer getDeviceType();

  public abstract IdentitySource getDeviceInformationSource();

  public abstract IdentitySource getUserInformationSource();

  // Methods for returning a modified passport (if it was modified)
  public Optional<String> getModifiedPassportString() {
    return Optional.ofNullable(modifiedPassportString);
  }

  public void setModifiedPassportString(String modifiedPassportString) {
    this.modifiedPassportString = modifiedPassportString;
  }

  public boolean isModifiedPassportHeaderForced() {
    return true;
  }
}
