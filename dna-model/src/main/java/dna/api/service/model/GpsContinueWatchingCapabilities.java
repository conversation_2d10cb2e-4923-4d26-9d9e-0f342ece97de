package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class GpsContinueWatchingCapabilities {
  private Boolean canHandleEmptySection;

  public Boolean getCanHandleEmptySection() {
    return canHandleEmptySection;
  }

  public GpsContinueWatchingCapabilities setCanHandleEmptySection(Boolean canHandleEmptySection) {
    this.canHandleEmptySection = canHandleEmptySection;
    return this;
  }
}
