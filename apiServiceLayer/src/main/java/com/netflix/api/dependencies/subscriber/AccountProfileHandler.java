package com.netflix.api.dependencies.subscriber;

import static com.netflix.api.service.identity.APIUserUtil.getMembershipStatus;
import static com.netflix.api.service.identity.APIUserUtil.isNotCurrentMember;
import static com.netflix.grpc.interceptor.cache.CacheCallOptionsKeys.CLIENT_CACHE_CONTROL;
import static com.netflix.messagingconsent.protogen.TOUConsent.TOUStatus.AcknowledgedOnBanner;
import static com.netflix.messagingconsent.protogen.TOUConsent.TOUStatus.AcknowledgedOnJoin;
import static com.netflix.messagingconsent.protogen.TOUConsent.TOUStatus.AutoAcknowledged;
import static com.netflix.servo.monitor.DynamicCounter.increment;

import com.netflix.api.grpc.GrpcCallHelpers.RxObservable;
import com.netflix.api.grpc.GrpcCallHelpers.RxSingle;
import com.netflix.api.platform.context.Contexts;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.platform.util.InterruptAdvice;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APIServiceRuntimeException;
import com.netflix.api.service.identity.APIServiceEndReasons;
import com.netflix.archaius.api.Property;
import com.netflix.lang.RequestVariable;
import com.netflix.membership.memberdata.protogen.MemberData;
import com.netflix.membership.memberdata.protogen.MemberDataRequest;
import com.netflix.membership.memberdata.protogen.MemberDataServiceGrpc.MemberDataServiceStub;
import com.netflix.messagingconsent.protogen.GetTOUConsentReply;
import com.netflix.messagingconsent.protogen.GetTOUConsentRequest;
import com.netflix.messagingconsent.protogen.MessagingConsentServiceGrpc.MessagingConsentServiceBlockingStub;
import com.netflix.messagingconsent.protogen.TOUConsent;
import com.netflix.messagingconsent.protogen.TOUConsent.TOUStatus;
import com.netflix.messagingconsent.protogen.UpdateTOUConsentRequest;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.subscriber.types.protogen.Membership;
import com.netflix.subscriber.types.protogen.ProfileCreation.Method;
import com.netflix.subscriberservice.protogen.AccountAttributes;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.subscriberservice.protogen.AccountProfilesRemote;
import com.netflix.subscriberservice.protogen.CustomerExtraDetailsResponse;
import com.netflix.subscriberservice.protogen.CustomersReadRequest;
import com.netflix.subscriberservice.protogen.ProfileAttributes;
import com.netflix.subscriberservice.protogen.ReadIfAccountHasPasswordRequest;
import com.netflix.subscriberservice.protogen.ReadIfAccountHasPasswordResponse;
import com.netflix.subscriberservice.protogen.ReadRequest;
import com.netflix.subscriberservice.protogen.SetAccountAttributesRequest;
import com.netflix.subscriberservice.protogen.SetAgeVerifiedRequest;
import com.netflix.subscriberservice.protogen.SetParentalControlAttributesRequest;
import com.netflix.subscriberservice.protogen.SetProfileUserAttributesRequest;
import com.netflix.subscriberservice.protogen.SubscriberManagementServiceGrpc.SubscriberManagementServiceBlockingStub;
import com.netflix.subscriberservice.protogen.SubscriberServiceGrpc.SubscriberServiceBlockingStub;
import com.netflix.subscriberservice.protogen.SubscriberServiceGrpc.SubscriberServiceStub;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import jakarta.annotation.Nonnull;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicReference;
import netflix.context.Context;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Observable;
import rx.Single;

@SuppressWarnings("deprecation")
@Component
public class AccountProfileHandler {

  private static final Logger logger = LoggerFactory.getLogger(AccountProfileHandler.class);

  private static final EnumSet<Membership.Status> MEMBER_STATUSES =
      EnumSet.of(Membership.Status.CURRENT_MEMBER, Membership.Status.FORMER_MEMBER);

  private static final Property<Boolean> ENABLE_CANWATCHNOW =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.canwatchnow.enable", Boolean.class)
          .orElse(true);

  private final SubscriberManagementServiceBlockingStub managementClient;
  private final SubscriberServiceBlockingStub subscriberService;
  private final SubscriberServiceStub subscriberServiceStub;
  private final MemberDataServiceStub memberDataServiceStub;
  private final MessagingConsentServiceBlockingStub messagingConsentService;

  private static final RequestVariable<ConcurrentMap<Long, Single<Optional<AccountProfileRemote>>>>
      cache =
          new RequestVariable<>() {
            @Override
            protected ConcurrentMap<Long, Single<Optional<AccountProfileRemote>>> initialValue() {
              return new ConcurrentHashMap<>();
            }
          };

  private static final RequestVariable<ConcurrentMap<Long, Observable<MemberData>>>
      memberDataCache =
          new RequestVariable<>() {
            @Override
            protected ConcurrentMap<Long, Observable<MemberData>> initialValue() {
              return new ConcurrentHashMap<>();
            }
          };

  private static final RequestVariable<AtomicReference<Single<List<AccountProfileRemote>>>>
      profilesCache =
          new RequestVariable<>() {
            @Override
            protected AtomicReference<Single<List<AccountProfileRemote>>> initialValue() {
              return new AtomicReference<>();
            }
          };

  private static final RequestVariable<ConcurrentMap<Long, CustomerExtraDetailsResponse>>
      custExtraDetailsCache =
          new RequestVariable<>() {
            @Override
            protected ConcurrentMap<Long, CustomerExtraDetailsResponse> initialValue() {
              return new ConcurrentHashMap<>();
            }
          };

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public AccountProfileHandler(
      @GrpcSpringClient("subscriberservice")
          SubscriberManagementServiceBlockingStub managementClient,
      @GrpcSpringClient("subscriberservice") SubscriberServiceBlockingStub subscriberService,
      @GrpcSpringClient("subscriberservice") SubscriberServiceStub subscriberServiceStub,
      @GrpcSpringClient("memberdata") MemberDataServiceStub memberDataServiceStub,
      @GrpcSpringClient("messagingconsent")
          MessagingConsentServiceBlockingStub messagingConsentService) {
    this.managementClient = managementClient;
    this.subscriberService = subscriberService;
    this.subscriberServiceStub = subscriberServiceStub;
    this.memberDataServiceStub = memberDataServiceStub;
    this.messagingConsentService = messagingConsentService;
  }

  public boolean isTimeForTOU(Long accountId) {
    GetTOUConsentRequest request =
        GetTOUConsentRequest.newBuilder().setAccountId(accountId).build();
    GetTOUConsentReply reply = messagingConsentService.getTOUConsent(request);
    return reply.getTouConsent().getIsTimeForTou();
  }

  @SuppressWarnings("ResultOfMethodCallIgnored")
  public void updateTOU(long accountId, @Nonnull String status, @Nonnull String source) {
    var touStatus =
        switch (status) {
          /* The three use cases supported currently */
          case "AcknowledgedBanner" -> AcknowledgedOnBanner;
          case "AcknowledgedOnJoin" -> AcknowledgedOnJoin;
          case "AutoClose" -> AutoAcknowledged;
          default -> null;
        };

    if (touStatus == null) {
      try {
        touStatus = TOUStatus.valueOf(status);
        increment("api.account.updatetou.raw", "status", status, "success", "true");
      } catch (IllegalArgumentException e) {
        increment("api.account.updatetou.raw", "status", status, "success", "false");
      }
    }
    increment(
        "api.account.updatetou",
        "status",
        status,
        "resolved",
        touStatus != null ? touStatus.name() : "null",
        "source",
        source);
    if (touStatus != null) {
      UpdateTOUConsentRequest request =
          UpdateTOUConsentRequest.newBuilder()
              .setAccountId(accountId)
              .setCountryCode(RequestContextWrapper.get().getCountry().getId())
              .setLocale(RequestContextWrapper.get().getLocale())
              .setTouConsent(
                  TOUConsent.newBuilder()
                      .setTouStatus(touStatus)
                      .setAcceptedTsMills(System.currentTimeMillis())
                      .build())
              .build();

      messagingConsentService.updateTOUConsent(request);
    }
  }

  @SuppressWarnings("ResultOfMethodCallIgnored")
  public void updateAccount(Long accountId, AccountAttributes accountAttributes) {
    if (accountAttributes != null) {
      SetAccountAttributesRequest request =
          SetAccountAttributesRequest.newBuilder()
              .setAccountId(accountId)
              .setAccountAttributes(accountAttributes)
              .build();
      managementClient.setAccountAttributes(request);
    }
  }

  public Long updateProfile(long accountId, long profileId, ProfileAttributes profileAttributes) {
    if (profileAttributes != null) {
      SetProfileUserAttributesRequest request =
          SetProfileUserAttributesRequest.newBuilder()
              .setAccountId(accountId)
              .setProfileId(profileId)
              .setProfileAttributes(profileAttributes)
              .build();
      return managementClient
          .setProfileUserAttributes(request)
          .getAccountProfile()
          .getBoxedProfileId();
    } else {
      return profileId;
    }
  }

  @SuppressWarnings("ResultOfMethodCallIgnored")
  public void updateProfileParentalControlAttributes(
      SetParentalControlAttributesRequest parentalControlAttributesRequest) {
    if (parentalControlAttributesRequest != null) {
      managementClient.setParentalControlAttributes(parentalControlAttributesRequest);
    }
  }

  @SuppressWarnings("ResultOfMethodCallIgnored")
  public void updateAgeVerified(long accountId, boolean isAgeVerified) {
    SetAgeVerifiedRequest request =
        SetAgeVerifiedRequest.newBuilder()
            .setAccountId(accountId)
            .setIsAgeVerified(isAgeVerified)
            .build();
    managementClient.setAgeVerified(request);
  }

  public Observable<Boolean> getAccountHasPassword(Long accountOwnerId) {
    ReadIfAccountHasPasswordRequest request =
        ReadIfAccountHasPasswordRequest.newBuilder().setAccountId(accountOwnerId).build();

    return RxObservable.call(subscriberServiceStub::readIfAccountHasPassword, request)
        .map(ReadIfAccountHasPasswordResponse::getIfAccountHasPassword);
  }

  public void invalidate(long customerId) {
    profilesCache.get().set(null);
    custExtraDetailsCache.get().remove(customerId);
    cache.get().remove(customerId);
  }

  public Optional<AccountProfileRemote> getBlocking(long customerId) {
    // false means use subscriber cache
    return get(customerId, false).toBlocking().value();
  }

  public Single<Optional<AccountProfileRemote>> get(long customerId) {
    // false means use subscriber cache
    return get(customerId, false);
  }

  public Single<Optional<AccountProfileRemote>> get(long customerId, boolean noCache) {
    Single<Optional<AccountProfileRemote>> o = cache.get().get(customerId);
    if (o != null) {
      return o;
    }
    o = Single.defer(() -> getCustomer(customerId, noCache)).cache();

    Single<Optional<AccountProfileRemote>> previous = cache.get().putIfAbsent(customerId, o);
    return previous == null ? o : previous;
  }

  private Single<Optional<AccountProfileRemote>> getCustomer(long customerId, boolean noCache) {
    ReadRequest request = ReadRequest.newBuilder().setProfileid(customerId).build();

    AccountProfileRemote accountProfileRemote = null;
    try {
      if (noCache) {
        accountProfileRemote =
            subscriberService.withOption(CLIENT_CACHE_CONTROL, "no-cache").getCustomer(request);
      } else {
        // FIXME should make this non-blocking
        accountProfileRemote = subscriberService.getCustomer(request);
      }
    } catch (Exception e) {
      InterruptAdvice.reinterruptIfNeeded(e);
      if (!(e instanceof StatusRuntimeException runtimeException)
          || runtimeException.getStatus().getCode() != Status.Code.NOT_FOUND) {
        return Single.error(new APIServiceRuntimeException("Customer lookup failed"));
      }
    }
    final Microcontext microcontext = CurrentMicrocontext.get();
    // user will be set by the microcontextfilter
    if (Objects.equals(customerId, microcontext.getUser().getCurrentUser().getId())) {
      if (accountProfileRemote == null) {
        // remove user from microcontext
        clearCurrentUser();
      } else {
        // update user in microcontext
        CurrentMicrocontext.set(microcontext.toProto());
      }
    }

    return Single.just(Optional.ofNullable(accountProfileRemote));
  }

  private void clearCurrentUser() {
    Context.Builder builder = CurrentMicrocontext.get().toProto().toBuilder();
    builder.setUser(builder.getUserBuilder().clearCurrentUser());
    CurrentMicrocontext.set(builder.build());
  }

  public Observable<MemberData> getMemberData(long accountOwnerId) {
    Observable<MemberData> memberData = memberDataCache.get().get(accountOwnerId);
    if (memberData != null) {
      return memberData;
    }
    Observable<MemberData> _memberData =
        RxObservable.defer(
                memberDataServiceStub::getMemberData,
                MemberDataRequest.newBuilder().setCustomerId(accountOwnerId).build())
            .onErrorReturn(
                t -> {
                  logger.error("Error fetching member data for {}", accountOwnerId);
                  return MemberData.getDefaultInstance();
                })
            .cache();
    Observable<MemberData> previous =
        memberDataCache.get().putIfAbsent(accountOwnerId, _memberData);
    return previous == null ? _memberData : previous;
  }

  public Observable<Boolean> cannotWatchDueToPayment(AccountProfileRemote accountProfile) {
    if (isMember(accountProfile)) {
      return getMemberData(accountProfile.getBoxedAccountOwnerId())
          .map(MemberData::getServiceEndReason)
          .map(
              reason ->
                  switch (reason) {
                    case SERVICE_END_PAYMENT_FAILURE,
                            SERVICE_END_PAYMENT_INPROGRESS,
                            SERVICE_END_NO_MOP,
                            SERVICE_END_PARTNER_PAYMENT_FAILURE ->
                        true;
                    default -> false;
                  });
    }
    return Observable.just(false);
  }

  private static boolean isMember(AccountProfileRemote accountProfile) {
    return MEMBER_STATUSES.contains(getMembershipStatus(accountProfile));
  }

  public Observable<List<APIServiceEndReasons>> getServiceEndReasons(
      AccountProfileRemote accountProfile) {
    if (isNotCurrentMember(accountProfile)) {
      increment("api.serviceendreason.call", "result", "membercheck");
      return Observable.just(List.of());
    }
    if (ENABLE_CANWATCHNOW.get() && accountProfile.getOptionalCanWatchNow().orElse(false)) {
      increment("api.serviceendreason.call", "result", "canwatchnow");
      return Observable.just(List.of());
    }
    increment("api.serviceendreason.call", "result", "passed");
    return getMemberData(accountProfile.getBoxedAccountOwnerId())
        .map(MemberData::getServiceEndReason)
        .map(com.netflix.membership.memberdata.protogen.ServiceEndReason::name)
        .map(APIServiceEndReasons::valueOf)
        .map(Collections::singletonList)
        .defaultIfEmpty(List.of());
  }

  public boolean isAutoCreatedProfile(AccountProfileRemote accountProfile) {
    Method profileCreationMethod = accountProfile.getProfileCreationInfo();
    return profileCreationMethod == Method.AUTO
        || profileCreationMethod == Method.AUTO_VIA_FORKLIFT;
  }

  public Single<List<AccountProfileRemote>> getProfiles(AccountProfileRemote accountProfile) {
    Single<List<AccountProfileRemote>> profiles = profilesCache.get().get();
    if (profiles != null) {
      return profiles;
    }
    profiles = _getProfiles(accountProfile);

    boolean success = profilesCache.get().compareAndSet(null, profiles);
    return success ? profiles : profilesCache.get().get();
  }

  private Single<List<AccountProfileRemote>> _getProfiles(AccountProfileRemote accountProfile) {
    return RxSingle.defer(
            subscriberServiceStub::getCustomers,
            CustomersReadRequest.newBuilder().setAcctId(accountProfile.getBoxedAcctId()).build())
        .map(AccountProfilesRemote::getProfilesListList)
        .onErrorReturn(ignore -> List.of(accountProfile));
  }

  public AccountProfileRemote currentAccountProfile() {
    Long customerId = Contexts.customerId().orElse(null);
    try {
      return customerId == null || customerId <= 0L ? null : getBlocking(customerId).orElse(null);
    } catch (Exception e) {
      InterruptAdvice.reinterruptIfNeeded(e);
      logger.error("Error calling subscriber with {}", customerId, e);
      return null;
    }
  }
}
