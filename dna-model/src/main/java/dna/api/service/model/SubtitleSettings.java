package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SubtitleSettings {
  private String ccCharColor;

  private String ccCharOpacity;

  private String ccCharSize;

  private String ccCharStyle;

  private String ccCharEdgeAttributes;

  private String ccCharEdgeColor;

  private String ccBackgroundColor;

  private String ccBackgroundOpacity;

  private String ccWindowColor;

  private String ccWindowOpacity;

  public String getCcCharColor() {
    return ccCharColor;
  }

  public String getCcCharOpacity() {
    return ccCharOpacity;
  }

  public String getCcCharSize() {
    return ccCharSize;
  }

  public String getCcCharStyle() {
    return ccCharStyle;
  }

  public String getCcCharEdgeAttributes() {
    return ccCharEdgeAttributes;
  }

  public String getCcCharEdgeColor() {
    return ccCharEdgeColor;
  }

  public String getCcBackgroundColor() {
    return ccBackgroundColor;
  }

  public String getCcBackgroundOpacity() {
    return ccBackgroundOpacity;
  }

  public String getCcWindowColor() {
    return ccWindowColor;
  }

  public String getCcWindowOpacity() {
    return ccWindowOpacity;
  }

  public SubtitleSettings setCcCharColor(String ccCharColor) {
    this.ccCharColor = ccCharColor;
    return this;
  }

  public SubtitleSettings setCcCharOpacity(String ccCharOpacity) {
    this.ccCharOpacity = ccCharOpacity;
    return this;
  }

  public SubtitleSettings setCcCharSize(String ccCharSize) {
    this.ccCharSize = ccCharSize;
    return this;
  }

  public SubtitleSettings setCcCharStyle(String ccCharStyle) {
    this.ccCharStyle = ccCharStyle;
    return this;
  }

  public SubtitleSettings setCcCharEdgeAttributes(String ccCharEdgeAttributes) {
    this.ccCharEdgeAttributes = ccCharEdgeAttributes;
    return this;
  }

  public SubtitleSettings setCcCharEdgeColor(String ccCharEdgeColor) {
    this.ccCharEdgeColor = ccCharEdgeColor;
    return this;
  }

  public SubtitleSettings setCcBackgroundColor(String ccBackgroundColor) {
    this.ccBackgroundColor = ccBackgroundColor;
    return this;
  }

  public SubtitleSettings setCcBackgroundOpacity(String ccBackgroundOpacity) {
    this.ccBackgroundOpacity = ccBackgroundOpacity;
    return this;
  }

  public SubtitleSettings setCcWindowColor(String ccWindowColor) {
    this.ccWindowColor = ccWindowColor;
    return this;
  }

  public SubtitleSettings setCcWindowOpacity(String ccWindowOpacity) {
    this.ccWindowOpacity = ccWindowOpacity;
    return this;
  }
}
