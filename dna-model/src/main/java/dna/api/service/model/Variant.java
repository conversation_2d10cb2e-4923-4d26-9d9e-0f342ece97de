package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.simone.APIVariant */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class Variant {
  private String id;

  private String correlationId;

  private String template;

  private Map<String, String> argumentData;

  public String getId() {
    return id;
  }

  public String getCorrelationId() {
    return correlationId;
  }

  public String getTemplate() {
    return template;
  }

  public Map<String, String> getArgumentData() {
    return argumentData;
  }

  public Variant setId(String id) {
    this.id = id;
    return this;
  }

  public Variant setCorrelationId(String correlationId) {
    this.correlationId = correlationId;
    return this;
  }

  public Variant setTemplate(String template) {
    this.template = template;
    return this;
  }

  public Variant setArgumentData(Map<String, String> argumentData) {
    this.argumentData = argumentData;
    return this;
  }
}
