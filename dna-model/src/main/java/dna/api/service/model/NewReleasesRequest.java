package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/** @dotnext com.netflix.api.service.list.APILolomoService */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NewReleasesRequest {
  private Map<String, Object> annotations;

  private Boolean forceRefresh;

  private String uuid;

  private Integer listMax;

  private Boolean allowFallback;

  public Map<String, Object> getAnnotations() {
    return annotations;
  }

  public Boolean getForceRefresh() {
    return forceRefresh;
  }

  public String getUuid() {
    return uuid;
  }

  public Integer getListMax() {
    return listMax;
  }

  public Boolean getAllowFallback() {
    return allowFallback;
  }

  public NewReleasesRequest setAnnotations(Map<String, Object> annotations) {
    this.annotations = annotations;
    return this;
  }

  public NewReleasesRequest setForceRefresh(Boolean forceRefresh) {
    this.forceRefresh = forceRefresh;
    return this;
  }

  public NewReleasesRequest setUuid(String uuid) {
    this.uuid = uuid;
    return this;
  }

  public NewReleasesRequest setListMax(Integer listMax) {
    this.listMax = listMax;
    return this;
  }

  public NewReleasesRequest setAllowFallback(Boolean allowFallback) {
    this.allowFallback = allowFallback;
    return this;
  }
}
