package com.netflix.api.service.prepostplay;

import static com.netflix.api.service.video.APIPostPlayRequest.ClientCapabilityOption.autoPlay;
import static com.netflix.api.service.video.APIPostPlayRequest.ClientCapabilityOption.originalPostPlay;
import static com.netflix.api.service.video.APIPostPlayRequest.ClientCapabilityOption.recommendations;
import static com.netflix.api.service.video.APIPostPlayRequest.ClientCapabilityOption.watchNext;
import static com.netflix.map.annotation.MapAnnotationConstants.USE_AUTOPLAY;
import static com.netflix.map.annotation.MapAnnotationConstants.USE_ORIGINAL_POST_PLAY;
import static com.netflix.map.annotation.MapAnnotationConstants.USE_RECOMMENDATIONS;
import static com.netflix.map.annotation.MapAnnotationConstants.USE_WATCH_NEXT;
import static java.util.Map.entry;

import com.netflix.api.grpc.GrpcCallHelpers.Future;
import com.netflix.api.platform.context.ImmutableRequestContext;
import com.netflix.api.platform.context.RequestContextWrapper;
import com.netflix.api.service.APIClientCapabilitiesInternal;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.abtest.PostplayLiveBypassAbTest;
import com.netflix.api.service.batch.USTListUtils;
import com.netflix.api.service.video.APIPostPlayRequest.ClientCapabilityOption;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.map.datatypes.MapAnnotations;
import com.netflix.map.datatypes.MapItem;
import com.netflix.map.datatypes.MapItemFactory;
import com.netflix.map.datatypes.MapList;
import com.netflix.map.datatypes.MapResponse;
import com.netflix.map.datatypes.impl.MapListImpl;
import com.netflix.map.datatypes.impl.MapResponseImpl;
import com.netflix.napa.protogen.PostplayServiceGrpc.PostplayServiceStub;
import com.netflix.napa.protogen.PreplayServiceGrpc.PreplayServiceStub;
import com.netflix.noir.protogen.NoirPage;
import com.netflix.noir.protogen.NoirPageRequest;
import com.netflix.p3.converter.datatype.P3LegacyItem;
import com.netflix.p3.converter.datatype.P3LegacyList;
import com.netflix.p3.converter.datatype.P3LegacyResponse;
import com.netflix.p3.converter.napa.NoirResponseConverter;
import com.netflix.p3.converter.napa.PostPlayRequestConverter;
import com.netflix.p3.converter.napa.PostplayCompleteWrapper;
import com.netflix.p3.converter.napa.PrePlayRequestConverter;
import com.netflix.server.context.CurrentVisitor;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.type.ISOCountry;
import com.netflix.type.NFCountry;
import com.netflix.type.SimpleVideo;
import com.netflix.type.TypeManager;
import com.netflix.type.Video;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletionStage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ThreePAdapter {
  private final Property<Boolean> IGNORE_BOOKMARKS_FOR_PREPLAY;

  private final Property<Boolean> DEBUG_PREPLAY_CALLS;

  private final Property<Boolean> PUBLISH_POSTPLAY_RESPONSE;

  private final PreplayServiceStub preplayServiceStub;
  private final PostplayServiceStub postPlayServiceStub;
  private final PrePlayRequestConverter prePlayRequestConverter;
  private final PostPlayRequestConverter postPlayRequestConverter;
  private final NoirResponseConverter noirResponseConverter;

  private final PostplayCompleteWrapper postplayCompleteWrapper;
  private final MapItemFactory mapItemFactory;
  private final PostplayLiveBypassAbTest postplayLiveBypassAbTest;

  @Autowired
  public ThreePAdapter(
      @GrpcSpringClient("napa") PreplayServiceStub preplayServiceStub,
      @GrpcSpringClient("napa") PostplayServiceStub postPlayServiceStub,
      PrePlayRequestConverter prePlayRequestConverter,
      PostPlayRequestConverter postPlayRequestConverter,
      NoirResponseConverter noirResponseConverter,
      PostplayCompleteWrapper postplayCompleteWrapper,
      MapItemFactory mapItemFactory,
      PostplayLiveBypassAbTest postplayLiveBypassAbTest,
      PropertyRepository propertyRepository) {
    this.preplayServiceStub = preplayServiceStub;
    this.postPlayServiceStub = postPlayServiceStub;
    this.prePlayRequestConverter = prePlayRequestConverter;
    this.postPlayRequestConverter = postPlayRequestConverter;
    this.noirResponseConverter = noirResponseConverter;
    this.postplayCompleteWrapper = postplayCompleteWrapper;
    this.mapItemFactory = mapItemFactory;
    this.postplayLiveBypassAbTest = postplayLiveBypassAbTest;

    this.IGNORE_BOOKMARKS_FOR_PREPLAY =
        propertyRepository.get("ignore.bookmarks.for.preplay", Boolean.class).orElse(false);
    this.DEBUG_PREPLAY_CALLS =
        propertyRepository.get("debug.preplay.calls", Boolean.class).orElse(false);
    this.PUBLISH_POSTPLAY_RESPONSE =
        propertyRepository.get("api.publish.postplay.response", Boolean.class).orElse(false);
  }

  public boolean ignoreBookmarksForPreplay() {
    return IGNORE_BOOKMARKS_FOR_PREPLAY.get();
  }

  public CompletionStage<MapResponse> getPrePlayPage(
      Map<String, Object> requestAnnotations, Boolean debug, int videoId) {

    debug = Optional.ofNullable(debug).orElse(false) || DEBUG_PREPLAY_CALLS.get();

    NoirPageRequest request = getNoirPageRequest(requestAnnotations, debug, videoId);

    return Future.call(preplayServiceStub::getPreplayNoirPage, request)
        .thenApply(response -> convertPreplayNoirPageResponse(request, response));
  }

  public CompletionStage<MapResponse> getPostPlayPage(
      Set<ClientCapabilityOption> clientCapabilityOptions,
      Integer limit,
      Map<String, Object> requestAnnotations,
      Boolean debug,
      int videoId) {

    NoirPageRequest request =
        getNoirPageRequest(clientCapabilityOptions, requestAnnotations, limit, debug, videoId);

    return postplayLiveBypassAbTest
        .shouldEvaluateLocalLivePostplay()
        .thenCompose(
            shouldEvaluate -> {
              CompletionStage<NoirPage> noirPageCompletableFuture =
                  shouldEvaluate
                      ? postplayCompleteWrapper.getPostPlayPage(request)
                      : Future.call(postPlayServiceStub::getPostplayNoirPage, request);
              return noirPageCompletableFuture;
            })
        .thenApply(response -> convertPostplayNoirPageResponse(request, response));
  }

  private NoirPageRequest getNoirPageRequest(
      Set<ClientCapabilityOption> clientCapabilityOptions,
      Map<String, Object> requestAnnotations,
      Integer limit,
      Boolean debug,
      int videoId) {
    ImmutableRequestContext requestContext = RequestContextWrapper.get();
    Map<String, Object> annotations = new HashMap<>();

    // translate clientCapabilityOptions
    if (clientCapabilityOptions != null) {
      for (ClientCapabilityOption option : clientCapabilityOptions) {
        annotations.put(clientCapabilityOptionMapping.get(option), true);
      }
    }

    if (requestAnnotations != null && !requestAnnotations.isEmpty()) {
      annotations.putAll(requestAnnotations);
    }

    APIClientCapabilitiesInternal.get(APIRequest.getCurrentRequest())
        .ifPresent(
            apiClientCapabilitiesInternal -> {
              if (apiClientCapabilitiesInternal.currentInteractiveTitlesPresent()) {
                annotations.put(
                    APIClientCapabilitiesInternal.INTERACTIVE_VIDEOS_FEATURE,
                    apiClientCapabilitiesInternal.getAllInteractiveTitlesImmutable());
              }
            });

    NFCountry country =
        Optional.ofNullable(requestContext.getCountry())
            .map(ISOCountry::getId)
            .map(NFCountry::findInstance)
            .orElse(null);

    return postPlayRequestConverter.createNoirPostplayRequest(
        CurrentVisitor.get(),
        country,
        requestContext.getNfLocale(),
        TypeManager.findObject(Video.class, videoId),
        limit,
        debug,
        annotations);
  }

  private static final Map<ClientCapabilityOption, String> clientCapabilityOptionMapping =
      Map.ofEntries(
          entry(recommendations, USE_RECOMMENDATIONS),
          entry(watchNext, USE_WATCH_NEXT),
          entry(originalPostPlay, USE_ORIGINAL_POST_PLAY),
          entry(autoPlay, USE_AUTOPLAY));

  private NoirPageRequest getNoirPageRequest(
      Map<String, Object> requestAnnotations, Boolean debug, int videoId) {
    ImmutableRequestContext requestContext = RequestContextWrapper.get();
    Map<String, Object> annotations = new HashMap<>();

    if (requestAnnotations != null && !requestAnnotations.isEmpty()) {
      annotations.putAll(requestAnnotations);
    }

    NFCountry country =
        Optional.ofNullable(requestContext.getCountry())
            .map(ISOCountry::getId)
            .map(NFCountry::findInstance)
            .orElse(null);

    return prePlayRequestConverter.createNoirPreplayRequest(
        CurrentVisitor.get(),
        country,
        requestContext.getNfLocale(),
        TypeManager.findObject(Video.class, videoId),
        debug,
        annotations);
  }

  private MapResponse convertPreplayNoirPageResponse(NoirPageRequest request, NoirPage response) {
    P3LegacyResponse p3LegacyResponse =
        noirResponseConverter.convertPreplayResponse(request, response);
    MapResponseImpl mapResponse = new MapResponseImpl();
    p3LegacyResponse.getAllLists().forEach(list -> mapResponse.addList(toMapList(list)));
    mapResponse.setAnnotations(toMapAnnotations(p3LegacyResponse.getAnnotations()));
    return mapResponse;
  }

  private MapResponse convertPostplayNoirPageResponse(NoirPageRequest request, NoirPage response) {
    P3LegacyResponse p3LegacyResponse = noirResponseConverter.convertResponse(request, response);
    MapResponseImpl mapResponse = new MapResponseImpl();
    p3LegacyResponse.getAllLists().forEach(list -> mapResponse.addList(toMapList(list)));
    mapResponse.setAnnotations(toMapAnnotations(p3LegacyResponse.getAnnotations()));
    if (PUBLISH_POSTPLAY_RESPONSE.get()) {
      var ctx = MantisPublishContext.getCurrent();
      ctx.add("api.postplay.noir.request", USTListUtils.toJson(request));
      ctx.add("api.postplay.noir.response", USTListUtils.toJson(response));
      ctx.add("api.postplay.p3legacy.response", USTListUtils.toJson(p3LegacyResponse));
      ctx.add("api.postplay.map.response", USTListUtils.toJson(mapResponse));
    }
    return mapResponse;
  }

  private MapList<?> toMapList(P3LegacyList<?> p3LegacyList) {
    List<MapItem<?>> list = new ArrayList<>(p3LegacyList.size());
    for (P3LegacyItem<?> p3LegacyItem : p3LegacyList) {
      list.add(toMapItem(p3LegacyItem));
    }
    return new MapListImpl<>(list, toMapAnnotations(p3LegacyList.getAnnotations()));
  }

  private MapItem<?> toMapItem(P3LegacyItem<?> p3LegacyItem) {
    if (p3LegacyItem instanceof P3LegacyList) {
      return toMapList((P3LegacyList<?>) p3LegacyItem);
    } else {
      Object item = p3LegacyItem.getItem();
      MapItem<?> mapItem;
      if (item instanceof Integer) {
        mapItem = mapItemFactory.newInstance(new SimpleVideo((Integer) item));
      } else {
        mapItem = mapItemFactory.newInstance(item);
      }
      mapItem.getAnnotations().addAllAnnotationsFromMap(p3LegacyItem.getAnnotations());
      return mapItem;
    }
  }

  private static MapAnnotations toMapAnnotations(Map<String, Object> annotations) {
    MapAnnotations mapAnnotations = new MapAnnotations();
    if (annotations != null && !annotations.isEmpty()) {
      mapAnnotations.addAllAnnotationsFromMap(new HashMap<>(annotations));
    }
    return mapAnnotations;
  }
}
