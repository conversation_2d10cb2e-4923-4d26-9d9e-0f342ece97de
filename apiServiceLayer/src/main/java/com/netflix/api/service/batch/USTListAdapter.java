package com.netflix.api.service.batch;

import static com.netflix.api.service.batch.USTListUtils.extractUuid;

import com.google.protobuf.GeneratedMessageV3;
import com.netflix.api.grpc.GrpcAsyncCall;
import com.netflix.api.grpc.GrpcCallHelpers.RxSingle;
import com.netflix.api.platform.exception.Exceptions;
import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.archaius.api.Property;
import com.netflix.gps.client.grps.page.ext.session.common.SessionKey;
import com.netflix.gps.client.grps.page.ext.session.common.SessionKey.KeyType;
import com.netflix.gps.client.grps.page.ext.session.common.SessionKeyParser;
import com.netflix.gps.maplogginglib.datatypes.MapJackson;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.map.annotation.MapAnnotationConstants;
import com.netflix.map.datatypes.MapResponse;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Registry;
import com.netflix.subscriberservice.common.MaturityLevel;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.ust.list.LegacyPageResponseConverter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import rx.Single;
import rx.functions.Func1;
import ust.list.v1.GetCachedListRequest;
import ust.list.v1.GetCachedListResponse;
import ust.list.v1.GetCachedLolomoRequest;
import ust.list.v1.GetCachedLolomoResponse;
import ust.list.v1.GetCategoriesRequest;
import ust.list.v1.GetCategoriesResponse;
import ust.list.v1.GetCharacterDataRequest;
import ust.list.v1.GetCharacterDataResponse;
import ust.list.v1.GetGalleryRequest;
import ust.list.v1.GetGalleryResponse;
import ust.list.v1.GetMenuNavigationSubGenresRequest;
import ust.list.v1.GetMenuNavigationSubGenresResponse;
import ust.list.v1.GetNewLolomoRequest;
import ust.list.v1.GetNewLolomoResponse;
import ust.list.v1.GetNewLolopiRequest;
import ust.list.v1.GetNewLolopiResponse;
import ust.list.v1.GetPreAppLolomoRequest;
import ust.list.v1.GetPreAppLolomoResponse;
import ust.list.v1.LegacyPageResponse;
import ust.list.v1.ListAccess;
import ust.list.v1.RefreshListRequest;
import ust.list.v1.RefreshListResponse;
import ust.list.v1.RefreshLolomoRequest;
import ust.list.v1.RefreshLolomoResponse;

@Component
public class USTListAdapter {
  private static final Logger logger = LoggerFactory.getLogger(USTListAdapter.class);

  private static final Property<Boolean> UST_LIST_DEBUG_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.ust.list.debug.enabled", Boolean.class)
          .orElse(false);

  private static final Property<Boolean> UST_LIST_MANTIS_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.ust.list.mantis.enabled", Boolean.class)
          .orElse(false);

  private static final Property<Boolean> LIST_CALL_MANTIS_ENABLED =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("api.list.call.mantis.enabled", Boolean.class)
          .orElse(false);

  private final ListAccess listAccess;
  private final SessionKeyParser sessionKeyParser;
  private final LegacyPageRequestUtils legacyPageRequestUtils;
  private final LegacyPageResponseConverter legacyResponseConverter;

  private final Registry registry;
  private final Id listMetric;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  public USTListAdapter(
      ListAccess listAccess,
      SessionKeyParser sessionKeyParser,
      LegacyPageRequestUtils legacyPageRequestUtils,
      LegacyPageResponseConverter legacyResponseConverter,
      Registry registry) {
    this.listAccess = listAccess;
    this.sessionKeyParser = sessionKeyParser;
    this.legacyPageRequestUtils = legacyPageRequestUtils;
    this.legacyResponseConverter = legacyResponseConverter;
    this.registry = registry;
    this.listMetric = registry.createId("ust.list.call");
  }

  public long markUstCall(String method) {
    return markCall(method, "ust");
  }

  public long markMapCall(String method) {
    return markCall(method, "map");
  }

  private long markCall(String method, String path) {
    registry.counter(listMetric.withTags("path", path, "method", method)).increment();
    long ts = System.nanoTime();
    if (LIST_CALL_MANTIS_ENABLED.get()) {
      MantisPublishContext.getCurrent().add(path + ".method.called." + ts, method);
    }
    return ts;
  }

  public Single<MapResponse> getNewLolopi(Map<String, Object> annotations) {
    return getNewLolopi(annotations, System.nanoTime());
  }

  public Single<MapResponse> getNewLolopi(Map<String, Object> annotations, long ts) {
    GetNewLolopiRequest request =
        GetNewLolopiRequest.newBuilder()
            .setRequest(legacyPageRequestUtils.buildLegacyPageRequest(annotations))
            .build();

    return callUst(listAccess::getNewLolopi, request, "getNewLolopi", ts)
        .map(GetNewLolopiResponse::getResponse)
        .map(responseConverter(ts));
  }

  public Single<MapResponse> getCharacterData(Map<String, Object> annotations) {
    long ts = System.nanoTime();

    GetCharacterDataRequest request =
        GetCharacterDataRequest.newBuilder()
            .setRequest(legacyPageRequestUtils.buildLegacyPageRequest(annotations))
            .build();

    return callUst(listAccess::getCharacterData, request, "getCharacterData", ts)
        .map(GetCharacterDataResponse::getResponse)
        .map(responseConverter(ts));
  }

  public Single<MapResponse> getNewLolomo(Map<String, Object> annotations) {
    long ts = System.nanoTime();

    if (annotations.containsKey(MapAnnotationConstants.UUID)) {
      return getListById(annotations, ts);
    }

    GetNewLolomoRequest request =
        GetNewLolomoRequest.newBuilder()
            .setRequest(legacyPageRequestUtils.buildLegacyPageRequest(annotations))
            .build();

    return callUst(listAccess::getNewLolomo, request, "getNewLolomo", ts)
        .map(GetNewLolomoResponse::getResponse)
        .map(responseConverter(ts));
  }

  public Single<MapResponse> refreshLolomo(Map<String, Object> annotations) {
    return refreshLolomo(annotations, System.nanoTime());
  }

  public Single<MapResponse> refreshLolomo(Map<String, Object> annotations, long ts) {
    RefreshLolomoRequest request =
        RefreshLolomoRequest.newBuilder()
            .setRequest(legacyPageRequestUtils.buildLegacyPageRequest(annotations))
            .build();

    return callUst(listAccess::refreshLolomo, request, "refreshLolomo", ts)
        .map(RefreshLolomoResponse::getResponse)
        .map(responseConverter(ts));
  }

  private Single<MapResponse> getCachedLolomo(Map<String, Object> annotations, long ts) {
    GetCachedLolomoRequest request =
        GetCachedLolomoRequest.newBuilder()
            .setRequest(legacyPageRequestUtils.buildLegacyCachedListRequest(annotations))
            .build();

    return callUst(listAccess::getCachedLolomo, request, "getCachedLolomo", ts)
        .map(GetCachedLolomoResponse::getResponse)
        .map(responseConverter(ts));
  }

  public Single<MapResponse> getPreAppLolomo(Map<String, Object> annotations) {
    long ts = System.nanoTime();

    GetPreAppLolomoRequest request =
        GetPreAppLolomoRequest.newBuilder()
            .setRequest(legacyPageRequestUtils.buildLegacyPageRequest(annotations))
            .build();

    return callUst(listAccess::getPreAppLolomo, request, "getPreAppLolomo", ts)
        .map(GetPreAppLolomoResponse::getResponse)
        .map(responseConverter(ts));
  }

  public Single<MapResponse> refreshList(Map<String, Object> annotations) {
    return refreshList(annotations, System.nanoTime());
  }

  public Single<MapResponse> refreshList(Map<String, Object> annotations, long ts) {
    RefreshListRequest request =
        RefreshListRequest.newBuilder()
            .setRequest(legacyPageRequestUtils.buildLegacyPageRequest(annotations))
            .build();

    return callUst(listAccess::refreshList, request, "refreshList", ts)
        .map(RefreshListResponse::getResponse)
        .map(responseConverter(ts));
  }

  private Single<MapResponse> getCachedList(Map<String, Object> annotations, long ts) {
    GetCachedListRequest request =
        GetCachedListRequest.newBuilder()
            .setRequest(legacyPageRequestUtils.buildLegacyCachedListRequest(annotations))
            .build();

    return callUst(listAccess::getCachedList, request, "getCachedList", ts)
        .map(GetCachedListResponse::getResponse)
        .map(responseConverter(ts));
  }

  public Single<MapResponse> getListById(Map<String, Object> annotations) {
    return getListById(annotations, System.nanoTime());
  }

  public Single<MapResponse> getListById(Map<String, Object> annotations, long ts) {

    String uuid = extractUuid(annotations);

    boolean shouldRefresh =
        isNotFallbackRequest(uuid)
            && isNotFallbackRequestSessionRequest(uuid)
            && isRefreshVolatileContent(annotations);

    if (sessionKeyParser.parse(uuid).getParentKey().isEmpty()) {
      return shouldRefresh ? refreshLolomo(annotations, ts) : getCachedLolomo(annotations, ts);
    }
    return shouldRefresh ? refreshList(annotations, ts) : getCachedList(annotations, ts);
  }

  public Single<MapResponse> getGallery(Map<String, Object> annotations) {
    return getGallery(annotations, System.nanoTime());
  }

  public Single<MapResponse> getGallery(Map<String, Object> annotations, long ts) {
    GetGalleryRequest request =
        GetGalleryRequest.newBuilder()
            .setRequest(legacyPageRequestUtils.buildLegacyPageRequest(annotations))
            .build();

    return callUst(listAccess::getGallery, request, "getGallery", ts)
        .map(GetGalleryResponse::getResponse)
        .map(responseConverter(ts));
  }

  public Single<MapResponse> getCategories(Map<String, Object> annotations) {
    return getCategories(annotations, System.nanoTime());
  }

  public Single<MapResponse> getCategories(Map<String, Object> annotations, long ts) {
    GetCategoriesRequest request =
        GetCategoriesRequest.newBuilder()
            .setRequest(legacyPageRequestUtils.buildLegacyPageRequest(annotations))
            .build();

    return callUst(listAccess::getCategories, request, "getCategories", ts)
        .map(GetCategoriesResponse::getResponse)
        .map(responseConverter(ts));
  }

  public Single<MapResponse> getMenuNavigationSubGenres(Map<String, Object> annotations) {
    return getMenuNavigationSubGenres(annotations, System.nanoTime());
  }

  public Single<MapResponse> getMenuNavigationSubGenres(Map<String, Object> annotations, long ts) {
    GetMenuNavigationSubGenresRequest request =
        GetMenuNavigationSubGenresRequest.newBuilder()
            .setRequest(legacyPageRequestUtils.buildLegacyPageRequest(annotations))
            .build();

    return callUst(
            listAccess::getMenuNavigationSubGenres, request, "getMenuNavigationSubGenres", ts)
        .map(GetMenuNavigationSubGenresResponse::getResponse)
        .map(responseConverter(ts));
  }

  public static boolean isFallbackUuid(final String uuid) {
    return StringUtils.isNotBlank(uuid) && uuid.startsWith("Fallback");
  }

  private boolean isFallbackSessionUuid(String uuid) {
    return StringUtils.isNotBlank(uuid) && isGpsFallbackSessionKey(uuid);
  }

  private static boolean isNotFallbackRequest(String uuid) {
    return !isFallbackUuid(uuid);
  }

  private boolean isNotFallbackRequestSessionRequest(String uuid) {
    return !isFallbackSessionUuid(uuid);
  }

  private boolean isGpsFallbackSessionKey(String uuid) {
    if (sessionKeyParser != null && sessionKeyParser.isSessionKey(uuid)) {
      final SessionKey sessionKey = sessionKeyParser.parse(uuid);
      return sessionKey.getType().equals(KeyType.fallback);
    }
    return false;
  }

  private static boolean isRefreshVolatileContent(Map<String, Object> annotations) {
    Object refreshVolatileContent =
        annotations.get(MapAnnotationConstants.REFRESH_VOLATILE_CONTENT);
    return ((refreshVolatileContent instanceof Boolean) && ((Boolean) refreshVolatileContent));
  }

  private static <Req extends GeneratedMessageV3, Resp extends GeneratedMessageV3>
      Single<Resp> callUst(
          GrpcAsyncCall<Req, Resp> ustCall, Req request, String ustMethod, long ts) {

    if (UST_LIST_MANTIS_ENABLED.get()) {
      MantisPublishContext.getCurrent()
          .add("ust." + ustMethod + ".request." + ts, USTListUtils.toJson(request));
    }
    return RxSingle.defer(ustCall, request)
        .doOnSuccess(
            response -> {
              if (UST_LIST_MANTIS_ENABLED.get()) {
                MantisPublishContext.getCurrent()
                    .add("ust." + ustMethod + ".response." + ts, USTListUtils.toJson(response));
              }
            })
        .doOnError(
            t -> {
              if (UST_LIST_DEBUG_ENABLED.get()) {
                logger.error("{} error", ustMethod, t);
              }
              if (UST_LIST_MANTIS_ENABLED.get()) {
                MantisPublishContext.getCurrent()
                    .add("ust." + ustMethod + ".error." + ts, Exceptions.getStackTrace(t));
              }
            });
  }

  private Func1<LegacyPageResponse, MapResponse> responseConverter(long ts) {
    return response -> {
      MapResponse mapResponse = legacyResponseConverter.toMapResponse(response);
      publishResponse(response, mapResponse, ts);
      return mapResponse;
    };
  }

  private void publishResponse(LegacyPageResponse response, MapResponse mapResponse, long ts) {
    if (UST_LIST_MANTIS_ENABLED.get()) {
      MantisPublishContext context = MantisPublishContext.getCurrent();
      try {
        context.add("api.ust.list.status.success", "true");
        context.add("api.ust.list.status." + ts, "success");
        context.add("api.ust.list.raw" + ts, USTListUtils.toJson(response));
        context.add("api.ust.list." + ts, MapJackson.toJson(mapResponse));
      } catch (IOException e) {
        context.add("api.ust.list.status.failure", "true");
        context.add("api.ust.list.status." + ts, "failure");
        context.add("api.ust.list.error." + ts, Exceptions.getStackTrace(e));
      }
    }
  }

  public static Map<String, Object> getAccountProfileInfoToAnnotations(
      final AccountProfileRemote accountProfile) {
    if (accountProfile == null) {
      return new HashMap<>();
    }
    HashMap<String, Object> map = new HashMap<>();
    accountProfile
        .getOptionalMaturityLevel()
        .map(MaturityLevel::getMaturityLevel)
        .ifPresent(maturityLevel -> map.put("maturityLevel", maturityLevel.name()));

    accountProfile
        .getOptionalMaturityLevel()
        .ifPresent((maturity) -> map.put("maturityInteger", maturity));

    map.put("hasExcludedContent", hasExcludedContent(accountProfile));
    map.put("membershipStatus", accountProfile.getMembershipStatusEnum().name());
    map.put("visitor", accountProfile.getBoxedProfileId());
    accountProfile.getOptionalAccountOwnerId().ifPresent(id -> map.put("accountOwnerId", id));
    map.put("experienceType", accountProfile.getExperienceTypeEnum().name());

    accountProfile.getOptionalPlanId().ifPresent(planId -> map.put("membershipPlanId", planId));
    accountProfile
        .getOptionalProfileName()
        .ifPresent(profileName -> map.put("profileName", profileName));
    map.put("isProfileFirstUse", accountProfile.getOptionalIsProfileFirstUse().orElse(false));
    return map;
  }

  public static boolean hasExcludedContent(AccountProfileRemote accountProfileRemote) {
    return accountProfileRemote.hasParentalControlState()
        ? accountProfileRemote.getParentalControlState().getBoxedHasExcludedContent()
        : false;
  }
}
