package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class ReactionRating {
  private Long timestamp;

  private String ratingValue;

  public Long getTimestamp() {
    return timestamp;
  }

  public String getRatingValue() {
    return ratingValue;
  }

  public ReactionRating setTimestamp(Long timestamp) {
    this.timestamp = timestamp;
    return this;
  }

  public ReactionRating setRatingValue(String ratingValue) {
    this.ratingValue = ratingValue;
    return this;
  }
}
