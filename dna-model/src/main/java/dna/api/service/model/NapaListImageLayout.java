package dna.api.service.model;

import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * A layout that is a two column list, where images are listed on the left and content is on the
 * right.
 */
@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NapaListImageLayout extends NapaLayout {}
