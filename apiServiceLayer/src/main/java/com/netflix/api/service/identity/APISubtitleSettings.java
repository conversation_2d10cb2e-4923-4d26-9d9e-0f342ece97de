package com.netflix.api.service.identity;

import com.netflix.api.annotations.ExcludeFromCoverage;

public interface APISubtitleSettings {

  @ExcludeFromCoverage
  enum Color {
    WHITE(0xFFFFFF),
    BLAC<PERSON>(0x000000),
    <PERSON>ED(0xC80000),
    <PERSON><PERSON><PERSON>(0x00C800),
    <PERSON><PERSON>UE(0x0000C8),
    YELLOW(0xEEDC00),
    MAGENTA(0xD60080),
    CYAN(0x009FDA);

    private final int hexCode;

    Color(int hexCode) {
      this.hexCode = hexCode;
    }

    /**
     * @return a hex representation of the color as an integer
     */
    public int asHex() {
      return hexCode;
    }
  }

  @ExcludeFromCoverage
  enum Opacity {
    OPAQUE,
    SEMI_TRANSPARENT,
    NONE
  }

  @ExcludeFromCoverage
  enum Size {
    SMALL,
    MEDIUM,
    LARGE
  }

  @ExcludeFromCoverage
  enum Style {
    MONOSPACED_SERIF,
    PROPORTIONAL_SERIF,
    MONOSPACED_SANS_SERIF,
    PROPORTIONAL_SANS_SERIF,
    CASUAL,
    CURSIVE,
    SMALL_CAPITALS
  }

  @ExcludeFromCoverage
  enum EdgeAttributes {
    NONE,
    RAISED,
    DEPRESSED,
    UNIFORM,
    DROP_SHADOW
  }

  APISubtitleSettings DEFAULTS =
      new APISubtitleSettings() {

        @Override
        public Color getCharacterColor() {
          return Color.WHITE;
        }

        @Override
        public Opacity getCharacterOpacity() {
          return Opacity.OPAQUE;
        }

        @Override
        public Size getCharacterSize() {
          return Size.MEDIUM;
        }

        @Override
        public Style getCharacterStyle() {
          return Style.PROPORTIONAL_SANS_SERIF;
        }

        @Override
        public EdgeAttributes getCharacterEdgeAttributes() {
          return EdgeAttributes.DROP_SHADOW;
        }

        @Override
        public Color getCharacterEdgeColor() {
          return Color.BLACK;
        }

        @Override
        public Color getBackgroundColor() {
          return null;
        }

        @Override
        public Opacity getBackgroundOpacity() {
          return Opacity.OPAQUE;
        }

        @Override
        public Color getWindowColor() {
          return null;
        }

        @Override
        public Opacity getWindowOpacity() {
          return Opacity.OPAQUE;
        }

        @Override
        public void setCharacterColor(Color color) {}

        @Override
        public void setCharacterOpacity(Opacity opacity) {}

        @Override
        public void setCharacterSize(Size size) {}

        @Override
        public void setCharacterStyle(Style style) {}

        @Override
        public void setCharacterEdgeAttributes(EdgeAttributes edgeAttributes) {}

        @Override
        public void setCharacterEdgeColor(Color color) {}

        @Override
        public void setBackgroundColor(Color color) {}

        @Override
        public void setBackgroundOpacity(Opacity opacity) {}

        @Override
        public void setWindowColor(Color color) {}

        @Override
        public void setWindowOpacity(Opacity opacity) {}
      };

  /**
   * Color of the characters within the subtitle
   *
   * @return a {@link Color} if the user has a setting for Character Color, null otherwise
   */
  Color getCharacterColor();

  /**
   * Opacity of the characters within the subtitle
   *
   * @return an {@link Opacity} if the user has a setting for Character Opacity, null otherwise
   */
  Opacity getCharacterOpacity();

  /**
   * Size of the characters within the subtitle
   *
   * @return a {@link Size} if the user has a setting for Character Size, null otherwise
   */
  Size getCharacterSize();

  /**
   * The font for the characters within the subtitle
   *
   * @return a {@link Style} if the user has a setting for Character Style, null otherwise
   */
  Style getCharacterStyle();

  /**
   * How to render the outer (edge) areas of the characters within the subttile
   *
   * @return an {@link EdgeAttributes} if the user has a setting for Character Edge Attributes, null
   *     otherwise
   */
  EdgeAttributes getCharacterEdgeAttributes();

  /**
   * The color with which to render the outer (edge) areas of the characters within a subtitle
   *
   * @return a {@link Color} if the user has a setting for Character Edge Color, null otherwise
   */
  Color getCharacterEdgeColor();

  /**
   * The background color for the area behind the characters within the subtitle window. For the
   * color of the entire subtitle window see {@link #getWindowColor()}
   *
   * @return a {@link Color} if the user has a setting for Background Color, null otherwise
   */
  Color getBackgroundColor();

  /**
   * The background opacity for the area behind the characters within the subtitle window. For the
   * opacity of the entire subtitle window see {@link #getWindowColor()}
   *
   * @return an {@link Opacity} if the user has a setting for Background Opacity, null otherwise
   */
  Opacity getBackgroundOpacity();

  /**
   * The window color for the entire text box that contains the subtitles. For the color of the area
   * only behind the characters see {@link #getBackgroundColor()}
   *
   * @return a {@link Color} if the user has a setting for Window Color, null otherwise
   */
  Color getWindowColor();

  /**
   * The window opacity for the entire text box that contains the subtitles. For the opacity of the
   * area only behind the characters see {@link #getBackgroundOpacity()}
   *
   * @return an {@link Opacity} if the user has a setting for Window Opacity, null otherwise
   */
  Opacity getWindowOpacity();

  /**
   * See {@link #getCharacterColor()} for description of property. After setting values use
   * {@code APIIdentityService.UpdateBuilder#setSubtitleSettings(APISubtitleSettings)} to persist the
   * changes.
   *
   * @param color the new value for character color
   */
  void setCharacterColor(Color color);

  /**
   * See {@link #getCharacterOpacity()} for description of property. After setting values use
   * {@code APIIdentityService.UpdateBuilder#setSubtitleSettings(APISubtitleSettings)} to persist the
   * changes.
   *
   * @param opacity the new value for character opacity
   */
  void setCharacterOpacity(Opacity opacity);

  /**
   * See {@link #getCharacterSize()} for description of property. After setting values use
   * {@code APIIdentityService.UpdateBuilder#setSubtitleSettings(APISubtitleSettings)} to persist the
   * changes.
   *
   * @param size the new value for character size
   */
  void setCharacterSize(Size size);

  /**
   * See {@link #getCharacterStyle()} for description of property. After setting values use
   * {@code APIIdentityService.UpdateBuilder#setSubtitleSettings(APISubtitleSettings)} to persist the
   * changes.
   *
   * @param style the new value for character style
   */
  void setCharacterStyle(Style style);

  /**
   * See {@link #getCharacterEdgeAttributes()} for description of property. After setting values use
   * {@code APIIdentityService.UpdateBuilder#setSubtitleSettings(APISubtitleSettings)} to persist
   * the changes.
   *
   * @param edgeAttributes the new value for character edge attributes
   */
  void setCharacterEdgeAttributes(EdgeAttributes edgeAttributes);

  /**
   * See {@link #getCharacterEdgeColor()} for description of property. After setting values use
   * {@code APIIdentityService.UpdateBuilder#setSubtitleSettings(APISubtitleSettings)} to persist
   * the changes.
   *
   * @param color the new value for character edge color
   */
  void setCharacterEdgeColor(Color color);

  /**
   * See {@link #getBackgroundColor()} for description of property. After setting values use
   * {@code APIIdentityService.UpdateBuilder#setSubtitleSettings(APISubtitleSettings)} to persist the
   * changes.
   *
   * @param color the new value for background color
   */
  void setBackgroundColor(Color color);

  /**
   * See {@link #getBackgroundOpacity()} for description of property. After setting values use
   * {@code APIIdentityService.UpdateBuilder#setSubtitleSettings(APISubtitleSettings)} to persist
   * the changes.
   *
   * @param opacity the new value for background opacity
   */
  void setBackgroundOpacity(Opacity opacity);

  /**
   * See {@link #getWindowColor()} for description of property. After setting values use
   * {@code APIIdentityService.UpdateBuilder#setSubtitleSettings(APISubtitleSettings)} to persist the
   * changes.
   *
   * @param color the new value for window color
   */
  void setWindowColor(Color color);

  /**
   * See {@link #getWindowOpacity()} for description of property. After setting values use
   * {@code APIIdentityService.UpdateBuilder#setSubtitleSettings(APISubtitleSettings)} to persist the
   * changes.
   *
   * @param opacity the new value for window opacity
   */
  void setWindowOpacity(Opacity opacity);
}
