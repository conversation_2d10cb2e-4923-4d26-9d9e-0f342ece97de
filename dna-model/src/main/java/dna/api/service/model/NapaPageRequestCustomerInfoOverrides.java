package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class NapaPageRequestCustomerInfoOverrides {
  private String esn;

  private Boolean hasExcludedContent;

  private Boolean isInternalUser;

  private Boolean isKidsProfile;

  private Boolean isFamilyProfile;

  private Integer maturityLevel;

  private Map<String, Integer> testAllocations;

  private Boolean isDvdUser;

  public String getEsn() {
    return esn;
  }

  public Boolean getHasExcludedContent() {
    return hasExcludedContent;
  }

  public Boolean getIsInternalUser() {
    return isInternalUser;
  }

  public Boolean getIsKidsProfile() {
    return isKidsProfile;
  }

  public Boolean getIsFamilyProfile() {
    return isFamilyProfile;
  }

  public Integer getMaturityLevel() {
    return maturityLevel;
  }

  public Map<String, Integer> getTestAllocations() {
    return testAllocations;
  }

  public Boolean getIsDvdUser() {
    return isDvdUser;
  }

  public NapaPageRequestCustomerInfoOverrides setEsn(String esn) {
    this.esn = esn;
    return this;
  }

  public NapaPageRequestCustomerInfoOverrides setHasExcludedContent(Boolean hasExcludedContent) {
    this.hasExcludedContent = hasExcludedContent;
    return this;
  }

  public NapaPageRequestCustomerInfoOverrides setIsInternalUser(Boolean isInternalUser) {
    this.isInternalUser = isInternalUser;
    return this;
  }

  public NapaPageRequestCustomerInfoOverrides setIsKidsProfile(Boolean isKidsProfile) {
    this.isKidsProfile = isKidsProfile;
    return this;
  }

  public NapaPageRequestCustomerInfoOverrides setIsFamilyProfile(Boolean isFamilyProfile) {
    this.isFamilyProfile = isFamilyProfile;
    return this;
  }

  public NapaPageRequestCustomerInfoOverrides setMaturityLevel(Integer maturityLevel) {
    this.maturityLevel = maturityLevel;
    return this;
  }

  public NapaPageRequestCustomerInfoOverrides setTestAllocations(
      Map<String, Integer> testAllocations) {
    this.testAllocations = testAllocations;
    return this;
  }

  public NapaPageRequestCustomerInfoOverrides setIsDvdUser(Boolean isDvdUser) {
    this.isDvdUser = isDvdUser;
    return this;
  }
}
