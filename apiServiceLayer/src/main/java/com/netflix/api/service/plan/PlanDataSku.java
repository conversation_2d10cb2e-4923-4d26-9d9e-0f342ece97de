package com.netflix.api.service.plan;

import com.netflix.api.platform.deprecated.DeprecatedMethodTracker;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.account.APIPlanDeviceType;
import com.netflix.membership.skuservice.protogen.Plan;
import com.netflix.membership.skuservice.protogen.Plan.VideoQuality;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

public class PlanDataSku implements PlanData {

  private final long planId;
  private final Plan plan;

  public PlanDataSku(long planId, Plan plan) {
    this.planId = planId;
    this.plan = plan;
  }

  @Override
  public long getPlanId() {
    return planId;
  }

  @Override
  public boolean isSDOnly() {
    return plan.getVideoQuality() == VideoQuality.SD;
  }

  @Override
  public boolean isHDEnabled() {
    final VideoQuality videoQuality = plan.getVideoQuality();
    return videoQuality == VideoQuality.HD
        || videoQuality == VideoQuality.HD720p
        || videoQuality == VideoQuality.UHD;
  }

  @Override
  public boolean isUHDEnabled() {
    return plan.getVideoQuality() == VideoQuality.UHD;
  }

  @Override
  public boolean isMobileOnly() {
    return plan.getLimitedToMobileOnly();
  }

  @Override
  public int getMaxStreams() {
    return plan.getMaxConcurrentStream();
  }

  @Override
  public int getMaxDownloadDevices() {
    return plan.getMaxDownloadDevices();
  }

  @Override
  @Deprecated
  public boolean isFreemium() {
    DeprecatedMethodTracker.incrementDeprecatedMetrics(
        "PlanDataSku",
        "isFreemium",
        Collections.emptyList(),
        APIRequest.getCurrentRequest().getRequestContext().getEndpointPath());
    return plan.getIsFreemium();
  }

  @Override
  public Set<APIPlanDeviceType> getAllowedDeviceTypes() {
    return plan.getAllowedDeviceTypesList().stream()
        .map(deviceType -> APIPlanDeviceType.valueOf(deviceType.name()))
        .collect(Collectors.toSet());
  }
}
