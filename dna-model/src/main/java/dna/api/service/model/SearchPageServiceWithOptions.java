package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SearchPageServiceWithOptions implements GraphData {
  public Map<String, Map<String, String>> entityPageId;

  public Map<String, String> prequeryPageId;

  public Map<String, Map<String, String>> queryPageId;

  public Map<String, Map<String, String>> filterPageId;

  public Map<String, Map<String, Atom<SearchPage>>> pageById;
}
