package com.netflix.api.service.titlegroup.vxs;

import static com.netflix.api.service.titlegroup.vxs.APIVXSTitleGroupCache.EntrySet;
import static com.netflix.api.service.titlegroup.vxs.APIVXSTitleGroupCache.Key;

import com.netflix.api.facade.service.VideoLookup;
import com.netflix.api.service.titlegroup.APITitleGroup;
import com.netflix.api.service.titlegroup.APITitleGroupDataSource;
import com.netflix.api.service.titlegroup.APITitleGroupImpl;
import com.netflix.api.service.titlegroup.APITitleGroupSet;
import com.netflix.api.service.titlegroup.APITitleGroupSetGroupMemberImpl;
import com.netflix.api.service.titlegroup.APITitleGroupSetImpl;
import com.netflix.api.service.titlegroup.APITitleGroupSetMember;
import com.netflix.api.service.titlegroup.APITitleGroupSetVideoMemberImpl;
import com.netflix.api.service.video.APIVideoFactory;
import com.netflix.videometadata.type.CompleteVideo;
import com.netflix.vxs.protogen.MemberKind;
import com.netflix.vxs.protogen.TitleGroup;
import com.netflix.vxs.protogen.TitleGroupMember;
import com.netflix.vxs.protogen.TitleGroupMemberships;
import com.netflix.vxs.protogen.VxsServiceGrpc;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import rx.Observable;

/** Request scoped data source for the title groups */
public class APIVXSTitleGroupDataSourceImpl implements APITitleGroupDataSource {

  private final APIVXSTitleGroupCache titleGroupCache;
  private final APIVXSTitleGroupFetcher titleGroupFetcher;
  private final VideoLookup videoLookup;

  public APIVXSTitleGroupDataSourceImpl(
      APIVideoFactory videoFactory, VxsServiceGrpc.VxsServiceStub vxsServiceClient) {
    this.titleGroupCache = new APIVXSTitleGroupCache();
    this.titleGroupFetcher = new APIVXSTitleGroupFetcher(vxsServiceClient);
    this.videoLookup = videoFactory.getVideoLookup();
  }

  @Override
  public Observable<List<APITitleGroupSet>> findGroupSets(
      Collection<APITitleGroupSetMember> members) {
    return titleGroupFetcher
        .fetchVXSSiblings(members)
        .filter(siblings -> siblings.getGroup() != null && siblings.getGroup().getIsAvailable())
        .map(siblings -> createGroupSetsRelativeTo(siblings.getId(), siblings.getGroup()))
        .filter(Optional::isPresent)
        .map(Optional::get)
        .cast(APITitleGroupSet.class)
        .toList();
  }

  @Override
  public Observable<List<APITitleGroupSet>> getGroupSets(Collection<APITitleGroup> groups) {
    List<Integer> groupIDs = groups.stream().map(APITitleGroup::getId).toList();
    return getGroupSet(groupIDs)
        .onErrorResumeNext(t -> Observable.just(List.of())); // do not cache errors;
  }

  @Override
  public Observable<Set<APITitleGroupSetMember>> findMembersFromIds(Collection<Integer> videoIds) {
    EntrySet<TitleGroupMemberships, Integer, FetchVXSTitleGroupMembershipsKey> cacheEntries =
        titleGroupCache.getCacheEntries(videoIds, FetchVXSTitleGroupMembershipsKey::createFromId);
    return findMembers(cacheEntries)
        .onErrorResumeNext(t -> Observable.just(Set.of())); // do not cache errors
  }

  private Observable<Set<APITitleGroupSetMember>> findMembers(
      EntrySet<TitleGroupMemberships, Integer, FetchVXSTitleGroupMembershipsKey> cacheEntries) {
    Collection<Integer> toFetchIds = cacheEntries.missingKeyIds();
    Observable<TitleGroupMemberships> entries = Observable.from(cacheEntries.existingEntries());

    if (!toFetchIds.isEmpty()) {
      Observable<TitleGroupMemberships> fetchEntries =
          titleGroupFetcher
              .fetchVXSMemberships(toFetchIds)
              .to(titleGroupCache.cacheOnNext(FetchVXSTitleGroupMembershipsKey::create));
      entries = Observable.mergeDelayError(entries, fetchEntries);
    }

    return entries
        .flatMap(
            membership ->
                Observable.from(
                    membership.getParentIdList().stream()
                        .map(
                            groupId ->
                                new APITitleGroupSetVideoMemberImpl(membership.getId(), groupId))
                        .toList()))
        .toList()
        .map(
            list -> {
              Set<Integer> groups =
                  list.stream()
                      .map(APITitleGroupSetMember::getParentGroupId)
                      .collect(Collectors.toSet());
              Map<Integer, CompleteVideo> localGroup = findLocalGroups(groups);
              return list.stream()
                  .filter(member -> localGroup.containsKey(member.getParentGroupId()))
                  .collect(Collectors.toCollection(HashSet::new));
            });
  }

  @Override
  public Observable<List<APITitleGroup>> findGroups(Collection<Integer> groups) {
    Map<Integer, CompleteVideo> locallyAvailableTitleGroup = findLocalGroups(groups);
    return fetchTitleGroups(locallyAvailableTitleGroup.keySet(), false)
        .map(
            titleGroup -> {
              CompleteVideo completeVideo = locallyAvailableTitleGroup.get(titleGroup.getId());
              return create(completeVideo, titleGroup);
            })
        .filter(Objects::nonNull)
        .toList();
  }

  private APITitleGroup create(CompleteVideo video, TitleGroup vxsTitleGroup) {
    return new APITitleGroupImpl(video, vxsTitleGroup);
  }

  private Observable<TitleGroup> fetchTitleGroupWithCache(
      Collection<Integer> groups, boolean includeMembers) {
    EntrySet<TitleGroup, Integer, FetchVXSTitleGroupKey> cacheEntries =
        titleGroupCache.getCacheEntries(
            groups, groupId -> new FetchVXSTitleGroupKey(groupId, includeMembers));

    Collection<Integer> toFetchIds = cacheEntries.missingKeyIds();
    Collection<TitleGroup> cachedEntries = cacheEntries.existingEntries();

    if (toFetchIds.isEmpty()) {
      return Observable.from(cachedEntries);
    }

    Observable<TitleGroup> fetchedTitleGroups =
        titleGroupFetcher
            .fetchVXSTitleGroups(toFetchIds, includeMembers)
            .to(
                titleGroupCache.cacheOnNext(
                    titleGroup -> new FetchVXSTitleGroupKey(titleGroup.getId(), includeMembers)));
    return Observable.mergeDelayError(Observable.from(cachedEntries), fetchedTitleGroups)
        .onErrorResumeNext(t -> Observable.empty()); // do not cache errors
  }

  private Observable<TitleGroup> fetchTitleGroups(
      Collection<Integer> groups, boolean includeMembers) {
    return fetchTitleGroupWithCache(groups, includeMembers).filter(TitleGroup::getIsAvailable);
  }

  private Observable<List<APITitleGroupSet>> getGroupSet(List<Integer> groups) {
    Observable<TitleGroup> titleGroupResponse = fetchTitleGroups(groups, true);
    return titleGroupResponse.toList().map(this::createGroupSets);
  }

  private Map<Integer, CompleteVideo> findLocalGroups(Collection<Integer> groups) {
    return videoLookup.getVideos(groups).values().stream()
        .filter(Optional::isPresent)
        .map(Optional::get)
        .filter(CompleteVideo::isContainer)
        .collect(Collectors.toMap(CompleteVideo::getId, group -> group));
  }

  private Map<Integer, CompleteVideo> findVideosVMS(List<Integer> videoIds) {
    Map<Integer, Optional<CompleteVideo>> map = videoLookup.getVideos(videoIds);
    return map.entrySet().stream()
        .filter(e -> e.getValue().isPresent() && e.getValue().get().isTitleGroup())
        .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().get()));
  }

  private Optional<CompleteVideo> findVideoVMS(Integer videoId) {
    Map<Integer, Optional<CompleteVideo>> videos = videoLookup.getVideos(Set.of(videoId));
    return Optional.ofNullable(videos)
        .flatMap(m -> m.get(videoId))
        .filter(CompleteVideo::isTitleGroup);
  }

  private Optional<APITitleGroupSetImpl> createGroupSetsRelativeTo(
      Integer relativeTo, TitleGroup titleGroup) {
    return createGroupSet(titleGroup)
        .map(
            groupSet -> {
              groupSet.getMembers().stream()
                  .filter(member -> member.getMemberId().equals(relativeTo))
                  .findAny()
                  .ifPresent(groupSet::setRelativeToMember);
              return groupSet;
            });
  }

  private List<APITitleGroupSet> createGroupSets(List<TitleGroup> groups) {
    List<APITitleGroupSet> list = new ArrayList<>(groups.size());
    Map<Integer, CompleteVideo> videos =
        findVideosVMS(groups.stream().map(TitleGroup::getId).toList());
    for (TitleGroup group : groups) {

      List<TitleGroupMember> members = group.getMemberList();
      List<APITitleGroupSetMember> groupSetMembers = new ArrayList<>(members.size());

      Integer groupId = group.getId();
      for (int i = 0; i < members.size(); i++) {
        TitleGroupMember member = members.get(i);
        APITitleGroupSetMember setMember = createMember(member.getKind(), member.getId(), groupId);
        if (setMember != null) {
          groupSetMembers.add(setMember);
        }
      }
      Optional<APITitleGroupSetImpl> groupSet = _createGroupSet(group, groupSetMembers, videos);
      groupSet.ifPresent(list::add);
    }
    return list;
  }

  private Optional<APITitleGroupSetImpl> createGroupSet(TitleGroup group) {
    List<TitleGroupMember> members = group.getMemberList();
    List<APITitleGroupSetMember> groupSetMembers = new ArrayList<>(members.size());

    Integer groupId = group.getId();
    for (int i = 0; i < members.size(); i++) {
      TitleGroupMember member = members.get(i);
      APITitleGroupSetMember setMember = createMember(member.getKind(), member.getId(), groupId);
      if (setMember != null) {
        groupSetMembers.add(setMember);
      }
    }
    return _createGroupSet(group, groupSetMembers);
  }

  private Optional<APITitleGroupSetImpl> _createGroupSet(
      TitleGroup vxsTitleGroup, List<APITitleGroupSetMember> members) {
    return findVideoVMS(vxsTitleGroup.getId())
        .map(
            completeVideo ->
                new APITitleGroupSetImpl(create(completeVideo, vxsTitleGroup), members));
  }

  private Optional<APITitleGroupSetImpl> _createGroupSet(
      TitleGroup vxsTitleGroup,
      List<APITitleGroupSetMember> members,
      Map<Integer, CompleteVideo> videos) {
    if (!videos.containsKey(vxsTitleGroup.getId())) {
      return Optional.empty();
    }
    CompleteVideo completeVideo = videos.get(vxsTitleGroup.getId());
    return Optional.of(new APITitleGroupSetImpl(create(completeVideo, vxsTitleGroup), members));
  }

  private APITitleGroupSetMember createMember(MemberKind kind, Integer memberId, Integer groupId) {
    return switch (kind) {
      case TITLE -> new APITitleGroupSetVideoMemberImpl(memberId, groupId);
      case COLLECTION -> new APITitleGroupSetGroupMemberImpl(memberId, groupId);
      default -> null;
    };
  }

  private static class FetchVXSTitleGroupKey extends Key<TitleGroup, Integer> {
    private FetchVXSTitleGroupKey(Integer groupId, boolean withMembers) {
      super(TitleGroup.class, groupId, withMembers);
    }
  }

  private static class FetchVXSTitleGroupMembershipsKey
      extends Key<TitleGroupMemberships, Integer> {
    private FetchVXSTitleGroupMembershipsKey(Integer videoId) {
      super(TitleGroupMemberships.class, videoId);
    }

    private static FetchVXSTitleGroupMembershipsKey createFromId(Integer videoId) {
      return new FetchVXSTitleGroupMembershipsKey(videoId);
    }

    private static FetchVXSTitleGroupMembershipsKey create(TitleGroupMemberships membership) {
      return new FetchVXSTitleGroupMembershipsKey(membership.getId());
    }
  }
}
