package dna.api.converters;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.protobuf.Message;
import java.util.function.Function;
import org.junit.jupiter.api.Test;

class ProtoUtilsTest {

  @Test
  void setFieldIfNotNull_withNonNullValue_callsFunction() {
    // Arrange
    String testValue = "test";
    Function<String, Message.Builder> mockFunction = mock(Function.class);
    Message.Builder mockBuilder = mock(Message.Builder.class);
    when(mockFunction.apply(testValue)).thenReturn(mockBuilder);

    // Act
    ProtoUtils.setFieldIfNotNull(testValue, mockFunction);

    // Assert
    verify(mockFunction).apply(testValue);
  }

  @Test
  void setFieldIfNotNull_withNullValue_doesNotCallFunction() {
    // Arrange
    Function<String, Message.Builder> mockFunction = mock(Function.class);

    // Act
    ProtoUtils.setFieldIfNotNull(null, mockFunction);

    // Assert
    verify(mockFunction, never()).apply(null);
  }

  @Test
  void overrideField_withNonNullValue_callsFunctionAndReturnsTrue() {
    // Arrange
    String testValue = "test";
    Function<String, Message.Builder> mockFunction = mock(Function.class);
    Message.Builder mockBuilder = mock(Message.Builder.class);
    when(mockFunction.apply(testValue)).thenReturn(mockBuilder);

    // Act
    boolean result = ProtoUtils.overrideField(testValue, mockFunction, false);

    // Assert
    assertTrue(result);
    verify(mockFunction).apply(testValue);
  }

  @Test
  void overrideField_withNullValue_doesNotCallFunctionAndReturnsOldChanged() {
    // Arrange
    Function<String, Message.Builder> mockFunction = mock(Function.class);

    // Act - test both true and false old values
    boolean resultWhenOldTrue = ProtoUtils.overrideField(null, mockFunction, true);
    boolean resultWhenOldFalse = ProtoUtils.overrideField(null, mockFunction, false);

    // Assert
    assertTrue(resultWhenOldTrue);
    assertFalse(resultWhenOldFalse);
    verify(mockFunction, never()).apply(null);
  }

  @Test
  void setIfTrue_withTrueValue_callsFunction() {
    // Arrange
    Function<Boolean, Message.Builder> mockFunction = mock(Function.class);
    Message.Builder mockBuilder = mock(Message.Builder.class);
    when(mockFunction.apply(true)).thenReturn(mockBuilder);

    // Act
    ProtoUtils.setIfTrue(true, mockFunction);

    // Assert
    verify(mockFunction).apply(true);
  }

  @Test
  void setIfTrue_withFalseValue_doesNotCallFunction() {
    // Arrange
    Function<Boolean, Message.Builder> mockFunction = mock(Function.class);

    // Act
    ProtoUtils.setIfTrue(false, mockFunction);

    // Assert
    verify(mockFunction, never()).apply(false);
  }

  @Test
  void setIfTrue_withNullValue_doesNotCallFunction() {
    // Arrange
    Function<Boolean, Message.Builder> mockFunction = mock(Function.class);

    // Act
    ProtoUtils.setIfTrue(null, mockFunction);

    // Assert
    verify(mockFunction, never()).apply(null);
  }

  @Test
  void convertAndSetFieldIfNotNull_withNonNullValue_callsBothFunctions() {
    // Arrange
    String sourceValue = "source";
    String targetValue = "target";
    Function<String, String> mockConverter = mock(Function.class);
    Function<String, Message.Builder> mockSetter = mock(Function.class);
    Message.Builder mockBuilder = mock(Message.Builder.class);

    when(mockConverter.apply(sourceValue)).thenReturn(targetValue);
    when(mockSetter.apply(targetValue)).thenReturn(mockBuilder);

    // Act
    ProtoUtils.convertAndSetFieldIfNotNull(sourceValue, mockConverter, mockSetter);

    // Assert
    verify(mockConverter).apply(sourceValue);
    verify(mockSetter).apply(targetValue);
  }

  @Test
  void convertAndSetFieldIfNotNull_withNullSource_doesNotCallFunctions() {
    // Arrange
    Function<String, String> mockConverter = mock(Function.class);
    Function<String, Message.Builder> mockSetter = mock(Function.class);

    // Act
    ProtoUtils.convertAndSetFieldIfNotNull(null, mockConverter, mockSetter);

    // Assert
    verify(mockConverter, never()).apply(null);
    verify(mockSetter, never()).apply(null);
  }

  @Test
  void convertAndSetFieldIfNotNull_withNullConvertedValue_doesNotCallSetter() {
    // Arrange
    String sourceValue = "source";
    Function<String, String> mockConverter = mock(Function.class);
    Function<String, Message.Builder> mockSetter = mock(Function.class);

    when(mockConverter.apply(sourceValue)).thenReturn(null);

    // Act
    ProtoUtils.convertAndSetFieldIfNotNull(sourceValue, mockConverter, mockSetter);

    // Assert
    verify(mockConverter).apply(sourceValue);
    verify(mockSetter, never()).apply(null);
  }
}
