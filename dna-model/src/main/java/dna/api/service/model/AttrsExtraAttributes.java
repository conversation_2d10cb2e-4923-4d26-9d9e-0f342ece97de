package dna.api.service.model;

import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class AttrsExtraAttributes {
  private Map<String, String> extraAttributes;

  public Map<String, String> getExtraAttributes() {
    return extraAttributes;
  }

  public AttrsExtraAttributes setExtraAttributes(Map<String, String> extraAttributes) {
    this.extraAttributes = extraAttributes;
    return this;
  }
}
