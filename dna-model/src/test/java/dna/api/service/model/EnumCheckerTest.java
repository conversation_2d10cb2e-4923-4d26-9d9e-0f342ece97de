package dna.api.service.model;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.empty;
import static org.hamcrest.Matchers.hasSize;

import dna.api.util.EnumUtil;
import org.junit.jupiter.api.Test;

class EnumCheckerTest {
  enum TestEnum {
    VALUE1,
    VALUE2
  }

  enum OtherTestEnum {
    VALUE1,
    VALUE2
  }

  enum OneMoreTestEnum {
    TEST1,
    VALUE1,
    TEST2
  }

  @Test
  void noMissingValues() {
    var missingValues = EnumUtil.findMissingValues(TestEnum.values(), OtherTestEnum.class);
    assertThat(missingValues, empty());
  }

  @Test
  void hasMissingValues() {
    var missingValues = EnumUtil.findMissingValues(TestEnum.values(), OneMoreTestEnum.class);
    assertThat(missingValues, hasSize(1));
    assertThat(missingValues, contains(TestEnum.VALUE2));
  }
}
