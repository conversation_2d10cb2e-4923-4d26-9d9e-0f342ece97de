package com.netflix.api.service.video;

import com.netflix.api.platform.util.PropertyRepositoryHolder;
import com.netflix.api.service.APICountry;
import com.netflix.api.service.APIRequest;
import com.netflix.api.service.APIRequestContext;
import com.netflix.api.service.RequestContextInternal;
import com.netflix.api.service.UnsupportedCountryException;
import com.netflix.archaius.api.Property;
import com.netflix.mantis.publish.api.MantisPublishContext;
import java.util.concurrent.atomic.AtomicReference;

public class GeoStatusHelper {

  private static final Property<Boolean> CHECK_COUNTRY_STATUS =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.checkCountryStatus", Boolean.class)
          .orElse(true);

  private static final Property<Boolean> PUBLISH_GEO_DATA =
      PropertyRepositoryHolder.getPropertyRepository()
          .get("com.netflix.api.getoStatus.publishGeoData", Boolean.class)
          .orElse(false);

  // to be only enabled in TEST; can be disabled after global launch

  public static void checkCountryStatus() {
    // check the country status and fail (throw
    // UnsupportedCountryException), if status is REJECT or F&F
    // but the user has not signed up for F&F in this country
    if (CHECK_COUNTRY_STATUS.get()) {
      @SuppressWarnings("unchecked")
      AtomicReference<UnsupportedCountryException> checkResult =
          (AtomicReference<UnsupportedCountryException>)
              RequestContextInternal.getCurrent().get("api.country.status.check");
      if (checkResult != null) {
        publishStatusCheckData("is_cached", "true");
        UnsupportedCountryException e = checkResult.get();
        if (e != null) {
          publishStatusCheckData("result", "reject");
          throw e;
        }
        publishStatusCheckData("result", "allow");
      } else {
        publishStatusCheckData("is_cached", "false");
        checkResult = new AtomicReference<>();
        try {
          APIRequest request = APIRequest.getCurrentRequest();
          APIRequestContext context = request.getRequestContext();
          APICountry country = context.getCountry();
          if (country != null) {
            publishStatusCheckData("country", country.getId());
            String status = country.getStatus();
            publishStatusCheckData("country_status", status);
            publishStatusCheckData("is_geo_overriden", Boolean.toString(context.isGeoOverriden()));
          } else {
            publishStatusCheckData("country", "null");
          }
          publishStatusCheckData("result", "allow");
        } catch (UnsupportedCountryException e) {
          checkResult.set(e);
          throw e;
        } finally {
          RequestContextInternal.getCurrent().put("api.country.status.check", checkResult);
        }
      }
    }
  }

  private static void publishStatusCheckData(String name, String value) {
    if (PUBLISH_GEO_DATA.get()) {
      MantisPublishContext.getCurrent().add("geo_status_check_" + name, value);
    }
  }
}
