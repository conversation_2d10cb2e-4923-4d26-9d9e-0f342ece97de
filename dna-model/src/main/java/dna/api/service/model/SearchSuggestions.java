package dna.api.service.model;

import com.netflix.demograph.graph.Atom;
import java.util.Map;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import netflix.falcor.client.data.GraphData;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class SearchSuggestions implements GraphData {
  /** @dotnext com.netflix.api.service.searchnew.APISearchList#getDisplayString */
  public String displayString;

  /** @dotnext com.netflix.api.service.searchnew.APISearchList#getOptionalAttributes */
  public Atom<Object> optionalAttributes;

  /** @dotnext com.netflix.api.service.searchnew.APISearchList#getReferenceId */
  public String searchSuggestionsId;

  /** @dotnext com.netflix.api.service.searchnew.APISearchList#getType */
  public String type;

  public Map<String, SearchSuggestionsItem> items;

  public String getDisplayString() {
    return displayString;
  }

  public Atom<Object> getOptionalAttributes() {
    return optionalAttributes;
  }

  public String getSearchSuggestionsId() {
    return searchSuggestionsId;
  }

  public String getType() {
    return type;
  }

  public SearchSuggestions setDisplayString(String displayString) {
    this.displayString = displayString;
    return this;
  }

  public SearchSuggestions setOptionalAttributes(Atom<Object> optionalAttributes) {
    this.optionalAttributes = optionalAttributes;
    return this;
  }

  public SearchSuggestions setSearchSuggestionsId(String searchSuggestionsId) {
    this.searchSuggestionsId = searchSuggestionsId;
    return this;
  }

  public SearchSuggestions setType(String type) {
    this.type = type;
    return this;
  }
}
