package com.netflix.api.service.video;

/**
 * Video-specific colors used to thematically differentiate the Netflix UI. As of this writing there
 * are two unique hexadecimal color values for each title, background and highlight.
 *
 * <p>Colors are managed through <a href="http://go/cinder">Cinder</a>. Any data-related issues
 * should be reported to the <a href="https://netflix.slack.com/messages/CCW6BCMN1">#video-color</a>
 * Slack channel.
 *
 * @see <a
 *     href="https://docs.google.com/document/d/1pBWcXwlt36zD0IQ2SrqDKMm1GzmYk23s35jZdY1-ucE/edit">Color
 *     Thieving</a>
 * @see <a
 *     href="https://splash.test.netflix.net/dashboard/#/cinder.oscar.memento.videoColors/">Cinder
 *     Dashboard (TEST)</a>
 * @see <a
 *     href="https://splash.prod.netflix.net/dashboard/#/cinder.oscar.memento.videoColors/">Cinder
 *     Dashboard (PROD)</a>
 */
public class APIVideoColors {

  private final String backgroundColor;
  private final String highlightColor;

  public APIVideoColors() {
    this(null, null);
  }

  public APIVideoColors(String backgroundColor, String highlightColor) {
    this.backgroundColor = backgroundColor;
    this.highlightColor = highlightColor;
  }

  /**
   * @return the background color in hexadecimal, or null if not available for the underlying video.
   */
  public String getBackgroundColor() {
    return backgroundColor;
  }

  /**
   * @return the highlight color in hexadecimal, or null if not available for the underlying video.
   */
  public String getHighlightColor() {
    return highlightColor;
  }

  public static Color hex2Rgb(String hexColor) {
    return new Color(
        Integer.valueOf(hexColor.substring(0, 2), 16),
        Integer.valueOf(hexColor.substring(2, 4), 16),
        Integer.valueOf(hexColor.substring(4, 6), 16));
  }

  @Override
  public String toString() {
    return "APIVideoColors{"
        + "backgroundColor='"
        + backgroundColor
        + '\''
        + ", highlightColor='"
        + highlightColor
        + '\''
        + '}';
  }
}
