package com.netflix.api.service.titlegroup;

import com.google.inject.Provider;
import com.netflix.api.service.titlegroup.vxs.APIVXSTitleGroupDataSourceImpl;
import com.netflix.api.service.video.APIVideoFactory;
import com.netflix.lang.RequestVariable;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.vxs.protogen.VxsServiceGrpc;
import java.util.concurrent.atomic.AtomicReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class APITitleGroupDataSourceProvider implements Provider<APITitleGroupDataSource> {
  private static final RequestVariable<AtomicReference<APITitleGroupDataSource>>
      scopedVXSDataSource =
          new RequestVariable<>() {
            @Override
            protected AtomicReference<APITitleGroupDataSource> initialValue() {
              return new AtomicReference<>();
            }
          };

  private final VxsServiceGrpc.VxsServiceStub vxsClient;
  private final Provider<APIVideoFactory> videoFactoryProvider;

  @Autowired
  APITitleGroupDataSourceProvider(
      @GrpcSpringClient("vxs") VxsServiceGrpc.VxsServiceStub vxsClient,
      Provider<APIVideoFactory> videoFactoryProvider) {
    this.vxsClient = vxsClient;
    this.videoFactoryProvider = videoFactoryProvider;
  }

  @Override
  public APITitleGroupDataSource get() {
    return getVxsDataSource();
  }

  public APITitleGroupDataSource getVxsDataSource() {
    AtomicReference<APITitleGroupDataSource> ref = scopedVXSDataSource.get();
    APITitleGroupDataSource attempt = ref.get();
    if (attempt == null) {
      attempt = new APIVXSTitleGroupDataSourceImpl(videoFactoryProvider.get(), vxsClient);
      if (!ref.compareAndSet(null, attempt)) {
        // another thread did set the data source before we tried to
        return ref.get();
      }
    }
    return attempt;
  }
}
