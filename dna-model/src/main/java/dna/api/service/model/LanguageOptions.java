package dna.api.service.model;

import java.util.List;
import javax.annotation.Generated;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Generated("netflix.falcor.schema:falcor-schema-codegen")
@EqualsAndHashCode
@ToString
public class LanguageOptions {
  private List<String> preferredLanguages;

  private List<String> supportedLanguages;

  private Boolean useNonMemberLanguagePreferences;

  public List<String> getPreferredLanguages() {
    return preferredLanguages;
  }

  public List<String> getSupportedLanguages() {
    return supportedLanguages;
  }

  public Boolean getUseNonMemberLanguagePreferences() {
    return useNonMemberLanguagePreferences;
  }

  public LanguageOptions setPreferredLanguages(List<String> preferredLanguages) {
    this.preferredLanguages = preferredLanguages;
    return this;
  }

  public LanguageOptions setSupportedLanguages(List<String> supportedLanguages) {
    this.supportedLanguages = supportedLanguages;
    return this;
  }

  public LanguageOptions setUseNonMemberLanguagePreferences(
      Boolean useNonMemberLanguagePreferences) {
    this.useNonMemberLanguagePreferences = useNonMemberLanguagePreferences;
    return this;
  }
}
