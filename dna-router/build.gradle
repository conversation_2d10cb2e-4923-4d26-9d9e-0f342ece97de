import org.graalvm.polyglot.Context
import org.graalvm.polyglot.HostAccess
import org.graalvm.polyglot.Source

buildscript {
    dependencies {
        classpath "netflix.falcor.schema:falcor-schema-gradle-plugin:3.19.1"
        classpath "org.graalvm.js:js:21.2.0"
        classpath "org.graalvm.js:js-scriptengine:21.2.0"
        // https://mvnrepository.com/artifact/org.graalvm.sdk/graal-sdk
        classpath 'org.graalvm.sdk:graal-sdk:21.2.0'
    }
}

apply plugin: 'netflix.spring-boot-netflix-application-core'
apply plugin: 'netflix.falcor.schema'
apply plugin: 'netflix.lombok'

apply from: file('../dependency-properties.gradle')
apply from: file('../dependency-overrides.gradle')

// create a custom configuration only for the bundle download
configurations {
    bundleOnly
}

repositories {
    // a way to treat the internal netflix npm repo as a gradle ivy repo
    ivy {

        url = 'https://artifacts.netflix.net/npm-local/'

        patternLayout {
            artifact '/[organisation]/[module]/-/[organisation]/[module]-[revision].tgz'
        }

        // This is required in Gradle 6.0+ as metadata file (ivy.xml)
        // is mandatory. Docs linked below this code section
        metadataSources { artifact() }
    }
}

def bundlePackageOrg = "@netflix-internal"
def bundlePackageName = "dna-graphql-schema-bundled"
def bundleVersion = "1.1.0"

dependencies {
    // DO NOT ADD NON-PROJECT DEPENDENCIES HERE
    implementation project(':api-service-layer')
    implementation project(":dna-client")
    implementation project(":dna-model")
    implementation project(":api-global-dependencies")

    implementation "jakarta.servlet:jakarta.servlet-api"

    testImplementation(platform("org.junit:junit-bom:${junitBomVersion}"))
    testImplementation("org.junit.jupiter:junit-jupiter")
    testImplementation("junit:junit")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    testImplementation("com.netflix.microcontext:microcontext-test:latest.release")
    testImplementation("org.assertj:assertj-core:${assert4jVersion}")
    testImplementation("org.hamcrest:hamcrest-library:${hamcrestVersion}")
    testImplementation("org.mockito:mockito-core:${mockitoVersion}")

    // don't forget to update the global.lock version by hand as well - dep updates can't do this automagically
    bundleOnly "$bundlePackageOrg:$bundlePackageName:$bundleVersion"
}
tasks.withType(Test).configureEach { 
    useJUnitPlatform()
}

task checkLatestNPMBundleVersion() {
    def bundleUrlMetadata = "http://artifacts.netflix.com/api/npm/npm-netflix/$bundlePackageOrg%2f$bundlePackageName"
    def jsonText = new URL( bundleUrlMetadata ).getText(  )
    def parsedJson = new groovy.json.JsonSlurper().parseText(jsonText)
    def currentLatest = parsedJson["dist-tags"].latest
    def bundleDependency = configurations.bundleOnly.resolvedConfiguration.firstLevelModuleDependencies.find { it.name.contains(bundlePackageName)}
    if (!bundleDependency.moduleVersion.equals(currentLatest)) {
        print("--- HEADS UP for DNA Router ---\nLocal resolved version of $bundlePackageOrg/$bundlePackageName is $bundleDependency.moduleVersion but current latest is $currentLatest\nYou likely need to update this (and the lock file version) by hand!\n-----\n")
    }
}

task extractGraphQLSchemaBundle(type: Copy) {
    // use our custom bundle configuration to get the bundled version
    def bundleJarFile = configurations.bundleOnly.files.find { it.name.contains("dna-graphql-schema-bundled") }
    def outpath = "$buildDir/dna-graphql-schema-bundled"
    // it looks like a jar/zip - but it's really a tar file
    from(tarTree(resources.gzip(bundleJarFile))) {
        include "package/**"
    }
    into outpath
}

task generateJsonAstFromGraphQLSchema() {
    dependsOn checkLatestNPMBundleVersion, extractGraphQLSchemaBundle
    doLast {
        def rootDir = "${rootProject.rootDir.toString()}"
        def resourceDir = "$rootDir/dna-router/src/main/resources"
        def extractedRoot = "$buildDir/dna-graphql-schema-bundled/package"
        def bundleFile = "$extractedRoot/dist/bundle.cjs.js"
        def globalsFile = "$extractedRoot/globals.js"
        def replacementsRoot = "$extractedRoot/replacements"

        // this constructs the args to pass to graal
        // we run graal with JS and run our ast generation bundle
        // this lets us bypass node / npm
        // it does require some "small" shims - which basically call out to their java
        // counterparts - but must be specified here (any 'require' statements probably need an entry here)

        Map<String, String> options = new HashMap<>();
        options.put("js.commonjs-global-properties", globalsFile.toString());
        options.put("js.commonjs-require", "true");
        options.put("js.scripting", "true");
        options.put("js.commonjs-require-cwd", "$replacementsRoot".toString());
        options.put("js.commonjs-core-modules-replacements",
                "path:$replacementsRoot/path,".toString() +
                "fs:$replacementsRoot/fs,".toString() +
                "util:$replacementsRoot/util".toString()
        );

        Context cx = Context.newBuilder("js")
                .allowExperimentalOptions(true)
                .environment("NODE_ENV", "prod")
                .environment("OUTFILE", "$resourceDir/api-ast.json")
                .environment("SCHEMA_SEARCH_DIR", "$resourceDir")
                .allowIO(true)
                .allowHostAccess(HostAccess.ALL)
                 //allows access to all Java classes
                .allowHostClassLookup(className -> true)
                .options(options)
                .build();

        Source s = Source.newBuilder("js", new File(bundleFile)).build();
        cx.eval(s)
        print 'Successfully generated json AST from GraphQL Schema'
    }
}

generateFalcorRoutesFromAst {
    dependsOn 'generateJsonAstFromGraphQLSchema', 'generateFalcorModelFromAst'
    packageName = "dna.api.service"
    routesSuperInterface = "Dna"
    targetDirectory = "${rootProject.rootDir.absolutePath}/dna-router/src/main/java/"
    schemaDirectory = "${rootProject.rootDir.toString()}/dna-router/src/main/resources"
    extendStructs = true
}

task generateFalcor() {
    description = 'Generates all the required artifacts from a json ast'
    dependsOn 'generateJsonAstFromGraphQLSchema', 'generateFalcorModelFromAst', 'generateFalcorRoutesFromAst', 'generateFalcorClientFromAst'
    doLast {
        print 'Successfully generated Falcor artifacts'
    }
}

generateFalcorModelFromAst {
    dependsOn generateJsonAstFromGraphQLSchema
    packageName = "dna.api.service"
    targetDirectory = "${rootProject.rootDir.absolutePath}/dna-model/src/main/java/"
    schemaDirectory = "${rootProject.rootDir.toString()}/dna-router/src/main/resources"
    extendStructs = true
}

generateFalcorClientFromAst {
    dependsOn generateJsonAstFromGraphQLSchema, generateFalcorModelFromAst
    packageName = "dna.api.service.client"
    modelPackage = "dna.api.service.model"
    targetDirectory = "${rootProject.rootDir.absolutePath}/dna-client/src/main/java/"
    schemaDirectory = "${rootProject.rootDir.toString()}/dna-router/src/main/resources"
    extendStructs = true
}

compileJava {
    dependsOn 'generateFalcorModelFromAst', 'generateFalcorRoutesFromAst'
}

processResources {
    dependsOn 'generateJsonAstFromGraphQLSchema'
}

if (project.hasProperty('includeProto2api')) {
    apply from: 'proto2api.gradle'
}

clean.doFirst {
    delete "${rootProject.rootDir.absolutePath}/dna-router/src/main/resources/graphql-schema/node_modules"
}

spotlessJava {
    dependsOn 'generateFalcorRoutesFromAst'
}

jakartaeeMigration {
    excludeTransform('netflix:urlutil')
}

dependencyRecommendations {
    mavenBom module: 'com.netflix.spring:spring-boot-netflix-bom:latest.release'
}
